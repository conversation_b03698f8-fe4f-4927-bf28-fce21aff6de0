{"generation": 10, "timestamp": "2025-07-31T18:19:31.783113", "best_solutions": [{"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}, {"individual_id": "gen8_off70", "energy_consumption": 20.0, "thermal_performance": 0.07080389515491378, "renovation_cost": 500.0, "constraint_violations": 0.139991828125128, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.008045687457004298, "overall_u_value": 0.4170568574088492, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.2435414533545843, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.27105497130330963], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[4.531418401090166, 1.4844691490555038]], "window_sizes": [[1.5544268166932302, 0.8]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:19:28.552780"}], "population_size": 250, "feasible_count": 250}