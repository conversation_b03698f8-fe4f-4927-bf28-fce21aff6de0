[{"generation": 0, "elapsed_time": 2.868928, "hypervolume": 2.6217201865126993, "diversity_metric": 0.5335523206494917, "convergence_metric": 1.0, "distribution_uniformity": 0.47355102563314116, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [25.111949307578392, 0.09618309790659685, 688.2497119255141], "average_objectives": [120.21481403266607, 0.5694718702504334, 4350.789850714856], "constraint_violation": 1.6271385458461487, "stagnation_count": 0}, {"generation": 1, "elapsed_time": 3.919778, "hypervolume": 2.6217201865126993, "diversity_metric": 0.3564590525784548, "convergence_metric": 1.0, "distribution_uniformity": 0.47355102563314116, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [25.111949307578392, 0.09618309790659685, 688.2497119255141], "average_objectives": [73.32505461918956, 0.38636038732854505, 2378.6008007446676], "constraint_violation": 1.1326749248943908, "stagnation_count": 0}, {"generation": 2, "elapsed_time": 5.037997, "hypervolume": 2.6217201865126993, "diversity_metric": 0.28779343857794704, "convergence_metric": 0.08633803157338409, "distribution_uniformity": 0.47355102563314116, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [25.111949307578392, 0.09618309790659685, 688.2497119255141], "average_objectives": [62.772172275842905, 0.31607318420231917, 1864.8292573711042], "constraint_violation": 1.0609601130110418, "stagnation_count": 1}, {"generation": 3, "elapsed_time": 6.239299, "hypervolume": 3.594297282726787, "diversity_metric": 0.3130343496509124, "convergence_metric": 0.09391030489527373, "distribution_uniformity": 0.5062566900052815, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [24.532465374697296, 0.09618309790659685, 564.5485148911552], "average_objectives": [53.55127281606244, 0.2803430784543986, 1753.1913145931373], "constraint_violation": 1.0083759516203927, "stagnation_count": 2}, {"generation": 4, "elapsed_time": 7.280355, "hypervolume": 3.564854213584576, "diversity_metric": 0.3071877498787618, "convergence_metric": 0.7921563249636285, "distribution_uniformity": 0.5490932108762661, "feasibility_ratio": 1.0, "improvement_rate": 0.2222222222222222, "best_objectives": [23.687835323726173, 0.0906059701132526, 564.5485148911552], "average_objectives": [47.414237447058085, 0.24664692177047712, 1440.2369028489125], "constraint_violation": 0.7428241159952034, "stagnation_count": 0}, {"generation": 5, "elapsed_time": 8.468161, "hypervolume": 3.564854213584576, "diversity_metric": 0.3055654757402098, "convergence_metric": 0.75520082455355, "distribution_uniformity": 0.5490932108762661, "feasibility_ratio": 1.0, "improvement_rate": 0.3333333333333333, "best_objectives": [23.687835323726173, 0.0906059701132526, 564.5485148911552], "average_objectives": [39.78339116639541, 0.19888041670007622, 1319.647718574767], "constraint_violation": 0.6917767258086415, "stagnation_count": 0}, {"generation": 6, "elapsed_time": 9.458345, "hypervolume": 0.6796353844247677, "diversity_metric": 0.20717124934930295, "convergence_metric": 0.7256825566362779, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.26666666666666666, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [32.745818052428284, 0.14124893002212024, 1202.790107769585], "constraint_violation": 0.2774953080326484, "stagnation_count": 1}, {"generation": 7, "elapsed_time": 10.542133, "hypervolume": 2.038906153274303, "diversity_metric": 0.16854876401172447, "convergence_metric": 0.7505646292035174, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.3888888888888889, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [30.98864421989733, 0.12583032733572674, 1162.1722791734776], "constraint_violation": 0.1333396593567077, "stagnation_count": 0}, {"generation": 8, "elapsed_time": 11.593151, "hypervolume": 6.796353844247678, "diversity_metric": 0.21254292586992687, "convergence_metric": 0.763762877760978, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.3333333333333333, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [28.81865881562687, 0.12278828625756308, 886.3949098597709], "constraint_violation": 0.14822724804487494, "stagnation_count": 1}, {"generation": 9, "elapsed_time": 12.694928, "hypervolume": 16.311249226194434, "diversity_metric": 0.1892057189619115, "convergence_metric": 0.7567617156885734, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.2916666666666667, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [26.830695479782914, 0.11521661676437411, 727.0395571725078], "constraint_violation": 0.14825952708570686, "stagnation_count": 2}, {"generation": 10, "elapsed_time": 13.837884, "hypervolume": 35.34103999008789, "diversity_metric": 0.19121583327152472, "convergence_metric": 0.7573647499814574, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.25925925925925924, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [24.787786048512245, 0.10502614486850212, 661.1526723144232], "constraint_violation": 0.14276131254396066, "stagnation_count": 3}, {"generation": 11, "elapsed_time": 15.03326, "hypervolume": 79.51733997769794, "diversity_metric": 0.17695833732685587, "convergence_metric": 0.7530875011980567, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.25925925925925924, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [22.441927431441364, 0.08758965162011793, 638.4246788842888], "constraint_violation": 0.14084579707514197, "stagnation_count": 4}, {"generation": 12, "elapsed_time": 16.327432, "hypervolume": 101.94530766371537, "diversity_metric": 0.15830248793191098, "convergence_metric": 0.7474907463795732, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.25925925925925924, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [21.739073131868157, 0.08323967006809951, 597.1156102255812], "constraint_violation": 0.03580245686114843, "stagnation_count": 5}, {"generation": 13, "elapsed_time": 17.806748, "hypervolume": 101.94530766371537, "diversity_metric": 0.1551947791356976, "convergence_metric": 0.7465584337407092, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.18518518518518517, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [21.559471661542382, 0.08290164381418243, 587.4211041308681], "constraint_violation": 0.03580245686114843, "stagnation_count": 6}, {"generation": 14, "elapsed_time": 19.416361, "hypervolume": 101.94530766371537, "diversity_metric": 0.15276486371059936, "convergence_metric": 0.7458294591131798, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.1111111111111111, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [21.445222421763603, 0.08270106893876988, 581.2993904638035], "constraint_violation": 0.03580245686114843, "stagnation_count": 7}, {"generation": 15, "elapsed_time": 20.746083, "hypervolume": 101.94530766371537, "diversity_metric": 0.14944892781554356, "convergence_metric": 0.744834678344663, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.1111111111111111, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [21.32499494739531, 0.08244103127976513, 575.0054293470265], "constraint_violation": 0.03696040552597706, "stagnation_count": 8}, {"generation": 16, "elapsed_time": 22.220348, "hypervolume": 101.94530766371537, "diversity_metric": 0.1437192051546292, "convergence_metric": 0.5367055051361336, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [21.12893825575432, 0.08207349363302946, 564.2870520640815], "constraint_violation": 0.03696040552597706, "stagnation_count": 9}, {"generation": 17, "elapsed_time": 23.642657, "hypervolume": 101.94530766371537, "diversity_metric": 0.13642820867030425, "convergence_metric": 0.04092846260109125, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.932881564113334, 0.08171273805946828, 553.5686747811366], "constraint_violation": 0.03696040552597706, "stagnation_count": 10}, {"generation": 18, "elapsed_time": 25.030063, "hypervolume": 101.94530766371537, "diversity_metric": 0.1271666125829789, "convergence_metric": 0.03814998377489364, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.736824872472344, 0.08135198248590708, 542.8502974981916], "constraint_violation": 0.03696040552597706, "stagnation_count": 11}, {"generation": 19, "elapsed_time": 26.522872, "hypervolume": 101.94530766371537, "diversity_metric": 0.1200270562959969, "convergence_metric": 0.03600811688879906, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.616899132902233, 0.0809801002585536, 536.5642423708971], "constraint_violation": 0.03696040552597706, "stagnation_count": 12}, {"generation": 20, "elapsed_time": 27.940187, "hypervolume": 101.94530766371537, "diversity_metric": 0.11575661071613212, "convergence_metric": 0.034726983214839635, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.551546902355234, 0.08071051353094645, 532.9914499432488], "constraint_violation": 0.03696040552597707, "stagnation_count": 13}, {"generation": 21, "elapsed_time": 29.398143, "hypervolume": 101.94530766371537, "diversity_metric": 0.1076347119648009, "convergence_metric": 0.032290413589440264, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.44262651811024, 0.08036127885702195, 527.0367958971682], "constraint_violation": 0.03696040552597707, "stagnation_count": 14}, {"generation": 22, "elapsed_time": 30.778198, "hypervolume": 101.94530766371537, "diversity_metric": 0.09327388967195692, "convergence_metric": 0.027982166901587068, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.288466758509493, 0.07976605599472243, 518.771777091984], "constraint_violation": 0.03696040552597707, "stagnation_count": 15}, {"generation": 23, "elapsed_time": 32.243299, "hypervolume": 101.94530766371537, "diversity_metric": 0.07494305599132088, "convergence_metric": 0.022482916797396255, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.145506756927535, 0.07924601694730748, 510.73986262795165], "constraint_violation": 0.030144482178953613, "stagnation_count": 16}, {"generation": 24, "elapsed_time": 33.566615, "hypervolume": 101.94530766371537, "diversity_metric": 0.05760884344718409, "convergence_metric": 0.017282653034155215, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.046775429036252, 0.07869897790868002, 504.59663549856003], "constraint_violation": 0.030144482178953613, "stagnation_count": 17}, {"generation": 25, "elapsed_time": 34.960698, "hypervolume": 101.94530766371537, "diversity_metric": 0.04146352365133201, "convergence_metric": 0.012439057095399594, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.025530062818785, 0.07821118093255561, 501.8406438034931], "constraint_violation": 0.016523224549443116, "stagnation_count": 18}, {"generation": 26, "elapsed_time": 36.414785, "hypervolume": 101.94530766371537, "diversity_metric": 0.03269163538431504, "convergence_metric": 0.009807490615294507, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.010778721523877, 0.07764800895366755, 500.85952972800214], "constraint_violation": 0.0, "stagnation_count": 19}, {"generation": 27, "elapsed_time": 38.367109, "hypervolume": 101.94530766371537, "diversity_metric": 0.019664354347805998, "convergence_metric": 0.005899306304341783, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.0, 0.07729907473889307, 500.0], "constraint_violation": 0.0, "stagnation_count": 20}, {"generation": 28, "elapsed_time": 40.244357, "hypervolume": 101.94530766371537, "diversity_metric": 0.018203695221153744, "convergence_metric": 0.005461108566346113, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.07499424931583643, 500.0], "average_objectives": [20.0, 0.07688314436657614, 500.0], "constraint_violation": 0.0, "stagnation_count": 21}, {"generation": 29, "elapsed_time": 41.720239, "hypervolume": 102.55865243755386, "diversity_metric": 0.025114732892894353, "convergence_metric": 0.0075344198678682995, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07639521729761714, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 22}, {"generation": 30, "elapsed_time": 43.148664, "hypervolume": 102.55865243755386, "diversity_metric": 0.021454521438965684, "convergence_metric": 0.01696507402365584, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07581562626251609, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 0}, {"generation": 31, "elapsed_time": 44.513425, "hypervolume": 102.55865243755386, "diversity_metric": 0.01903824283112635, "convergence_metric": 0.016240190441304043, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07553833934763812, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 1}, {"generation": 32, "elapsed_time": 45.810672, "hypervolume": 102.55865243755386, "diversity_metric": 0.01442503974285013, "convergence_metric": 0.014856229514821177, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.0751848037023619, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 2}, {"generation": 33, "elapsed_time": 47.209683, "hypervolume": 102.55865243755386, "diversity_metric": 0.01442503974285013, "convergence_metric": 0.014856229514821177, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.0751848037023619, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 3}, {"generation": 34, "elapsed_time": 48.503236, "hypervolume": 102.55865243755386, "diversity_metric": 0.01442503974285013, "convergence_metric": 0.00432751192285504, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.0751848037023619, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 4}, {"generation": 35, "elapsed_time": 49.844329, "hypervolume": 102.55865243755386, "diversity_metric": 0.01442503974285013, "convergence_metric": 0.00432751192285504, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.0751848037023619, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 5}, {"generation": 36, "elapsed_time": 51.216441, "hypervolume": 102.55865243755386, "diversity_metric": 0.01442503974285013, "convergence_metric": 0.00432751192285504, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.0751848037023619, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 6}, {"generation": 37, "elapsed_time": 52.491601, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 7}, {"generation": 38, "elapsed_time": 53.98214, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 8}, {"generation": 39, "elapsed_time": 55.269213, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 9}, {"generation": 40, "elapsed_time": 56.605236, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 10}, {"generation": 41, "elapsed_time": 57.91783, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 11}, {"generation": 42, "elapsed_time": 59.192318, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 12}, {"generation": 43, "elapsed_time": 60.568833, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 13}, {"generation": 44, "elapsed_time": 61.837049, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 14}, {"generation": 45, "elapsed_time": 63.089788, "hypervolume": 102.55865243755386, "diversity_metric": 0.013712180074379876, "convergence_metric": 0.004113654022313964, "distribution_uniformity": 0.07548623297930128, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07515883639700797, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 15}, {"generation": 46, "elapsed_time": 63.962652, "hypervolume": 1.2785123263846976, "diversity_metric": 0.012342882527261179, "convergence_metric": 0.003702864758178345, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07509460197957483, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 16}, {"generation": 47, "elapsed_time": 64.727188, "hypervolume": 1.2785123263846976, "diversity_metric": 0.011880447337164991, "convergence_metric": 0.7035641342011495, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07507444731169659, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 17}, {"generation": 48, "elapsed_time": 65.518453, "hypervolume": 3.2740149840233186, "diversity_metric": 0.010882826508252189, "convergence_metric": 0.7032648479524756, "distribution_uniformity": 0.3333333333333333, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07499629826929907, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 18}, {"generation": 49, "elapsed_time": 66.409616, "hypervolume": 9.260522956939178, "diversity_metric": 0.011235007852964463, "convergence_metric": 0.7033705023558893, "distribution_uniformity": 0.21712927295533252, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07492918614123609, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 19}, {"generation": 50, "elapsed_time": 67.236159, "hypervolume": 22.56387400786331, "diversity_metric": 0.011845145646849046, "convergence_metric": 0.7035535436940547, "distribution_uniformity": 0.1482675827043134, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.0747914162097641, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 20}, {"generation": 51, "elapsed_time": 68.283506, "hypervolume": 61.80875960808949, "diversity_metric": 0.012645597050741511, "convergence_metric": 0.7037936791152224, "distribution_uniformity": 0.09441387963324659, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06867437033778025, 500.0], "average_objectives": [20.0, 0.07438499491192176, 500.8402978686383], "constraint_violation": 0.0102107256562775, "stagnation_count": 21}, {"generation": 52, "elapsed_time": 69.91977, "hypervolume": 147.57755435637634, "diversity_metric": 0.01258407948193003, "convergence_metric": 0.7037752238445789, "distribution_uniformity": 0.0630275852150841, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.07347439046320205, 500.92160123554356], "constraint_violation": 0.0102107256562775, "stagnation_count": 22}, {"generation": 53, "elapsed_time": 70.818964, "hypervolume": 2.941382211011898, "diversity_metric": 0.01477012028705132, "convergence_metric": 0.7044310360861153, "distribution_uniformity": 0.3333333333333333, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.0732106408110684, 501.1655113362594], "constraint_violation": 0.0102107256562775, "stagnation_count": 0}, {"generation": 54, "elapsed_time": 71.594713, "hypervolume": 7.609340153015298, "diversity_metric": 0.018519638112099345, "convergence_metric": 0.7055558914336297, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.07292474741578732, 501.7346349045962], "constraint_violation": 0.0102107256562775, "stagnation_count": 1}, {"generation": 55, "elapsed_time": 72.522941, "hypervolume": 20.33465150631235, "diversity_metric": 0.02494453553701527, "convergence_metric": 0.7074833606611045, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.07208991859871838, 503.4420056096068], "constraint_violation": 0.0102107256562775, "stagnation_count": 2}, {"generation": 56, "elapsed_time": 73.558744, "hypervolume": 54.993678111527885, "diversity_metric": 0.032875210577982004, "convergence_metric": 0.7098625631733946, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06979290619682295, 508.15760089011223], "constraint_violation": 0.0102107256562775, "stagnation_count": 3}, {"generation": 57, "elapsed_time": 75.16982, "hypervolume": 130.1944957439821, "diversity_metric": 0.02516429193705069, "convergence_metric": 0.7075492875811151, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06486235264037901, 518.2392183863652], "constraint_violation": 0.0102107256562775, "stagnation_count": 4}, {"generation": 58, "elapsed_time": 77.031118, "hypervolume": 144.5826268355777, "diversity_metric": 0.010106421982015343, "convergence_metric": 0.7030319265946046, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 5}, {"generation": 59, "elapsed_time": 78.889868, "hypervolume": 144.58262683557768, "diversity_metric": 0.010106421982015326, "convergence_metric": 0.7030319265946046, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 6}, {"generation": 60, "elapsed_time": 80.630645, "hypervolume": 144.58262683557768, "diversity_metric": 0.010106421982015395, "convergence_metric": 0.7030319265946046, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 7}, {"generation": 61, "elapsed_time": 82.366119, "hypervolume": 144.58262683557768, "diversity_metric": 0.010106421982015395, "convergence_metric": 0.7030319265946046, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.037037037037037035, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 8}, {"generation": 62, "elapsed_time": 84.126125, "hypervolume": 144.58262683557768, "diversity_metric": 0.010106421982015395, "convergence_metric": 0.1964289612880335, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 9}, {"generation": 63, "elapsed_time": 85.792822, "hypervolume": 144.58262683557768, "diversity_metric": 0.010106421982015395, "convergence_metric": 0.0030319265946049537, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 10}, {"generation": 64, "elapsed_time": 87.51126, "hypervolume": 144.58262683557768, "diversity_metric": 0.010106421982015395, "convergence_metric": 0.0030319265946046102, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 11}, {"generation": 65, "elapsed_time": 89.132778, "hypervolume": 144.58262683557768, "diversity_metric": 0.010106421982015395, "convergence_metric": 0.0030319265946046102, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06390100453001332, 519.4315046903586], "constraint_violation": 0.0, "stagnation_count": 12}, {"generation": 66, "elapsed_time": 90.748076, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 13}, {"generation": 67, "elapsed_time": 92.581042, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003930923337179439, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 14}, {"generation": 68, "elapsed_time": 94.269199, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003930923337179439, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 15}, {"generation": 69, "elapsed_time": 96.160682, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003930923337179439, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 16}, {"generation": 70, "elapsed_time": 97.823067, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003930923337179438, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234533], "constraint_violation": 0.0, "stagnation_count": 17}, {"generation": 71, "elapsed_time": 99.539989, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.0031592331994759145, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234533], "constraint_violation": 0.0, "stagnation_count": 18}, {"generation": 72, "elapsed_time": 101.249575, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 19}, {"generation": 73, "elapsed_time": 102.948183, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 20}, {"generation": 74, "elapsed_time": 104.680725, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 21}, {"generation": 75, "elapsed_time": 106.448513, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 22}, {"generation": 76, "elapsed_time": 108.121583, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 23}, {"generation": 77, "elapsed_time": 109.813191, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 24}, {"generation": 78, "elapsed_time": 111.457505, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 25}, {"generation": 79, "elapsed_time": 113.476498, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 26}, {"generation": 80, "elapsed_time": 115.302583, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 27}, {"generation": 81, "elapsed_time": 117.199161, "hypervolume": 144.64638282827042, "diversity_metric": 0.010530777331586421, "convergence_metric": 0.003159233199475915, "distribution_uniformity": 0.0, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06392878851861102, 519.3502013234532], "constraint_violation": 0.0, "stagnation_count": 28}, {"generation": 82, "elapsed_time": 118.992105, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 29}, {"generation": 83, "elapsed_time": 120.80605, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.003244359248736249, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 30}, {"generation": 84, "elapsed_time": 123.720166, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.003244359248736249, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 31}, {"generation": 85, "elapsed_time": 125.374479, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.003244359248736249, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 32}, {"generation": 86, "elapsed_time": 127.006283, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.003244359248736249, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 33}, {"generation": 87, "elapsed_time": 128.866802, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 34}, {"generation": 88, "elapsed_time": 130.617218, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 35}, {"generation": 89, "elapsed_time": 132.22691, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 36}, {"generation": 90, "elapsed_time": 133.834383, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 37}, {"generation": 91, "elapsed_time": 135.523219, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 38}, {"generation": 92, "elapsed_time": 137.29665, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 39}, {"generation": 93, "elapsed_time": 139.080771, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 40}, {"generation": 94, "elapsed_time": 141.054973, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 41}, {"generation": 95, "elapsed_time": 142.739909, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 42}, {"generation": 96, "elapsed_time": 144.375803, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 43}, {"generation": 97, "elapsed_time": 146.171801, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 44}, {"generation": 98, "elapsed_time": 147.724588, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 45}, {"generation": 99, "elapsed_time": 149.411878, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 46}, {"generation": 100, "elapsed_time": 151.270496, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 47}, {"generation": 101, "elapsed_time": 153.091472, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 48}, {"generation": 102, "elapsed_time": 154.762385, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 49}, {"generation": 103, "elapsed_time": 156.387075, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 50}, {"generation": 104, "elapsed_time": 158.09735, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 51}, {"generation": 105, "elapsed_time": 159.891452, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 52}, {"generation": 106, "elapsed_time": 161.650407, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 53}, {"generation": 107, "elapsed_time": 163.440445, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 54}, {"generation": 108, "elapsed_time": 165.10785, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 55}, {"generation": 109, "elapsed_time": 166.597157, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 56}, {"generation": 110, "elapsed_time": 168.240047, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 57}, {"generation": 111, "elapsed_time": 169.916084, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 58}, {"generation": 112, "elapsed_time": 171.508713, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 59}, {"generation": 113, "elapsed_time": 173.315173, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 60}, {"generation": 114, "elapsed_time": 175.137214, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 61}, {"generation": 115, "elapsed_time": 176.866145, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 62}, {"generation": 116, "elapsed_time": 178.481741, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 63}, {"generation": 117, "elapsed_time": 180.218919, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 64}, {"generation": 118, "elapsed_time": 181.858707, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 65}, {"generation": 119, "elapsed_time": 183.547114, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 66}, {"generation": 120, "elapsed_time": 185.228584, "hypervolume": 144.65342056182538, "diversity_metric": 0.010530711455731867, "convergence_metric": 0.00315921343671955, "distribution_uniformity": 0.05959570095991737, "feasibility_ratio": 1.0, "improvement_rate": 0.0, "best_objectives": [20.0, 0.06359538065543865, 500.0], "average_objectives": [20.0, 0.06393187864194419, 519.3455792101702], "constraint_violation": 6.945187825311283e-07, "stagnation_count": 67}]