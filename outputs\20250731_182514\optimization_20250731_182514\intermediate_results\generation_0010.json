{"generation": 10, "timestamp": "2025-07-31T18:25:31.893111", "best_solutions": [{"individual_id": "gen6_off128", "energy_consumption": 20.0, "thermal_performance": 0.06276011923086597, "renovation_cost": 500.0, "constraint_violations": 0.1805011946353583, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006235071313148847, "overall_u_value": 0.41408494550667446, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 0.9636926221602858, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [13.689602038883676], "window_positions": [[4.507764329057215, 1.814060329673352]], "window_sizes": [[1.1958146252327322, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:26.347280"}, {"individual_id": "gen6_off128", "energy_consumption": 20.0, "thermal_performance": 0.06276011923086597, "renovation_cost": 500.0, "constraint_violations": 0.1805011946353583, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006235071313148847, "overall_u_value": 0.41408494550667446, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 0.9636926221602858, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [13.689602038883676], "window_positions": [[4.507764329057215, 1.814060329673352]], "window_sizes": [[1.1958146252327322, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:26.347280"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}, {"individual_id": "gen2_off112", "energy_consumption": 20.0, "thermal_performance": 0.06854143382542105, "renovation_cost": 500.0, "constraint_violations": 0.16351414389401214, "is_feasible": true, "auxiliary_metrics": {"window_wall_ratio": 0.006994328516072661, "overall_u_value": 0.41580009771450566, "solar_heat_gain_coefficient": 0.6, "window_count": 1, "average_window_area": 1.0810434154441906, "shading_coverage_ratio": 0.0, "window_types": [1], "frame_depths": [0.10072351802120011], "shading_depths": [0.0], "shading_angles": [0.0], "window_positions": [[5.316150110933371, 1.814060329673352]], "window_sizes": [[1.3414313827596112, 0.8058879711165347]], "frame_count": 1, "shading_count": 0, "plain_window_count": 0}, "evaluation_timestamp": "2025-07-31T18:25:21.556588"}], "population_size": 250, "feasible_count": 250}