"""
通用工具函数
提供系统中各模块通用的辅助功能
"""

import os
import json
import pickle
import hashlib
import shutil
import time
from typing import Any, Dict, List, Optional, Union, Tuple
from pathlib import Path
import numpy as np
from datetime import datetime, timedelta


class FileUtils:
    """文件操作工具类"""
    
    @staticmethod
    def ensure_directory(directory: Union[str, Path]) -> str:
        """
        确保目录存在，如不存在则创建
        
        Args:
            directory: 目录路径
            
        Returns:
            目录路径字符串
        """
        dir_path = Path(directory)
        dir_path.mkdir(parents=True, exist_ok=True)
        return str(dir_path)
    
    @staticmethod
    def get_file_size(file_path: Union[str, Path]) -> int:
        """
        获取文件大小（字节）
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（字节）
        """
        return os.path.getsize(file_path)
    
    @staticmethod
    def get_file_extension(file_path: Union[str, Path]) -> str:
        """
        获取文件扩展名
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件扩展名（包括点号）
        """
        return Path(file_path).suffix.lower()
    
    @staticmethod
    def is_image_file(file_path: Union[str, Path]) -> bool:
        """
        判断是否为图像文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            True如果是图像文件
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif'}
        return FileUtils.get_file_extension(file_path) in image_extensions
    
    @staticmethod
    def copy_file(src: Union[str, Path], dst: Union[str, Path]) -> str:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            目标文件路径
        """
        dst_path = Path(dst)
        FileUtils.ensure_directory(dst_path.parent)
        shutil.copy2(src, dst)
        return str(dst_path)
    
    @staticmethod
    def move_file(src: Union[str, Path], dst: Union[str, Path]) -> str:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            
        Returns:
            目标文件路径
        """
        dst_path = Path(dst)
        FileUtils.ensure_directory(dst_path.parent)
        shutil.move(src, dst)
        return str(dst_path)
    
    @staticmethod
    def delete_file(file_path: Union[str, Path], ignore_errors: bool = True) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            ignore_errors: 是否忽略错误
            
        Returns:
            True如果删除成功
        """
        try:
            os.remove(file_path)
            return True
        except Exception:
            if not ignore_errors:
                raise
            return False
    
    @staticmethod
    def get_files_in_directory(directory: Union[str, Path], 
                              pattern: str = "*",
                              recursive: bool = False) -> List[str]:
        """
        获取目录中的文件列表
        
        Args:
            directory: 目录路径
            pattern: 文件模式
            recursive: 是否递归搜索
            
        Returns:
            文件路径列表
        """
        dir_path = Path(directory)
        if recursive:
            files = list(dir_path.rglob(pattern))
        else:
            files = list(dir_path.glob(pattern))
        
        return [str(f) for f in files if f.is_file()]
    
    @staticmethod
    def clean_directory(directory: Union[str, Path], 
                       keep_subdirs: bool = True) -> None:
        """
        清理目录中的文件
        
        Args:
            directory: 目录路径
            keep_subdirs: 是否保留子目录
        """
        dir_path = Path(directory)
        if not dir_path.exists():
            return
        
        for item in dir_path.iterdir():
            if item.is_file():
                item.unlink()
            elif item.is_dir() and not keep_subdirs:
                shutil.rmtree(item)


class DataUtils:
    """数据处理工具类"""
    
    @staticmethod
    def save_json(data: Any, file_path: Union[str, Path], 
                 indent: int = 2, ensure_ascii: bool = False) -> None:
        """
        保存数据为JSON文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
            indent: 缩进空格数
            ensure_ascii: 是否确保ASCII编码
        """
        file_path = Path(file_path)
        FileUtils.ensure_directory(file_path.parent)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=ensure_ascii,
                     default=DataUtils._json_serializer)
    
    @staticmethod
    def load_json(file_path: Union[str, Path]) -> Any:
        """
        从JSON文件加载数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的数据
        """
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @staticmethod
    def save_pickle(data: Any, file_path: Union[str, Path]) -> None:
        """
        保存数据为Pickle文件
        
        Args:
            data: 要保存的数据
            file_path: 文件路径
        """
        file_path = Path(file_path)
        FileUtils.ensure_directory(file_path.parent)
        
        with open(file_path, 'wb') as f:
            pickle.dump(data, f)
    
    @staticmethod
    def load_pickle(file_path: Union[str, Path]) -> Any:
        """
        从Pickle文件加载数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            加载的数据
        """
        with open(file_path, 'rb') as f:
            return pickle.load(f)
    
    @staticmethod
    def _json_serializer(obj: Any) -> Any:
        """JSON序列化辅助函数"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'to_dict'):
            return obj.to_dict()
        else:
            return str(obj)
    
    @staticmethod
    def normalize_array(arr: np.ndarray, method: str = 'minmax') -> np.ndarray:
        """
        数组归一化
        
        Args:
            arr: 输入数组
            method: 归一化方法 ('minmax', 'zscore', 'robust')
            
        Returns:
            归一化后的数组
        """
        if method == 'minmax':
            arr_min, arr_max = arr.min(), arr.max()
            if arr_max == arr_min:
                return np.zeros_like(arr)
            return (arr - arr_min) / (arr_max - arr_min)
        
        elif method == 'zscore':
            arr_mean, arr_std = arr.mean(), arr.std()
            if arr_std == 0:
                return np.zeros_like(arr)
            return (arr - arr_mean) / arr_std
        
        elif method == 'robust':
            arr_median = np.median(arr)
            arr_mad = np.median(np.abs(arr - arr_median))
            if arr_mad == 0:
                return np.zeros_like(arr)
            return (arr - arr_median) / arr_mad
        
        else:
            raise ValueError(f"未知的归一化方法: {method}")
    
    @staticmethod
    def calculate_statistics(data: Union[List, np.ndarray]) -> Dict[str, float]:
        """
        计算数据统计信息
        
        Args:
            data: 输入数据
            
        Returns:
            统计信息字典
        """
        arr = np.array(data)
        
        return {
            'count': len(arr),
            'mean': float(np.mean(arr)),
            'std': float(np.std(arr)),
            'min': float(np.min(arr)),
            'max': float(np.max(arr)),
            'median': float(np.median(arr)),
            'q25': float(np.percentile(arr, 25)),
            'q75': float(np.percentile(arr, 75)),
            'skewness': float(DataUtils._calculate_skewness(arr)),
            'kurtosis': float(DataUtils._calculate_kurtosis(arr))
        }
    
    @staticmethod
    def _calculate_skewness(arr: np.ndarray) -> float:
        """计算偏度"""
        mean = np.mean(arr)
        std = np.std(arr)
        if std == 0:
            return 0.0
        return np.mean(((arr - mean) / std) ** 3)
    
    @staticmethod
    def _calculate_kurtosis(arr: np.ndarray) -> float:
        """计算峰度"""
        mean = np.mean(arr)
        std = np.std(arr)
        if std == 0:
            return 0.0
        return np.mean(((arr - mean) / std) ** 4) - 3


class ValidationUtils:
    """验证工具类"""
    
    @staticmethod
    def validate_file_exists(file_path: Union[str, Path]) -> bool:
        """
        验证文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            True如果文件存在
        """
        return Path(file_path).exists() and Path(file_path).is_file()
    
    @staticmethod
    def validate_directory_exists(dir_path: Union[str, Path]) -> bool:
        """
        验证目录是否存在
        
        Args:
            dir_path: 目录路径
            
        Returns:
            True如果目录存在
        """
        return Path(dir_path).exists() and Path(dir_path).is_dir()
    
    @staticmethod
    def validate_number_range(value: float, min_val: float, max_val: float) -> bool:
        """
        验证数值是否在指定范围内
        
        Args:
            value: 要验证的值
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            True如果在范围内
        """
        return min_val <= value <= max_val
    
    @staticmethod
    def validate_list_not_empty(lst: List) -> bool:
        """
        验证列表不为空
        
        Args:
            lst: 要验证的列表
            
        Returns:
            True如果不为空
        """
        return lst is not None and len(lst) > 0
    
    @staticmethod
    def validate_dict_has_keys(d: Dict, required_keys: List[str]) -> bool:
        """
        验证字典包含必需的键
        
        Args:
            d: 要验证的字典
            required_keys: 必需的键列表
            
        Returns:
            True如果包含所有必需的键
        """
        return all(key in d for key in required_keys)


class HashUtils:
    """哈希工具类"""
    
    @staticmethod
    def calculate_file_hash(file_path: Union[str, Path], 
                           algorithm: str = 'md5') -> str:
        """
        计算文件哈希值
        
        Args:
            file_path: 文件路径
            algorithm: 哈希算法 ('md5', 'sha1', 'sha256')
            
        Returns:
            哈希值字符串
        """
        hash_obj = hashlib.new(algorithm)
        
        with open(file_path, 'rb') as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        
        return hash_obj.hexdigest()
    
    @staticmethod
    def calculate_string_hash(text: str, algorithm: str = 'md5') -> str:
        """
        计算字符串哈希值
        
        Args:
            text: 输入字符串
            algorithm: 哈希算法
            
        Returns:
            哈希值字符串
        """
        hash_obj = hashlib.new(algorithm)
        hash_obj.update(text.encode('utf-8'))
        return hash_obj.hexdigest()
    
    @staticmethod
    def calculate_data_hash(data: Any, algorithm: str = 'md5') -> str:
        """
        计算数据对象哈希值
        
        Args:
            data: 数据对象
            algorithm: 哈希算法
            
        Returns:
            哈希值字符串
        """
        # 将数据序列化为字符串
        if isinstance(data, (dict, list)):
            data_str = json.dumps(data, sort_keys=True, default=str)
        else:
            data_str = str(data)
        
        return HashUtils.calculate_string_hash(data_str, algorithm)


class TimeUtils:
    """时间工具类"""
    
    @staticmethod
    def get_timestamp(format_str: str = '%Y%m%d_%H%M%S') -> str:
        """
        获取当前时间戳字符串
        
        Args:
            format_str: 时间格式字符串
            
        Returns:
            时间戳字符串
        """
        return datetime.now().strftime(format_str)
    
    @staticmethod
    def parse_timestamp(timestamp_str: str, format_str: str = '%Y%m%d_%H%M%S') -> datetime:
        """
        解析时间戳字符串
        
        Args:
            timestamp_str: 时间戳字符串
            format_str: 时间格式字符串
            
        Returns:
            datetime对象
        """
        return datetime.strptime(timestamp_str, format_str)
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """
        格式化持续时间
        
        Args:
            seconds: 秒数
            
        Returns:
            格式化的时间字符串
        """
        if seconds < 60:
            return f"{seconds:.2f}秒"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.2f}分钟"
        else:
            hours = seconds / 3600
            return f"{hours:.2f}小时"
    
    @staticmethod
    def get_date_range(start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        获取日期范围内的所有日期
        
        Args:
            start_date: 起始日期
            end_date: 结束日期
            
        Returns:
            日期列表
        """
        dates = []
        current_date = start_date
        
        while current_date <= end_date:
            dates.append(current_date)
            current_date += timedelta(days=1)
        
        return dates


class MathUtils:
    """数学工具类"""
    
    @staticmethod
    def calculate_distance(point1: Tuple[float, float], 
                          point2: Tuple[float, float]) -> float:
        """
        计算两点间距离
        
        Args:
            point1: 点1坐标 (x, y)
            point2: 点2坐标 (x, y)
            
        Returns:
            距离
        """
        return np.sqrt((point1[0] - point2[0])**2 + (point1[1] - point2[1])**2)
    
    @staticmethod
    def calculate_area_rectangle(width: float, height: float) -> float:
        """
        计算矩形面积
        
        Args:
            width: 宽度
            height: 高度
            
        Returns:
            面积
        """
        return width * height
    
    @staticmethod
    def calculate_window_wall_ratio(window_area: float, wall_area: float) -> float:
        """
        计算窗墙比
        
        Args:
            window_area: 窗户面积
            wall_area: 墙体面积
            
        Returns:
            窗墙比
        """
        if wall_area <= 0:
            return 0.0
        return window_area / wall_area
    
    @staticmethod
    def clamp(value: float, min_val: float, max_val: float) -> float:
        """
        将值限制在指定范围内
        
        Args:
            value: 输入值
            min_val: 最小值
            max_val: 最大值
            
        Returns:
            限制后的值
        """
        return max(min_val, min(value, max_val))
    
    @staticmethod
    def interpolate_linear(x: float, x1: float, y1: float, x2: float, y2: float) -> float:
        """
        线性插值
        
        Args:
            x: 插值点
            x1, y1: 已知点1
            x2, y2: 已知点2
            
        Returns:
            插值结果
        """
        if x2 == x1:
            return y1
        return y1 + (y2 - y1) * (x - x1) / (x2 - x1)


class MemoryUtils:
    """内存工具类"""
    
    @staticmethod
    def get_memory_usage() -> Dict[str, float]:
        """
        获取内存使用情况
        
        Returns:
            内存使用情况字典 (单位: MB)
        """
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                'rss': memory_info.rss / 1024 / 1024,      # 物理内存
                'vms': memory_info.vms / 1024 / 1024,      # 虚拟内存
                'percent': process.memory_percent()         # 内存使用百分比
            }
        except ImportError:
            return {'error': 'psutil not available'}
    
    @staticmethod
    def clear_cache():
        """清理内存缓存"""
        import gc
        gc.collect()


# 便利函数
def create_timestamped_filename(base_name: str, extension: str = '') -> str:
    """
    创建带时间戳的文件名
    
    Args:
        base_name: 基础文件名
        extension: 文件扩展名
        
    Returns:
        带时间戳的文件名
    """
    timestamp = TimeUtils.get_timestamp()
    if extension and not extension.startswith('.'):
        extension = '.' + extension
    
    return f"{base_name}_{timestamp}{extension}"


def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 默认值（当分母为零时）
        
    Returns:
        除法结果
    """
    if denominator == 0:
        return default
    return numerator / denominator


def format_number(value: float, decimals: int = 2) -> str:
    """
    格式化数字显示
    
    Args:
        value: 数值
        decimals: 小数位数
        
    Returns:
        格式化的数字字符串
    """
    return f"{value:.{decimals}f}"


def convert_units(value: float, from_unit: str, to_unit: str) -> float:
    """
    单位转换
    
    Args:
        value: 数值
        from_unit: 源单位
        to_unit: 目标单位
        
    Returns:
        转换后的数值
    """
    # 长度单位转换（以米为基准）
    length_units = {
        'm': 1.0,
        'cm': 0.01,
        'mm': 0.001,
        'km': 1000.0,
        'ft': 0.3048,
        'in': 0.0254
    }
    
    # 面积单位转换（以平方米为基准）
    area_units = {
        'm2': 1.0,
        'cm2': 0.0001,
        'mm2': 0.000001,
        'km2': 1000000.0,
        'ft2': 0.092903,
        'in2': 0.00064516
    }
    
    if from_unit in length_units and to_unit in length_units:
        # 先转换为米，再转换为目标单位
        meters = value * length_units[from_unit]
        return meters / length_units[to_unit]
    
    elif from_unit in area_units and to_unit in area_units:
        # 先转换为平方米，再转换为目标单位
        square_meters = value * area_units[from_unit]
        return square_meters / area_units[to_unit]
    
    else:
        raise ValueError(f"不支持的单位转换: {from_unit} -> {to_unit}")