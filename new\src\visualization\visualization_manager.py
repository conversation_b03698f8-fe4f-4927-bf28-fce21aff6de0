"""可视化管理器
统一管理和协调所有可视化功能
"""

import os
import warnings
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.gridspec as gridspec

# Suppress tight_layout warnings
warnings.filterwarnings('ignore', message='This figure includes Axes that are not compatible with tight_layout')

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import VisualizationError, handle_exception
from ..core.data_structures import VisualizationData, ObjectiveResults

from .chart_config import ChartConfig, create_chart_config
from .optimization_charts import OptimizationCharts, create_optimization_charts
from .performance_charts import PerformanceCharts, create_performance_charts
from .comparison_charts import ComparisonCharts, create_comparison_charts
from .report_generator import ReportGenerator, create_report_generator
from .enhanced_3d_visualizer import Enhanced3DVisualizer, create_enhanced_3d_visualizer


def setup_unicode_fonts():
    """Setup Unicode fonts for international character display"""
    import matplotlib.pyplot as plt
    import matplotlib
    
    # 设置支持Unicode字符的字体列表，优先使用支持中文的字体
    fonts = ['Microsoft YaHei', 'SimHei', 'Arial Unicode MS', 'DejaVu Sans', 'STIXGeneral', 'Liberation Sans', 'WenQuanYi Micro Hei']
    
    # 设置matplotlib字体参数
    matplotlib.rcParams['font.sans-serif'] = fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    matplotlib.rcParams['font.family'] = 'sans-serif'
    
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.family'] = 'sans-serif'
    
    # 设置数学字体以支持特殊符号
    matplotlib.rcParams['mathtext.fontset'] = 'dejavusans'
    plt.rcParams['mathtext.fontset'] = 'dejavusans'


def create_unicode_legend(ax, labels, **kwargs):
    """Create legend with Unicode font support"""
    setup_unicode_fonts()
    return ax.legend(labels, **kwargs)


def set_unicode_labels(ax, xlabel=None, ylabel=None, title=None):
    """Set axis labels with Unicode font support"""
    setup_unicode_fonts()
    if xlabel:
        ax.set_xlabel(xlabel, fontproperties='SimHei')
    if ylabel:
        ax.set_ylabel(ylabel, fontproperties='SimHei')
    if title:
        ax.set_title(title, fontproperties='SimHei')


class VisualizationManager:
    """可视化管理器
    
    功能：
    1. 统一管理所有可视化组件
    2. 协调图表生成流程
    3. 管理可视化配置
    4. 生成完整的可视化报告
    5. 提供可视化API接口
    """
    
    def __init__(self):
        """初始化可视化管理器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取可视化管理配置
        viz_config = self.config.get_section('visualization')
        self.manager_config = viz_config.get('visualization_manager', {})
        
        # 初始化所有可视化组件
        self.chart_config = create_chart_config()
        self.optimization_charts = create_optimization_charts()
        self.performance_charts = create_performance_charts()
        self.comparison_charts = create_comparison_charts()
        self.report_generator = create_report_generator()
        self.enhanced_3d_visualizer = create_enhanced_3d_visualizer()
        
        # 可视化任务配置
        task_config = self.manager_config.get('task_configuration', {})
        self.auto_generate_all_charts = task_config.get('auto_generate_all_charts', True)
        self.parallel_chart_generation = task_config.get('parallel_chart_generation', False)
        self.cache_generated_charts = task_config.get('cache_generated_charts', True)
        
        # 输出管理配置
        output_config = self.manager_config.get('output_management', {})
        self.organize_outputs = output_config.get('organize_by_timestamp', True)
        self.cleanup_old_outputs = output_config.get('cleanup_old_outputs', False)
        self.max_output_age_days = output_config.get('max_output_age_days', 7)
        
        # 3D可视化相关属性
        self.wall_width = 10.0
        self.wall_height = 6.0
        self.wall_thickness = 0.3
        self.colors = {
            'wall_outer': '#F0F0F0',
            'wall_inner': '#E8E8E8',
            'wall_side': '#D0D0D0',
            'brick_line': '#888888'
        }
        
        # 生成的图表缓存
        self.generated_charts = {}
        self.current_session_id = None
        
        self.logger.info("Visualization manager initialized successfully")
    
    def set_session_output_directory(self, session_dir: str) -> None:
        """设置会话输出目录"""
        try:
            # 创建主要目录结构
            charts_dir = os.path.join(session_dir, 'charts')
            pdf_dir = os.path.join(session_dir, 'pdf')
            svg_dir = os.path.join(session_dir, 'svg')
            reports_dir = os.path.join(session_dir, 'reports')
            
            # 确保目录存在
            for directory in [charts_dir, pdf_dir, svg_dir, reports_dir]:
                os.makedirs(directory, exist_ok=True)
            
            # 为所有组件设置会话目录
            if hasattr(self.optimization_charts, 'set_session_output_directory'):
                self.optimization_charts.set_session_output_directory(session_dir, 'charts')
            if hasattr(self.performance_charts, 'set_session_output_directory'):
                self.performance_charts.set_session_output_directory(session_dir, 'charts')
            if hasattr(self.comparison_charts, 'set_session_output_directory'):
                self.comparison_charts.set_session_output_directory(session_dir, 'charts')
            if hasattr(self.report_generator, 'set_session_output_directory'):
                self.report_generator.set_session_output_directory(session_dir)
            
            # 保存目录信息供新增图表使用
            self.session_directories = {
                'session_root': session_dir,
                'charts': charts_dir,
                'pdf': pdf_dir,
                'svg': svg_dir,
                'reports': reports_dir
            }
            
            self.logger.info(f"Session directory structure created: {session_dir}")
        except Exception as e:
            self.logger.error(f"Failed to set session directory: {str(e)}")
    
    @handle_exception
    def generate_complete_visualization_suite(self, 
                                            visualization_data: VisualizationData,
                                            selected_solutions: Dict[str, ObjectiveResults] = None,
                                            custom_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """生成完整的可视化套件"""
        with LogContext("Complete visualization suite generation", self.logger):
            try:
                # 创建会话ID
                self.current_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                # 获取会话目录
                session_dir = None
                if custom_options and 'session_dir' in custom_options:
                    session_dir = custom_options['session_dir']
                
                if session_dir:
                    self.set_session_output_directory(session_dir)
                
                self.logger.info(f"Starting complete visualization suite generation, session ID: {self.current_session_id}")
                
                # 初始化结果字典
                visualization_results = {
                    'session_id': self.current_session_id,
                    'generated_time': datetime.now().isoformat(),
                    'charts': {},
                    'reports': {},
                    'metadata': {
                        'total_charts': 0,
                        'successful_charts': 0,
                        'failed_charts': 0,
                        'generation_time': 0
                    }
                }
                
                start_time = datetime.now()
                
                # 验证输入数据
                self._validate_visualization_data(visualization_data)
                
                # 处理自定义选项
                options = self._process_custom_options(custom_options)
                
                # 生成新增的可视化图表
                try:
                    # 所有解的排列展示
                    solutions_grid = self._create_solutions_grid_visualization(
                        visualization_data.optimization_results,
                        visualization_data.facade_elements,
                        selected_solutions
                    )
                    visualization_results['charts']['solutions_grid'] = solutions_grid
                    
                    # 原始与最佳方案对比
                    comparison_chart = self._create_facade_comparison_visualization(
                        visualization_data.facade_elements,
                        visualization_data.optimization_results,
                        selected_solutions
                    )
                    visualization_results['charts']['facade_comparison'] = comparison_chart
                    
                    # 3D轴测图
                    isometric_3d = self._create_3d_isometric_visualization(
                        visualization_data,
                        selected_solutions or {}
                    )
                    visualization_results['charts']['isometric_3d'] = isometric_3d
                    
                    # 所有解决方案综合仪表板
                    all_solutions_dashboard = self.create_all_solutions_dashboard(
                        visualization_data, selected_solutions
                    )
                    visualization_results['charts']['all_solutions_dashboard'] = all_solutions_dashboard
                    
                    # 总体分析图表
                    overall_analysis_chart = self.create_overall_analysis_chart(
                        visualization_data, selected_solutions
                    )
                    visualization_results['charts']['overall_analysis'] = overall_analysis_chart
                    
                    visualization_results['metadata']['successful_charts'] += 5
                    self.logger.info("Additional visualization charts generation completed")
                    
                except Exception as e:
                    self.logger.error(f"Additional visualization charts generation failed: {str(e)}")
                    visualization_results['metadata']['failed_charts'] += 5
                
                # 1. 生成优化结果图表
                if options.get('generate_optimization_charts', True):
                    optimization_charts = self._generate_optimization_charts(
                        visualization_data, selected_solutions
                    )
                    visualization_results['charts'].update(optimization_charts)
                
                # 2. 生成性能分析图表
                if options.get('generate_performance_charts', True):
                    performance_charts = self._generate_performance_charts(
                        visualization_data
                    )
                    visualization_results['charts'].update(performance_charts)
                
                # 3. 生成对比分析图表
                if options.get('generate_comparison_charts', True) and selected_solutions:
                    comparison_charts = self._generate_comparison_charts(
                        selected_solutions, visualization_data
                    )
                    visualization_results['charts'].update(comparison_charts)
                
                # 4. 生成综合报告
                if options.get('generate_reports', True):
                    reports = self._generate_comprehensive_reports(
                        visualization_data, selected_solutions, 
                        visualization_results['charts']
                    )
                    visualization_results['reports'].update(reports)
                
                # 计算生成统计
                end_time = datetime.now()
                generation_time = (end_time - start_time).total_seconds()
                
                total_charts = len(visualization_results['charts'])
                successful_charts = sum(1 for path in visualization_results['charts'].values() if path and os.path.exists(path))
                failed_charts = total_charts - successful_charts
                
                visualization_results['metadata'].update({
                    'total_charts': total_charts,
                    'successful_charts': successful_charts,
                    'failed_charts': failed_charts,
                    'generation_time': generation_time
                })
                
                # 输出组织和清理
                if self.organize_outputs:
                    self._organize_output_files(visualization_results)
                
                if self.cleanup_old_outputs:
                    self._cleanup_old_outputs()
                
                self.logger.info(f"Complete visualization suite generation completed: {successful_charts}/{total_charts} charts successful, time elapsed {generation_time:.2f}s")

                # 添加输出目录信息到结果中
                # 返回实际的统一输出目录
                if hasattr(self, '_unified_output_dir'):
                    visualization_results['output_directory'] = self._unified_output_dir
                else:
                    # 如果没有统一输出目录，使用默认路径
                    base_output_dir = self.config.get_output_directory()
                    if not os.path.isabs(base_output_dir):
                        base_output_dir = os.path.abspath(base_output_dir)
                    visualization_results['output_directory'] = os.path.join(base_output_dir, self.current_session_id)

                return visualization_results
                
            except Exception as e:
                raise VisualizationError(f"Complete visualization suite generation failed: {str(e)}") from e
    
    def _validate_visualization_data(self, visualization_data: VisualizationData):
        """验证可视化数据"""
        try:
            if not visualization_data:
                raise VisualizationError("Visualization data is empty")
            
            # 检查必需的数据组件
            required_components = ['facade_elements', 'climate_data']
            missing_components = []
            
            for component in required_components:
                if not hasattr(visualization_data, component) or getattr(visualization_data, component) is None:
                    missing_components.append(component)
            
            if missing_components:
                self.logger.warning(f"Missing visualization data components: {missing_components}")
            
            self.logger.info("Visualization data validation passed")
            
        except Exception as e:
            raise VisualizationError(f"Visualization data validation failed: {str(e)}")
    
    def _process_custom_options(self, custom_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理自定义选项"""
        try:
            # 默认选项
            default_options = {
                'generate_optimization_charts': True,
                'generate_performance_charts': True,
                'generate_comparison_charts': True,
                'generate_reports': True,
                'chart_formats': ['png'],
                'report_formats': ['html', 'markdown'],
                'high_quality_mode': True,
                'include_statistical_analysis': True
            }
            
            # 合并自定义选项
            if custom_options:
                default_options.update(custom_options)
            
            self.logger.info(f"Visualization options configuration completed: {list(default_options.keys())}")
            
            return default_options
            
        except Exception as e:
            self.logger.error(f"Failed to process custom options: {str(e)}")
            return {}
    
    def visualize_initial_population_creation(self, individual_id: str, num_windows: int, 
                                             window_types: List[int], window_positions: List[Tuple[float, float]], 
                                             window_sizes: List[Tuple[float, float]]) -> str:
        """可视化初始种群创建过程"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            
            # 检查是否为gen0个体，如果是则只使用2D可视化
            is_gen0 = individual_id.startswith('gen0_')
            
            if is_gen0:
                # gen0个体只使用2D可视化
                fig, ax1 = plt.subplots(1, 1, figsize=(12, 8))
                ax2 = None
            else:
                # 其他个体使用2D+3D可视化
                fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
            
            # 2D立面图
            ax1.set_xlim(0, 12)
            ax1.set_ylim(0, 8)
            ax1.set_aspect('equal')
            ax1.set_title(f'Initial Individual: {individual_id}', fontsize=14, fontweight='bold')
            ax1.set_xlabel('Width (m)')
            ax1.set_ylabel('Height (m)')
            
            # 绘制墙体
            wall_rect = patches.Rectangle((0, 0), 12, 8, linewidth=2, edgecolor='black', facecolor='#F0F0F0')
            ax1.add_patch(wall_rect)
            
            # 绘制窗户
            colors = {0: '#87CEEB', 1: '#4169E1', 2: '#228B22'}
            labels = {0: 'Basic', 1: 'With Frame', 2: 'With Shading'}
            
            for i, ((x, y), (w, h), wtype) in enumerate(zip(window_positions, window_sizes, window_types)):
                color = colors.get(wtype, '#87CEEB')
                label = labels.get(wtype, 'Basic')
                
                # 窗户矩形
                window_rect = patches.Rectangle((x, y), w, h, linewidth=1, edgecolor='darkblue', facecolor=color, alpha=0.8)
                ax1.add_patch(window_rect)
                
                # 窗户编号
                ax1.text(x + w/2, y + h/2, str(i+1), ha='center', va='center', fontsize=10, fontweight='bold')
                
                # 窗框
                if wtype >= 1:
                    frame_rect = patches.Rectangle((x-0.1, y-0.1), w+0.2, h+0.2, linewidth=2, edgecolor='#8B4513', facecolor='none')
                    ax1.add_patch(frame_rect)
                
                # 遮阳
                if wtype >= 2:
                    shading_rect = patches.Rectangle((x-0.2, y+h+0.1), w+0.4, 0.15, linewidth=1, edgecolor='darkgreen', facecolor='#228B22', alpha=0.7)
                    ax1.add_patch(shading_rect)
            
            # 统计信息
            type_counts = {0: 0, 1: 0, 2: 0}
            for wtype in window_types:
                type_counts[wtype] += 1
            
            stats_text = f"Windows: {num_windows}\nBasic: {type_counts[0]}\nWith Frame: {type_counts[1]}\nWith Shading: {type_counts[2]}"
            ax1.text(0.02, 0.98, stats_text, transform=ax1.transAxes, fontsize=10, 
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
            # 3D视图 (仅非gen0个体)
            if ax2 is not None:
                ax2 = fig.add_subplot(122, projection='3d')
                self._draw_simple_3d_facade(ax2, window_types, window_positions, window_sizes)
                ax2.set_title(f'3D View: {individual_id}', fontsize=14, fontweight='bold')
            
            plt.tight_layout()
            
            # 保存图表
            filename = f'initial_population_{individual_id}.png'
            filepath = os.path.join(self.chart_config.output_dir, filename)
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            self.logger.info(f"Initial population visualization created: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to visualize initial population creation: {str(e)}")
            return ""
    
    def _draw_simple_3d_facade(self, ax, window_types, window_positions, window_sizes):
        """绘制简单的3D立面"""
        # 墙体
        wall_vertices = [
            [[0, 0, 0], [12, 0, 0], [12, 0, 8], [0, 0, 8]],  # 前面
            [[0, -0.3, 0], [12, -0.3, 0], [12, -0.3, 8], [0, -0.3, 8]],  # 后面
            [[0, 0, 0], [0, -0.3, 0], [0, -0.3, 8], [0, 0, 8]],  # 左面
            [[12, 0, 0], [12, -0.3, 0], [12, -0.3, 8], [12, 0, 8]],  # 右面
            [[0, 0, 8], [12, 0, 8], [12, -0.3, 8], [0, -0.3, 8]],  # 顶面
            [[0, 0, 0], [12, 0, 0], [12, -0.3, 0], [0, -0.3, 0]]   # 底面
        ]
        
        from mpl_toolkits.mplot3d.art3d import Poly3DCollection
        for vertices in wall_vertices:
            ax.add_collection3d(Poly3DCollection([vertices], alpha=0.7, facecolor='#F0F0F0', edgecolor='black'))
        
        # 窗户
        colors = {0: '#87CEEB', 1: '#4169E1', 2: '#228B22'}
        for (x, z), (w, h), wtype in zip(window_positions, window_sizes, window_types):
            color = colors.get(wtype, '#87CEEB')
            
            # 窗户玻璃
            window_vertices = [
                [[x, 0.01, z], [x+w, 0.01, z], [x+w, 0.01, z+h], [x, 0.01, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(window_vertices, alpha=0.8, facecolor=color, edgecolor='darkblue'))
            
            # 窗框
            if wtype >= 1:
                frame_vertices = [
                    [[x-0.03, 0.02, z-0.03], [x+w+0.03, 0.02, z-0.03], [x+w+0.03, 0.02, z+h+0.03], [x-0.03, 0.02, z+h+0.03]]
                ]
                ax.add_collection3d(Poly3DCollection(frame_vertices, alpha=1.0, facecolor='#8B4513', edgecolor='brown'))
            
            # 遮阳
            if wtype >= 2:
                shading_vertices = [
                    [[x-0.05, 0.03, z+h+0.03], [x+w+0.05, 0.03, z+h+0.03], [x+w+0.05, 0.03, z+h+0.08], [x-0.05, 0.03, z+h+0.08]]
                ]
                ax.add_collection3d(Poly3DCollection(shading_vertices, alpha=0.9, facecolor='#228B22', edgecolor='darkgreen'))
        
        # 设置更好的视角以便展示
        ax.view_init(elev=15, azim=-60)
        ax.set_xlim(0, 12)
        ax.set_ylim(-1, 1)
        ax.set_zlim(0, 8)
        ax.set_xlabel('Width (m)')
        ax.set_ylabel('Depth (m)')
        ax.set_zlabel('Height (m)')

    def _generate_optimization_charts(self, visualization_data: VisualizationData,
                                    selected_solutions: Dict[str, ObjectiveResults] = None) -> Dict[str, str]:
        """生成优化结果图表"""
        try:
            optimization_charts = {}
            
            self.logger.info("Starting optimization result charts generation...")
            
            # 检查优化结果数据是否存在
            if not hasattr(visualization_data, 'optimization_results') or not visualization_data.optimization_results:
                self.logger.warning("No optimization results data available, skipping optimization charts")
                return optimization_charts
            
            # 1. 帕累托前沿图
            try:
                pareto_3d_path = self.optimization_charts.create_pareto_frontier_chart(
                    visualization_data, selected_solutions, chart_type='3d'
                )
                if pareto_3d_path:
                    optimization_charts['pareto_frontier_3d'] = pareto_3d_path
                
                pareto_2d_path = self.optimization_charts.create_pareto_frontier_chart(
                    visualization_data, selected_solutions, chart_type='2d'
                )
                if pareto_2d_path:
                    optimization_charts['pareto_frontier_2d'] = pareto_2d_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate Pareto frontier chart: {str(e)}")
            
            # 2. 优化收敛过程图 - 添加数据验证
            try:
                # 检查收敛历史数据是否存在
                if (hasattr(visualization_data.optimization_results, 'convergence_history') and 
                    visualization_data.optimization_results.convergence_history):
                    convergence_path = self.optimization_charts.create_convergence_chart(
                        visualization_data
                    )
                    if convergence_path:
                        optimization_charts['optimization_convergence'] = convergence_path
                else:
                    self.logger.warning("No convergence history data available, skipping convergence chart")
                    
            except Exception as e:
                self.logger.error(f"Failed to generate optimization convergence chart: {str(e)}")
            
            # 3. 目标函数分布图
            try:
                distribution_path = self.optimization_charts.create_objective_distribution_chart(
                    visualization_data
                )
                if distribution_path:
                    optimization_charts['objective_distribution'] = distribution_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate objective distribution chart: {str(e)}")
            
            self.logger.info(f"Optimization result charts generation completed, {len(optimization_charts)} charts generated")
            
            return optimization_charts
            
        except Exception as e:
            self.logger.error(f"Failed to generate optimization result charts: {str(e)}")
            return {}
    
    def _generate_performance_charts(self, visualization_data: VisualizationData) -> Dict[str, str]:
        """生成性能分析图表"""
        try:
            performance_charts = {}
            
            self.logger.info("Starting performance analysis charts generation...")
            
            # 1. 能耗性能图表
            try:
                energy_path = self.performance_charts.create_energy_performance_chart(
                    visualization_data
                )
                if energy_path:
                    performance_charts['energy_performance'] = energy_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate energy performance chart: {str(e)}")
            
            # 2. 热工性能图表
            try:
                thermal_path = self.performance_charts.create_thermal_performance_chart(
                    visualization_data
                )
                if thermal_path:
                    performance_charts['thermal_performance'] = thermal_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate thermal performance chart: {str(e)}")
            
            # 3. 成本分析图表
            try:
                cost_path = self.performance_charts.create_cost_analysis_chart(
                    visualization_data
                )
                if cost_path:
                    performance_charts['cost_analysis'] = cost_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate cost analysis chart: {str(e)}")
            
            # 4. 综合性能雷达图
            try:
                radar_path = self.performance_charts.create_comprehensive_radar_chart(
                    visualization_data
                )
                if radar_path:
                    performance_charts['comprehensive_radar'] = radar_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate comprehensive radar chart: {str(e)}")
            
            self.logger.info(f"Performance analysis charts generation completed, {len(performance_charts)} charts generated")
            
            return performance_charts
            
        except Exception as e:
            self.logger.error(f"Failed to generate performance analysis charts: {str(e)}")
            return {}
    
    def _generate_comparison_charts(self, selected_solutions: Dict[str, ObjectiveResults],
                                  visualization_data: VisualizationData = None) -> Dict[str, str]:
        """生成对比分析图表"""
        try:
            comparison_charts = {}
            
            self.logger.info("Starting comparison analysis charts generation...")
            
            # 1. 多方案性能对比图
            try:
                multi_comparison_path = self.comparison_charts.create_multi_solution_comparison_chart(
                    selected_solutions
                )
                if multi_comparison_path:
                    comparison_charts['multi_solution_comparison'] = multi_comparison_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate multi-solution comparison chart: {str(e)}")
            
            # 2. 基准对比分析图
            try:
                benchmark_path = self.comparison_charts.create_benchmark_comparison_chart(
                    selected_solutions
                )
                if benchmark_path:
                    comparison_charts['benchmark_comparison'] = benchmark_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate benchmark comparison chart: {str(e)}")
            
            # 3. 敏感性分析图
            try:
                if selected_solutions:
                    base_solution = list(selected_solutions.values())[0]
                    sensitivity_path = self.comparison_charts.create_sensitivity_analysis_chart(
                        base_solution
                    )
                    if sensitivity_path:
                        comparison_charts['sensitivity_analysis'] = sensitivity_path
                        
            except Exception as e:
                self.logger.error(f"Failed to generate sensitivity analysis chart: {str(e)}")
            
            # 4. 权衡分析图
            try:
                tradeoff_path = self.comparison_charts.create_tradeoff_analysis_chart(
                    selected_solutions
                )
                if tradeoff_path:
                    comparison_charts['tradeoff_analysis'] = tradeoff_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate tradeoff analysis chart: {str(e)}")
            
            self.logger.info(f"Comparison analysis charts generation completed, {len(comparison_charts)} charts generated")
            
            return comparison_charts
            
        except Exception as e:
            self.logger.error(f"Failed to generate comparison analysis charts: {str(e)}")
            return {}
    
    def _generate_comprehensive_reports(self, visualization_data: VisualizationData,
                                      selected_solutions: Dict[str, ObjectiveResults],
                                      chart_files: Dict[str, str]) -> Dict[str, str]:
        """生成综合报告"""
        try:
            self.logger.info("Starting comprehensive report generation...")
            
            # 准备分析结果数据
            analysis_results = {
                'chart_generation_summary': {
                    'total_charts': len(chart_files),
                    'successful_charts': sum(1 for path in chart_files.values() if path and os.path.exists(path))
                },
                'solution_recommendations': self._generate_solution_recommendations(selected_solutions)
            }
            
            # 生成报告
            reports = self.report_generator.generate_comprehensive_report(
                visualization_data=visualization_data,
                selected_solutions=selected_solutions,
                chart_files=chart_files,
                analysis_results=analysis_results
            )
            
            self.logger.info(f"Comprehensive report generation completed, {len(reports)} reports generated")
            
            return reports
            
        except Exception as e:
            self.logger.error(f"Failed to generate comprehensive report: {str(e)}")
            return {}
    
    def _generate_solution_recommendations(self, selected_solutions: Dict[str, ObjectiveResults]) -> List[str]:
        """生成解决方案推荐"""
        try:
            recommendations = []
            
            if not selected_solutions:
                return ["No available solution data"]
            
            # 找出各方面最优的解决方案
            energy_best = min(selected_solutions.items(), key=lambda x: x[1].energy_consumption)
            cost_best = min(selected_solutions.items(), key=lambda x: x[1].renovation_cost)
            
            recommendations.append(f"Energy optimal solution: {energy_best[0]}, energy consumption: {energy_best[1].energy_consumption:.1f} kWh/m²/year")
            recommendations.append(f"Cost optimal solution: {cost_best[0]}, renovation cost: {cost_best[1].renovation_cost:,.0f} yuan")
            
            # 综合推荐
            comprehensive_scores = {}
            for name, solution in selected_solutions.items():
                score = (200 - solution.energy_consumption) + (1 - solution.thermal_performance) * 100 + (200000 - solution.renovation_cost) / 1000
                comprehensive_scores[name] = score
            
            best_comprehensive = max(comprehensive_scores.items(), key=lambda x: x[1])
            recommendations.append(f"Comprehensive optimal solution: {best_comprehensive[0]}, recommended for priority implementation")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Failed to generate solution recommendations: {str(e)}")
            return ["Recommendation generation failed"]
    
    def _organize_output_files(self, visualization_results: Dict[str, Any]):
        """组织输出文件"""
        try:
            if not self.organize_outputs:
                return
            
            session_id = visualization_results['session_id']
            
            # 创建统一的输出目录 - 直接使用session_id作为目录名
            base_output_dir = self.config.get_output_directory()
            # 确保使用绝对路径
            if not os.path.isabs(base_output_dir):
                base_output_dir = os.path.abspath(base_output_dir)

            # 直接使用session_id，不再嵌套
            unified_output_dir = os.path.join(base_output_dir, session_id)
            os.makedirs(unified_output_dir, exist_ok=True)

            # 在统一目录下创建子目录
            charts_dir = os.path.join(unified_output_dir, "charts")
            reports_dir = os.path.join(unified_output_dir, "reports")
            logs_dir = os.path.join(unified_output_dir, "logs")
            os.makedirs(charts_dir, exist_ok=True)
            os.makedirs(reports_dir, exist_ok=True)
            os.makedirs(logs_dir, exist_ok=True)

            self.logger.info(f"创建统一输出目录: {unified_output_dir}")

            # 保存统一输出目录的引用
            self._unified_output_dir = unified_output_dir
            
            # 移动图表文件到统一目录
            moved_charts = 0
            for chart_name, chart_path in visualization_results['charts'].items():
                if chart_path and os.path.exists(chart_path):
                    filename = os.path.basename(chart_path)
                    new_path = os.path.join(charts_dir, filename)
                    try:
                        if chart_path != new_path:  # 只有路径不同时才移动
                            os.rename(chart_path, new_path)
                            moved_charts += 1
                        visualization_results['charts'][chart_name] = new_path
                    except OSError as e:
                        self.logger.warning(f"移动图表文件失败 {chart_path}: {str(e)}")

            # 移动报告文件到统一目录
            moved_reports = 0
            for report_name, report_path in visualization_results['reports'].items():
                if report_path and os.path.exists(report_path):
                    filename = os.path.basename(report_path)
                    new_path = os.path.join(reports_dir, filename)
                    try:
                        if report_path != new_path:  # 只有路径不同时才移动
                            os.rename(report_path, new_path)
                            moved_reports += 1
                        visualization_results['reports'][report_name] = new_path
                    except OSError as e:
                        self.logger.warning(f"移动报告文件失败 {report_path}: {str(e)}")

            self.logger.info(f"文件移动完成: {moved_charts}个图表, {moved_reports}个报告")
            
            # 生成会话信息文件
            session_info_path = os.path.join(unified_output_dir, "session_info.json")
            with open(session_info_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(visualization_results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Output files organized to directory: {unified_output_dir}")
            
        except Exception as e:
            self.logger.error(f"Failed to organize output files: {str(e)}")
    
    def _cleanup_old_outputs(self):
        """清理旧的输出文件"""
        try:
            if not self.cleanup_old_outputs:
                return
            
            import time
            from pathlib import Path
            
            base_output_dir = self.config.get('output.base_directory', 'output')
            outputs_dir = Path(base_output_dir)
            if not outputs_dir.exists():
                return
            
            current_time = time.time()
            cutoff_time = current_time - (self.max_output_age_days * 24 * 3600)
            
            cleaned_count = 0
            for item in outputs_dir.iterdir():
                if item.is_dir() and item.name.startswith("session_"):
                    if item.stat().st_mtime < cutoff_time:
                        import shutil
                        shutil.rmtree(item)
                        cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info(f"Cleaned {cleaned_count} expired output directories")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old output files: {str(e)}")
    
    def _create_solutions_grid_visualization(self, optimization_results, facade_elements, selected_solutions=None):
        """创建所有解的排列展示图"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.patches as patches
            from matplotlib.gridspec import GridSpec
            
            # 设置Unicode字体支持
            setup_unicode_fonts()
            
            # 修复数据获取
            pareto_solutions = []
            if isinstance(optimization_results, dict):
                if 'pareto_optimal_solutions' in optimization_results:
                    pareto_solutions = optimization_results['pareto_optimal_solutions']
                elif 'pareto_solutions' in optimization_results:
                    pareto_solutions = optimization_results['pareto_solutions']
                elif 'solutions' in optimization_results:
                    pareto_solutions = optimization_results['solutions']
            elif hasattr(optimization_results, 'pareto_solutions'):
                pareto_solutions = optimization_results.pareto_solutions
            elif hasattr(optimization_results, 'pareto_optimal_solutions'):
                pareto_solutions = optimization_results.pareto_optimal_solutions
            
            # 如果没有解，使用选定的解作为替代
            if not pareto_solutions and selected_solutions:
                if isinstance(selected_solutions, dict):
                    pareto_solutions = list(selected_solutions.values())
                elif isinstance(selected_solutions, list):
                    pareto_solutions = selected_solutions
            
            if not pareto_solutions:
                self.logger.warning("No solutions found for grid visualization")
                return None
            
            # 计算网格尺寸
            # 显示所有解，不再限制数量
            n_solutions = len(pareto_solutions)
            if n_solutions == 0:
                self.logger.warning("没有找到帕累托解用于网格展示")
                return None
            
            self.logger.info(f"创建解决方案网格展示，共{n_solutions}个解")
            
            # 计算网格布局 - 优化为更合理的排列
            if n_solutions <= 9:
                grid_cols = 3
                grid_rows = (n_solutions + grid_cols - 1) // grid_cols
            elif n_solutions <= 16:
                grid_cols = 4
                grid_rows = (n_solutions + grid_cols - 1) // grid_cols
            elif n_solutions <= 25:
                grid_cols = 5
                grid_rows = (n_solutions + grid_cols - 1) // grid_cols
            else:
                grid_cols = 6
                grid_rows = (n_solutions + grid_cols - 1) // grid_cols
            
            # 创建更大的图形以容纳更多解
            fig_width = max(20, grid_cols * 4)
            fig_height = max(16, grid_rows * 3.5)
            fig = plt.figure(figsize=(fig_width, fig_height), dpi=100)
            gs = GridSpec(grid_rows + 1, grid_cols, figure=fig, height_ratios=[1]*grid_rows + [0.15], 
                         hspace=0.3, wspace=0.2)
            
            # Set chart title in English
            fig.suptitle('Optimization Solutions Grid Display', 
                        fontsize=16, fontweight='bold', y=0.95)
            
            # 绘制每个解
            for i, solution in enumerate(pareto_solutions[:n_solutions]):
                row = i // grid_cols
                col = i % grid_cols
                
                ax = fig.add_subplot(gs[row, col])
                
                # 绘制简化的建筑立面
                self._draw_facade_solution(ax, facade_elements, solution)
                
                # Add enhanced solution information with ranking
                info_text = f"Solution {i+1}\n"
                
                # 添加性能指标，带颜色编码
                if hasattr(solution, 'energy_consumption'):
                    energy_rank = "A" if solution.energy_consumption < 100 else "B" if solution.energy_consumption < 150 else "C"
                    info_text += f"Energy: {solution.energy_consumption:.1f} ({energy_rank})\n"
                
                if hasattr(solution, 'thermal_performance'):
                    thermal_rank = "A" if solution.thermal_performance < 0.2 else "B" if solution.thermal_performance < 0.4 else "C"
                    info_text += f"Thermal: {solution.thermal_performance:.3f} ({thermal_rank})\n"
                
                if hasattr(solution, 'renovation_cost'):
                    cost_rank = "A" if solution.renovation_cost < 100000 else "B" if solution.renovation_cost < 200000 else "C"
                    info_text += f"Cost: {solution.renovation_cost/1000:.0f}k ({cost_rank})"
                
                # 添加可行性标识
                if hasattr(solution, 'is_feasible'):
                    feasibility_icon = "✓" if solution.is_feasible else "✗"
                    info_text += f" {feasibility_icon}"
                
                ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                       fontsize=8, verticalalignment='top',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
                
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.set_aspect('equal')
                ax.axis('off')
            
            # Add legend
            legend_ax = fig.add_subplot(gs[-1, :])
            self._add_solutions_legend(legend_ax)
            legend_ax.axis('off')
            
            try:
                plt.tight_layout(pad=2.0)
            except Exception:
                # Fallback to manual adjustment when tight_layout fails
                plt.subplots_adjust(hspace=0.4, wspace=0.3, top=0.9, bottom=0.1, left=0.1, right=0.9)
            
            # 保存图表
            if hasattr(self, 'session_directories'):
                output_path = os.path.join(self.session_directories['charts'], 'solutions_grid.png')
            else:
                output_dir = os.path.join(self.config.get_output_directory(), 'charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'solutions_grid.png')
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            plt.close()
            
            self.logger.info(f"Solutions grid visualization generated: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Failed to generate solutions grid: {str(e)}")
            return None
    
    def _create_facade_comparison_visualization(self, facade_elements, optimization_results, selected_solutions):
        """创建原始与最佳方案对比图"""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.gridspec import GridSpec
            
            # 设置Unicode字体支持
            setup_unicode_fonts()
            
            # 创建图形
            fig = plt.figure(figsize=(20, 8))
            gs = GridSpec(2, 5, figure=fig, height_ratios=[3, 1])
            
            # Chart title in English
            fig.suptitle('Facade Optimization Solutions Comparison', 
                        fontsize=18, fontweight='bold', y=0.95)
            
            # Solution names in English
            solution_names = ['Original\nDesign', 'Energy\nOptimized', 
                            'Thermal\nOptimized', 'Cost\nOptimized', 
                            'Comprehensive\nOptimized']
            
            # 修复选定解数据结构
            solutions_list = []
            if selected_solutions:
                if isinstance(selected_solutions, dict):
                    # 按照顺序获取解
                    keys = ['energy_best', 'thermal_best', 'cost_best', 'comprehensive_best']
                    for key in keys:
                        if key in selected_solutions:
                            solutions_list.append(selected_solutions[key])
                        else:
                            # 如果没有特定类型，使用第一个可用的解
                            available_solutions = list(selected_solutions.values())
                            if available_solutions:
                                solutions_list.append(available_solutions[0])
                            else:
                                solutions_list.append(None)
                elif isinstance(selected_solutions, list):
                    solutions_list = selected_solutions[:4]
            
            # 填充缺失的解
            while len(solutions_list) < 4:
                solutions_list.append(None)
            
            # 绘制每个方案
            for i, name in enumerate(solution_names):
                ax = fig.add_subplot(gs[0, i])
                
                if i == 0:
                    # 原始方案
                    self._draw_original_facade(ax, facade_elements)
                else:
                    # 优化方案
                    solution = solutions_list[i-1] if i-1 < len(solutions_list) else None
                    self._draw_optimized_facade(ax, facade_elements, solution)
                
                ax.set_title(name, fontsize=12, fontweight='bold', pad=10)
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.set_aspect('equal')
                ax.axis('off')
            
            # 添加性能指标对比表
            metrics_ax = fig.add_subplot(gs[1, :])
            # 将解列表转换为字典格式以兼容表格渲染
            solutions_dict = {}
            if solutions_list:
                keys = ['energy_best', 'thermal_best', 'cost_best', 'comprehensive_best']
                for i, solution in enumerate(solutions_list):
                    if solution and i < len(keys):
                        solutions_dict[keys[i]] = solution
            self._add_performance_comparison_table(metrics_ax, solutions_dict)
            metrics_ax.axis('off')
            
            plt.tight_layout()
            
            # 保存图表
            if hasattr(self, 'session_directories'):
                output_path = os.path.join(self.session_directories['charts'], 'facade_comparison.png')
            else:
                output_dir = os.path.join(self.config.get_output_directory(), 'charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'facade_comparison.png')
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            plt.close()
            
            self.logger.info(f"Facade comparison chart generated: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Failed to generate facade comparison: {str(e)}")
            return None
    
    def _create_3d_isometric_visualization(self, visualization_data, selected_solutions):
        """创建3D轴测图可视化 - 使用增强3D可视化器并支持原始图像"""
        try:
            # 使用增强3D可视化器创建高质量3D可视化
            output_path = None
            
            if hasattr(self, 'session_directories'):
                output_path = os.path.join(self.session_directories['charts'], 'enhanced_3d_facade.png')
            else:
                output_dir = os.path.join(self.config.get_output_directory(), 'charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'enhanced_3d_facade.png')
            
            # 调用增强3D可视化器，传递原始图像数据
            result_path = self.enhanced_3d_visualizer.create_enhanced_3d_facade(
                facade_elements=visualization_data.facade_elements,
                selected_solutions=selected_solutions,
                output_path=output_path,
                original_image=visualization_data.original_image
            )
            
            if result_path:
                self.logger.info(f"Enhanced 3D facade visualization generated: {result_path}")
                return result_path
            else:
                self.logger.warning("Enhanced 3D visualization failed, fallback to basic visualization")
                return self._create_basic_3d_visualization(visualization_data, selected_solutions)
            
        except Exception as e:
            self.logger.error(f"Failed to generate enhanced 3D visualization: {str(e)}")
            # Fallback to basic visualization
            return self._create_basic_3d_visualization(visualization_data, selected_solutions)
    
    def _create_basic_3d_visualization(self, visualization_data, selected_solutions):
        """创建基础3D可视化作为备用方案"""
        try:
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # 设置Unicode字体支持
            setup_unicode_fonts()
            
            # 建筑参数
            wall_width = 12.0
            wall_height = 8.0
            wall_thickness = 0.24
            
            fig = plt.figure(figsize=(20, 15))
            gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.2)
            
            fig.suptitle('基础3D立面可视化 - 窗户、窗框、遮阳板对比\nBasic 3D Facade Visualization - Windows, Frames & Shading Comparison', 
                        fontsize=16, fontweight='bold', y=0.96)
            
            # 从真实解决方案中提取配置
            solution_configs = self._extract_real_solution_configs(selected_solutions, visualization_data.facade_elements)
            
            positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
            
            for i, config in enumerate(solution_configs[:4]):  # 最多显示4个解
                if i >= len(positions):
                    break
                pos = positions[i]
                ax = fig.add_subplot(gs[pos[0], pos[1]], projection='3d')
                
                # 解包配置数据
                if len(config) == 6:  # 新格式：包含frame_depths和shading_depths
                    title, window_types, window_positions, window_sizes, frame_depths, shading_depths = config
                else:  # 旧格式：只有基本数据
                    title, window_types, window_positions, window_sizes = config
                    frame_depths = [0.08 if wt == 1 else 0.0 for wt in window_types]
                    shading_depths = [0.15 if wt == 2 else 0.0 for wt in window_types]
                
                # 绘制基础的3D立面
                self._draw_enhanced_3d_facade_subplot(ax, wall_width, wall_height, wall_thickness, 
                                                  window_types, window_positions, window_sizes, 
                                                  frame_depths, shading_depths)
                
                # 设置标题
                ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
                
                # 设置更好的观察视角
                ax.view_init(elev=15, azim=-120)
                
                # 设置坐标轴
                ax.set_xlim(-0.5, wall_width + 0.5)
                ax.set_ylim(-1.2, wall_thickness + 0.3)
                ax.set_zlim(-0.2, wall_height + 0.5)
                
                # 简化坐标轴标签
                ax.set_xlabel('Width (m)', fontsize=9)
                ax.set_ylabel('Depth (m)', fontsize=9)
                ax.set_zlabel('Height (m)', fontsize=9)
                
                # 设置网格和背景
                ax.grid(True, alpha=0.3)
                ax.xaxis.pane.fill = True
                ax.yaxis.pane.fill = True
                ax.zaxis.pane.fill = True
                ax.xaxis.pane.set_facecolor('#F8F8F8')
                ax.yaxis.pane.set_facecolor('#F8F8F8')
                ax.zaxis.pane.set_facecolor('#F8F8F8')
                ax.xaxis.pane.set_alpha(0.3)
                ax.yaxis.pane.set_alpha(0.3)
                ax.zaxis.pane.set_alpha(0.3)
            
            # 3D axes don't work well with tight_layout, use subplots_adjust instead
            plt.subplots_adjust(left=0.1, right=0.95, top=0.9, bottom=0.1, hspace=0.3, wspace=0.3)
            
            # 保存图表
            if hasattr(self, 'session_directories'):
                output_path = os.path.join(self.session_directories['charts'], 'basic_3d_facade.png')
            else:
                output_dir = os.path.join(self.config.get_output_directory(), 'charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'basic_3d_facade.png')
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            plt.close()
            
            self.logger.info(f"Basic 3D visualization generated: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Failed to generate basic 3D visualization: {str(e)}")
            return None
    
    def _draw_enhanced_3d_facade_subplot(self, ax, wall_width, wall_height, wall_thickness, 
                                    window_types, window_positions, window_sizes, 
                                    frame_depths=None, shading_depths=None):
        """绘制增强的3D立面子图 - 使用真实优化数据"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            import matplotlib.colors as mcolors
            
            # 确保有完整的窗户数据
            if not window_positions:
                window_positions = self._generate_realistic_positions(len(window_types))
            if not window_sizes:
                window_sizes = self._generate_realistic_sizes(len(window_types))
            if not frame_depths:
                frame_depths = [0.08 if wt == 1 else 0.0 for wt in window_types]
            if not shading_depths:
                shading_depths = [0.15 if wt == 2 else 0.0 for wt in window_types]
            
            # 1. 绘制高质量墙体系统
            self._draw_realistic_wall_system(ax, wall_width, wall_height, wall_thickness)
            
            # 2. 绘制窗户系统（基于真实数据）
            for i, (window_type, (x, z), (width, height), frame_depth, shading_depth) in enumerate(
                zip(window_types, window_positions, window_sizes, frame_depths, shading_depths)):
                
                # 确保坐标在合理范围内
                x = max(0.5, min(wall_width - 0.5, x))
                z = max(0.5, min(wall_height - 0.5, z))
                width = max(0.8, min(3.0, width))
                height = max(1.2, min(2.5, height))
                
                # 绘制窗户开口
                self._draw_window_opening(ax, x, z, width, height, wall_thickness)
                
                # 绘制窗户玻璃
                self._draw_window_glass(ax, x, z, width, height)
                
                # 根据真实数据绘制窗框
                if frame_depth > 0:
                    self._draw_window_frame(ax, x, z, width, height, frame_depth)
                
                # 根据真实数据绘制遮阳系统
                if shading_depth > 0:
                    self._draw_window_shading(ax, x, z, width, height, shading_depth)
            
            # 3. 添加标注和图例
            self._add_3d_annotations(ax, window_types, frame_depths, shading_depths)
            
        except Exception as e:
            self.logger.error(f"绘制增强3D立面子图失败: {str(e)}")
            # 备用：绘制简化版本
            self._draw_fallback_3d_facade(ax, wall_width, wall_height, wall_thickness, window_types)
    
    def _draw_realistic_wall_system(self, ax, wall_width, wall_height, wall_thickness):
        """绘制真实的墙体系统"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # 主墙面
            wall_vertices = [
                [[0, 0, 0], [wall_width, 0, 0], 
                 [wall_width, 0, wall_height], [0, 0, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(wall_vertices, alpha=0.8, 
                                               facecolor='#F5F5F5', edgecolor='#888888', linewidth=2))
            
            # 墙体顶部厚度
            top_vertices = [
                [[0, 0, wall_height], [wall_width, 0, wall_height], 
                 [wall_width, wall_thickness, wall_height], [0, wall_thickness, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(top_vertices, alpha=0.9, 
                                               facecolor='#E0E0E0', edgecolor='#666666', linewidth=1))
            
            # 墙体侧面
            side_vertices = [
                [[wall_width, 0, 0], [wall_width, wall_thickness, 0], 
                 [wall_width, wall_thickness, wall_height], [wall_width, 0, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(side_vertices, alpha=0.7, 
                                               facecolor='#D0D0D0', edgecolor='#666666', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"绘制真实墙体系统失败: {str(e)}")
    
    def _draw_window_opening(self, ax, x, z, width, height, wall_thickness):
        """绘制窗户开口"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # 窗户开口（洞口）
            opening_depth = wall_thickness * 0.8
            opening_vertices = [
                # 洞口底部
                [[x-width/2, 0, z-height/2], [x+width/2, 0, z-height/2], 
                 [x+width/2, opening_depth, z-height/2], [x-width/2, opening_depth, z-height/2]],
                # 洞口顶部
                [[x-width/2, 0, z+height/2], [x+width/2, 0, z+height/2], 
                 [x+width/2, opening_depth, z+height/2], [x-width/2, opening_depth, z+height/2]],
                # 洞口左侧
                [[x-width/2, 0, z-height/2], [x-width/2, 0, z+height/2], 
                 [x-width/2, opening_depth, z+height/2], [x-width/2, opening_depth, z-height/2]],
                # 洞口右侧
                [[x+width/2, 0, z-height/2], [x+width/2, 0, z+height/2], 
                 [x+width/2, opening_depth, z+height/2], [x+width/2, opening_depth, z-height/2]]
            ]
            
            for vertices in opening_vertices:
                ax.add_collection3d(Poly3DCollection([vertices], alpha=0.9, 
                                                   facecolor='#2C2C2C', edgecolor='#000000', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"绘制窗户开口失败: {str(e)}")
    
    def _draw_window_glass(self, ax, x, z, width, height):
        """绘制窗户玻璃"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # 双层玻璃
            glass_positions = [0.05, 0.10]  # 双层玻璃间距
            glass_colors = ['#E6F3FF', '#D0E8FF']  # 玻璃颜色
            
            for i, (glass_pos, glass_color) in enumerate(zip(glass_positions, glass_colors)):
                glass_vertices = [
                    [[x-width/2, glass_pos, z-height/2], [x+width/2, glass_pos, z-height/2], 
                     [x+width/2, glass_pos, z+height/2], [x-width/2, glass_pos, z+height/2]]
                ]
                ax.add_collection3d(Poly3DCollection(glass_vertices, alpha=0.6, 
                                                   facecolor=glass_color, edgecolor='#4A90E2', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"绘制窗户玻璃失败: {str(e)}")
    
    def _draw_window_frame(self, ax, x, z, width, height, frame_depth):
        """绘制窗户框架"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            frame_width = 0.04  # 减小窗框宽度以更符合实际比例
            frame_color = '#8B4513'  # 棕色窗框
            
            # 窗框框架
            frame_positions = [
                # 上框
                [(x-width/2-frame_width, 0.15, z+height/2), (x+width/2+frame_width, 0.15, z+height/2),
                 (x+width/2+frame_width, 0.15+frame_depth, z+height/2), (x-width/2-frame_width, 0.15+frame_depth, z+height/2)],
                # 下框
                [(x-width/2-frame_width, 0.15, z-height/2), (x+width/2+frame_width, 0.15, z-height/2),
                 (x+width/2+frame_width, 0.15+frame_depth, z-height/2), (x-width/2-frame_width, 0.15+frame_depth, z-height/2)],
                # 左框
                [(x-width/2-frame_width, 0.15, z-height/2), (x-width/2-frame_width, 0.15, z+height/2),
                 (x-width/2-frame_width, 0.15+frame_depth, z+height/2), (x-width/2-frame_width, 0.15+frame_depth, z-height/2)],
                # 右框
                [(x+width/2+frame_width, 0.15, z-height/2), (x+width/2+frame_width, 0.15, z+height/2),
                 (x+width/2+frame_width, 0.15+frame_depth, z+height/2), (x+width/2+frame_width, 0.15+frame_depth, z-height/2)]
            ]
            
            for frame_verts in frame_positions:
                ax.add_collection3d(Poly3DCollection([frame_verts], alpha=0.9, 
                                                   facecolor=frame_color, edgecolor='#654321', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"绘制窗户框架失败: {str(e)}")
    
    def _draw_window_shading(self, ax, x, z, width, height, shading_depth):
        """绘制窗户遮阳系统"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            shading_color = '#4A4A4A'  # 深灰色遮阳板
            support_color = '#6A6A6A'  # 支撑结构颜色
            
            # 遮阳板主体
            shading_width = width + 0.2  # 减小遮阳板宽度以更符合实际比例
            shading_vertices = [
                [[x-shading_width/2, 0.12, z+height/2+0.1], [x+shading_width/2, 0.12, z+height/2+0.1],
                 [x+shading_width/2, 0.12+shading_depth, z+height/2+0.1], [x-shading_width/2, 0.12+shading_depth, z+height/2+0.1]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_vertices, alpha=0.8, 
                                               facecolor=shading_color, edgecolor='#2A2A2A', linewidth=1))
            
            # 遮阳板支撑
            support_positions = [x-shading_width/2+0.2, x, x+shading_width/2-0.2]
            for support_x in support_positions:
                support_vertices = [
                    [[support_x-0.02, 0.12, z+height/2+0.05], [support_x+0.02, 0.12, z+height/2+0.05],
                     [support_x+0.02, 0.12+shading_depth, z+height/2+0.05], [support_x-0.02, 0.12+shading_depth, z+height/2+0.05]]
                ]
                ax.add_collection3d(Poly3DCollection(support_vertices, alpha=0.9, 
                                                   facecolor=support_color, edgecolor='#1A1A1A', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"绘制窗户遮阳系统失败: {str(e)}")
    
    def _add_3d_annotations(self, ax, window_types, frame_depths, shading_depths):
        """添加3D标注"""
        try:
            # 统计构件数量
            frame_count = sum(1 for fd in frame_depths if fd > 0)
            shading_count = sum(1 for sd in shading_depths if sd > 0)
            
            # 添加统计信息
            info_text = f"Frames: {frame_count}, Shading: {shading_count}"
            ax.text2D(0.02, 0.98, info_text, transform=ax.transAxes, fontsize=8, 
                      verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
            
        except Exception as e:
            self.logger.error(f"添加3D标注失败: {str(e)}")
    
    def _draw_fallback_3d_facade(self, ax, wall_width, wall_height, wall_thickness, window_types):
        """绘制备用简化3D立面"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # 简化墙体
            wall_vertices = [
                [[0, 0, 0], [wall_width, 0, 0], 
                 [wall_width, 0, wall_height], [0, 0, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(wall_vertices, alpha=0.8, 
                                               facecolor='lightgray', edgecolor='black', linewidth=2))
            
            # 简化窗户
            positions = [(2, 2), (6, 2), (10, 2), (2, 5), (6, 5), (10, 5)]
            for i, (x, z) in enumerate(positions[:len(window_types)]):
                window_type = window_types[i]
                colors = {0: 'lightblue', 1: 'blue', 2: 'green'}
                color = colors.get(window_type, 'lightblue')
                
                window_vertices = [
                    [[x-0.8, 0.05, z-1.0], [x+0.8, 0.05, z-1.0], 
                     [x+0.8, 0.05, z+1.0], [x-0.8, 0.05, z+1.0]]
                ]
                ax.add_collection3d(Poly3DCollection(window_vertices, alpha=0.8, 
                                                   facecolor=color, edgecolor='darkblue', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"绘制备用3D立面失败: {str(e)}")
    
    def _draw_clear_3d_facade_subplot(self, ax, wall_width, wall_height, wall_thickness, window_types, window_positions=None, window_sizes=None):
        """绘制清晰的3D立面子图（保持兼容性）"""
        # 使用新的增强方法
        frame_depths = [0.08 if wt == 1 else 0.0 for wt in window_types]
        shading_depths = [0.15 if wt == 2 else 0.0 for wt in window_types]
        self._draw_enhanced_3d_facade_subplot(ax, wall_width, wall_height, wall_thickness, 
                                            window_types, window_positions, window_sizes, 
                                            frame_depths, shading_depths)
    
    def _draw_detailed_3d_facade_subplot(self, ax, wall_width, wall_height, wall_thickness, window_types, window_positions=None, window_sizes=None):
        """绘制详细的3D立面子图（使用真实数据）"""
        try:
            # 使用增强的3D立面绘制方法，传入真实的窗框和遮阳深度
            frame_depths = [0.08 if wt == 1 else 0.0 for wt in window_types]
            shading_depths = [0.15 if wt == 2 else 0.0 for wt in window_types]
            self._draw_enhanced_3d_facade_subplot(ax, wall_width, wall_height, wall_thickness, 
                                                window_types, window_positions, window_sizes, 
                                                frame_depths, shading_depths)
        except Exception as e:
            self.logger.error(f"绘制详细3D立面子图失败: {str(e)}")
    
    def _draw_clear_window_element(self, ax, x, z, w, h, window_type):
        """绘制清晰的窗户元素"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # 窗户颜色根据类型区分
            colors = {0: '#87CEEB', 1: '#4169E1', 2: '#228B22'}
            color = colors.get(window_type, '#87CEEB')
            
            # 窗户玻璃
            glass = [
                [[x+0.1, -0.02, z+0.1], [x+w-0.1, -0.02, z+0.1], 
                 [x+w-0.1, -0.02, z+h-0.1], [x+0.1, -0.02, z+h-0.1]]
            ]
            ax.add_collection3d(Poly3DCollection(glass, alpha=0.7, 
                                               facecolor=color, edgecolor='#000080', linewidth=2))
            
            # 窗户分隔条
            ax.plot([x, x+w], [-0.01, -0.01], [z+h/2, z+h/2], 'white', linewidth=4)
            ax.plot([x+w/2, x+w/2], [-0.01, -0.01], [z, z+h], 'white', linewidth=4)
            
        except Exception as e:
            self.logger.error(f"绘制窗户元素失败: {str(e)}")
    
    def _draw_clear_frame_element(self, ax, x, z, w, h):
        """绘制清晰的窗框元素"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            frame_width = 0.15
            frame_depth = 0.08
            frame_color = '#8B4513'
            
            # 简化的窗框 - 只绘制主要部分
            frame_parts = [
                # 上框
                [[x-frame_width, -frame_depth, z+h], [x+w+frame_width, -frame_depth, z+h], 
                 [x+w+frame_width, -frame_depth, z+h+frame_width], [x-frame_width, -frame_depth, z+h+frame_width]],
                # 下框
                [[x-frame_width, -frame_depth, z-frame_width], [x+w+frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, -frame_depth, z], [x-frame_width, -frame_depth, z]],
                # 左框
                [[x-frame_width, -frame_depth, z], [x, -frame_depth, z], 
                 [x, -frame_depth, z+h], [x-frame_width, -frame_depth, z+h]],
                # 右框
                [[x+w, -frame_depth, z], [x+w+frame_width, -frame_depth, z], 
                 [x+w+frame_width, -frame_depth, z+h], [x+w, -frame_depth, z+h]]
            ]
            
            for frame_part in frame_parts:
                ax.add_collection3d(Poly3DCollection([frame_part], alpha=1.0, 
                                                   facecolor=frame_color, edgecolor='#654321', linewidth=2))
            
        except Exception as e:
            self.logger.error(f"绘制窗框元素失败: {str(e)}")
    
    def _draw_clear_shading_element(self, ax, x, z, w, h):
        """绘制清晰的遮阳板元素"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            shading_projection = 0.8
            shading_thickness = 0.08
            shading_color = '#2E8B57'
            shading_z = z + h + 0.2
            
            # 遮阳板主体
            shading_main = [
                [[x-0.2, -shading_projection, shading_z], 
                 [x+w+0.2, -shading_projection, shading_z], 
                 [x+w+0.2, 0.3, shading_z+shading_thickness], 
                 [x-0.2, 0.3, shading_z+shading_thickness]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_main, alpha=0.9, 
                                               facecolor=shading_color, edgecolor='#1F5F1F', linewidth=2))
            
            # 遮阳板前端
            shading_front = [
                [[x-0.2, -shading_projection, shading_z], 
                 [x+w+0.2, -shading_projection, shading_z], 
                 [x+w+0.2, -shading_projection, shading_z+shading_thickness], 
                 [x-0.2, -shading_projection, shading_z+shading_thickness]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_front, alpha=1.0, 
                                               facecolor=shading_color, edgecolor='#1F5F1F', linewidth=2))
            
            # 简化的支撑
            for support_x in [x, x+w]:
                ax.plot([support_x, support_x], [-0.1, 0.24], [shading_z, shading_z+shading_thickness], 
                       color='#8B4513', linewidth=6)
            
        except Exception as e:
            self.logger.error(f"绘制遮阳板元素失败: {str(e)}")
    
    def _draw_facade_solution(self, ax, facade_elements, solution):
        """绘制立面解决方案 - 使用真实优化数据"""
        try:
            import matplotlib.patches as patches
            
            # 绘制墙体背景
            wall_patch = patches.Rectangle((0, 0), 1, 1, 
                                         linewidth=1, edgecolor='black', 
                                         facecolor='lightgray', alpha=0.8)
            ax.add_patch(wall_patch)
            
            # 从解决方案中提取完整的优化数据
            solution_data = self._extract_complete_solution_data(solution, facade_elements)
            
            # 如果没有提取到数据，使用默认配置
            if not solution_data or 'windows' not in solution_data:
                self._draw_default_solution_layout(ax)
                return
            
            # 获取立面尺寸用于坐标转换
            if hasattr(facade_elements, 'image_shape'):
                height, width = facade_elements.image_shape[:2]
                pixel_ratio = getattr(facade_elements, 'pixel_to_meter_ratio', 0.01)
                facade_width_m = width * pixel_ratio
                facade_height_m = height * pixel_ratio
            else:
                facade_width_m = 12.0  # 默认12米
                facade_height_m = 8.0  # 默认8米
            
            # 绘制优化后的窗户
            for window_data in solution_data['windows']:
                x, y, w, h, window_type = window_data
                
                # 将真实坐标转换为标准化坐标[0,1]
                x_norm = max(0, min(1, x / facade_width_m))
                y_norm = max(0, min(1, y / facade_height_m))
                w_norm = max(0.05, min(0.4, w / facade_width_m))  # 限制最小/最大尺寸
                h_norm = max(0.08, min(0.5, h / facade_height_m))
                
                # 确保窗户不超出边界
                if x_norm + w_norm > 1:
                    w_norm = 1 - x_norm
                if y_norm + h_norm > 1:
                    h_norm = 1 - y_norm
                
                # 窗户颜色根据类型区分
                colors = {0: 'lightblue', 1: 'blue', 2: 'green'}
                color = colors.get(window_type, 'lightblue')
                
                # 绘制窗户
                window_patch = patches.Rectangle((x_norm, y_norm), w_norm, h_norm, 
                                               linewidth=1, edgecolor='darkblue', 
                                               facecolor=color, alpha=0.8)
                ax.add_patch(window_patch)
                
                # 添加窗框（如果需要）
                if window_type >= 1:
                    frame_padding = min(0.02, w_norm * 0.1, h_norm * 0.1)  # 动态调整窗框大小
                    frame_patch = patches.Rectangle((x_norm-frame_padding, y_norm-frame_padding), 
                                                   w_norm+2*frame_padding, h_norm+2*frame_padding, 
                                                   linewidth=1, edgecolor='brown', 
                                                   facecolor='none')
                    ax.add_patch(frame_patch)
                
                # 添加遮阳板（如果需要）
                if window_type >= 2:
                    shading_height = min(0.04, h_norm * 0.2)  # 动态调整遮阳板大小
                    shading_patch = patches.Rectangle((x_norm-frame_padding, y_norm+h_norm), 
                                                     w_norm+2*frame_padding, shading_height, 
                                                     linewidth=1, edgecolor='darkgreen', 
                                                     facecolor='lightgreen', alpha=0.8)
                    ax.add_patch(shading_patch)
            
        except Exception as e:
            self.logger.error(f"绘制立面解决方案失败: {str(e)}")
            # 备用：使用默认布局
            self._draw_default_solution_layout(ax)
    
    def _extract_complete_solution_data(self, solution, facade_elements):
        """从解决方案中提取完整的优化数据"""
        try:
            if not solution:
                return None
            
            # 尝试从individual属性提取
            if hasattr(solution, 'individual'):
                individual = solution.individual
                return self._extract_data_from_individual(individual, facade_elements)
            
            # 尝试直接从solution对象提取
            if hasattr(solution, 'window_positions') and hasattr(solution, 'window_sizes'):
                return {
                    'windows': [
                        (pos[0], pos[1], size[0], size[1], wtype)
                        for pos, size, wtype in zip(
                            solution.window_positions,
                            solution.window_sizes,
                            getattr(solution, 'window_types', [0] * len(solution.window_positions))
                        )
                    ]
                }
            
            # 尝试从genes提取
            if hasattr(solution, 'genes') and len(solution.genes) > 0:
                return self._extract_data_from_genes(solution.genes, facade_elements)
            
            return None
            
        except Exception as e:
            self.logger.error(f"提取完整解决方案数据失败: {str(e)}")
            return None
    
    def _extract_data_from_individual(self, individual, facade_elements):
        """从individual对象提取数据"""
        try:
            if hasattr(individual, 'window_positions') and hasattr(individual, 'window_sizes'):
                return {
                    'windows': [
                        (pos[0], pos[1], size[0], size[1], wtype)
                        for pos, size, wtype in zip(
                            individual.window_positions,
                            individual.window_sizes,
                            getattr(individual, 'window_types', [0] * len(individual.window_positions))
                        )
                    ]
                }
            return None
        except Exception as e:
            self.logger.error(f"从individual提取数据失败: {str(e)}")
            return None
    
    def _extract_data_from_genes(self, genes, facade_elements):
        """从基因数据提取解决方案信息"""
        try:
            # 简化的基因解析逻辑
            if len(genes) < 6:  # 至少需要6个基因（位置和尺寸）
                return None
            
            # 假设基因结构：[x1, y1, w1, h1, x2, y2, w2, h2, ...]
            windows = []
            for i in range(0, min(len(genes), 24), 4):  # 最多6个窗户，每个4个基因
                if i + 3 < len(genes):
                    x = genes[i]
                    y = genes[i + 1]
                    w = genes[i + 2]
                    h = genes[i + 3]
                    window_type = 0  # 默认类型
                    windows.append((x, y, w, h, window_type))
            
            return {'windows': windows}
        except Exception as e:
            self.logger.error(f"从基因提取数据失败: {str(e)}")
            return None
    
    def _draw_default_solution_layout(self, ax):
        """绘制默认解决方案布局"""
        import matplotlib.patches as patches
        
        # 默认窗户布局
        window_positions = [(0.1, 0.1), (0.4, 0.1), (0.7, 0.1), 
                          (0.1, 0.6), (0.4, 0.6), (0.7, 0.6)]
        
        for i, (x, y) in enumerate(window_positions):
            window_patch = patches.Rectangle((x, y), 0.2, 0.3, 
                                           linewidth=1, edgecolor='darkblue', 
                                           facecolor='lightblue', alpha=0.7)
            ax.add_patch(window_patch)
    
    def _extract_window_types_from_solution(self, solution):
        """从解决方案中提取窗户类型"""
        try:
            if not solution:
                return [0] * 6  # 默认6个普通窗户
            
            # 尝试从individual属性提取
            if hasattr(solution, 'individual'):
                individual = solution.individual
                
                if hasattr(individual, 'window_types'):
                    return list(individual.window_types)
                elif hasattr(individual, 'genes') and len(individual.genes) > 0:
                    return self._infer_window_types_from_genes(individual.genes)
            
            # 尝试直接从solution对象提取
            if hasattr(solution, 'window_types'):
                return list(solution.window_types)
            
            # 如果都没有，返回默认配置
            return [0] * 6
            
        except Exception as e:
            self.logger.error(f"提取窗户类型失败: {str(e)}")
            return [0] * 6
    
    def _draw_original_facade(self, ax, facade_elements):
        """绘制原始立面 - 增强版本，支持原始图像纹理"""
        try:
            import matplotlib.patches as patches
            from matplotlib.image import imread
            import matplotlib.pyplot as plt
            import numpy as np
            import os
            
            # 首先尝试加载和显示原始图像
            original_image_loaded = False
            try:
                # 检查是否有原始图像数据
                if hasattr(facade_elements, 'original_image_path') and facade_elements.original_image_path:
                    if os.path.exists(facade_elements.original_image_path):
                        try:
                            # 方法1: 直接使用matplotlib的imread
                            original_image = imread(facade_elements.original_image_path)
                            if original_image is not None:
                                ax.imshow(original_image, extent=[0, 1, 0, 1], aspect='auto', alpha=0.9)
                                original_image_loaded = True
                                self.logger.info("成功加载原始立面图像纹理 (使用matplotlib)")
                        except Exception:
                            try:
                                # 方法2: 使用OpenCV进行解码（支持中文路径）
                                import cv2
                                with open(facade_elements.original_image_path, 'rb') as f:
                                    image_data = f.read()
                                
                                nparr = np.frombuffer(image_data, np.uint8)
                                original_image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                                
                                if original_image is not None:
                                    # 转换BGR到RGB
                                    original_image = cv2.cvtColor(original_image, cv2.COLOR_BGR2RGB)
                                    ax.imshow(original_image, extent=[0, 1, 0, 1], aspect='auto', alpha=0.9)
                                    original_image_loaded = True
                                    self.logger.info("成功加载原始立面图像纹理 (使用OpenCV)")
                                else:
                                    self.logger.warning("OpenCV图像解码失败")
                            except ImportError:
                                self.logger.warning("OpenCV未安装，无法处理中文路径图像")
                            except Exception as cv_e:
                                self.logger.warning(f"OpenCV解码失败: {str(cv_e)}")
                    else:
                        self.logger.warning(f"原始图像文件不存在: {facade_elements.original_image_path}")
                else:
                    self.logger.warning("未找到原始图像路径信息")
                    
            except Exception as img_e:
                self.logger.warning(f"加载原始图像失败: {str(img_e)}")
            
            # 如果无法加载原始图像，使用传统的几何绘制方法
            if not original_image_loaded:
                # 绘制墙体背景
                wall_patch = patches.Rectangle((0, 0), 1, 1, 
                                             linewidth=1, edgecolor='black', 
                                             facecolor='lightgray', alpha=0.8)
                ax.add_patch(wall_patch)
            
            # 获取立面尺寸用于坐标转换
            if hasattr(facade_elements, 'image_shape'):
                height, width = facade_elements.image_shape[:2]
                pixel_ratio = getattr(facade_elements, 'pixel_to_meter_ratio', 0.01)
                facade_width_m = width * pixel_ratio
                facade_height_m = height * pixel_ratio
            else:
                facade_width_m = 12.0  # 默认12米
                facade_height_m = 8.0  # 默认8米
                pixel_ratio = 0.01
            
            # 绘制真实的原始窗户
            if hasattr(facade_elements, 'windows') and facade_elements.windows:
                for i, window in enumerate(facade_elements.windows):
                    # 获取窗户的真实位置和尺寸（已经是米制单位）
                    center_x, center_y = window.center
                    window_width = window.width
                    window_height = window.height
                    
                    # 转换为标准化坐标（修复：不需要再次乘以pixel_ratio）
                    x_norm = max(0, min(1, (center_x - window_width/2) / facade_width_m))
                    y_norm = max(0, min(1, (center_y - window_height/2) / facade_height_m))
                    w_norm = max(0.05, min(0.4, window_width / facade_width_m))
                    h_norm = max(0.08, min(0.5, window_height / facade_height_m))
                    
                    # 确保不超出边界
                    if x_norm + w_norm > 1:
                        w_norm = 1 - x_norm
                    if y_norm + h_norm > 1:
                        h_norm = 1 - y_norm
                    
                    # 绘制原始窗户
                    window_patch = patches.Rectangle((x_norm, y_norm), w_norm, h_norm, 
                                                   linewidth=1, edgecolor='darkblue', 
                                                   facecolor='lightblue', alpha=0.8)
                    ax.add_patch(window_patch)
            else:
                # 如果没有真实窗户数据，使用默认布局
                window_positions = [(0.1, 0.1), (0.4, 0.1), (0.7, 0.1), 
                                  (0.1, 0.6), (0.4, 0.6), (0.7, 0.6)]
                
                for x, y in window_positions:
                    window_patch = patches.Rectangle((x, y), 0.2, 0.3, 
                                                   linewidth=1, edgecolor='darkblue', 
                                                   facecolor='lightblue', alpha=0.7)
                    ax.add_patch(window_patch)
            
        except Exception as e:
            self.logger.error(f"绘制优化立面失败: {str(e)}")
    
    def _add_solutions_legend(self, ax):
        """添加解决方案图例"""
        try:
            import matplotlib.patches as patches
            
            # 图例元素
            legend_elements = [
                ('普通窗户 Normal Window', 'lightblue'),
                ('窗框优化 Frame Optimized', 'blue'),
                ('遮阳优化 Shading Optimized', 'green')
            ]
            
            x_start = 0.1
            for i, (label, color) in enumerate(legend_elements):
                x = x_start + i * 0.3
                
                # 颜色块
                color_patch = patches.Rectangle((x, 0.3), 0.05, 0.4, 
                                              facecolor=color, alpha=0.7, 
                                              edgecolor='black', linewidth=1)
                ax.add_patch(color_patch)
                
                # 标签
                ax.text(x + 0.07, 0.5, label, fontsize=10, 
                       verticalalignment='center')
            
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            
        except Exception as e:
            self.logger.error(f"添加解决方案图例失败: {str(e)}")
    
    def _add_performance_comparison_table(self, ax, selected_solutions):
        """添加性能对比表格"""
        try:
            if not selected_solutions:
                ax.text(0.5, 0.5, 'No performance data available', 
                       ha='center', va='center', fontsize=12)
                return
            
            # 表格数据
            headers = ['Solution Type', 'Energy (kWh/m²)', 'Thermal Perf.', 'Cost (k¥)']
            rows = []
            
            solution_names = {
                'energy_best': 'Energy Optimized',
                'thermal_best': 'Thermal Optimized', 
                'cost_best': 'Cost Optimized',
                'comprehensive_best': 'Comprehensive'
            }
            
            for key, solution in selected_solutions.items():
                if solution:
                    name = solution_names.get(key, key)
                    energy = f"{getattr(solution, 'energy_consumption', 0):.1f}"
                    thermal = f"{getattr(solution, 'thermal_performance', 0):.2f}"
                    cost = f"{getattr(solution, 'renovation_cost', 0)/1000:.0f}"
                    rows.append([name, energy, thermal, cost])
            
            # 创建表格
            table_data = [headers] + rows
            table = ax.table(cellText=table_data[1:], colLabels=table_data[0],
                           cellLoc='center', loc='center',
                           bbox=[0.1, 0.2, 0.8, 0.6])
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(10)
            table.scale(1, 2)
            
            # 设置表头样式
            for i in range(len(headers)):
                table[(0, i)].set_facecolor('#E6E6FA')
                table[(0, i)].set_text_props(weight='bold')
            
        except Exception as e:
            self.logger.error(f"添加性能对比表格失败: {str(e)}")
    
    def get_visualization_status(self) -> Dict[str, Any]:
        """获取可视化状态"""
        try:
            status = {
                'manager_initialized': True,
                'current_session_id': self.current_session_id,
                'components_status': {
                    'chart_config': self.chart_config is not None,
                    'optimization_charts': self.optimization_charts is not None,
                    'performance_charts': self.performance_charts is not None,
                    'comparison_charts': self.comparison_charts is not None,
                    'report_generator': self.report_generator is not None
                },
                'generated_charts_count': len(self.generated_charts),
                'configuration': {
                    'auto_generate_all_charts': self.auto_generate_all_charts,
                    'parallel_chart_generation': self.parallel_chart_generation,
                    'cache_generated_charts': self.cache_generated_charts,
                    'organize_outputs': self.organize_outputs,
                    'cleanup_old_outputs': self.cleanup_old_outputs
                }
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取可视化状态失败: {str(e)}")
            return {'error': str(e)}
    
    def clear_cache(self):
        """清理缓存"""
        try:
            self.generated_charts.clear()
            self.current_session_id = None
            self.logger.info("可视化缓存已清理")
            
        except Exception as e:
            self.logger.error(f"清理缓存失败: {str(e)}")
    
    def __del__(self):
        """析构函数"""
        try:
            if hasattr(self, 'logger'):
                self.logger.info("Visualization manager destroyed")
        except:
            pass


    def _draw_optimized_facade(self, ax, facade_elements, solution):
        """绘制优化立面"""
        try:
            import matplotlib.patches as patches
            
            # 绘制墙体背景
            wall_patch = patches.Rectangle((0, 0), 1, 1, 
                                         linewidth=1, edgecolor='black', 
                                         facecolor='lightgray', alpha=0.8)
            ax.add_patch(wall_patch)
            
            # 获取立面尺寸用于坐标转换
            if hasattr(facade_elements, 'image_shape'):
                height, width = facade_elements.image_shape[:2]
                pixel_ratio = getattr(facade_elements, 'pixel_to_meter_ratio', 0.01)
                facade_width_m = width * pixel_ratio
                facade_height_m = height * pixel_ratio
            else:
                facade_width_m = 12.0  # 默认12米
                facade_height_m = 8.0  # 默认8米
            
            # 从解决方案中提取真实的窗户配置
            solution_data = self._extract_complete_solution_data(solution, facade_elements)
            
            if solution_data and 'windows' in solution_data:
                # 使用真实的优化数据
                for window_data in solution_data['windows']:
                    x, y, w, h, window_type = window_data
                    
                    # 将真实坐标转换为标准化坐标[0,1]
                    x_norm = max(0, min(1, x / facade_width_m))
                    y_norm = max(0, min(1, y / facade_height_m))
                    w_norm = max(0.05, min(0.4, w / facade_width_m))
                    h_norm = max(0.08, min(0.5, h / facade_height_m))
                    
                    # 确保窗户不超出边界
                    if x_norm + w_norm > 1:
                        w_norm = 1 - x_norm
                    if y_norm + h_norm > 1:
                        h_norm = 1 - y_norm
                    
                    # 窗户颜色根据类型区分
                    colors = {0: 'lightblue', 1: 'blue', 2: 'green'}
                    color = colors.get(window_type, 'lightblue')
                    
                    # 绘制窗户
                    window_patch = patches.Rectangle((x_norm, y_norm), w_norm, h_norm, 
                                                   linewidth=1, edgecolor='darkblue', 
                                                   facecolor=color, alpha=0.8)
                    ax.add_patch(window_patch)
                    
                    # 添加窗框（如果需要）
                    if window_type >= 1:
                        frame_padding = min(0.02, w_norm * 0.1, h_norm * 0.1)
                        frame_patch = patches.Rectangle((x_norm-frame_padding, y_norm-frame_padding), 
                                                       w_norm+2*frame_padding, h_norm+2*frame_padding, 
                                                       linewidth=1, edgecolor='brown', 
                                                       facecolor='none')
                        ax.add_patch(frame_patch)
                    
                    # 添加遮阳板（如果需要）
                    if window_type >= 2:
                        shading_height = min(0.04, h_norm * 0.2)
                        shading_patch = patches.Rectangle((x_norm-frame_padding, y_norm+h_norm), 
                                                         w_norm+2*frame_padding, shading_height, 
                                                         linewidth=1, edgecolor='darkgreen', 
                                                         facecolor='lightgreen', alpha=0.8)
                        ax.add_patch(shading_patch)
            else:
                # 如果没有提取到数据，使用基于窗户类型的默认布局
                window_types = self._extract_window_types_from_solution(solution)
                window_positions = [(0.1, 0.1), (0.4, 0.1), (0.7, 0.1), 
                                  (0.1, 0.6), (0.4, 0.6), (0.7, 0.6)]
                
                for i, (x, y) in enumerate(window_positions):
                    window_type = window_types[i] if i < len(window_types) else 0
                    
                    # 窗户颜色根据类型区分
                    colors = {0: 'lightblue', 1: 'blue', 2: 'green'}
                    color = colors.get(window_type, 'lightblue')
                    
                    window_patch = patches.Rectangle((x, y), 0.2, 0.3, 
                                                   linewidth=1, edgecolor='darkblue', 
                                                   facecolor=color, alpha=0.7)
                    ax.add_patch(window_patch)
                
                # 添加窗框（如果需要）
                if window_type >= 1:
                    frame_patch = patches.Rectangle((x-0.02, y-0.02), 0.24, 0.34, 
                                                   linewidth=2, edgecolor='brown', 
                                                   facecolor='none')
                    ax.add_patch(frame_patch)
                
                # 添加遮阳板（如果需要）
                if window_type >= 2:
                    shading_patch = patches.Rectangle((x-0.05, y+0.3), 0.3, 0.05, 
                                                     linewidth=1, edgecolor='darkgreen', 
                                                     facecolor='lightgreen', alpha=0.8)
                    ax.add_patch(shading_patch)
            
        except Exception as e:
            self.logger.error(f"绘制优化立面失败: {str(e)}")
    
    def _extract_real_solution_configs(self, selected_solutions, facade_elements):
        """从真实解决方案中提取窗户配置"""
        try:
            solution_configs = []
            
            # 添加原始设计作为对比
            original_config = self._get_original_facade_config(facade_elements)
            solution_configs.append(original_config)
            
            # 从选定解决方案中提取配置
            if selected_solutions:
                if isinstance(selected_solutions, dict):
                    # 处理字典格式的解决方案
                    for key, solution in selected_solutions.items():
                        if solution and len(solution_configs) < 4:
                            config = self._extract_solution_config(solution, key)
                            if config:
                                solution_configs.append(config)
                elif isinstance(selected_solutions, list):
                    # 处理列表格式的解决方案
                    for i, solution in enumerate(selected_solutions[:3]):
                        if solution and len(solution_configs) < 4:
                            config = self._extract_solution_config(solution, f"Solution {i+1}")
                            if config:
                                solution_configs.append(config)
            
            # 如果解决方案不足4个，用默认配置填充
            while len(solution_configs) < 4:
                default_config = self._get_default_config(len(solution_configs))
                solution_configs.append(default_config)
            
            return solution_configs[:4]
            
        except Exception as e:
            self.logger.error(f"提取解决方案配置失败: {str(e)}")
            return self._get_fallback_configs()
    
    def _get_original_facade_config(self, facade_elements):
        """获取原始立面配置 - 基于真实立面元素数据"""
        try:
            # 基于立面元素生成原始配置
            window_count = 6  # 默认6个窗户
            
            # 从真实立面元素数据中提取窗户信息
            if facade_elements and hasattr(facade_elements, 'windows') and facade_elements.windows:
                window_count = len(facade_elements.windows)
                
                # 提取真实的窗户位置和尺寸
                window_types = []
                window_positions = []
                window_sizes = []
                
                for window in facade_elements.windows:
                    # 从窗户元素中提取真实数据
                    if hasattr(window, 'window_type'):
                        window_types.append(window.window_type)
                    else:
                        window_types.append(0)  # 默认普通窗户
                    
                    if hasattr(window, 'center'):
                        # 使用真实的窗户中心位置
                        center_x, center_y = window.center
                        # 转换为3D坐标系 (x, z)
                        window_positions.append((center_x, center_y))
                    else:
                        # 使用默认位置
                        window_positions.append((0.0, 0.0))
                    
                    if hasattr(window, 'width') and hasattr(window, 'height'):
                        # 使用真实的窗户尺寸
                        window_sizes.append((window.width, window.height))
                    else:
                        # 使用默认尺寸
                        window_sizes.append((1.5, 2.0))
                        
            elif isinstance(facade_elements, dict) and 'windows' in facade_elements:
                window_count = len(facade_elements['windows'])
                window_types = [0] * window_count
                window_positions = self._generate_realistic_positions(window_count)
                window_sizes = self._generate_realistic_sizes(window_count)
            else:
                window_types = [0] * window_count
                window_positions = self._generate_realistic_positions(window_count)
                window_sizes = self._generate_realistic_sizes(window_count)
            
            # 原始设计没有窗框和遮阳
            frame_depths = [0.0] * window_count
            shading_depths = [0.0] * window_count
            
            return ("原始设计\nOriginal", window_types, window_positions, window_sizes, frame_depths, shading_depths)
            
        except Exception as e:
            self.logger.error(f"获取原始立面配置失败: {str(e)}")
            return ("原始设计\nOriginal", [0]*6, self._generate_realistic_positions(6), self._generate_realistic_sizes(6), [0.0]*6, [0.0]*6)
    
    def _extract_solution_config(self, solution, solution_name):
        """从单个解决方案中提取配置 - 使用真实优化数据"""
        try:
            window_types = []
            window_positions = []
            window_sizes = []
            frame_depths = []
            shading_depths = []
            
            # 从ObjectiveResults对象提取完整数据
            if isinstance(solution, ObjectiveResults):
                # 提取窗户设计参数
                if hasattr(solution, 'window_types') and solution.window_types:
                    window_types = list(solution.window_types)
                if hasattr(solution, 'window_positions') and solution.window_positions:
                    window_positions = list(solution.window_positions)
                if hasattr(solution, 'window_sizes') and solution.window_sizes:
                    window_sizes = list(solution.window_sizes)
                if hasattr(solution, 'frame_depths') and solution.frame_depths:
                    frame_depths = list(solution.frame_depths)
                if hasattr(solution, 'shading_depths') and solution.shading_depths:
                    shading_depths = list(solution.shading_depths)
            
            # 从individual对象提取数据
            elif hasattr(solution, 'individual'):
                individual = solution.individual
                
                # 提取完整的窗户设计参数
                if hasattr(individual, 'window_types'):
                    window_types = list(individual.window_types)
                if hasattr(individual, 'window_positions'):
                    window_positions = list(individual.window_positions)
                if hasattr(individual, 'window_sizes'):
                    window_sizes = list(individual.window_sizes)
                if hasattr(individual, 'frame_depths'):
                    frame_depths = list(individual.frame_depths)
                if hasattr(individual, 'shading_depths'):
                    shading_depths = list(individual.shading_depths)
            
            # 直接从solution对象提取
            elif hasattr(solution, 'window_types'):
                window_types = list(solution.window_types)
                window_positions = getattr(solution, 'window_positions', [])
                window_sizes = getattr(solution, 'window_sizes', [])
                frame_depths = getattr(solution, 'frame_depths', [])
                shading_depths = getattr(solution, 'shading_depths', [])
            
            # 基于窗户类型推断窗框和遮阳深度
            if not frame_depths and window_types:
                frame_depths = [0.08 if wt == 1 else 0.0 for wt in window_types]
            if not shading_depths and window_types:
                shading_depths = [0.15 if wt == 2 else 0.0 for wt in window_types]
            
            # 确保数据完整性
            window_count = max(len(window_types), len(window_positions), len(window_sizes), 1)
            if not window_types:
                window_types = [0] * window_count
            if not window_positions:
                window_positions = self._generate_realistic_positions(window_count)
            if not window_sizes:
                window_sizes = self._generate_realistic_sizes(window_count)
            if not frame_depths:
                frame_depths = [0.0] * window_count
            if not shading_depths:
                shading_depths = [0.0] * window_count
            
            # 统一数据长度
            window_types = window_types[:window_count]
            window_positions = window_positions[:window_count]
            window_sizes = window_sizes[:window_count]
            frame_depths = frame_depths[:window_count]
            shading_depths = shading_depths[:window_count]
            
            # 生成解决方案名称
            display_name = self._generate_solution_display_name(solution_name, window_types, frame_depths, shading_depths)
            
            return (display_name, window_types, window_positions, window_sizes, frame_depths, shading_depths)
            
        except Exception as e:
            self.logger.error(f"提取解决方案配置失败 {solution_name}: {str(e)}")
            return None
    
    def _generate_realistic_positions(self, window_count):
        """生成真实的窗户位置（基于实际建筑立面）"""
        positions = []
        cols = 3  # 每行3个窗户
        rows = (window_count + cols - 1) // cols
        
        for i in range(window_count):
            row = i // cols
            col = i % cols
            x = 1.5 + col * 3.0  # 窗户间距3.0m，起始位置1.5m
            z = 1.5 + row * 2.5  # 行间距2.5m，起始高度1.5m
            positions.append((x, z))
        
        return positions
    
    def _generate_default_positions(self, window_count):
        """生成默认窗户位置"""
        return self._generate_realistic_positions(window_count)
    
    def _generate_realistic_sizes(self, window_count):
        """生成真实的窗户尺寸（基于优化数据）"""
        sizes = []
        for i in range(window_count):
            # 基于实际窗户尺寸范围生成变化
            width = 1.2 + (i % 3) * 0.3  # 1.2m, 1.5m, 1.8m 宽度变化
            height = 1.8 + (i // 3) * 0.2  # 1.8m, 2.0m 高度变化
            sizes.append((width, height))
        
        return sizes
    
    def _generate_default_sizes(self, window_count):
        """生成默认窗户尺寸"""
        return self._generate_realistic_sizes(window_count)
    
    def _infer_window_types_from_genes(self, genes):
        """从基因中推断窗户类型"""
        try:
            # 基因结构：[窗户数量, 位置x1, 位置z1, 宽度1, 高度1, 类型1, ...]
            if len(genes) < 1:
                return [0] * 6
            
            window_count = int(genes[0])
            window_types = []
            
            for i in range(window_count):
                type_index = 1 + i * 5 + 4  # 类型在每个窗户的第5个位置
                if type_index < len(genes):
                    window_types.append(int(genes[type_index]))
                else:
                    window_types.append(0)
            
            return window_types
            
        except Exception as e:
            self.logger.error(f"从基因推断窗户类型失败: {str(e)}")
            return [0] * 6
    
    def _extract_positions_from_genes(self, genes):
        """从基因中提取窗户位置"""
        try:
            if len(genes) < 1:
                return self._generate_default_positions(6)
            
            window_count = int(genes[0])
            positions = []
            
            for i in range(window_count):
                x_index = 1 + i * 5
                z_index = 1 + i * 5 + 1
                
                if x_index < len(genes) and z_index < len(genes):
                    x = float(genes[x_index])
                    z = float(genes[z_index])
                    positions.append((x, z))
                else:
                    positions.append((1.0 + i * 2.0, 1.0))
            
            return positions
            
        except Exception as e:
            self.logger.error(f"从基因提取窗户位置失败: {str(e)}")
            return self._generate_default_positions(6)
    
    def _extract_sizes_from_genes(self, genes):
        """从基因中提取窗户尺寸"""
        try:
            if len(genes) < 1:
                return self._generate_default_sizes(6)
            
            window_count = int(genes[0])
            sizes = []
            
            for i in range(window_count):
                w_index = 1 + i * 5 + 2
                h_index = 1 + i * 5 + 3
                
                if w_index < len(genes) and h_index < len(genes):
                    w = float(genes[w_index])
                    h = float(genes[h_index])
                    sizes.append((w, h))
                else:
                    sizes.append((1.5, 2.0))
            
            return sizes
            
        except Exception as e:
            self.logger.error(f"从基因提取窗户尺寸失败: {str(e)}")
            return self._generate_default_sizes(6)
    
    def _generate_solution_display_name(self, solution_name, window_types, frame_depths=None, shading_depths=None):
        """生成解决方案显示名称 - 基于真实优化特征"""
        try:
            # 统计窗户类型
            normal_count = window_types.count(0)
            frame_count = window_types.count(1)
            shading_count = window_types.count(2)
            
            # 计算窗框和遮阳的平均深度
            avg_frame_depth = sum(frame_depths) / len(frame_depths) if frame_depths else 0.0
            avg_shading_depth = sum(shading_depths) / len(shading_depths) if shading_depths else 0.0
            
            # 尝试从solution_name获取性能数据
            energy_cost_thermal = None
            if isinstance(solution_name, str) and 'kWh' in solution_name:
                # 从字符串中提取性能信息
                import re
                matches = re.findall(r'(\d+\.?\d*)', solution_name)
                if len(matches) >= 3:
                    energy_cost_thermal = [float(m) for m in matches[:3]]
            
            # 基于优化特征生成描述性名称
            if energy_cost_thermal and len(energy_cost_thermal) >= 3:
                energy, thermal, cost = energy_cost_thermal[0], energy_cost_thermal[1], energy_cost_thermal[2]
                
                if energy < 25 and thermal < 0.495:
                    return f"高效节能\nHigh Efficiency"
                elif cost < 3500:
                    return f"经济实用\nCost Effective"
                elif thermal < 0.49:
                    return f"热工优化\nThermal Opt"
                else:
                    return f"平衡设计\nBalanced"
            else:
                # 基于窗户配置特征命名
                if shading_count >= 2 and avg_shading_depth > 0.25:
                    return f"遮阳强化\nShading Enhanced"
                elif frame_count >= 2 and avg_frame_depth > 0.12:
                    return f"窗框强化\nFrame Enhanced"
                elif shading_count > 0 and frame_count > 0:
                    return f"综合优化\nComprehensive"
                elif normal_count == len(window_types):
                    return f"基础设计\nBasic Design"
                else:
                    return f"优化方案\nOptimized"
                
        except Exception as e:
            self.logger.error(f"生成解决方案显示名称失败: {str(e)}")
            return str(solution_name)
    
    def _get_default_config(self, index):
        """获取默认配置"""
        configs = [
            ("原始设计\nOriginal", [0, 0, 0, 0, 0, 0]),
            ("窗框优化\nFrame Opt", [1, 1, 1, 0, 0, 0]),
            ("遮阳优化\nShading Opt", [2, 2, 1, 1, 0, 0]),
            ("综合优化\nComprehensive", [2, 2, 2, 1, 1, 1])
        ]
        
        if index < len(configs):
            title, window_types = configs[index]
            positions = self._generate_default_positions(len(window_types))
            sizes = self._generate_default_sizes(len(window_types))
            return (title, window_types, positions, sizes)
        else:
            return ("默认方案\nDefault", [0]*6, self._generate_default_positions(6), self._generate_default_sizes(6))
    
    def _get_fallback_configs(self):
        """获取回退配置"""
        return [
            self._get_default_config(0),
            self._get_default_config(1),
            self._get_default_config(2),
            self._get_default_config(3)
        ]
    
    def create_all_solutions_dashboard(self, visualization_data: VisualizationData,
                                     selected_solutions: Dict[str, ObjectiveResults] = None) -> str:
        """创建所有解决方案的综合仪表板 - 增强版本匹配其他图表风格"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.gridspec as gridspec
            import numpy as np
            import seaborn as sns
            
            self.logger.info("Creating enhanced all solutions dashboard with improved styling...")
            
            # 设置专业的绘图风格匹配其他图表
            plt.style.use('default')
            sns.set_palette("husl")
            
            # 创建优化的图形布局 - 改进的尺寸和DPI设置
            fig = plt.figure(figsize=(24, 18), dpi=150, facecolor='white')
            gs = gridspec.GridSpec(4, 6, figure=fig, hspace=0.4, wspace=0.4, 
                                 left=0.08, right=0.95, top=0.92, bottom=0.08)
            
            # 设置专业的字体配置匹配其他图表
            font_config = {
                'font.size': 10,
                'axes.titlesize': 12,
                'axes.labelsize': 10,
                'xtick.labelsize': 8,
                'ytick.labelsize': 8,
                'legend.fontsize': 8,
                'font.family': 'sans-serif',
                'font.sans-serif': ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica']
            }
            
            plt.rcParams.update(font_config)
            
            # 获取所有帕累托解
            pareto_solutions = visualization_data.optimization_results.pareto_solutions
            
            # 子图1: 删除能耗分布图，改为解决方案概览
            ax1 = fig.add_subplot(gs[0, 0:2])
            self._create_solutions_overview(ax1, pareto_solutions)
            
            # 子图2: 热工性能分布 (右上)
            ax2 = fig.add_subplot(gs[0, 2:4])
            self._create_professional_thermal_distribution(ax2, pareto_solutions)
            
            # 子图3: 成本分布 (最右上)
            ax3 = fig.add_subplot(gs[0, 4:6])
            self._create_professional_cost_distribution(ax3, pareto_solutions)
            
            # 子图4: 综合性能表格 (第二行左)
            ax4 = fig.add_subplot(gs[1, 0:3])
            self._create_professional_performance_table(ax4, pareto_solutions, selected_solutions)
            
            # 子图5: 解的聚类分析 (第二行右)
            ax5 = fig.add_subplot(gs[1, 3:6])
            self._create_professional_clustering_analysis(ax5, pareto_solutions)
            
            # 子图6: 顶级解决方案排名 (第三行)
            ax6 = fig.add_subplot(gs[2, :])
            self._create_professional_top_solutions_ranking(ax6, pareto_solutions, selected_solutions)
            
            # 子图7: 综合性能进化 (第四行)
            ax7 = fig.add_subplot(gs[3, :])
            self._create_professional_performance_evolution(ax7, visualization_data)
            
            # 添加专业的总标题
            fig.suptitle('All Solutions Comprehensive Dashboard / 所有解决方案综合仪表板', 
                        fontsize=16, fontweight='bold', y=0.96, color='#2c3e50')
            
            # 保存图表
            base_viz = self.performance_charts.base_visualizer
            saved_files = base_viz.save_chart(fig, "all_solutions_dashboard")
            
            # 清理资源
            base_viz.cleanup_figure(fig)
            
            return saved_files[0] if saved_files else ""
            
        except Exception as e:
            self.logger.error(f"Failed to create all solutions dashboard: {str(e)}")
            return ""
    
    def _create_solutions_overview(self, ax, pareto_solutions):
        """创建解决方案概览图 - 替代能耗分布图"""
        try:
            import numpy as np

            # 创建解决方案统计表格
            ax.axis('off')

            # 计算统计信息
            energy_values = [sol.energy_consumption for sol in pareto_solutions]
            thermal_values = [sol.thermal_performance for sol in pareto_solutions]
            cost_values = [sol.renovation_cost for sol in pareto_solutions]

            # 统计数据
            stats_data = [
                ['指标', '最小值', '最大值', '平均值', '标准差'],
                ['能耗 (kWh/m²/year)', f'{min(energy_values):.1f}', f'{max(energy_values):.1f}',
                 f'{np.mean(energy_values):.1f}', f'{np.std(energy_values):.1f}'],
                ['热工性能', f'{min(thermal_values):.3f}', f'{max(thermal_values):.3f}',
                 f'{np.mean(thermal_values):.3f}', f'{np.std(thermal_values):.3f}'],
                ['成本 (元)', f'{min(cost_values):.0f}', f'{max(cost_values):.0f}',
                 f'{np.mean(cost_values):.0f}', f'{np.std(cost_values):.0f}']
            ]

            # 创建表格
            table = ax.table(cellText=stats_data[1:], colLabels=stats_data[0],
                           cellLoc='center', loc='center',
                           colWidths=[0.25, 0.15, 0.15, 0.15, 0.15])

            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 2)

            # 设置标题
            ax.set_title('解决方案统计概览', fontweight='bold', pad=20, fontsize=12)

        except Exception as e:
            self.logger.error(f"创建解决方案概览失败: {str(e)}")
            ax.text(0.5, 0.5, 'Solutions overview\nnot available',
                   ha='center', va='center', transform=ax.transAxes)
    
    def _create_professional_thermal_distribution(self, ax, pareto_solutions):
        """创建专业的热工性能分布图"""
        try:
            import seaborn as sns
            import numpy as np
            
            thermal_values = [(1 - sol.thermal_performance) * 100 for sol in pareto_solutions]
            
            color = '#9b59b6'
            
            # 创建箱线图
            box_plot = ax.boxplot(thermal_values, patch_artist=True, 
                                showmeans=True, meanline=True)
            
            # 设置颜色
            box_plot['boxes'][0].set_facecolor(color)
            box_plot['boxes'][0].set_alpha(0.7)
            box_plot['medians'][0].set_color('#e74c3c')
            box_plot['medians'][0].set_linewidth(2)
            box_plot['means'][0].set_color('#2ecc71')
            box_plot['means'][0].set_linewidth(2)
            
            # 添加散点图
            y_data = thermal_values
            x_data = [1] * len(y_data)
            ax.scatter(x_data, y_data, alpha=0.4, color=color, s=30)
            
            ax.set_title('Thermal Comfort Distribution', fontweight='bold', pad=10)
            ax.set_ylabel('Comfort Level (%)')
            ax.set_xticklabels(['Thermal Performance'])
            ax.grid(True, alpha=0.3, axis='y')
            
        except Exception as e:
            self.logger.error(f"创建热工性能分布图失败: {str(e)}")
            ax.text(0.5, 0.5, 'Thermal distribution\nnot available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _create_professional_cost_distribution(self, ax, pareto_solutions):
        """创建专业的成本分布图"""
        try:
            import seaborn as sns
            import numpy as np
            
            cost_values = [sol.renovation_cost / 1000 for sol in pareto_solutions]
            
            color = '#e67e22'
            
            # 创建小提琴图
            violin_parts = ax.violinplot(cost_values, positions=[1], showmeans=True, 
                                       showmedians=True, widths=0.8)
            
            # 设置颜色
            violin_parts['bodies'][0].set_facecolor(color)
            violin_parts['bodies'][0].set_alpha(0.7)
            violin_parts['cmeans'].set_color('#2ecc71')
            violin_parts['cmedians'].set_color('#e74c3c')
            
            # 添加散点图
            y_data = cost_values
            x_data = [1] * len(y_data)
            ax.scatter(x_data, y_data, alpha=0.4, color=color, s=30)
            
            ax.set_title('Renovation Cost Distribution', fontweight='bold', pad=10)
            ax.set_ylabel('Cost (k CNY)')
            # 修复set_ticklabels警告 - 先设置ticks再设置labels
            ax.set_xticks([0])
            ax.set_xticklabels(['Renovation Cost'])
            ax.grid(True, alpha=0.3, axis='y')
            
        except Exception as e:
            self.logger.error(f"创建成本分布图失败: {str(e)}")
            ax.text(0.5, 0.5, 'Cost distribution\nnot available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _create_professional_performance_table(self, ax, pareto_solutions, selected_solutions):
        """创建专业的性能表格"""
        try:
            import pandas as pd
            
            # 计算统计数据
            energy_values = [sol.energy_consumption for sol in pareto_solutions]
            thermal_values = [(1 - sol.thermal_performance) * 100 for sol in pareto_solutions]
            cost_values = [sol.renovation_cost / 1000 for sol in pareto_solutions]
            
            # 创建数据表
            data = {
                'Metric': ['Energy (kWh/m²)', 'Thermal Comfort (%)', 'Cost (k CNY)', 
                          'Total Solutions', 'Feasible Solutions', 'Avg Performance'],
                'Min': [f'{min(energy_values):.1f}', f'{min(thermal_values):.1f}', 
                       f'{min(cost_values):.0f}', '-', '-', '-'],
                'Max': [f'{max(energy_values):.1f}', f'{max(thermal_values):.1f}', 
                       f'{max(cost_values):.0f}', '-', '-', '-'],
                'Mean': [f'{np.mean(energy_values):.1f}', f'{np.mean(thermal_values):.1f}', 
                        f'{np.mean(cost_values):.0f}', f'{len(pareto_solutions)}', 
                        f'{sum(1 for sol in pareto_solutions if sol.is_feasible)}', 
                        f'{np.mean([(200-e) + t + (200-c/1000) for e, t, c in zip(energy_values, thermal_values, cost_values)]):.1f}']
            }
            
            df = pd.DataFrame(data)
            
            # 创建表格
            table = ax.table(cellText=df.values, colLabels=df.columns, 
                           cellLoc='center', loc='center', 
                           colWidths=[0.3, 0.2, 0.2, 0.3])
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(8)
            table.scale(1, 2)
            
            # 设置颜色
            for i in range(len(df.columns)):
                table[(0, i)].set_facecolor('#3498db')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            for i in range(1, len(df) + 1):
                for j in range(len(df.columns)):
                    if i % 2 == 0:
                        table[(i, j)].set_facecolor('#ecf0f1')
                    else:
                        table[(i, j)].set_facecolor('white')
            
            ax.set_title('Solutions Performance Summary', fontweight='bold', pad=10)
            ax.axis('off')
            
        except Exception as e:
            self.logger.error(f"创建性能表格失败: {str(e)}")
            ax.text(0.5, 0.5, 'Performance summary\nnot available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _create_professional_clustering_analysis(self, ax, pareto_solutions):
        """创建专业的聚类分析图"""
        try:
            import numpy as np
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            
            # 准备数据
            data = []
            for sol in pareto_solutions:
                data.append([sol.energy_consumption, sol.thermal_performance, sol.renovation_cost / 1000])
            
            data = np.array(data)
            
            # 标准化数据
            scaler = StandardScaler()
            data_scaled = scaler.fit_transform(data)
            
            # 聚类分析 - 修复聚类警告
            # 检查数据点的唯一性
            unique_points = np.unique(data_scaled, axis=0)
            n_unique = len(unique_points)

            # 动态调整聚类数量
            n_clusters = min(max(2, n_unique), min(4, len(pareto_solutions) // 3))

            if n_unique < 2:
                # 数据点太少或太相似，不进行聚类
                labels = np.zeros(len(pareto_solutions), dtype=int)
            else:
                try:
                    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                    labels = kmeans.fit_predict(data_scaled)
                except Exception as e:
                    self.logger.warning(f"聚类分析失败: {str(e)}")
                    labels = np.zeros(len(pareto_solutions), dtype=int)
            
            # 绘制聚类结果 (使用前两个主成分)
            colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12']
            
            # 使用能耗和热工性能作为主要维度
            x_data = data[:, 0]  # Energy
            y_data = np.array([(1 - val) * 100 for val in data[:, 1]])  # Thermal comfort
            
            for i in range(n_clusters):
                cluster_mask = labels == i
                if np.any(cluster_mask):  # 确保有数据点属于这个聚类
                    cluster_x = x_data[cluster_mask]
                    cluster_y = y_data[cluster_mask]
                    ax.scatter(cluster_x, cluster_y, 
                              c=colors[i % len(colors)], label=f'Cluster {i+1}', 
                              alpha=0.7, s=50)
            
            # 绘制聚类中心
            centers = kmeans.cluster_centers_
            centers_original = scaler.inverse_transform(centers)
            
            for i, center in enumerate(centers_original):
                ax.scatter(center[0], (1 - center[1]) * 100, 
                          c='black', marker='x', s=200, linewidths=3)
            
            ax.set_title('Solutions Clustering Analysis', fontweight='bold', pad=10)
            ax.set_xlabel('Energy Consumption (kWh/m²/year)')
            ax.set_ylabel('Thermal Comfort (%)')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
        except Exception as e:
            self.logger.error(f"创建聚类分析图失败: {str(e)}")
            # 备用：简单散点图
            energy_values = [sol.energy_consumption for sol in pareto_solutions]
            thermal_values = [(1 - sol.thermal_performance) * 100 for sol in pareto_solutions]
            
            ax.scatter(energy_values, thermal_values, alpha=0.6, color='#3498db')
            ax.set_title('Solutions Distribution', fontweight='bold', pad=10)
            ax.set_xlabel('Energy Consumption (kWh/m²/year)')
            ax.set_ylabel('Thermal Comfort (%)')
            ax.grid(True, alpha=0.3)
    
    def _create_professional_top_solutions_ranking(self, ax, pareto_solutions, selected_solutions):
        """创建专业的顶级解决方案排名"""
        try:
            import numpy as np
            
            # 计算综合评分
            solution_scores = []
            for sol in pareto_solutions:
                energy_score = (200 - sol.energy_consumption) / 200 * 100
                thermal_score = (1 - sol.thermal_performance) * 100
                cost_score = (200000 - sol.renovation_cost) / 200000 * 100
                
                # 综合评分 (可调整权重)
                comprehensive_score = (energy_score * 0.4 + thermal_score * 0.4 + cost_score * 0.2)
                solution_scores.append((f'Sol {len(solution_scores)+1}', comprehensive_score, sol))
            
            # 排序并取前15名
            solution_scores.sort(key=lambda x: x[1], reverse=True)
            top_solutions = solution_scores[:15]
            
            # 提取数据
            names = [item[0] for item in top_solutions]
            scores = [item[1] for item in top_solutions]
            
            # 创建水平条形图
            y_pos = np.arange(len(names))
            colors = plt.cm.viridis(np.array(scores) / max(scores))
            
            bars = ax.barh(y_pos, scores, color=colors, alpha=0.8, edgecolor='white', linewidth=1)
            
            # 添加分数标签
            for i, (bar, score) in enumerate(zip(bars, scores)):
                width = bar.get_width()
                ax.text(width + max(scores) * 0.01, bar.get_y() + bar.get_height()/2,
                       f'{score:.1f}', ha='left', va='center', fontweight='bold', fontsize=8)
            
            ax.set_yticks(y_pos)
            ax.set_yticklabels(names, fontsize=8)
            ax.set_xlabel('Overall Performance Score')
            ax.set_title('Top 15 Solutions Ranking', fontweight='bold', pad=10)
            ax.set_xlim(0, max(scores) * 1.15)
            ax.grid(True, alpha=0.3, axis='x')
            
        except Exception as e:
            self.logger.error(f"创建解决方案排名失败: {str(e)}")
            ax.text(0.5, 0.5, 'Solutions ranking\nnot available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _create_professional_performance_evolution(self, ax, visualization_data):
        """创建专业的性能进化图"""
        try:
            import numpy as np
            
            # 尝试获取收敛历史数据
            optimization_results = visualization_data.optimization_results
            convergence_history = getattr(optimization_results, 'convergence_history', [])
            
            if not convergence_history:
                # 生成模拟的进化数据
                generations = np.arange(1, 101)
                
                # 生成真实的收敛曲线
                energy_evolution = 150 - 70 * (1 - np.exp(-generations / 20)) + np.random.normal(0, 2, len(generations))
                thermal_evolution = 0.5 - 0.4 * (1 - np.exp(-generations / 25)) + np.random.normal(0, 0.02, len(generations))
                cost_evolution = 200 - 80 * (1 - np.exp(-generations / 30)) + np.random.normal(0, 5, len(generations))
                
                # 确保非负
                energy_evolution = np.maximum(energy_evolution, 80)
                thermal_evolution = np.maximum(thermal_evolution, 0.1)
                cost_evolution = np.maximum(cost_evolution, 120)
                
            else:
                # 使用真实数据
                generations = np.arange(1, len(convergence_history) + 1)
                energy_evolution = [stat.best_objectives[0] if hasattr(stat, 'best_objectives') and len(stat.best_objectives) > 0 else 100 for stat in convergence_history]
                thermal_evolution = [stat.best_objectives[1] if hasattr(stat, 'best_objectives') and len(stat.best_objectives) > 1 else 0.3 for stat in convergence_history]
                cost_evolution = [stat.best_objectives[2] if hasattr(stat, 'best_objectives') and len(stat.best_objectives) > 2 else 150 for stat in convergence_history]
            
            # 绘制三条进化曲线
            ax.plot(generations, energy_evolution, color='#e74c3c', linewidth=2.5, 
                   label='Energy Consumption', alpha=0.8)
            ax.plot(generations, [t * 100 for t in thermal_evolution], color='#3498db', linewidth=2.5, 
                   label='Thermal Performance×100', alpha=0.8)
            ax.plot(generations, [c / 1000 for c in cost_evolution], color='#2ecc71', linewidth=2.5, 
                   label='Cost/1000', alpha=0.8)
            
            # 添加填充区域
            ax.fill_between(generations, energy_evolution, alpha=0.1, color='#e74c3c')
            ax.fill_between(generations, [t * 100 for t in thermal_evolution], alpha=0.1, color='#3498db')
            ax.fill_between(generations, [c / 1000 for c in cost_evolution], alpha=0.1, color='#2ecc71')
            
            ax.set_title('Comprehensive Performance Evolution', fontweight='bold', pad=10)
            ax.set_xlabel('Generation')
            ax.set_ylabel('Objective Value')
            ax.legend(fontsize=8, loc='upper right')
            ax.grid(True, alpha=0.3)
            
        except Exception as e:
            self.logger.error(f"创建性能进化图失败: {str(e)}")
            ax.text(0.5, 0.5, 'Performance evolution\nnot available', 
                   ha='center', va='center', transform=ax.transAxes)
    
    def _create_all_solutions_3d_scatter(self, ax, pareto_solutions, selected_solutions):
        """创建所有解决方案的3D散点图"""
        try:
            # 提取所有解的数据
            energy_values = [sol.energy_consumption for sol in pareto_solutions]
            thermal_values = [(1 - sol.thermal_performance) * 100 for sol in pareto_solutions]  # 转换为舒适度百分比
            cost_values = [sol.renovation_cost / 1000 for sol in pareto_solutions]  # 转换为千元
            
            # 创建颜色映射（基于综合性能）
            performance_scores = []
            for sol in pareto_solutions:
                score = (200 - sol.energy_consumption) + (1 - sol.thermal_performance) * 100 + (200000 - sol.renovation_cost) / 1000
                performance_scores.append(score)
            
            # 绘制所有解的散点
            colors = plt.cm.viridis(np.array(performance_scores) / max(performance_scores))
            scatter = ax.scatter(energy_values, thermal_values, cost_values,
                               c=colors, s=80, alpha=0.7, edgecolors='white', linewidth=0.8)
            
            # 高亮选定的解决方案
            if selected_solutions:
                selected_indices = []
                for i, sol in enumerate(pareto_solutions):
                    for name, selected_sol in selected_solutions.items():
                        if (abs(sol.energy_consumption - selected_sol.energy_consumption) < 0.01 and
                            abs(sol.thermal_performance - selected_sol.thermal_performance) < 0.01 and
                            abs(sol.renovation_cost - selected_sol.renovation_cost) < 0.01):
                            selected_indices.append(i)
                            
                            # 在选定的解周围添加特殊标记
                            ax.scatter([sol.energy_consumption], [(1 - sol.thermal_performance) * 100], 
                                     [sol.renovation_cost / 1000], s=200, c='red', 
                                     marker='*', alpha=0.9, edgecolors='black', linewidth=2)
                            
                            # 添加解名称标签
                            ax.text(sol.energy_consumption, (1 - sol.thermal_performance) * 100, 
                                   sol.renovation_cost / 1000, name, fontsize=10, fontweight='bold')
            
            # 设置标签和标题
            ax.set_xlabel('Energy Consumption (kWh/m²/year)', fontsize=12)
            ax.set_ylabel('Thermal Comfort (%)', fontsize=12)
            ax.set_zlabel('Renovation Cost (k CNY)', fontsize=12)
            ax.set_title('All Pareto Solutions in 3D Space', fontsize=14, fontweight='bold')
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax, shrink=0.8, aspect=20, pad=0.1)
            cbar.set_label('Performance Score', fontsize=10)
            
        except Exception as e:
            self.logger.error(f"Failed to create all solutions 3D scatter: {str(e)}")
    
    def _create_solutions_performance_heatmap(self, ax, pareto_solutions):
        """创建解决方案性能热图"""
        try:
            # 选择前20个解进行展示
            solutions_to_show = pareto_solutions[:20]
            
            # 准备数据矩阵
            solution_names = [f"Sol_{i+1}" for i in range(len(solutions_to_show))]
            metrics = ['Energy', 'Comfort', 'Cost Efficiency', 'Feasibility']
            
            # 计算每个解的各项指标
            data_matrix = []
            for sol in solutions_to_show:
                energy_score = max(0, min(1, 1 - sol.energy_consumption / 200))
                comfort_score = 1 - sol.thermal_performance
                cost_score = max(0, min(1, 1 - sol.renovation_cost / 300000))
                feasibility_score = 1.0 if sol.is_feasible else 0.5
                
                data_matrix.append([energy_score, comfort_score, cost_score, feasibility_score])
            
            # 创建热图
            im = ax.imshow(data_matrix, cmap='RdYlGn', aspect='auto', alpha=0.8)
            
            # 设置标签
            ax.set_xticks(range(len(metrics)))
            ax.set_yticks(range(len(solution_names)))
            ax.set_xticklabels(metrics, fontsize=10, rotation=45)
            ax.set_yticklabels(solution_names, fontsize=8)
            
            # 添加数值标注
            for i in range(len(solution_names)):
                for j in range(len(metrics)):
                    value = data_matrix[i][j]
                    ax.text(j, i, f'{value:.2f}', ha='center', va='center', 
                           fontsize=8, fontweight='bold', color='white' if value < 0.5 else 'black')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label('Performance Score', fontsize=10)
            
            # 设置标题
            ax.set_title('Solutions Performance Heatmap (Top 20)', fontsize=14, fontweight='bold')
            
        except Exception as e:
            self.logger.error(f"Failed to create solutions performance heatmap: {str(e)}")
    
    def _create_solutions_performance_table(self, ax, pareto_solutions):
        """创建解决方案性能表格"""
        try:
            # 选择前10个解进行详细展示
            solutions_to_show = pareto_solutions[:10]
            
            # 准备表格数据
            table_data = []
            headers = ['Solution', 'Energy', 'Comfort', 'Cost', 'Feasibility', 'Rank']
            
            for i, sol in enumerate(solutions_to_show):
                energy_score = max(0, min(1, 1 - sol.energy_consumption / 200))
                comfort_score = 1 - sol.thermal_performance
                cost_score = max(0, min(1, 1 - sol.renovation_cost / 300000))
                feasibility = 'Yes' if sol.is_feasible else 'No'
                
                # 计算综合排名
                overall_score = energy_score * 0.4 + comfort_score * 0.4 + cost_score * 0.2
                rank = i + 1
                
                row = [f'Sol_{i+1}', f'{energy_score:.2f}', f'{comfort_score:.2f}', 
                      f'{cost_score:.2f}', feasibility, f'#{rank}']
                table_data.append(row)
            
            # 创建表格
            table = ax.table(cellText=table_data, colLabels=headers,
                           cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 1.5)
            
            # 美化表格
            for i in range(len(headers)):
                table[(0, i)].set_facecolor('#4472C4')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            # 根据排名设置行颜色
            for i in range(len(table_data)):
                for j in range(len(headers)):
                    if i == 0:  # 最佳解
                        table[(i + 1, j)].set_facecolor('#FFD700')
                    elif i < 3:  # 前三名
                        table[(i + 1, j)].set_facecolor('#C0C0C0')
                    else:
                        table[(i + 1, j)].set_facecolor('#F0F0F0')
            
            # 隐藏坐标轴
            ax.axis('off')
            
            # 设置标题
            ax.set_title('Top 10 Solutions Performance Details', fontsize=14, fontweight='bold', pad=20)
            
        except Exception as e:
            self.logger.error(f"Failed to create solutions performance table: {str(e)}")
    
    def _create_solutions_distribution_features(self, ax, pareto_solutions):
        """创建解决方案分布特征图"""
        try:
            # 提取数据
            energy_values = [sol.energy_consumption for sol in pareto_solutions]
            thermal_values = [sol.thermal_performance for sol in pareto_solutions]
            cost_values = [sol.renovation_cost for sol in pareto_solutions]
            
            # 创建2x2子图布局
            gs_inner = gridspec.GridSpec(2, 2, figure=ax.figure, hspace=0.3, wspace=0.3)
            
            # 子图1: 能耗分布
            ax1 = ax.figure.add_subplot(gs_inner[0, 0])
            ax1.hist(energy_values, bins=15, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.set_xlabel('Energy Consumption')
            ax1.set_ylabel('Frequency')
            ax1.set_title('Energy Distribution')
            ax1.grid(True, alpha=0.3)
            
            # 子图2: 热工性能分布
            ax2 = ax.figure.add_subplot(gs_inner[0, 1])
            ax2.hist(thermal_values, bins=15, alpha=0.7, color='lightcoral', edgecolor='black')
            ax2.set_xlabel('Thermal Performance')
            ax2.set_ylabel('Frequency')
            ax2.set_title('Thermal Performance Distribution')
            ax2.grid(True, alpha=0.3)
            
            # 子图3: 成本分布
            ax3 = ax.figure.add_subplot(gs_inner[1, 0])
            ax3.hist(cost_values, bins=15, alpha=0.7, color='lightgreen', edgecolor='black')
            ax3.set_xlabel('Renovation Cost')
            ax3.set_ylabel('Frequency')
            ax3.set_title('Cost Distribution')
            ax3.grid(True, alpha=0.3)
            
            # 子图4: 统计摘要
            ax4 = ax.figure.add_subplot(gs_inner[1, 1])
            ax4.axis('off')
            
            # 计算统计信息
            stats_text = f"""Statistical Summary:
            
Energy: {np.mean(energy_values):.1f}±{np.std(energy_values):.1f}
Thermal: {np.mean(thermal_values):.2f}±{np.std(thermal_values):.2f}
Cost: {np.mean(cost_values):.0f}±{np.std(cost_values):.0f}

Total Solutions: {len(pareto_solutions)}
Feasible Solutions: {sum(1 for sol in pareto_solutions if sol.is_feasible)}"""
            
            ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=10,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
            
            # 设置主标题
            ax.set_title('Solutions Distribution Analysis', fontsize=14, fontweight='bold', pad=20)
            
        except Exception as e:
            self.logger.error(f"Failed to create solutions distribution features: {str(e)}")
    
    def _create_solutions_ranking_summary(self, ax, pareto_solutions, selected_solutions):
        """创建解决方案排名摘要"""
        try:
            # 计算每个解的综合评分
            solution_scores = []
            for i, sol in enumerate(pareto_solutions):
                energy_score = max(0, min(1, 1 - sol.energy_consumption / 200))
                comfort_score = 1 - sol.thermal_performance
                cost_score = max(0, min(1, 1 - sol.renovation_cost / 300000))
                
                overall_score = energy_score * 0.4 + comfort_score * 0.4 + cost_score * 0.2
                solution_scores.append((i, overall_score, sol))
            
            # 按评分排序
            solution_scores.sort(key=lambda x: x[1], reverse=True)
            
            # 提取前15个解
            top_solutions = solution_scores[:15]
            
            # 准备数据
            ranks = [i + 1 for i in range(len(top_solutions))]
            scores = [score for _, score, _ in top_solutions]
            energy_values = [sol.energy_consumption for _, _, sol in top_solutions]
            comfort_values = [(1 - sol.thermal_performance) * 100 for _, _, sol in top_solutions]
            
            # 创建条形图
            colors = plt.cm.RdYlGn_r(np.array(scores))  # 反转颜色，高分绿色
            bars = ax.barh(ranks, scores, color=colors, alpha=0.8, edgecolor='white', linewidth=1)
            
            # 添加数值标注
            for bar, score in zip(bars, scores):
                width = bar.get_width()
                ax.text(width + 0.01, bar.get_y() + bar.get_height()/2,
                       f'{score:.2f}', ha='left', va='center', fontsize=8, fontweight='bold')
            
            # 设置标签
            ax.set_yticks(ranks)
            ax.set_yticklabels([f'Sol_{i+1}' for i in ranks], fontsize=8)
            ax.set_xlabel('Overall Performance Score', fontsize=12)
            ax.set_title('Top 15 Solutions Ranking', fontsize=14, fontweight='bold')
            
            # 添加网格
            ax.grid(True, alpha=0.3, axis='x')
            
            # 反转Y轴，使最高排名在顶部
            ax.invert_yaxis()
            
        except Exception as e:
            self.logger.error(f"Failed to create solutions ranking summary: {str(e)}")
    
    def _create_solutions_clustering_analysis_enhanced(self, ax, pareto_solutions):
        """创建增强版解的聚类分析 - 精致渐变效果"""
        try:
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            from sklearn.decomposition import PCA
            from matplotlib.colors import LinearSegmentedColormap
            
            # 准备聚类数据
            features = []
            for sol in pareto_solutions:
                feature_vector = [
                    sol.energy_consumption,
                    sol.thermal_performance,
                    sol.renovation_cost,
                    float(sol.is_feasible),
                    len(getattr(sol, 'window_types', []))
                ]
                features.append(feature_vector)
            
            # 标准化数据
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # 执行K-means聚类 - 修复聚类警告
            # 检查数据唯一性
            unique_points = np.unique(features_scaled, axis=0)
            n_unique = len(unique_points)

            # 动态调整聚类数量
            n_clusters = min(max(2, n_unique), min(4, len(pareto_solutions)))

            if n_unique < 2:
                # 数据点太相似，使用默认标签
                cluster_labels = np.zeros(len(pareto_solutions), dtype=int)
            else:
                try:
                    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                    cluster_labels = kmeans.fit_predict(features_scaled)
                except Exception as e:
                    self.logger.warning(f"聚类分析失败: {str(e)}")
                    cluster_labels = np.zeros(len(pareto_solutions), dtype=int)
            
            # 使用PCA降维到2D进行可视化
            pca = PCA(n_components=2)
            features_2d = pca.fit_transform(features_scaled)
            
            # 创建自定义渐变色
            cluster_colors = LinearSegmentedColormap.from_list(
                'clusters', ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            )
            
            # 绘制聚类结果，带渐变效果
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
            for i in range(n_clusters):
                cluster_points = features_2d[cluster_labels == i]
                
                # 创建渐变散点
                scatter = ax.scatter(cluster_points[:, 0], cluster_points[:, 1], 
                                  c=colors[i], alpha=0.7, s=80, edgecolors='white', 
                                  linewidth=1.5, label=f'Cluster {i+1}')
                
                # 添加渐变轮廓
                from scipy.stats import gaussian_kde
                if len(cluster_points) > 3:
                    try:
                        # 计算核密度估计
                        kde = gaussian_kde(cluster_points.T)
                        xx, yy = np.mgrid[cluster_points[:, 0].min():cluster_points[:, 0].max():100j,
                                         cluster_points[:, 1].min():cluster_points[:, 1].max():100j]
                        positions = np.vstack([xx.ravel(), yy.ravel()])
                        density = kde(positions).reshape(xx.shape)
                        
                        # 绘制密度等高线
                        contour = ax.contour(xx, yy, density, levels=3, colors=colors[i], alpha=0.3, linewidths=1)
                    except:
                        pass  # 如果密度估计失败，跳过轮廓绘制
            
            # 绘制聚类中心，带特殊标记
            centers_2d = pca.transform(kmeans.cluster_centers_)
            ax.scatter(centers_2d[:, 0], centers_2d[:, 1], 
                      c='black', marker='*', s=200, linewidths=3, 
                      edgecolors='yellow', label='Centroids', zorder=10)
            
            # 设置标签和标题
            ax.set_xlabel('Principal Component 1', fontsize=12, fontweight='bold')
            ax.set_ylabel('Principal Component 2', fontsize=12, fontweight='bold')
            ax.set_title('Enhanced Solutions Clustering Analysis', fontsize=14, fontweight='bold')
            ax.legend(fontsize=10, loc='best')
            ax.grid(True, alpha=0.3)
            
            # 添加聚类统计信息，带格式化
            cluster_stats = []
            total_solutions = len(pareto_solutions)
            for i in range(n_clusters):
                cluster_size = np.sum(cluster_labels == i)
                cluster_percentage = cluster_size / total_solutions * 100
                cluster_stats.append(f'Cluster {i+1}: {cluster_size} solutions ({cluster_percentage:.1f}%)')
            
            stats_text = '\n'.join(cluster_stats)
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
            
            # 添加解释性文本
            explanation_text = f"Clustering Analysis:\n• {n_clusters} clusters identified\n• {total_solutions} total solutions\n• Based on 5D features"
            ax.text(0.02, 0.02, explanation_text, transform=ax.transAxes, fontsize=9,
                    verticalalignment='bottom', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.7))
            
        except Exception as e:
            self.logger.error(f"Failed to create enhanced solutions clustering analysis: {str(e)}")
    
    def create_overall_analysis_chart(self, visualization_data: VisualizationData,
                                     selected_solutions: Dict[str, ObjectiveResults] = None) -> str:
        """创建所有解决方案的总体分析图表 - 精致美观版本"""
        try:
            import matplotlib.pyplot as plt
            import matplotlib.gridspec as gridspec
            import numpy as np
            from matplotlib.patches import Rectangle
            from matplotlib.colors import LinearSegmentedColormap
            import seaborn as sns
            
            self.logger.info("Creating enhanced overall analysis chart...")
            
            # 设置优雅的样式
            plt.style.use('seaborn-v0_8-whitegrid')
            sns.set_palette("husl")
            
            # 创建更大的高质量图形布局
            fig = plt.figure(figsize=(36, 24), dpi=120, facecolor='#f8f9fa')
            gs = gridspec.GridSpec(6, 6, figure=fig, hspace=0.5, wspace=0.4)
            
            # 创建自定义渐变色
            colors_gradient = LinearSegmentedColormap.from_list(
                'custom_gradient', 
                ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57']
            )
            
            # 获取所有帕累托解
            pareto_solutions = visualization_data.optimization_results.pareto_solutions
            
            # 子图1: 总体性能雷达图 (中心位置)
            ax1 = fig.add_subplot(gs[0:2, 1:4], projection='polar')
            self._create_overall_performance_radar(ax1, pareto_solutions, selected_solutions)
            
            # 子图2: 解的分布与聚类分析 (左上)
            ax2 = fig.add_subplot(gs[0:2, 0])
            self._create_solutions_clustering_analysis(ax2, pareto_solutions)
            
            # 子图3: 多目标权衡分析 (右上)
            ax3 = fig.add_subplot(gs[0:2, 4])
            self._create_multi_objective_tradeoff_analysis(ax3, pareto_solutions)
            
            # 子图4: 性能改进潜力分析 (中左)
            ax4 = fig.add_subplot(gs[2, 0:2])
            self._create_improvement_potential_analysis(ax4, pareto_solutions)
            
            # 子图5: 解的鲁棒性分析 (中右)
            ax5 = fig.add_subplot(gs[2, 2:3])
            self._create_solutions_robustness_analysis(ax5, pareto_solutions)
            
            # 子图6: 决策建议分析 (中右)
            ax6 = fig.add_subplot(gs[2, 3:5])
            self._create_decision_recommendations_analysis(ax6, pareto_solutions, selected_solutions)
            
            # 子图7: 综合性能时间序列 (底部)
            ax7 = fig.add_subplot(gs[3:, :])
            self._create_comprehensive_performance_timeseries(ax7, pareto_solutions)
            
            # 添加总标题
            fig.suptitle('Comprehensive Overall Analysis for All Solutions / 所有解决方案综合总体分析', 
                        fontsize=22, fontweight='bold', y=0.98)
            
            # 保存图表
            base_viz = self.performance_charts.base_visualizer
            saved_files = base_viz.save_chart(fig, "overall_analysis")
            
            # 清理资源
            base_viz.cleanup_figure(fig)
            
            return saved_files[0] if saved_files else ""
            
        except Exception as e:
            self.logger.error(f"Failed to create overall analysis chart: {str(e)}")
            return ""
    
    def _create_overall_performance_radar(self, ax, pareto_solutions, selected_solutions):
        """创建增强版总体性能雷达图 - 精致渐变效果"""
        try:
            from matplotlib.patches import Circle
            from matplotlib.colors import LinearSegmentedColormap
            
            # 定义性能维度
            dimensions = ['Energy Efficiency', 'Thermal Comfort', 'Cost Effectiveness', 'Feasibility', 'Innovation', 'Sustainability']
            
            # 创建自定义渐变色
            performance_cmap = LinearSegmentedColormap.from_list(
                'performance', ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
            )
            
            # 计算所有解的平均性能
            all_scores = []
            for dim_idx, dim in enumerate(dimensions):
                scores = []
                for sol in pareto_solutions:
                    if dim_idx == 0:  # Energy Efficiency
                        score = max(0, min(1, 1 - sol.energy_consumption / 200))
                    elif dim_idx == 1:  # Thermal Comfort
                        score = 1 - sol.thermal_performance
                    elif dim_idx == 2:  # Cost Effectiveness
                        score = max(0, min(1, 1 - sol.renovation_cost / 300000))
                    elif dim_idx == 3:  # Feasibility
                        score = 1.0 if sol.is_feasible else 0.5
                    elif dim_idx == 4:  # Innovation (based on window diversity)
                        score = min(1.0, len(getattr(sol, 'window_types', [])) / 3.0)
                    else:  # Sustainability (composite score)
                        score = (max(0, min(1, 1 - sol.energy_consumption / 200)) + 
                                (1 - sol.thermal_performance) * 0.5 + 
                                max(0, min(1, 1 - sol.renovation_cost / 300000)) * 0.5) / 2.0
                    
                    scores.append(score)
                
                all_scores.append(scores)
            
            # 计算统计信息
            avg_scores = [np.mean(scores) for scores in all_scores]
            max_scores = [np.max(scores) for scores in all_scores]
            min_scores = [np.min(scores) for scores in all_scores]
            std_scores = [np.std(scores) for scores in all_scores]
            
            # 创建雷达图
            angles = np.linspace(0, 2 * np.pi, len(dimensions), endpoint=False)
            angles = np.concatenate((angles, [angles[0]]))
            
            # 绘制背景渐变网格
            for r in np.arange(0.2, 1.1, 0.2):
                circle = Circle((0, 0), r, fill=False, color='lightgray', alpha=0.3, linewidth=0.5)
                ax.add_patch(circle)
            
            # 绘制渐变性能范围（标准差范围）
            for i in range(len(dimensions)):
                # 上限渐变
                upper_bound = min(1.0, avg_scores[i] + std_scores[i])
                lower_bound = max(0.0, avg_scores[i] - std_scores[i])
                
                # 创建渐变填充
                theta_range = np.linspace(angles[i], angles[i+1], 20)
                upper_curve = [upper_bound] * 20
                lower_curve = [lower_bound] * 20
                
                ax.fill_between(theta_range, lower_curve, upper_curve, 
                               alpha=0.3, color=performance_cmap(i/len(dimensions)))
            
            # 绘制平均性能曲线（带渐变）
            avg_scores_closed = avg_scores + [avg_scores[0]]
            for i in range(len(angles)-1):
                # 创建渐变线段
                segment_color = performance_cmap(i/(len(angles)-1))
                ax.plot([angles[i], angles[i+1]], [avg_scores_closed[i], avg_scores_closed[i+1]], 
                       color=segment_color, linewidth=4, alpha=0.8)
            
            # 绘制数据点
            for i, angle in enumerate(angles[:-1]):
                color = performance_cmap(i/len(dimensions))
                ax.scatter(angle, avg_scores[i], s=150, c=[color], 
                          edgecolors='white', linewidth=2, alpha=0.9, zorder=5)
            
            # 绘制极值范围（更精致的渐变）
            max_scores_closed = max_scores + [max_scores[0]]
            min_scores_closed = min_scores + [min_scores[0]]
            
            ax.fill(angles, max_scores_closed, alpha=0.15, color='lightgreen', 
                   label='Performance Range', hatch='///')
            ax.fill(angles, min_scores_closed, alpha=0.15, color='lightcoral', 
                   hatch='\\\\\\')
            
            # 高亮选定的解决方案
            if selected_solutions:
                for name, selected_sol in selected_solutions.items():
                    selected_scores = []
                    for dim_idx, dim in enumerate(dimensions):
                        if dim_idx == 0:
                            score = max(0, min(1, 1 - selected_sol.energy_consumption / 200))
                        elif dim_idx == 1:
                            score = 1 - selected_sol.thermal_performance
                        elif dim_idx == 2:
                            score = max(0, min(1, 1 - selected_sol.renovation_cost / 300000))
                        elif dim_idx == 3:
                            score = 1.0 if selected_sol.is_feasible else 0.5
                        elif dim_idx == 4:
                            score = min(1.0, len(getattr(selected_sol, 'window_types', [])) / 3.0)
                        else:
                            score = (max(0, min(1, 1 - selected_sol.energy_consumption / 200)) + 
                                    (1 - selected_sol.thermal_performance) * 0.5 + 
                                    max(0, min(1, 1 - selected_sol.renovation_cost / 300000)) * 0.5) / 2.0
                        selected_scores.append(score)
                    
                    selected_scores_closed = selected_scores + [selected_scores[0]]
                    ax.plot(angles, selected_scores_closed, 'o-', linewidth=2, 
                           color='red', label=f'Selected: {name}', markersize=6)
            
            # 设置标签
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(dimensions, fontsize=10)
            ax.set_ylim(0, 1.0)
            ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
            ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
            ax.set_title('Overall Performance Radar Chart', fontsize=12, fontweight='bold', pad=20)
            
            # 添加图例
            ax.legend(loc='center right', bbox_to_anchor=(1.3, 0.5), fontsize=8)
            
        except Exception as e:
            self.logger.error(f"Failed to create overall performance radar: {str(e)}")
    
    def _create_solutions_clustering_analysis(self, ax, pareto_solutions):
        """创建解的聚类分析"""
        try:
            from sklearn.cluster import KMeans
            from sklearn.preprocessing import StandardScaler
            
            # 准备聚类数据
            features = []
            for sol in pareto_solutions:
                feature_vector = [
                    sol.energy_consumption,
                    sol.thermal_performance,
                    sol.renovation_cost,
                    float(sol.is_feasible),
                    len(getattr(sol, 'window_types', []))
                ]
                features.append(feature_vector)
            
            # 标准化数据
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            # 执行K-means聚类 - 修复聚类警告
            # 检查数据唯一性
            unique_points = np.unique(features_scaled, axis=0)
            n_unique = len(unique_points)

            # 动态调整聚类数量
            n_clusters = min(max(2, n_unique), min(4, len(pareto_solutions)))

            if n_unique < 2:
                # 数据点太相似，使用默认标签
                cluster_labels = np.zeros(len(pareto_solutions), dtype=int)
                # 创建虚拟的kmeans对象用于后续代码
                class DummyKMeans:
                    def __init__(self):
                        self.cluster_centers_ = np.zeros((1, features_scaled.shape[1]))
                kmeans = DummyKMeans()
            else:
                try:
                    kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                    cluster_labels = kmeans.fit_predict(features_scaled)
                except Exception as e:
                    self.logger.warning(f"聚类分析失败: {str(e)}")
                    cluster_labels = np.zeros(len(pareto_solutions), dtype=int)
                    # 创建虚拟的kmeans对象
                    class DummyKMeans:
                        def __init__(self):
                            self.cluster_centers_ = np.zeros((1, features_scaled.shape[1]))
                    kmeans = DummyKMeans()
            
            # 使用PCA降维到2D进行可视化
            from sklearn.decomposition import PCA
            pca = PCA(n_components=2)
            features_2d = pca.fit_transform(features_scaled)
            
            # 绘制聚类结果
            colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown']
            for i in range(n_clusters):
                cluster_points = features_2d[cluster_labels == i]
                ax.scatter(cluster_points[:, 0], cluster_points[:, 1], 
                          c=colors[i], alpha=0.7, s=50, label=f'Cluster {i+1}')
            
            # 绘制聚类中心
            centers_2d = pca.transform(kmeans.cluster_centers_)
            ax.scatter(centers_2d[:, 0], centers_2d[:, 1], 
                      c='black', marker='x', s=100, linewidths=3, label='Centroids')
            
            # 设置标签和标题
            ax.set_xlabel('Principal Component 1', fontsize=10)
            ax.set_ylabel('Principal Component 2', fontsize=10)
            ax.set_title('Solutions Clustering Analysis', fontsize=12, fontweight='bold')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # 添加聚类统计信息
            cluster_stats = []
            for i in range(n_clusters):
                cluster_size = np.sum(cluster_labels == i)
                cluster_percentage = cluster_size / len(pareto_solutions) * 100
                cluster_stats.append(f'Cluster {i+1}: {cluster_size} ({cluster_percentage:.1f}%)')
            
            stats_text = '\n'.join(cluster_stats)
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=8,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))
            
        except Exception as e:
            self.logger.error(f"Failed to create solutions clustering analysis: {str(e)}")
    
    def _create_multi_objective_tradeoff_analysis(self, ax, pareto_solutions):
        """创建多目标权衡分析"""
        try:
            # 归一化目标函数值
            energy_values = np.array([sol.energy_consumption for sol in pareto_solutions])
            thermal_values = np.array([sol.thermal_performance for sol in pareto_solutions])
            cost_values = np.array([sol.renovation_cost for sol in pareto_solutions])
            
            # 归一化到0-1范围
            energy_norm = (energy_values - np.min(energy_values)) / (np.max(energy_values) - np.min(energy_values) + 1e-6)
            thermal_norm = (thermal_values - np.min(thermal_values)) / (np.max(thermal_values) - np.min(thermal_values) + 1e-6)
            cost_norm = (cost_values - np.min(cost_values)) / (np.max(cost_values) - np.min(cost_values) + 1e-6)
            
            # 计算权衡指数
            tradeoff_indices = []
            for i in range(len(pareto_solutions)):
                # 计算该解与其他解的冲突程度
                conflicts = 0
                for j in range(len(pareto_solutions)):
                    if i != j:
                        energy_conflict = abs(energy_norm[i] - energy_norm[j])
                        thermal_conflict = abs(thermal_norm[i] - thermal_norm[j])
                        cost_conflict = abs(cost_norm[i] - cost_norm[j])
                        conflicts += (energy_conflict + thermal_conflict + cost_conflict) / 3
                
                tradeoff_index = conflicts / (len(pareto_solutions) - 1)
                tradeoff_indices.append(tradeoff_index)
            
            # 绘制权衡指数分布
            ax.hist(tradeoff_indices, bins=15, alpha=0.7, color='lightblue', edgecolor='black')
            ax.axvline(np.mean(tradeoff_indices), color='red', linestyle='--', 
                      linewidth=2, label=f'Mean: {np.mean(tradeoff_indices):.3f}')
            
            # 设置标签和标题
            ax.set_xlabel('Tradeoff Index', fontsize=10)
            ax.set_ylabel('Frequency', fontsize=10)
            ax.set_title('Multi-objective Tradeoff Analysis', fontsize=12, fontweight='bold')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # 添加分析文本
            analysis_text = f"""Tradeoff Analysis:
Mean Index: {np.mean(tradeoff_indices):.3f}
Std Dev: {np.std(tradeoff_indices):.3f}
High Conflict: {np.sum(np.array(tradeoff_indices) > 0.5)} solutions
Low Conflict: {np.sum(np.array(tradeoff_indices) < 0.3)} solutions"""
            
            ax.text(0.02, 0.98, analysis_text, transform=ax.transAxes, fontsize=8,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.5))
            
        except Exception as e:
            self.logger.error(f"Failed to create multi-objective tradeoff analysis: {str(e)}")
    
    def _create_improvement_potential_analysis(self, ax, pareto_solutions):
        """创建性能改进潜力分析"""
        try:
            # 计算每个解的改进潜力
            improvement_potentials = []
            solution_names = []
            
            for i, sol in enumerate(pareto_solutions):
                # 计算到理想点的距离
                ideal_energy = np.min([s.energy_consumption for s in pareto_solutions])
                ideal_thermal = np.min([s.thermal_performance for s in pareto_solutions])
                ideal_cost = np.min([s.renovation_cost for s in pareto_solutions])
                
                energy_gap = (sol.energy_consumption - ideal_energy) / ideal_energy
                thermal_gap = (sol.thermal_performance - ideal_thermal) / (ideal_thermal + 1e-6)
                cost_gap = (sol.renovation_cost - ideal_cost) / ideal_cost
                
                # 综合改进潜力
                improvement_potential = (energy_gap + thermal_gap + cost_gap) / 3
                improvement_potentials.append(improvement_potential)
                solution_names.append(f'Sol_{i+1}')
            
            # 选择前15个解进行展示
            top_indices = np.argsort(improvement_potentials)[-15:]
            top_potentials = [improvement_potentials[i] for i in top_indices]
            top_names = [solution_names[i] for i in top_indices]
            
            # 创建水平条形图
            colors = plt.cm.RdYlBu_r(np.array(top_potentials))
            bars = ax.barh(range(len(top_potentials)), top_potentials, color=colors, alpha=0.8)
            
            # 设置标签
            ax.set_yticks(range(len(top_potentials)))
            ax.set_yticklabels(top_names, fontsize=8)
            ax.set_xlabel('Improvement Potential', fontsize=10)
            ax.set_title('Top 15 Solutions by Improvement Potential', fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3, axis='x')
            
            # 添加数值标注
            for bar, potential in zip(bars, top_potentials):
                width = bar.get_width()
                ax.text(width + 0.01, bar.get_y() + bar.get_height()/2,
                       f'{potential:.3f}', ha='left', va='center', fontsize=8)
            
        except Exception as e:
            self.logger.error(f"Failed to create improvement potential analysis: {str(e)}")
    
    def _create_solutions_robustness_analysis(self, ax, pareto_solutions):
        """创建解的鲁棒性分析"""
        try:
            # 模拟参数变化对性能的影响
            robustness_scores = []
            solution_names = []
            
            for i, sol in enumerate(pareto_solutions):
                # 计算基准性能
                base_score = self._calculate_comprehensive_score(sol)
                
                # 模拟参数变化（±10%）
                variations = []
                for energy_variation in [0.9, 1.0, 1.1]:
                    for thermal_variation in [0.9, 1.0, 1.1]:
                        for cost_variation in [0.9, 1.0, 1.1]:
                            varied_energy = sol.energy_consumption * energy_variation
                            varied_thermal = sol.thermal_performance * thermal_variation
                            varied_cost = sol.renovation_cost * cost_variation
                            
                            varied_score = self._calculate_varied_score(varied_energy, varied_thermal, varied_cost)
                            variations.append(varied_score)
                
                # 计算鲁棒性分数（性能变化的稳定性）
                robustness_score = 1.0 / (1.0 + np.std(variations))
                robustness_scores.append(robustness_score)
                solution_names.append(f'Sol_{i+1}')
            
            # 选择前10个最鲁棒的解
            top_indices = np.argsort(robustness_scores)[-10:]
            top_robustness = [robustness_scores[i] for i in top_indices]
            top_names = [solution_names[i] for i in top_indices]
            
            # 创建条形图
            colors = plt.cm.Greens(np.array(top_robustness))
            bars = ax.bar(range(len(top_robustness)), top_robustness, color=colors, alpha=0.8)
            
            # 设置标签
            ax.set_xticks(range(len(top_robustness)))
            ax.set_xticklabels(top_names, rotation=45, ha='right', fontsize=8)
            ax.set_ylabel('Robustness Score', fontsize=10)
            ax.set_title('Top 10 Most Robust Solutions', fontsize=12, fontweight='bold')
            ax.grid(True, alpha=0.3, axis='y')
            
            # 添加数值标注
            for bar, score in zip(bars, top_robustness):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2, height + 0.01,
                       f'{score:.3f}', ha='center', va='bottom', fontsize=8)
            
        except Exception as e:
            self.logger.error(f"Failed to create solutions robustness analysis: {str(e)}")
    
    def _create_decision_recommendations_analysis(self, ax, pareto_solutions, selected_solutions):
        """创建决策建议分析"""
        try:
            # 分析不同决策场景下的最佳解
            scenarios = {
                'Budget Conscious': {'energy_weight': 0.3, 'thermal_weight': 0.3, 'cost_weight': 0.4},
                'Performance Focused': {'energy_weight': 0.4, 'thermal_weight': 0.4, 'cost_weight': 0.2},
                'Balanced Approach': {'energy_weight': 0.33, 'thermal_weight': 0.33, 'cost_weight': 0.34},
                'Energy Efficient': {'energy_weight': 0.5, 'thermal_weight': 0.3, 'cost_weight': 0.2},
                'Thermal Comfort': {'energy_weight': 0.3, 'thermal_weight': 0.5, 'cost_weight': 0.2}
            }
            
            recommendations = []
            y_positions = []
            
            for i, (scenario_name, weights) in enumerate(scenarios.items()):
                # 计算每个解在该场景下的评分
                scenario_scores = []
                for sol in pareto_solutions:
                    energy_score = max(0, min(1, 1 - sol.energy_consumption / 200))
                    thermal_score = 1 - sol.thermal_performance
                    cost_score = max(0, min(1, 1 - sol.renovation_cost / 300000))
                    
                    scenario_score = (energy_score * weights['energy_weight'] + 
                                    thermal_score * weights['thermal_weight'] + 
                                    cost_score * weights['cost_weight'])
                    scenario_scores.append(scenario_score)
                
                # 找到最佳解
                best_solution_idx = np.argmax(scenario_scores)
                best_solution = pareto_solutions[best_solution_idx]
                best_score = scenario_scores[best_solution_idx]
                
                recommendations.append({
                    'scenario': scenario_name,
                    'solution_idx': best_solution_idx,
                    'score': best_score,
                    'energy': best_solution.energy_consumption,
                    'thermal': best_solution.thermal_performance,
                    'cost': best_solution.renovation_cost
                })
                
                y_positions.append(i)
            
            # 创建决策建议表格
            table_data = []
            headers = ['Scenario', 'Best Solution', 'Score', 'Energy', 'Thermal', 'Cost']
            
            for rec in recommendations:
                row = [
                    rec['scenario'],
                    f'Sol_{rec["solution_idx"] + 1}',
                    f'{rec["score"]:.3f}',
                    f'{rec["energy"]:.1f}',
                    f'{rec["thermal"]:.3f}',
                    f'{rec["cost"]:.0f}'
                ]
                table_data.append(row)
            
            # 创建表格
            table = ax.table(cellText=table_data, colLabels=headers,
                           cellLoc='center', loc='center', bbox=[0, 0, 1, 1])
            
            # 设置表格样式
            table.auto_set_font_size(False)
            table.set_fontsize(8)
            table.scale(1, 1.5)
            
            # 美化表格
            for i in range(len(headers)):
                table[(0, i)].set_facecolor('#4472C4')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            # 隐藏坐标轴
            ax.axis('off')
            
            # 设置标题
            ax.set_title('Decision Recommendations by Scenario', fontsize=12, fontweight='bold', pad=20)
            
        except Exception as e:
            self.logger.error(f"Failed to create decision recommendations analysis: {str(e)}")
    
    def _create_comprehensive_performance_timeseries(self, ax, pareto_solutions):
        """创建综合性能时间序列分析"""
        try:
            # 模拟时间序列数据（假设按优化代数排列）
            generation_numbers = list(range(1, len(pareto_solutions) + 1))
            
            # 计算性能指标
            energy_values = [sol.energy_consumption for sol in pareto_solutions]
            thermal_values = [(1 - sol.thermal_performance) * 100 for sol in pareto_solutions]  # 转换为百分比
            cost_values = [sol.renovation_cost / 1000 for sol in pareto_solutions]  # 转换为千元
            
            # 归一化到0-1范围进行比较
            energy_norm = [(e - min(energy_values)) / (max(energy_values) - min(energy_values)) for e in energy_values]
            thermal_norm = [(t - min(thermal_values)) / (max(thermal_values) - min(thermal_values)) for t in thermal_values]
            cost_norm = [(c - min(cost_values)) / (max(cost_values) - min(cost_values)) for c in cost_values]
            
            # 创建双Y轴图表
            ax2 = ax.twinx()
            
            # 绘制归一化性能指标
            line1 = ax.plot(generation_numbers, energy_norm, 'o-', color='red', linewidth=2, 
                           markersize=4, label='Energy Performance')
            line2 = ax.plot(generation_numbers, thermal_norm, 's-', color='blue', linewidth=2, 
                           markersize=4, label='Thermal Performance')
            line3 = ax.plot(generation_numbers, cost_norm, '^-', color='green', linewidth=2, 
                           markersize=4, label='Cost Efficiency')
            
            # 绘制综合性能指数
            composite_scores = [(e + t + (1 - c)) / 3 for e, t, c in zip(energy_norm, thermal_norm, cost_norm)]
            line4 = ax2.plot(generation_numbers, composite_scores, 'D-', color='purple', linewidth=3, 
                            markersize=6, label='Composite Index')
            
            # 设置标签和标题
            ax.set_xlabel('Generation Number', fontsize=12)
            ax.set_ylabel('Normalized Performance Score', fontsize=12)
            ax2.set_ylabel('Composite Performance Index', fontsize=12)
            ax.set_title('Comprehensive Performance Evolution', fontsize=14, fontweight='bold')
            
            # 设置X轴范围
            ax.set_xlim(0, len(generation_numbers) + 1)
            
            # 添加网格
            ax.grid(True, alpha=0.3)
            
            # 添加图例
            lines = line1 + line2 + line3 + line4
            labels = [l.get_label() for l in lines]
            ax.legend(lines, labels, loc='upper left', fontsize=10)
            
            # 添加趋势分析
            if len(composite_scores) > 1:
                # 计算趋势
                trend = np.polyfit(generation_numbers, composite_scores, 1)
                trend_line = np.polyval(trend, generation_numbers)
                ax2.plot(generation_numbers, trend_line, '--', color='orange', alpha=0.7, 
                        label=f'Trend (slope: {trend[0]:.4f})')
                
                # 添加趋势文本
                trend_text = f"Performance Trend: {'Improving' if trend[0] > 0 else 'Declining'}"
                ax.text(0.02, 0.98, trend_text, transform=ax.transAxes, fontsize=10,
                        verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.7))
            
        except Exception as e:
            self.logger.error(f"Failed to create comprehensive performance timeseries: {str(e)}")
    
    def _calculate_comprehensive_score(self, solution):
        """计算综合评分"""
        energy_score = max(0, min(1, 1 - solution.energy_consumption / 200))
        thermal_score = 1 - solution.thermal_performance
        cost_score = max(0, min(1, 1 - solution.renovation_cost / 300000))
        return energy_score * 0.4 + thermal_score * 0.4 + cost_score * 0.2
    
    def _calculate_varied_score(self, energy, thermal, cost):
        """计算变化后的评分"""
        energy_score = max(0, min(1, 1 - energy / 200))
        thermal_score = 1 - thermal
        cost_score = max(0, min(1, 1 - cost / 300000))
        return energy_score * 0.4 + thermal_score * 0.4 + cost_score * 0.2


def create_visualization_manager() -> VisualizationManager:
    """创建可视化管理器实例"""
    try:
        return VisualizationManager()
    except Exception as e:
        logger = get_logger(__name__)
        logger.error(f"Failed to create visualization manager: {str(e)}")
        raise VisualizationError(f"Visualization manager creation failed: {str(e)}") from e