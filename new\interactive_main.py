"""
交互式建筑立面优化系统
支持用户自主选择立面图、EPW文件、朝向、改造模式等参数
支持拖拽文件到终端输入
"""

import os
import sys
import traceback
from pathlib import Path
from datetime import datetime

# 设置字体 - 支持中文显示
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS', 'Arial', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 10

# 添加源码路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# 导入核心模块
from src.core.data_structures import RenovationMode, Orientation
from main import AutoBuildingFacadeOptimizer


class InteractiveFacadeOptimizer:
    """交互式立面优化器"""
    
    def __init__(self):
        self.optimizer = None
        
    def clean_file_path(self, path_str: str) -> str:
        """清理文件路径，移除引号和多余空格"""
        path_str = path_str.strip()
        # 移除首尾的引号
        if path_str.startswith('"') and path_str.endswith('"'):
            path_str = path_str[1:-1]
        elif path_str.startswith("'") and path_str.endswith("'"):
            path_str = path_str[1:-1]
        return path_str.strip()
    
    def get_facade_image_path(self) -> str:
        """获取立面图片路径"""
        print("\n=== 选择立面图片 ===")
        print("请输入立面图片路径（支持拖拽文件到终端）:")
        print("支持格式: .png, .jpg, .jpeg, .bmp, .tiff")
        
        while True:
            path_input = input("立面图片路径: ").strip()
            if not path_input:
                print("路径不能为空，请重新输入")
                continue
                
            # 清理路径
            path_input = self.clean_file_path(path_input)
            
            # 验证文件
            if not os.path.exists(path_input):
                print(f"文件不存在: {path_input}")
                print("请检查路径是否正确，或重新拖拽文件")
                continue
                
            # 验证文件格式
            valid_extensions = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff'}
            file_ext = Path(path_input).suffix.lower()
            if file_ext not in valid_extensions:
                print(f"不支持的文件格式: {file_ext}")
                print(f"支持的格式: {', '.join(valid_extensions)}")
                continue
                
            print(f"✓ 立面图片: {Path(path_input).name}")
            return path_input
    
    def get_epw_file_path(self) -> str:
        """获取EPW气候文件路径"""
        print("\n=== 选择气候文件 ===")
        print("请输入EPW气候文件路径（支持拖拽文件到终端）:")
        print("EPW文件包含建筑所在地的气候数据")
        
        while True:
            path_input = input("EPW文件路径: ").strip()
            if not path_input:
                print("路径不能为空，请重新输入")
                continue
                
            # 清理路径
            path_input = self.clean_file_path(path_input)
            
            # 验证文件
            if not os.path.exists(path_input):
                print(f"文件不存在: {path_input}")
                print("请检查路径是否正确，或重新拖拽文件")
                continue
                
            # 验证文件格式
            if not path_input.lower().endswith('.epw'):
                print("文件必须是EPW格式（.epw扩展名）")
                continue
                
            print(f"✓ 气候文件: {Path(path_input).name}")
            return path_input
    
    def get_building_orientation(self) -> Orientation:
        """获取建筑朝向"""
        print("\n=== 选择建筑朝向 ===")
        print("请选择建筑立面的朝向:")
        
        orientations = [
            (1, Orientation.NORTH, "北向 (North)"),
            (2, Orientation.NORTHEAST, "东北向 (Northeast)"),
            (3, Orientation.EAST, "东向 (East)"),
            (4, Orientation.SOUTHEAST, "东南向 (Southeast)"),
            (5, Orientation.SOUTH, "南向 (South) - 推荐"),
            (6, Orientation.SOUTHWEST, "西南向 (Southwest)"),
            (7, Orientation.WEST, "西向 (West)"),
            (8, Orientation.NORTHWEST, "西北向 (Northwest)")
        ]
        
        for num, _, desc in orientations:
            print(f"{num}. {desc}")
        
        while True:
            try:
                choice = input("请选择朝向 (1-8): ").strip()
                if not choice:
                    print("请输入选择")
                    continue
                    
                choice_num = int(choice)
                if 1 <= choice_num <= 8:
                    selected_orientation = orientations[choice_num - 1][1]
                    print(f"✓ 建筑朝向: {orientations[choice_num - 1][2]}")
                    return selected_orientation
                else:
                    print("请输入1-8之间的数字")
            except ValueError:
                print("请输入有效的数字")
    
    def get_renovation_mode(self) -> RenovationMode:
        """获取改造模式"""
        print("\n=== 选择改造模式 ===")
        print("请选择立面改造模式:")
        
        modes = [
            (1, RenovationMode.NEW_CONSTRUCTION, "新建项目", "自由添加窗户大小位置和窗框或遮阳"),
            (2, RenovationMode.RENOVATION, "改造项目", "不能增删窗户，窗户只能横向变化大小，高度不变"),
            (3, RenovationMode.MAJOR_RENOVATION, "大幅度改造", "可以自由增删窗户，原有窗户可以自由变化"),
            (4, RenovationMode.STRICT_RENOVATION, "严格改造", "只允许窗户水平宽度变化，中轴对称，严禁增删窗户")
        ]
        
        for num, _, name, desc in modes:
            print(f"{num}. {name} - {desc}")
        
        while True:
            try:
                choice = input("请选择改造模式 (1-4): ").strip()
                if not choice:
                    print("请输入选择")
                    continue
                    
                choice_num = int(choice)
                if 1 <= choice_num <= 4:
                    selected_mode = modes[choice_num - 1][1]
                    print(f"✓ 改造模式: {modes[choice_num - 1][2]} - {modes[choice_num - 1][3]}")
                    return selected_mode
                else:
                    print("请输入1-4之间的数字")
            except ValueError:
                print("请输入有效的数字")
    
    def get_optimization_parameters(self) -> tuple:
        """获取优化参数"""
        print("\n=== 优化参数设置 ===")
        print("设置遗传算法参数（可直接回车使用默认值）:")
        
        # 获取代数
        while True:
            generations_input = input("优化代数 (默认20): ").strip()
            if not generations_input:
                generations = 20
                break
            try:
                generations = int(generations_input)
                if generations > 0:
                    break
                else:
                    print("代数必须大于0")
            except ValueError:
                print("请输入有效的数字")
        
        # 获取种群大小
        while True:
            population_input = input("种群大小 (默认50): ").strip()
            if not population_input:
                population = 50
                break
            try:
                population = int(population_input)
                if population > 0:
                    break
                else:
                    print("种群大小必须大于0")
            except ValueError:
                print("请输入有效的数字")
        
        print(f"✓ 优化参数: {generations}代, 种群{population}")
        return generations, population
    
    def run_interactive_optimization(self):
        """运行交互式优化流程"""
        print("=" * 60)
        print("🏢 交互式建筑立面优化系统")
        print("=" * 60)
        print("本系统将帮助您优化建筑立面设计，提升能效和舒适性")
        
        try:
            # 1. 获取输入参数
            image_path = self.get_facade_image_path()
            epw_path = self.get_epw_file_path()
            orientation = self.get_building_orientation()
            renovation_mode = self.get_renovation_mode()
            generations, population = self.get_optimization_parameters()
            
            # 2. 确认参数
            print("\n=== 参数确认 ===")
            print(f"立面图片: {Path(image_path).name}")
            print(f"气候文件: {Path(epw_path).name}")
            print(f"建筑朝向: {orientation.value}")
            print(f"改造模式: {renovation_mode.value}")
            print(f"优化参数: {generations}代, 种群{population}")
            
            confirm = input("\n确认开始优化? (y/n): ").strip().lower()
            if confirm not in ['y', 'yes', '是', '确认']:
                print("优化已取消")
                return
            
            # 3. 创建并配置优化器
            print("\n" + "=" * 60)
            print("🚀 开始立面优化...")
            print("=" * 60)
            
            self.optimizer = AutoBuildingFacadeOptimizer()
            self.optimizer.image_path = image_path
            self.optimizer.epw_path = epw_path
            self.optimizer.orientation = orientation
            self.optimizer.renovation_mode = renovation_mode
            
            # 4. 运行优化
            start_time = datetime.now()
            success = self.optimizer.run_complete_optimization()
            end_time = datetime.now()
            
            # 5. 显示结果
            if success:
                duration = end_time - start_time
                print("\n" + "=" * 60)
                print("✅ 优化完成!")
                print("=" * 60)
                print(f"总耗时: {duration}")
                print(f"结果保存在: output/ 目录")
                print("请查看生成的报告和图表了解优化结果")
            else:
                print("\n❌ 优化失败，请检查输入文件和参数")
                
        except KeyboardInterrupt:
            print("\n\n用户中断优化过程")
        except Exception as e:
            print(f"\n❌ 发生错误: {str(e)}")
            traceback.print_exc()


def main():
    """主函数"""
    interactive_optimizer = InteractiveFacadeOptimizer()
    interactive_optimizer.run_interactive_optimization()


if __name__ == "__main__":
    main()
