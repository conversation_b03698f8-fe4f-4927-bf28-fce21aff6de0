"""
Performance Analysis Visualization Charts
Implement professional visualization for energy consumption, thermal performance, cost analysis, and other performance metrics (4 types of charts)
"""

import numpy as np
import matplotlib.pyplot as plt
# Set English font - avoid Chinese font issues
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
import seaborn as sns
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, Circle
import matplotlib.dates as mdates
from datetime import datetime, timedelta

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import VisualizationError, handle_exception
from ..core.data_structures import (
    VisualizationData, EnergyBreakdown, ThermalPerformanceMetrics, 
    CostBreakdown, HourlyClimateData
)
from .base_visualizer import BaseVisualizer, create_base_visualizer


class PerformanceCharts:
    """
    Performance analysis visualization charts class
    
    Features:
    1. Energy performance visualization
    2. Thermal performance visualization  
    3. Cost analysis visualization
    4. Comprehensive performance radar chart
    """
    
    def __init__(self):
        """Initialize performance analysis visualization charts"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.base_visualizer = create_base_visualizer()
        
        # Get performance visualization configuration
        viz_config = self.config.get_section('visualization')
        self.performance_config = viz_config.get('performance_charts', {})
        
        # Chart style configuration
        style_config = self.performance_config.get('chart_styles', {})
        self.use_gradient_colors = style_config.get('use_gradient_colors', True)
        self.show_benchmarks = style_config.get('show_benchmarks', True)
        self.enable_annotations = style_config.get('enable_annotations', True)
        
        self.logger.info("Performance analysis visualization charts initialized successfully")
    
    def set_session_output_directory(self, session_dir: str) -> None:
        """Set session output directory"""
        if hasattr(self.base_visualizer, 'set_session_output_directory'):
            self.base_visualizer.set_session_output_directory(session_dir)
            self.output_dir = self.base_visualizer.output_dir
    
    @handle_exception
    def create_energy_performance_chart(self, visualization_data: VisualizationData) -> str:
        """
        Create energy performance visualization chart
        
        Args:
            visualization_data: Visualization data
            
        Returns:
            Saved chart file path
            
        Raises:
            VisualizationError: Thrown when visualization fails
        """
        with LogContext("Energy performance chart creation", self.logger):
            try:
                energy_breakdown = visualization_data.energy_breakdown
                
                if not energy_breakdown:
                    raise VisualizationError("No energy analysis data available")
                
                # 简化为1x2布局，只保留最重要的图表
                fig, axes = self.base_visualizer.create_subplot_figure(1, 2,
                    figsize=self.base_visualizer.chart_config.get_figure_size('medium'))

                # 确保axes是数组
                if not hasattr(axes, '__len__'):
                    axes = [axes]
                elif len(axes.shape) > 1:
                    axes = axes.flatten()

                # 子图1: 能耗构成饼图
                self._create_simplified_energy_pie(axes[0], energy_breakdown)

                # 子图2: 月度能耗趋势
                self._create_simplified_monthly_trend(axes[1], energy_breakdown)
                
                # Adjust layout with error handling
                try:
                    plt.tight_layout(pad=3.0)
                except Exception:
                    # If tight_layout fails, use subplots_adjust as fallback
                    plt.subplots_adjust(hspace=0.4, wspace=0.3, top=0.9, bottom=0.1)
                
                # Save chart
                saved_files = self.base_visualizer.save_chart(fig, "energy_performance")
                
                # Clean up resources
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"Failed to create energy performance chart: {str(e)}") from e

    def _create_simplified_energy_pie(self, ax, energy_breakdown: EnergyBreakdown):
        """创建简化的能耗构成饼图"""
        try:
            # 能耗数据
            categories = ['供暖', '制冷', '照明', '通风']
            values = [
                energy_breakdown.heating_energy,
                energy_breakdown.cooling_energy,
                energy_breakdown.lighting_energy,
                energy_breakdown.ventilation_energy
            ]

            # 过滤零值
            non_zero_data = [(cat, val) for cat, val in zip(categories, values) if val > 0]
            if not non_zero_data:
                ax.text(0.5, 0.5, '无能耗数据', ha='center', va='center', transform=ax.transAxes,
                       fontsize=self.base_visualizer.chart_config.current_font.label_size)
                ax.set_title('能耗构成', fontsize=self.base_visualizer.chart_config.current_font.title_size)
                return

            categories, values = zip(*non_zero_data)

            # 获取颜色
            colors = self.base_visualizer.chart_config.get_color_sequence(len(values))

            # 创建饼图
            wedges, texts, autotexts = ax.pie(values, labels=categories, colors=colors,
                                            autopct='%1.1f%%', startangle=90,
                                            textprops={'fontsize': self.base_visualizer.chart_config.current_font.tick_size})

            # 设置标题
            ax.set_title('能耗构成分析', fontsize=self.base_visualizer.chart_config.current_font.title_size, pad=20)

            # 添加总能耗信息
            total_energy = sum(values)
            ax.text(0.5, -1.3, f'总能耗: {total_energy:.1f} kWh/m²/year',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=self.base_visualizer.chart_config.current_font.label_size,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))

        except Exception as e:
            self.logger.error(f"创建能耗饼图失败: {str(e)}")
            ax.text(0.5, 0.5, '图表生成失败', ha='center', va='center', transform=ax.transAxes)

    def _create_simplified_monthly_trend(self, ax, energy_breakdown: EnergyBreakdown):
        """创建简化的月度能耗趋势图"""
        try:
            if not energy_breakdown.monthly_breakdown or len(energy_breakdown.monthly_breakdown) != 12:
                ax.text(0.5, 0.5, '无月度数据', ha='center', va='center', transform=ax.transAxes,
                       fontsize=self.base_visualizer.chart_config.current_font.label_size)
                ax.set_title('月度能耗趋势', fontsize=self.base_visualizer.chart_config.current_font.title_size)
                return

            months = ['1月', '2月', '3月', '4月', '5月', '6月',
                     '7月', '8月', '9月', '10月', '11月', '12月']
            monthly_values = energy_breakdown.monthly_breakdown

            # 创建折线图
            color = self.base_visualizer.chart_config.get_color_sequence(1)[0]
            ax.plot(months, monthly_values, marker='o', linewidth=2.5, markersize=6,
                   color=color, markerfacecolor='white', markeredgecolor=color, markeredgewidth=2)

            # 设置标题和标签
            ax.set_title('月度能耗趋势', fontsize=self.base_visualizer.chart_config.current_font.title_size, pad=20)
            ax.set_ylabel('能耗 (kWh/m²/month)', fontsize=self.base_visualizer.chart_config.current_font.label_size)

            # 设置刻度
            ax.tick_params(axis='x', rotation=45, labelsize=self.base_visualizer.chart_config.current_font.tick_size)
            ax.tick_params(axis='y', labelsize=self.base_visualizer.chart_config.current_font.tick_size)

            # 添加网格
            ax.grid(True, alpha=0.3, linestyle='--')

            # 添加数值标签
            max_val = max(monthly_values)
            max_idx = monthly_values.index(max_val)
            ax.annotate(f'峰值: {max_val:.1f}',
                       xy=(max_idx, max_val), xytext=(max_idx, max_val + max_val * 0.1),
                       ha='center', fontsize=self.base_visualizer.chart_config.current_font.tick_size,
                       arrowprops=dict(arrowstyle='->', color='red', alpha=0.7))

        except Exception as e:
            self.logger.error(f"创建月度趋势图失败: {str(e)}")
            ax.text(0.5, 0.5, '图表生成失败', ha='center', va='center', transform=ax.transAxes)

    def _create_energy_breakdown_pie(self, ax, energy_breakdown: EnergyBreakdown):
        """Create energy breakdown pie chart"""
        try:
            # Energy data
            categories = ['Heating', 'Cooling', 'Lighting', 'Ventilation']
            categories_en = ['Heating', 'Cooling', 'Lighting', 'Ventilation']
            values = [
                energy_breakdown.heating_energy,
                energy_breakdown.cooling_energy,
                energy_breakdown.lighting_energy,
                energy_breakdown.ventilation_energy
            ]
            
            # Filter zero values
            non_zero_data = [(cat, cat_en, val) for cat, cat_en, val in zip(categories, categories_en, values) if val > 0]
            if not non_zero_data:
                ax.text(0.5, 0.5, 'No Energy Data', ha='center', va='center', transform=ax.transAxes)
                return
            
            categories, categories_en, values = zip(*non_zero_data)
            
            # Get colors
            colors = self.base_visualizer.chart_config.get_color_sequence(len(values))
            
            # Create pie chart
            wedges, texts, autotexts = ax.pie(values, labels=None, colors=colors, autopct='%1.1f%%',
                                            startangle=90, pctdistance=0.85, 
                                            wedgeprops=dict(width=0.7, edgecolor='white', linewidth=2))
            
            # Beautify text
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontsize(self.base_visualizer.chart_config.current_font.annotation_size)
                autotext.set_fontweight('bold')
            
            # Create bilingual legend
            legend_labels = []
            for i, (cat, cat_en, val) in enumerate(zip(categories, categories_en, values)):
                if self.base_visualizer.chart_config.bilingual_mode:
                    label = f"{cat}/{cat_en}: {val:.1f} kWh"
                else:
                    label = f"{cat}: {val:.1f} kWh"
                legend_labels.append(label)
            
            ax.legend(wedges, legend_labels, loc="center left", bbox_to_anchor=(1, 0, 0.5, 1),
                     fontsize=self.base_visualizer.chart_config.current_font.legend_size)
            
            # Set title
            title_zh = "Building Energy Consumption Analysis"
            title_en = "Building Energy Consumption Breakdown"
            title = self.base_visualizer.chart_config.get_bilingual_text(title_zh, title_en)
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size,
                        fontweight='bold', pad=20)
            
            # Add total energy annotation
            total_energy = sum(values)
            ax.text(0, 0, f'Total Energy\n{total_energy:.1f}\nkWh/m²/year', 
                   ha='center', va='center',
                   fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                   fontweight='bold',
                   bbox=dict(boxstyle="round,pad=0.3",
                           facecolor=self.base_visualizer.chart_config.current_palette.background,
                           alpha=0.8))
            
        except Exception as e:
            self.logger.error(f"Failed to create energy breakdown pie chart: {str(e)}")
    
    def _create_monthly_energy_trend(self, ax, energy_breakdown: EnergyBreakdown):
        """Create monthly energy trend chart"""
        try:
            # Simulate monthly data (should be obtained from monthly_breakdown in practice)
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                     'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            months_en = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                        'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            
            # Generate monthly energy data
            if hasattr(energy_breakdown, 'monthly_breakdown') and energy_breakdown.monthly_breakdown:
                monthly_data = energy_breakdown.monthly_breakdown[:12]  # Ensure 12 months
                while len(monthly_data) < 12:
                    monthly_data.append(energy_breakdown.total_energy / 12)
            else:
                # Simulate seasonal variations
                base_energy = energy_breakdown.total_energy / 12
                seasonal_factors = [1.4, 1.3, 1.1, 0.9, 0.7, 0.6, 0.8, 0.9, 0.8, 0.9, 1.2, 1.4]
                monthly_data = [base_energy * factor for factor in seasonal_factors]
            
            # Get colors
            colors = self.base_visualizer.create_color_gradient(np.array(monthly_data))
            
            # Create bar chart
            bars = ax.bar(range(12), monthly_data, color=colors, alpha=0.8, 
                         edgecolor='white', linewidth=1)
            
            # Add value annotations
            if self.enable_annotations:
                for i, (bar, value) in enumerate(zip(bars, monthly_data)):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + max(monthly_data) * 0.01,
                           f'{value:.1f}', ha='center', va='bottom',
                           fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 1)
            
            # Add trend line
            z = np.polyfit(range(12), monthly_data, 2)  # Quadratic fitting
            p = np.poly1d(z)
            x_smooth = np.linspace(0, 11, 100)
            ax.plot(x_smooth, p(x_smooth), 
                   color=self.base_visualizer.chart_config.current_palette.accent,
                   linestyle='--', linewidth=2, alpha=0.8, label='Trend Line')
            
            # Set x-axis labels
            if self.base_visualizer.chart_config.bilingual_mode:
                labels = [f"{zh}\n{en}" for zh, en in zip(months, months_en)]
            else:
                labels = months
            
            ax.set_xticks(range(12))
            ax.set_xticklabels(labels, fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set title and labels
            title_zh = "Monthly Energy Consumption Trend"
            title_en = "Monthly Energy Consumption Trend"
            xlabel_zh = "Month"
            xlabel_en = "Month"
            ylabel_zh = "Energy Consumption (kWh/m²)"
            ylabel_en = "Energy Consumption (kWh/m²)"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # Add average line
            avg_energy = np.mean(monthly_data)
            ax.axhline(y=avg_energy, color=self.base_visualizer.chart_config.current_palette.success,
                      linestyle=':', linewidth=2, alpha=0.7, label=f'Annual Average: {avg_energy:.1f}')
            
            # Add legend
            ax.legend(fontsize=self.base_visualizer.chart_config.current_font.legend_size - 1)
            
        except Exception as e:
            self.logger.error(f"Failed to create monthly energy trend chart: {str(e)}")
    
    def _create_energy_comparison_bar(self, ax, energy_breakdown: EnergyBreakdown):
        """Create energy comparison bar chart"""
        try:
            # Comparison data
            categories = ['Current', 'Baseline', 'Excellent', 'Passive House']
            categories_en = ['Current', 'Baseline', 'Excellent', 'Passive House']
            
            # Energy data
            current_energy = energy_breakdown.total_energy
            benchmark_values = [current_energy, 120, 60, 30]  # Simulated benchmark values
            
            # Create grouped bar chart
            x = np.arange(len(categories))
            width = 0.35
            
            colors = self.base_visualizer.chart_config.get_color_sequence(4)
            
            bars = ax.bar(x, benchmark_values, width, color=colors, alpha=0.8,
                         edgecolor='white', linewidth=1)
            
            # Highlight current solution
            bars[0].set_edgecolor(self.base_visualizer.chart_config.current_palette.accent)
            bars[0].set_linewidth(3)
            
            # Add value annotations
            for i, (bar, value) in enumerate(zip(bars, benchmark_values)):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(benchmark_values) * 0.02,
                       f'{value:.1f}', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                       fontweight='bold' if i == 0 else 'normal')
            
            # Add performance level annotations
            performance_levels = ['Current', 'Good', 'Excellent', 'Outstanding']
            performance_colors = [
                self.base_visualizer.chart_config.current_palette.warning,
                self.base_visualizer.chart_config.current_palette.neutral,
                self.base_visualizer.chart_config.current_palette.success,
                self.base_visualizer.chart_config.current_palette.primary
            ]
            
            for i, (bar, level, color) in enumerate(zip(bars, performance_levels, performance_colors)):
                ax.text(bar.get_x() + bar.get_width()/2., bar.get_height()/2,
                       level, ha='center', va='center',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                       color='white', fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.2", facecolor=color, alpha=0.8))
            
            # Set x-axis labels
            if self.base_visualizer.chart_config.bilingual_mode:
                labels = [f"{zh}\n{en}" for zh, en in zip(categories, categories_en)]
            else:
                labels = categories
            
            ax.set_xticks(x)
            ax.set_xticklabels(labels, fontsize=self.base_visualizer.chart_config.current_font.tick_size)
            
            # Set title and labels
            title_zh = "Energy Performance Comparison"
            title_en = "Energy Performance Comparison"
            xlabel_zh = "Building Type"
            xlabel_en = "Building Type"
            ylabel_zh = "Energy Consumption (kWh/m²/year)"
            ylabel_en = "Energy Consumption (kWh/m²/year)"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # Add target line
            if self.show_benchmarks:
                target_energy = 80  # Target energy consumption
                ax.axhline(y=target_energy, color=self.base_visualizer.chart_config.current_palette.error,
                          linestyle='--', linewidth=2, alpha=0.7, label=f'Target: {target_energy}')
                ax.legend()
            
        except Exception as e:
            self.logger.error(f"Failed to create energy comparison bar chart: {str(e)}")
    
    def _create_hourly_energy_heatmap(self, ax, energy_breakdown: EnergyBreakdown):
        """Create hourly energy heatmap"""
        try:
            # 生成24小时x12月的能耗数据矩阵
            hours = list(range(24))
            months = list(range(1, 13))
            
            # 模拟逐时能耗数据
            np.random.seed(42)  # 确保可重复
            base_energy = energy_breakdown.total_energy / (24 * 365)
            
            # 创建能耗矩阵
            energy_matrix = np.zeros((24, 12))
            for h in range(24):
                for m in range(12):
                    # 时间因子
                    if 6 <= h <= 18:  # 白天
                        hour_factor = 1.2 + 0.3 * np.sin((h - 6) / 12 * np.pi)
                    else:  # 夜间
                        hour_factor = 0.6
                    
                    # 季节因子
                    season_factor = 1.0 + 0.4 * np.sin((m - 6) / 6 * np.pi)
                    
                    # 随机变化
                    random_factor = np.random.normal(1.0, 0.1)
                    
                    energy_matrix[h][m] = base_energy * hour_factor * season_factor * random_factor * 24
            
            # 创建热图
            im = ax.imshow(energy_matrix, cmap='YlOrRd', aspect='auto', interpolation='bilinear')
            
            # 设置刻度标签
            ax.set_xticks(range(12))
            ax.set_xticklabels(['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                              fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            ax.set_yticks(range(0, 24, 4))
            ax.set_yticklabels([f'{h}:00' for h in range(0, 24, 4)],
                              fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set title and labels
            title_zh = "Hourly Energy Distribution Heatmap"
            title_en = "Hourly Energy Distribution Heatmap"
            xlabel_zh = "Month"
            xlabel_en = "Month"
            ylabel_zh = "Hour"
            ylabel_en = "Hour"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, shrink=0.8)
            cbar.set_label(self.base_visualizer.chart_config.get_bilingual_text(
                "Energy Intensity (W/m²)", "Energy Intensity (W/m²)"),
                fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
            # Add value annotations（仅显示部分）
            if self.enable_annotations:
                for h in range(0, 24, 6):
                    for m in range(0, 12, 3):
                        value = energy_matrix[h][m]
                        ax.text(m, h, f'{value:.0f}', ha='center', va='center',
                               color='white' if value > np.mean(energy_matrix) else 'black',
                               fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 2)
            
        except Exception as e:
            self.logger.error(f"Failed to create hourly energy heatmap: {str(e)}")
    
    @handle_exception
    def create_thermal_performance_chart(self, visualization_data: VisualizationData) -> str:
        """
        Create thermal performance visualization chart
        
        Args:
            visualization_data: Visualization data
            
        Returns:
            Saved chart file path
        """
        with LogContext("Thermal performance chart creation", self.logger):
            try:
                thermal_metrics = visualization_data.thermal_metrics
                
                if not thermal_metrics:
                    raise VisualizationError("No thermal performance data available")
                
                # 简化为1x2布局，只保留最重要的图表
                fig, axes = self.base_visualizer.create_subplot_figure(1, 2,
                    figsize=self.base_visualizer.chart_config.get_figure_size('medium'))

                # 确保axes是数组
                if not hasattr(axes, '__len__'):
                    axes = [axes]
                elif len(axes.shape) > 1:
                    axes = axes.flatten()

                # 子图1: 舒适度分析
                self._create_simplified_comfort_chart(axes[0], thermal_metrics)

                # 子图2: 热工性能指标
                self._create_simplified_thermal_metrics_chart(axes[1], thermal_metrics)
                
                # Adjust layout with error handling
                try:
                    plt.tight_layout(pad=3.0)
                except Exception:
                    # If tight_layout fails, use subplots_adjust as fallback
                    plt.subplots_adjust(hspace=0.4, wspace=0.3, top=0.9, bottom=0.1)
                
                # Save chart
                saved_files = self.base_visualizer.save_chart(fig, "thermal_performance")
                
                # Clean up resources
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"Failed to create thermal performance chart: {str(e)}") from e

    def _create_simplified_comfort_chart(self, ax, thermal_metrics: ThermalPerformanceMetrics):
        """创建简化的舒适度分析图"""
        try:
            # 舒适度数据
            comfort_hours = thermal_metrics.comfort_hours
            total_hours = 8760  # 一年总小时数
            comfort_ratio = comfort_hours / total_hours * 100

            # 创建饼图显示舒适度比例
            labels = ['舒适时间', '不舒适时间']
            values = [comfort_ratio, 100 - comfort_ratio]
            colors = ['#2E8B57', '#CD5C5C']  # 绿色和红色

            wedges, texts, autotexts = ax.pie(values, labels=labels, colors=colors,
                                            autopct='%1.1f%%', startangle=90,
                                            textprops={'fontsize': self.base_visualizer.chart_config.current_font.tick_size})

            # 设置标题
            ax.set_title('年度舒适度分析', fontsize=self.base_visualizer.chart_config.current_font.title_size, pad=20)

            # 添加舒适小时数信息
            ax.text(0.5, -1.3, f'舒适小时数: {comfort_hours:.0f} 小时',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=self.base_visualizer.chart_config.current_font.label_size,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))

        except Exception as e:
            self.logger.error(f"创建舒适度图表失败: {str(e)}")
            ax.text(0.5, 0.5, '图表生成失败', ha='center', va='center', transform=ax.transAxes)

    def _create_simplified_thermal_metrics_chart(self, ax, thermal_metrics: ThermalPerformanceMetrics):
        """创建简化的热工性能指标图"""
        try:
            # 热工性能指标
            metrics = {
                '传热系数': thermal_metrics.thermal_transmittance,
                '热桥效应': thermal_metrics.thermal_bridge_effect,
                '热惰性': thermal_metrics.thermal_inertia / 10.0,  # 归一化到0-1范围
            }

            # 创建条形图
            categories = list(metrics.keys())
            values = list(metrics.values())

            colors = self.base_visualizer.chart_config.get_color_sequence(len(categories))
            bars = ax.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=1.5)

            # 设置标题和标签
            ax.set_title('热工性能指标', fontsize=self.base_visualizer.chart_config.current_font.title_size, pad=20)
            ax.set_ylabel('指标值', fontsize=self.base_visualizer.chart_config.current_font.label_size)

            # 设置刻度
            ax.tick_params(axis='x', rotation=0, labelsize=self.base_visualizer.chart_config.current_font.tick_size)
            ax.tick_params(axis='y', labelsize=self.base_visualizer.chart_config.current_font.tick_size)

            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{value:.3f}', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.tick_size)

            # 添加网格
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')
            ax.set_axisbelow(True)

            # 设置y轴范围
            ax.set_ylim(0, max(values) * 1.2)

        except Exception as e:
            self.logger.error(f"创建热工指标图表失败: {str(e)}")
            ax.text(0.5, 0.5, '图表生成失败', ha='center', va='center', transform=ax.transAxes)

    def _create_comfort_zone_chart(self, ax, thermal_metrics: ThermalPerformanceMetrics):
        """Create comfort zone analysis chart"""
        try:
            # Temperature range data
            min_temp, max_temp = thermal_metrics.operative_temperature_range
            comfort_hours = thermal_metrics.comfort_hours
            total_hours = 8760  # Total hours in a year
            
            # Create temperature distribution data
            temperatures = np.linspace(min_temp - 2, max_temp + 2, 100)
            
            # Simulate temperature probability density
            from scipy import stats
            temp_distribution = stats.norm(loc=(min_temp + max_temp) / 2, 
                                         scale=(max_temp - min_temp) / 4)
            density = temp_distribution.pdf(temperatures)
            
            # Plot temperature distribution curve
            ax.plot(temperatures, density, linewidth=3, 
                   color=self.base_visualizer.chart_config.current_palette.primary,
                   label='Temperature Distribution')
            
            # Fill comfort zone
            comfort_min, comfort_max = 20, 26  # Comfort temperature range
            comfort_mask = (temperatures >= comfort_min) & (temperatures <= comfort_max)
            ax.fill_between(temperatures, 0, density, where=comfort_mask, 
                           alpha=0.3, color=self.base_visualizer.chart_config.current_palette.success,
                           label='Comfort Zone')
            
            # Fill overheating zone
            hot_mask = temperatures > comfort_max
            ax.fill_between(temperatures, 0, density, where=hot_mask,
                           alpha=0.3, color=self.base_visualizer.chart_config.current_palette.error,
                           label='Overheating Zone')
            
            # Fill overcooling zone
            cold_mask = temperatures < comfort_min
            ax.fill_between(temperatures, 0, density, where=cold_mask,
                           alpha=0.3, color=self.base_visualizer.chart_config.current_palette.warning,
                           label='Overcooling Zone')
            
            # Add comfort percentage annotation
            comfort_percentage = (comfort_hours / total_hours) * 100
            ax.text(0.95, 0.95, f'Comfort Hours: {comfort_hours:.0f}h\nComfort Rate: {comfort_percentage:.1f}%',
                   transform=ax.transAxes, ha='right', va='top',
                   fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                   bbox=dict(boxstyle="round,pad=0.5",
                           facecolor=self.base_visualizer.chart_config.current_palette.background,
                           alpha=0.9, edgecolor=self.base_visualizer.chart_config.current_palette.grid))
            
            # Set title and labels
            title_zh = "Indoor Temperature Comfort Analysis"
            title_en = "Indoor Temperature Comfort Analysis"
            xlabel_zh = "Indoor Temperature (°C)"
            xlabel_en = "Indoor Temperature (°C)"
            ylabel_zh = "Probability Density"
            ylabel_en = "Probability Density"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # Add legend
            ax.legend(fontsize=self.base_visualizer.chart_config.current_font.legend_size - 1)
            
        except Exception as e:
            self.logger.error(f"Failed to create comfort zone analysis chart: {str(e)}")
    
    def _create_seasonal_thermal_chart(self, ax, thermal_metrics: ThermalPerformanceMetrics):
        """Create seasonal thermal performance chart"""
        try:
            # Season data
            seasons = ['Spring', 'Summer', 'Autumn', 'Winter']
            seasons_en = ['Spring', 'Summer', 'Autumn', 'Winter']
            
            # Get seasonal performance data
            if hasattr(thermal_metrics, 'seasonal_performance') and thermal_metrics.seasonal_performance:
                seasonal_data = thermal_metrics.seasonal_performance
                performance_values = [
                    seasonal_data.get('spring', 0.8),
                    seasonal_data.get('summer', 0.7),
                    seasonal_data.get('autumn', 0.8),
                    seasonal_data.get('winter', 0.6)
                ]
            else:
                # 模拟季节性能数据
                performance_values = [0.8, 0.7, 0.8, 0.6]
            
            # Convert to percentage
            performance_percentages = [p * 100 for p in performance_values]
            
            # Create polar coordinate subplot
            theta = np.linspace(0, 2 * np.pi, len(seasons), endpoint=False)
            theta = np.concatenate((theta, [theta[0]]))  # Close the loop
            values = performance_values + [performance_values[0]]  # Close the loop
            
            # Plot radar chart
            ax.clear()  # 清除原有坐标系
            
            # 重新创建为极坐标
            ax.remove()
            ax = plt.subplot(2, 2, 2, projection='polar')
            
            # 绘制数据线
            line = ax.plot(theta, values, 'o-', linewidth=2, 
                          color=self.base_visualizer.chart_config.current_palette.primary,
                          markersize=8, markerfacecolor=self.base_visualizer.chart_config.current_palette.accent)
            
            # Fill area
            ax.fill(theta, values, alpha=0.25, 
                   color=self.base_visualizer.chart_config.current_palette.primary)
            
            # Set labels
            ax.set_xticks(theta[:-1])
            if self.base_visualizer.chart_config.bilingual_mode:
                labels = [f"{zh}\n{en}" for zh, en in zip(seasons, seasons_en)]
            else:
                labels = seasons
            ax.set_xticklabels(labels, fontsize=self.base_visualizer.chart_config.current_font.tick_size)
            
            # Set radial range
            ax.set_ylim(0, 1.0)
            ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
            ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
            
            # Set title
            title_zh = "Seasonal Thermal Performance"
            title_en = "Seasonal Thermal Performance"
            title = self.base_visualizer.chart_config.get_bilingual_text(title_zh, title_en)
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size,
                        fontweight='bold', pad=20)
            
            # Add grid
            ax.grid(True, alpha=0.3)
            
        except Exception as e:
            self.logger.error(f"Failed to create seasonal thermal performance chart: {str(e)}")
    
    def _create_thermal_radar_chart(self, ax, thermal_metrics: ThermalPerformanceMetrics):
        """Create thermal parameters radar chart"""
        try:
            # Thermal parameters
            parameters = ['U-Value', 'Thermal Bridge', 'Thermal Inertia', 'Comfort Hours', 'Temperature Stability']
            parameters_en = ['U-Value', 'Thermal Bridge', 'Thermal Inertia', 'Comfort Hours', 'Temperature Stability']
            
            # Normalize parameter values (convert to 0-1 scores)
            u_value_score = max(0, min(1, 1 - thermal_metrics.thermal_transmittance / 3.0))  # Lower U-value is better
            bridge_score = max(0, min(1, 1 - thermal_metrics.thermal_bridge_effect))  # Lower thermal bridge is better
            inertia_score = max(0, min(1, thermal_metrics.thermal_inertia / 12))  # Moderate thermal inertia is best
            comfort_score = max(0, min(1, thermal_metrics.comfort_hours / 8760))  # More comfort hours are better
            stability_score = 0.8  # Simulated temperature stability score
            
            values = [u_value_score, bridge_score, inertia_score, comfort_score, stability_score]
            
            # 创建极坐标图
            ax.clear()
            ax.remove()
            ax = plt.subplot(2, 2, 3, projection='polar')
            
            # 角度
            theta = np.linspace(0, 2 * np.pi, len(parameters), endpoint=False)
            theta = np.concatenate((theta, [theta[0]]))  # Close the loop
            values_closed = values + [values[0]]  # 闭合
            
            # Plot radar chart
            ax.plot(theta, values_closed, 'o-', linewidth=3,
                   color=self.base_visualizer.chart_config.current_palette.secondary,
                   markersize=8, markerfacecolor=self.base_visualizer.chart_config.current_palette.accent)
            
            # Fill area
            ax.fill(theta, values_closed, alpha=0.25,
                   color=self.base_visualizer.chart_config.current_palette.secondary)
            
            # Set labels
            ax.set_xticks(theta[:-1])
            if self.base_visualizer.chart_config.bilingual_mode:
                labels = [f"{zh}\n{en}" for zh, en in zip(parameters, parameters_en)]
            else:
                labels = parameters
            ax.set_xticklabels(labels, fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set radial range
            ax.set_ylim(0, 1.0)
            ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
            ax.set_yticklabels(['Poor', 'Fair', 'Good', 'Very Good', 'Excellent'])
            
            # Set title
            title_zh = "Thermal Parameter Comprehensive Evaluation"
            title_en = "Thermal Parameters Assessment"
            title = self.base_visualizer.chart_config.get_bilingual_text(title_zh, title_en)
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size,
                        fontweight='bold', pad=20)
            
            # 添加平均分标注
            avg_score = np.mean(values)
            ax.text(0, 1.1, f'Overall Score: {avg_score:.2f}', transform=ax.transAxes,
                   ha='center', va='center',
                   fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                   bbox=dict(boxstyle="round,pad=0.3",
                           facecolor=self.base_visualizer.chart_config.current_palette.background,
                           alpha=0.9))
            
        except Exception as e:
            self.logger.error(f"Failed to create thermal parameters radar chart: {str(e)}")
    
    def _create_temperature_distribution_chart(self, ax, thermal_metrics: ThermalPerformanceMetrics):
        """Create temperature distribution box plot"""
        try:
            # 生成模拟温度数据
            min_temp, max_temp = thermal_metrics.operative_temperature_range
            
            # 四个季节的温度分布
            seasons = ['Spring', 'Summer', 'Autumn', 'Winter']
            
            # 模拟各季节温度数据
            np.random.seed(42)
            spring_temps = np.random.normal(22, 2, 100)
            summer_temps = np.random.normal(25, 3, 100)
            autumn_temps = np.random.normal(21, 2.5, 100)
            winter_temps = np.random.normal(20, 2, 100)
            
            temp_data = [spring_temps, summer_temps, autumn_temps, winter_temps]
            
            # 创建箱线图
            box_plot = ax.boxplot(temp_data, labels=seasons, patch_artist=True,
                                showmeans=True, meanline=True,
                                boxprops=dict(alpha=0.7),
                                medianprops=dict(color='white', linewidth=2),
                                meanprops=dict(color=self.base_visualizer.chart_config.current_palette.accent, linewidth=2),
                                whiskerprops=dict(color=self.base_visualizer.chart_config.current_palette.text),
                                capprops=dict(color=self.base_visualizer.chart_config.current_palette.text))
            
            # 为每个箱子设置不同颜色
            colors = self.base_visualizer.chart_config.get_color_sequence(4)
            for patch, color in zip(box_plot['boxes'], colors):
                patch.set_facecolor(color)
            
            # 添加舒适区间带
            comfort_min, comfort_max = 20, 26
            ax.axhspan(comfort_min, comfort_max, alpha=0.2, 
                      color=self.base_visualizer.chart_config.current_palette.success,
                      label='舒适区间')
            
            # Set title and labels
            title_zh = "Seasonal Indoor Temperature Distribution"
            title_en = "Seasonal Indoor Temperature Distribution"
            xlabel_zh = "Season"
            xlabel_en = "Season"
            ylabel_zh = "Indoor Temperature (°C)"
            ylabel_en = "Indoor Temperature (°C)"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # 添加统计信息
            overall_temps = np.concatenate(temp_data)
            stats_text = f'全年统计:\n平均: {np.mean(overall_temps):.1f}°C\n标准差: {np.std(overall_temps):.1f}°C'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes,
                   ha='left', va='top',
                   fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                   bbox=dict(boxstyle="round,pad=0.3",
                           facecolor=self.base_visualizer.chart_config.current_palette.background,
                           alpha=0.9))
            
            # Add legend
            ax.legend()
            
        except Exception as e:
            self.logger.error(f"创建温度分布箱线图失败: {str(e)}")
    
    @handle_exception
    def create_cost_analysis_chart(self, visualization_data: VisualizationData) -> str:
        """
        创建成本分析可视化图表
        
        Args:
            visualization_data: 可视化数据
            
        Returns:
            保存的图表文件路径
        """
        with LogContext("成本分析图表创建", self.logger):
            try:
                cost_breakdown = visualization_data.cost_breakdown
                
                if not cost_breakdown:
                    raise VisualizationError("没有成本分析数据")
                
                # 简化为1x2布局，只保留最重要的图表
                fig, axes = self.base_visualizer.create_subplot_figure(1, 2,
                    figsize=self.base_visualizer.chart_config.get_figure_size('medium'))

                # 确保axes是数组
                if not hasattr(axes, '__len__'):
                    axes = [axes]
                elif len(axes.shape) > 1:
                    axes = axes.flatten()

                # 子图1: 成本构成分析
                self._create_simplified_cost_breakdown_chart(axes[0], cost_breakdown)

                # 子图2: 成本效益分析
                self._create_simplified_cost_benefit_chart(axes[1], cost_breakdown)
                
                # Adjust layout with error handling - tight_layout often fails with complex subplots
                try:
                    plt.tight_layout(pad=3.0)
                except Exception as e:
                    self.logger.debug(f"tight_layout failed, using subplots_adjust: {str(e)}")
                    # If tight_layout fails, use subplots_adjust as fallback
                    plt.subplots_adjust(hspace=0.4, wspace=0.3, top=0.9, bottom=0.1, left=0.1, right=0.95)
                
                # Save chart
                saved_files = self.base_visualizer.save_chart(fig, "cost_analysis")
                
                # Clean up resources
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"成本分析图表创建失败: {str(e)}") from e

    def _create_simplified_cost_breakdown_chart(self, ax, cost_breakdown: CostBreakdown):
        """创建简化的成本构成图表"""
        try:
            # 成本构成数据
            categories = ['材料成本', '人工成本', '设备成本', '维护成本']
            values = [
                cost_breakdown.material_cost,
                cost_breakdown.labor_cost,
                cost_breakdown.equipment_cost,
                cost_breakdown.maintenance_cost
            ]

            # 过滤零值
            non_zero_data = [(cat, val) for cat, val in zip(categories, values) if val > 0]
            if not non_zero_data:
                ax.text(0.5, 0.5, '无成本数据', ha='center', va='center', transform=ax.transAxes,
                       fontsize=self.base_visualizer.chart_config.current_font.label_size)
                ax.set_title('成本构成分析', fontsize=self.base_visualizer.chart_config.current_font.title_size)
                return

            categories, values = zip(*non_zero_data)

            # 获取颜色
            colors = self.base_visualizer.chart_config.get_color_sequence(len(values))

            # 创建条形图
            bars = ax.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=1.5)

            # 设置标题和标签
            ax.set_title('成本构成分析', fontsize=self.base_visualizer.chart_config.current_font.title_size, pad=20)
            ax.set_ylabel('成本 (元)', fontsize=self.base_visualizer.chart_config.current_font.label_size)

            # 设置刻度
            ax.tick_params(axis='x', rotation=45, labelsize=self.base_visualizer.chart_config.current_font.tick_size)
            ax.tick_params(axis='y', labelsize=self.base_visualizer.chart_config.current_font.tick_size)

            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(values) * 0.01,
                       f'{value:.0f}', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.tick_size)

            # 添加网格
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')
            ax.set_axisbelow(True)

            # 添加总成本信息
            total_cost = sum(values)
            ax.text(0.5, -0.15, f'总成本: {total_cost:.0f} 元',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=self.base_visualizer.chart_config.current_font.label_size,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.7))

        except Exception as e:
            self.logger.error(f"创建成本构成图表失败: {str(e)}")
            ax.text(0.5, 0.5, '图表生成失败', ha='center', va='center', transform=ax.transAxes)

    def _create_simplified_cost_benefit_chart(self, ax, cost_breakdown: CostBreakdown):
        """创建简化的成本效益分析图表"""
        try:
            # 成本效益数据
            initial_cost = cost_breakdown.total_initial_cost
            lifecycle_cost = cost_breakdown.lifecycle_cost
            cost_per_area = cost_breakdown.cost_per_area

            # 创建对比图
            categories = ['初始成本', '生命周期成本']
            values = [initial_cost, lifecycle_cost]
            colors = ['#FF6B6B', '#4ECDC4']

            bars = ax.bar(categories, values, color=colors, alpha=0.8, edgecolor='white', linewidth=1.5)

            # 设置标题和标签
            ax.set_title('成本效益分析', fontsize=self.base_visualizer.chart_config.current_font.title_size, pad=20)
            ax.set_ylabel('成本 (元)', fontsize=self.base_visualizer.chart_config.current_font.label_size)

            # 设置刻度
            ax.tick_params(axis='x', rotation=0, labelsize=self.base_visualizer.chart_config.current_font.tick_size)
            ax.tick_params(axis='y', labelsize=self.base_visualizer.chart_config.current_font.tick_size)

            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(values) * 0.01,
                       f'{value:.0f}', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.tick_size)

            # 添加网格
            ax.grid(True, alpha=0.3, linestyle='--', axis='y')
            ax.set_axisbelow(True)

            # 添加单位面积成本信息
            ax.text(0.5, -0.15, f'单位面积成本: {cost_per_area:.0f} 元/m²',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=self.base_visualizer.chart_config.current_font.label_size,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='lightyellow', alpha=0.7))

        except Exception as e:
            self.logger.error(f"创建成本效益图表失败: {str(e)}")
            ax.text(0.5, 0.5, '图表生成失败', ha='center', va='center', transform=ax.transAxes)

    def _create_cost_waterfall_chart(self, ax, cost_breakdown: CostBreakdown):
        """创建成本构成瀑布图"""
        try:
            # 成本组成
            categories = ['Material Cost', 'Labor Cost', 'Equipment Cost', 'Other Costs', 'Total Cost']
            values = [
                cost_breakdown.material_cost,
                cost_breakdown.labor_cost,
                cost_breakdown.equipment_cost,
                cost_breakdown.total_initial_cost - cost_breakdown.material_cost - 
                cost_breakdown.labor_cost - cost_breakdown.equipment_cost,
                cost_breakdown.total_initial_cost
            ]
            
            # 计算累积值
            cumulative = [0]
            for i in range(len(values) - 1):
                cumulative.append(cumulative[-1] + values[i])
            cumulative.append(0)  # 总成本从0开始
            
            # 颜色设置
            colors = [self.base_visualizer.chart_config.current_palette.primary] * (len(values) - 1)
            colors.append(self.base_visualizer.chart_config.current_palette.accent)  # 总成本使用特殊颜色
            
            # 创建瀑布图
            for i, (cat, val, cum, color) in enumerate(zip(categories, values, cumulative, colors)):
                if i < len(categories) - 1:  # 非总成本
                    ax.bar(cat, val, bottom=cum, color=color, alpha=0.8, edgecolor='white', linewidth=1)
                    # 添加连接线
                    if i < len(categories) - 2:
                        ax.plot([i + 0.4, i + 1.6], [cum + val, cum + val], 
                               color=self.base_visualizer.chart_config.current_palette.neutral,
                               linestyle='--', alpha=0.5)
                else:  # 总成本
                    ax.bar(cat, val, color=color, alpha=0.8, edgecolor='white', linewidth=2)
            
            # Add value annotations
            for i, (cat, val, cum) in enumerate(zip(categories, values, cumulative)):
                if i < len(categories) - 1:
                    y_pos = cum + val / 2
                else:
                    y_pos = val / 2
                
                ax.text(i, y_pos, f'{val:,.0f}', ha='center', va='center',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                       fontweight='bold', color='white')
            
            # Set title and labels
            title_zh = "Renovation Cost Composition Analysis"
            title_en = "Renovation Cost Breakdown"
            xlabel_zh = "Cost Category"
            xlabel_en = "Cost Category"
            ylabel_zh = "Cost (CNY)"
            ylabel_en = "Cost (CNY)"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # 自定义Y轴格式
            self.base_visualizer.customize_axis_format(ax, y_format='currency')
            
            # 旋转x轴标签
            plt.setp(ax.get_xticklabels(), rotation=45, ha='right')
            
        except Exception as e:
            self.logger.error(f"创建成本构成瀑布图失败: {str(e)}")
    
    def _create_lifecycle_cost_chart(self, ax, cost_breakdown: CostBreakdown):
        """创建生命周期成本分析图"""
        try:
            # 时间轴（25年）
            years = list(range(0, 26))
            
            # 初始投资
            initial_cost = cost_breakdown.total_initial_cost
            
            # 年维护成本
            annual_maintenance = cost_breakdown.maintenance_cost
            
            # 计算累积成本
            cumulative_costs = [initial_cost]  # 第0年
            for year in range(1, 26):
                cumulative_cost = initial_cost + annual_maintenance * year
                cumulative_costs.append(cumulative_cost)
            
            # 计算年度成本
            annual_costs = [initial_cost] + [annual_maintenance] * 25
            
            # 创建组合图
            # 柱状图显示年度成本
            bars = ax.bar(years[1:], annual_costs[1:], alpha=0.6, 
                         color=self.base_visualizer.chart_config.current_palette.secondary,
                         label='Annual Maintenance Cost')
            
            # 初始投资单独显示
            ax.bar([0], [initial_cost], alpha=0.8,
                  color=self.base_visualizer.chart_config.current_palette.primary,
                  label='Initial Investment')
            
            # 创建第二个y轴显示累积成本
            ax2 = ax.twinx()
            line = ax2.plot(years, cumulative_costs, 'o-', linewidth=3, markersize=4,
                           color=self.base_visualizer.chart_config.current_palette.accent,
                           label='Cumulative Cost')
            
            # 设置第一个y轴
            ax.set_xlabel(self.base_visualizer.chart_config.get_bilingual_text("年份", "Year"),
                         fontsize=self.base_visualizer.chart_config.current_font.label_size)
            ax.set_ylabel(self.base_visualizer.chart_config.get_bilingual_text("年度成本 (元)", "Annual Cost (CNY)"),
                         fontsize=self.base_visualizer.chart_config.current_font.label_size,
                         color=self.base_visualizer.chart_config.current_palette.secondary)
            
            # 设置第二个y轴
            ax2.set_ylabel(self.base_visualizer.chart_config.get_bilingual_text("累积成本 (元)", "Cumulative Cost (CNY)"),
                          fontsize=self.base_visualizer.chart_config.current_font.label_size,
                          color=self.base_visualizer.chart_config.current_palette.accent)
            
            # Set title
            title_zh = "Life Cycle Cost Analysis"
            title_en = "Life Cycle Cost Analysis"
            title = self.base_visualizer.chart_config.get_bilingual_text(title_zh, title_en)
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size,
                        fontweight='bold', pad=20)
            
            # 添加重要时间点标注
            milestone_years = [5, 10, 15, 20, 25]
            for year in milestone_years:
                cost = cumulative_costs[year]
                ax2.annotate(f'Year {year}\n{cost:,.0f}', 
                           xy=(year, cost), xytext=(year, cost + initial_cost * 0.1),
                           arrowprops=dict(arrowstyle='->', alpha=0.7),
                           fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 1,
                           ha='center')
            
            # 合并图例
            lines1, labels1 = ax.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
            
        except Exception as e:
            self.logger.error(f"创建生命周期成本分析图失败: {str(e)}")
    
    def _create_cost_benefit_chart(self, ax, cost_breakdown: CostBreakdown):
        """创建成本效益分析图"""
        try:
            # 基于真实能耗数据计算节能收益
            years = list(range(1, 26))
            
            # 获取能源节约数据，使用安全的属性访问
            energy_savings = getattr(cost_breakdown, 'energy_savings', 0)
            if energy_savings <= 0:
                # 如果没有能源节约数据，基于改造成本估算
                energy_savings = cost_breakdown.total_initial_cost * 0.15  # 假设15%的年回报率
            
            # 计算年节能收益
            annual_savings = energy_savings
            cumulative_savings = [annual_savings * year for year in years]
            
            # 累积维护成本
            maintenance_cost = getattr(cost_breakdown, 'maintenance_cost', cost_breakdown.total_initial_cost * 0.02)
            if maintenance_cost <= 0:
                maintenance_cost = cost_breakdown.total_initial_cost * 0.02  # 假设2%的年维护成本
            
            cumulative_maintenance = [maintenance_cost * year for year in years]
            
            # 净收益 = 累积节能收益 - 累积维护成本
            net_benefits = [savings - maintenance for savings, maintenance 
                          in zip(cumulative_savings, cumulative_maintenance)]
            
            # 投资回收 = 净收益 - 初始投资
            initial_investment = cost_breakdown.total_initial_cost
            investment_recovery = [benefit - initial_investment for benefit in net_benefits]
            
            # 创建面积图
            ax.fill_between(years, 0, cumulative_savings, alpha=0.3, 
                           color=self.base_visualizer.chart_config.current_palette.success,
                           label='Cumulative Energy Savings')
            
            ax.fill_between(years, 0, cumulative_maintenance, alpha=0.3,
                           color=self.base_visualizer.chart_config.current_palette.warning,
                           label='Cumulative Maintenance Cost')
            
            # 绘制净收益曲线
            ax.plot(years, net_benefits, linewidth=3,
                   color=self.base_visualizer.chart_config.current_palette.primary,
                   label='Net Benefits')
            
            # 绘制投资回收曲线
            ax.plot(years, investment_recovery, linewidth=3, linestyle='--',
                   color=self.base_visualizer.chart_config.current_palette.accent,
                   label='Investment Recovery')
            
            # 添加回收期标注
            payback_year = None
            for i, recovery in enumerate(investment_recovery):
                if recovery > 0:
                    payback_year = years[i]
                    break
            
            if payback_year:
                ax.axvline(x=payback_year, color=self.base_visualizer.chart_config.current_palette.error,
                          linestyle=':', linewidth=2, alpha=0.7)
                ax.text(payback_year + 0.5, max(net_benefits) * 0.5, 
                       f'Payback Period\n{payback_year} years',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                       bbox=dict(boxstyle="round,pad=0.3",
                               facecolor=self.base_visualizer.chart_config.current_palette.background,
                               alpha=0.9))
            
            # 添加零线
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            
            # Set title and labels
            title_zh = "Cost-Benefit Analysis"
            title_en = "Cost-Benefit Analysis"
            xlabel_zh = "Year"
            xlabel_en = "Year"
            ylabel_zh = "Cumulative Amount (CNY)"
            ylabel_en = "Cumulative Amount (CNY)"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # 设置Y轴格式
            self.base_visualizer.customize_axis_format(ax, y_format='currency')
            
            # Add legend
            ax.legend(fontsize=self.base_visualizer.chart_config.current_font.legend_size - 1)
            
        except Exception as e:
            self.logger.error(f"创建成本效益分析图失败: {str(e)}")
    
    def _create_payback_analysis_chart(self, ax, cost_breakdown: CostBreakdown):
        """创建投资回收期分析图"""
        try:
            # 不同节能水平的回收期分析
            energy_savings = [30, 40, 50, 60, 70]  # kWh/m²/年
            electricity_price = 0.6  # 元/kWh
            # 基于真实立面面积计算建筑面积
            building_area = getattr(cost_breakdown, 'building_area', 1000)  # 使用真实面积或默认1000m²
            
            # 计算回收期
            payback_periods = []
            for saving in energy_savings:
                annual_benefit = saving * electricity_price * building_area
                if annual_benefit > 0:
                    payback = cost_breakdown.total_initial_cost / annual_benefit
                    payback_periods.append(min(payback, 30))  # 最大30年
                else:
                    payback_periods.append(30)
            
            # 创建柱状图
            colors = self.base_visualizer.create_color_gradient(np.array(payback_periods))
            bars = ax.bar(energy_savings, payback_periods, color=colors, alpha=0.8,
                         edgecolor='white', linewidth=1)
            
            # Add value annotations
            for bar, period in zip(bars, payback_periods):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                       f'{period:.1f}年', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                       fontweight='bold')
            
            # 添加基准线
            target_payback = 10  # 目标回收期10年
            ax.axhline(y=target_payback, color=self.base_visualizer.chart_config.current_palette.success,
                      linestyle='--', linewidth=2, alpha=0.7, label=f'目标回收期: {target_payback}年')
            
            acceptable_payback = 15  # 可接受回收期15年
            ax.axhline(y=acceptable_payback, color=self.base_visualizer.chart_config.current_palette.warning,
                      linestyle='--', linewidth=2, alpha=0.7, label=f'可接受回收期: {acceptable_payback}年')
            
            # Set title and labels
            title_zh = "Energy Savings vs. Payback Period"
            title_en = "Energy Savings vs. Payback Period"
            xlabel_zh = "Energy Savings (kWh/m²/year)"
            xlabel_en = "Energy Savings (kWh/m²/year)"
            ylabel_zh = "Payback Period (years)"
            ylabel_en = "Payback Period (years)"
            
            self.base_visualizer.add_title_and_labels(ax, title_zh, xlabel_zh, ylabel_zh,
                                                    title_en, xlabel_en, ylabel_en)
            
            # 添加最优区域标注
            optimal_mask = np.array(payback_periods) <= target_payback
            if any(optimal_mask):
                optimal_savings = [s for s, optimal in zip(energy_savings, optimal_mask) if optimal]
                ax.fill_between([min(optimal_savings) - 2, max(optimal_savings) + 2], 
                               0, target_payback, alpha=0.1,
                               color=self.base_visualizer.chart_config.current_palette.success,
                               label='最优区域')
            
            # Add legend
            ax.legend(fontsize=self.base_visualizer.chart_config.current_font.legend_size - 1)
            
            # 设置y轴范围
            ax.set_ylim(0, max(payback_periods) * 1.1)
            
        except Exception as e:
            self.logger.error(f"创建投资回收期分析图失败: {str(e)}")
    
    @handle_exception
    def create_comprehensive_radar_chart(self, visualization_data: VisualizationData) -> str:
        """
        创建综合性能雷达图
        
        Args:
            visualization_data: 可视化数据
            
        Returns:
            保存的图表文件路径
        """
        with LogContext("综合性能雷达图创建", self.logger):
            try:
                energy_breakdown = visualization_data.energy_breakdown
                thermal_metrics = visualization_data.thermal_metrics
                cost_breakdown = visualization_data.cost_breakdown
                
                # 创建图形
                fig, ax = self.base_visualizer.create_figure(
                    figsize=self.base_visualizer.chart_config.get_figure_size('default')
                )
                
                # 重新创建为极坐标
                ax.remove()
                ax = plt.subplot(111, projection='polar')
                
                # 性能维度
                dimensions = ['Energy Performance', 'Thermal Performance', 'Economic Performance', 'Environmental Impact', 'Technical Feasibility', 'User Satisfaction']
                dimensions_en = ['Energy', 'Thermal', 'Economic', 'Environmental', 'Technical', 'User Satisfaction']
                
                # 获取优化结果数据
                optimization_results = visualization_data.optimization_results
                
                # 计算原方案各维度评分（0-1）
                original_energy = max(0, min(1, 1 - cost_breakdown.baseline_energy_cost / 200))  # 使用基准能耗
                original_thermal = 0.6  # 原方案热工性能（假设值）
                original_economic = max(0, min(1, 1 - cost_breakdown.baseline_energy_cost / 2000))  # 原方案经济性
                original_environmental = original_energy * 0.8 + 0.2
                original_technical = 0.7  # 原方案技术可行性
                original_user = (original_thermal + original_energy) / 2
                
                original_values = [original_energy, original_thermal, original_economic,
                                 original_environmental, original_technical, original_user]
                
                # 计算优化方案各维度评分
                if optimization_results.pareto_solutions:
                    # 获取四个维度的最佳解
                    best_energy = optimization_results.best_energy_solution
                    best_thermal = optimization_results.best_thermal_solution
                    best_cost = optimization_results.best_cost_solution
                    
                    # 能源最优解
                    energy_opt_energy = max(0, min(1, 1 - best_energy.energy_consumption / 200))
                    energy_opt_thermal = max(0, min(1, 1 - best_energy.thermal_performance))
                    energy_opt_economic = max(0, min(1, 1 - best_energy.renovation_cost / 2000))
                    energy_opt_environmental = energy_opt_energy * 0.8 + 0.2
                    energy_opt_technical = 0.9
                    energy_opt_user = (energy_opt_thermal + energy_opt_energy) / 2
                    
                    energy_opt_values = [energy_opt_energy, energy_opt_thermal, energy_opt_economic,
                                       energy_opt_environmental, energy_opt_technical, energy_opt_user]
                    
                    # 热工最优解
                    thermal_opt_energy = max(0, min(1, 1 - best_thermal.energy_consumption / 200))
                    thermal_opt_thermal = max(0, min(1, 1 - best_thermal.thermal_performance))
                    thermal_opt_economic = max(0, min(1, 1 - best_thermal.renovation_cost / 2000))
                    thermal_opt_environmental = thermal_opt_energy * 0.8 + 0.2
                    thermal_opt_technical = 0.9
                    thermal_opt_user = (thermal_opt_thermal + thermal_opt_energy) / 2
                    
                    thermal_opt_values = [thermal_opt_energy, thermal_opt_thermal, thermal_opt_economic,
                                         thermal_opt_environmental, thermal_opt_technical, thermal_opt_user]
                    
                    # 成本最优解
                    cost_opt_energy = max(0, min(1, 1 - best_cost.energy_consumption / 200))
                    cost_opt_thermal = max(0, min(1, 1 - best_cost.thermal_performance))
                    cost_opt_economic = max(0, min(1, 1 - best_cost.renovation_cost / 2000))
                    cost_opt_environmental = cost_opt_energy * 0.8 + 0.2
                    cost_opt_technical = 0.85
                    cost_opt_user = (cost_opt_thermal + cost_opt_energy) / 2
                    
                    cost_opt_values = [cost_opt_energy, cost_opt_thermal, cost_opt_economic,
                                      cost_opt_environmental, cost_opt_technical, cost_opt_user]
                    
                    # 综合最优解（取平均值）
                    comprehensive_values = [
                        (energy_opt_values[i] + thermal_opt_values[i] + cost_opt_values[i]) / 3
                        for i in range(6)
                    ]
                else:
                    # 如果没有优化结果，使用模拟数据
                    energy_opt_values = [0.85, 0.75, 0.65, 0.8, 0.9, 0.8]
                    thermal_opt_values = [0.75, 0.9, 0.7, 0.75, 0.85, 0.85]
                    cost_opt_values = [0.7, 0.65, 0.9, 0.7, 0.8, 0.7]
                    comprehensive_values = [0.8, 0.85, 0.75, 0.8, 0.85, 0.8]
                
                # 角度
                theta = np.linspace(0, 2 * np.pi, len(dimensions), endpoint=False)
                theta = np.concatenate((theta, [theta[0]]))  # Close the loop
                
                # 绘制原方案
                original_closed = original_values + [original_values[0]]
                ax.plot(theta, original_closed, 'o-', linewidth=3,
                       color='#FF6B6B', markersize=8, label='Original Scheme')
                
                # 绘制四个优化方案
                energy_closed = energy_opt_values + [energy_opt_values[0]]
                ax.plot(theta, energy_closed, 's-', linewidth=2.5,
                       color='#4ECDC4', markersize=7, label='Energy Optimized')
                
                thermal_closed = thermal_opt_values + [thermal_opt_values[0]]
                ax.plot(theta, thermal_closed, '^-', linewidth=2.5,
                       color='#45B7D1', markersize=7, label='Thermal Optimized')
                
                cost_closed = cost_opt_values + [cost_opt_values[0]]
                ax.plot(theta, cost_closed, 'v-', linewidth=2.5,
                       color='#96CEB4', markersize=7, label='Cost Optimized')
                
                comprehensive_closed = comprehensive_values + [comprehensive_values[0]]
                ax.plot(theta, comprehensive_closed, 'D-', linewidth=3,
                       color='#FFEAA7', markersize=8, label='Comprehensive Optimized')
                
                # Set labels
                ax.set_xticks(theta[:-1])
                if self.base_visualizer.chart_config.bilingual_mode:
                    labels = [f"{zh}\n{en}" for zh, en in zip(dimensions, dimensions_en)]
                else:
                    labels = dimensions_en
                ax.set_xticklabels(labels, fontsize=self.base_visualizer.chart_config.current_font.tick_size)
                
                # Set radial range和标签
                ax.set_ylim(0, 1.0)
                ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
                ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
                
                # Set title
                title_zh = "Original Scheme vs Four Dimensions Optimization Comparison"
                title_en = "Original vs Four Optimization Dimensions Comparison"
                title = self.base_visualizer.chart_config.get_bilingual_text(title_zh, title_en)
                ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size + 2,
                            fontweight='bold', pad=30)
                
                # Add legend
                ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
                
                # Add grid
                ax.grid(True, alpha=0.3)
                
                # Save chart
                saved_files = self.base_visualizer.save_chart(fig, "comprehensive_radar")
                
                # Clean up resources
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"综合性能雷达图创建失败: {str(e)}") from e
    
    def _get_performance_grade(self, score: float) -> str:
        """Get performance grade based on score"""
        if score >= 0.9:
            return "Excellent (A+)"
        elif score >= 0.8:
            return "Very Good (A)"
        elif score >= 0.7:
            return "Good (B)"
        elif score >= 0.6:
            return "Fair (C)"
        else:
            return "Needs Improvement (D)"


def create_performance_charts() -> PerformanceCharts:
    """
    创建性能分析可视化图表实例
    
    Returns:
        配置好的性能分析可视化图表
    """
    return PerformanceCharts()