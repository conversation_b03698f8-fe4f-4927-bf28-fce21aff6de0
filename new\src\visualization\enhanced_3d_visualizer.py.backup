"""
增强的3D可视化器 - 高质量真实感建筑立面渲染
提供专业级建筑可视化效果，摒弃简单立方体形式

核心改进：
1. 真实墙体厚度和材质纹理（砖墙、内墙面）
2. 立体窗户结构（双层玻璃、窗台、洞口）
3. 精细窗框系统（外框、内框、侧面厚度）
4. 真实遮阳板结构（主体、支撑、投影阴影）
5. 专业材质和光影效果
6. 单面墙体精细化展示
7. 修复遮阳板和窗框在可视化中的显示问题
"""

import numpy as np
import matplotlib.pyplot as plt

# 设置matplotlib中文字体支持
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
import matplotlib.colors as mcolors
import os
from typing import Dict, List, Tuple, Any, Optional

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import VisualizationError, handle_exception



def setup_chinese_fonts():
    """设置中文字体"""
    import matplotlib.pyplot as plt
    import matplotlib
    fonts = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
    matplotlib.rcParams['font.sans-serif'] = fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False

def create_chinese_legend(ax, labels, **kwargs):
    """创建中文图例"""
    setup_chinese_fonts()
    return ax.legend(labels, **kwargs)

def set_chinese_labels(ax, xlabel=None, ylabel=None, title=None):
    """设置中文标签"""
    setup_chinese_fonts()
    if xlabel:
        ax.set_xlabel(xlabel, fontproperties='SimHei')
    if ylabel:
        ax.set_ylabel(ylabel, fontproperties='SimHei')
    if title:
        ax.set_title(title, fontproperties='SimHei')


class Enhanced3DVisualizer:
    """
    增强的3D可视化器
    
    功能：
    1. 高质量3D立面渲染
    2. 真实感墙体、窗户、遮阳板和窗框
    3. 材质和光影效果
    4. 只显示发生变化的墙面
    """
    
    def __init__(self):
        """初始化增强3D可视化器 - 专业建筑可视化参数"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # === 真实建筑构件尺寸参数（按中国建筑标准）===
        self.wall_thickness = 0.24      # 标准砖墙厚度24cm
        self.window_depth = 0.18        # 窗户洞口深度18cm
        self.frame_width = 0.50         # 窗框宽度50cm（大幅增加，更明显）
        self.frame_depth = 0.35         # 窗框厚度35cm（大幅增加，更立体）
        self.shading_thickness = 0.25   # 遮阳板厚度25cm（大幅增加）
        self.shading_projection = 1.2   # 遮阳板伸出长度1.2米（合理增加）
        self.window_sill_depth = 0.15   # 窗台深度15cm
        self.window_sill_height = 0.08  # 窗台高度8cm
        
        # 墙体和显示尺寸
        self.wall_width = 12.0    # 墙体宽度12米（更宽展示）
        self.wall_height = 8.0    # 墙体高度8米（更高展示）
        
        # 材质颜色定义（大幅加深颜色，提高对比度）
        self.colors = {
            'wall_outer': '#A0A0A0',      # 外墙面 - 深灰色（大幅加深）
            'wall_inner': '#C0C0C0',      # 内墙面 - 中灰色（大幅加深）
            'wall_side': '#808080',       # 墙体侧面 - 更深灰色（大幅加深）
            'brick_line': '#606060',      # 砖缝线 - 深灰色（大幅加深）
            'window_glass': '#1E3A8A',    # 窗户玻璃 - 深蓝色（大幅加深）
            'window_frame': '#4A2C17',    # 窗框 - 深棕色（大幅加深）
            'window_sill': '#707070',     # 窗台 - 深灰色（大幅加深）
            'shading_main': '#0F5132',    # 遮阳板主体 - 深绿色（大幅加深）
            'shading_support': '#4A2C17', # 遮阳板支撑 - 深棕色（大幅加深）
            'door': '#4A2C17'             # 门 - 深棕色（大幅加深）
        }
        
        self.logger.info("增强3D可视化器初始化完成 - 专业建筑渲染模式")
    
    @handle_exception
    def create_enhanced_3d_facade(self, facade_elements, selected_solutions, output_path=None):
        """
        创建增强的3D立面可视化
        
        Args:
            facade_elements: 立面元素数据
            selected_solutions: 选定的解决方案
            output_path: 输出路径
            
        Returns:
            保存的图表文件路径
        """
        with LogContext("增强3D立面可视化创建", self.logger):
            try:
                fig = plt.figure(figsize=(20, 15))
                
                # 创建2x2子图布局，只显示发生变化的墙面
                gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.2)
                
                fig.suptitle('专业建筑立面3D可视化 - 真实墙体、窗户、遮阳板与窗框系统\nProfessional 3D Facade Visualization - Realistic Walls, Windows, Shading & Frame Systems', 
                            fontsize=16, fontweight='bold', y=0.96)
                
                # 解决方案名称
                solution_names = ['Original Design\n原始设计', 
                                'Energy Optimized\n能耗优化', 
                                'Thermal Optimized\n热工优化', 
                                'Comprehensive Optimized\n综合优化']
                
                # 处理选定解数据
                solutions_list = self._process_selected_solutions(selected_solutions)
                
                positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
                
                for i, (pos, name) in enumerate(zip(positions, solution_names)):
                    ax = fig.add_subplot(gs[pos[0], pos[1]], projection='3d')
                    
                    # 绘制增强的3D立面
                    if i == 0:
                        # 原始设计
                        self._draw_enhanced_3d_facade(ax, facade_elements, None)
                    else:
                        # 优化方案
                        solution = solutions_list[i-1] if i-1 < len(solutions_list) else None
                        self._draw_enhanced_3d_facade(ax, facade_elements, solution)
                    
                    # 设置子图标题
                    ax.set_title(name, fontsize=14, fontweight='bold', pad=20)
                    
                    # 设置真实的3D视角
                    self._setup_realistic_3d_view(ax)
                
                plt.tight_layout()
                
                # 保存图表
                if output_path is None:
                    output_path = 'outputs/charts/enhanced_3d_facade.png'
                
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                           facecolor='white', edgecolor='none')
                
                # 同时保存高质量PDF版本
                pdf_path = output_path.replace('.png', '.pdf')
                plt.savefig(pdf_path, format='pdf', dpi=300, bbox_inches='tight', 
                           facecolor='white')
                
                plt.close()
                
                self.logger.info(f"增强3D立面可视化已生成: {output_path}")
                return output_path
                
            except Exception as e:
                raise VisualizationError(f"增强3D立面可视化创建失败: {str(e)}") from e
    
    def _process_selected_solutions(self, selected_solutions):
        """处理选定解数据结构 - 修复数据传递问题"""
        try:
            solutions_list = []
            
            if selected_solutions:
                self.logger.info(f"处理选定解数据，类型: {type(selected_solutions)}")
                
                if isinstance(selected_solutions, dict):
                    # 字典格式：{'energy_best': solution, 'thermal_best': solution, ...}
                    keys = ['energy_best', 'thermal_best', 'comprehensive_best']
                    for key in keys:
                        solution = selected_solutions.get(key, None)
                        if solution:
                            self.logger.debug(f"找到解决方案 {key}: {type(solution)}")
                            # 如果是ObjectiveResults，尝试获取个体数据
                            if hasattr(solution, 'individual_id') and hasattr(solution, 'auxiliary_metrics'):
                                # 从auxiliary_metrics中提取窗户类型数据
                                aux_data = solution.auxiliary_metrics
                                if 'window_types' in aux_data:
                                    self.logger.debug(f"解决方案 {key} 包含窗户类型数据: {aux_data['window_types']}")
                        solutions_list.append(solution)
                        
                elif isinstance(selected_solutions, list):
                    # 列表格式
                    solutions_list = selected_solutions[:3]
                    self.logger.info(f"使用列表格式的解决方案，共{len(solutions_list)}个")
                    
                else:
                    # 单个解决方案
                    solutions_list = [selected_solutions, None, None]
                    self.logger.info("使用单个解决方案")
            
            # 填充缺失的解
            while len(solutions_list) < 3:
                solutions_list.append(None)
            
            # 记录最终处理结果
            valid_solutions = sum(1 for sol in solutions_list if sol is not None)
            self.logger.info(f"解决方案数据处理完成: {valid_solutions}/3 个有效解决方案")
            
            return solutions_list
            
        except Exception as e:
            self.logger.error(f"处理选定解数据失败: {str(e)}")
            return [None, None, None]
    
    def _draw_enhanced_3d_facade(self, ax, facade_elements, solution):
        """
        绘制清晰易读的3D立面可视化
        
        改进重点：
        1. 简化复杂的细节，突出主要结构
        2. 使用清晰的颜色对比和标注
        3. 重点展示窗户、窗框、遮阳板的差异
        4. 提高可读性和理解性
        """
        try:
            # === 1. 绘制简洁清晰的墙体 ===
            self._draw_clear_wall_system(ax)
            
            # === 2. 处理窗户数据 ===
            window_data = self._get_clear_window_data(facade_elements, solution)
            
            # === 3. 绘制清晰的窗户系统 ===
            for i, window_info in enumerate(window_data):
                x, z, w, h, window_type, has_frame, has_shading = window_info
                
                # 绘制基础窗户
                self._draw_clear_window(ax, x, z, w, h, window_type)
                
                # 绘制窗框（如果有）
                if has_frame:
                    self._draw_clear_window_frame(ax, x, z, w, h)
                
                # 绘制遮阳板（如果有）
                if has_shading:
                    self._draw_clear_shading(ax, x, z, w, h)
                
                # 添加窗户标签
                self._add_window_label(ax, x, z, w, h, i, window_type)
            
            # === 4. 添加清晰的图例和说明 ===
            self._add_clear_legend(ax, window_data)
            
        except Exception as e:
            self.logger.error(f"3D立面渲染失败: {str(e)}")
            self._draw_simple_error(ax, str(e))
    
    def _draw_clear_wall_system(self, ax):
        """绘制简洁清晰的墙体系统（修复遮挡问题，提高透明度）"""
        try:
            # 主墙面 - 放在窗户后面，Y坐标为正值，大幅提高透明度
            wall_vertices = [
                [[0, 0.1, 0], [self.wall_width, 0.1, 0], 
                 [self.wall_width, 0.1, self.wall_height], [0, 0.1, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(wall_vertices, alpha=1.0,  # 完全不透明
                                               facecolor=self.colors['wall_outer'], 
                                               edgecolor='#404040', linewidth=2.0))  # 更深的边框
            
            # 墙体厚度边缘 - 在墙体后面，使用更深的颜色
            thickness_vertices = [
                [[0, 0.1, self.wall_height], [self.wall_width, 0.1, self.wall_height], 
                 [self.wall_width, self.wall_thickness, self.wall_height], [0, self.wall_thickness, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(thickness_vertices, alpha=1.0,
                                               facecolor=self.colors['wall_side'], 
                                               edgecolor='#303030', linewidth=2.0))  # 更深的边框
            
            # 墙体左右侧面 - 显示墙体厚度，使用更深的颜色
            for x_pos in [0, self.wall_width]:
                side_vertices = [
                    [[x_pos, 0.1, 0], [x_pos, self.wall_thickness, 0], 
                     [x_pos, self.wall_thickness, self.wall_height], [x_pos, 0.1, self.wall_height]]
                ]
                ax.add_collection3d(Poly3DCollection(side_vertices, alpha=1.0, 
                                                   facecolor=self.colors['wall_inner'], 
                                                   edgecolor='#404040', linewidth=1.5))  # 更深的边框
            
            # 简化网格线，减少视觉干扰，使用更深的颜色
            for i in range(2, int(self.wall_width), 2):  # 每2米一条线
                ax.plot([i, i], [0, 0], [0, self.wall_height], 'k--', alpha=0.6, linewidth=0.8)  # 更深更粗
            for i in range(2, int(self.wall_height), 2):  # 每2米一条线
                ax.plot([0, self.wall_width], [0, 0], [i, i], 'k--', alpha=0.6, linewidth=0.8)  # 更深更粗
                
        except Exception as e:
            self.logger.error(f"绘制清晰墙体失败: {str(e)}")
    
    def _get_clear_window_data(self, facade_elements, solution):
        """获取清晰的窗户数据 - 修复遮阳和窗框显示问题"""
        try:
            window_data = []
            
            # 使用标准的2x3窗户布局，便于理解
            standard_positions = [
                (1.5, 1.0, 2.0, 1.5),   # 左下
                (4.5, 1.0, 2.0, 1.5),   # 中下  
                (7.5, 1.0, 2.0, 1.5),   # 右下
                (1.5, 4.0, 2.0, 1.5),   # 左上
                (4.5, 4.0, 2.0, 1.5),   # 中上
                (7.5, 4.0, 2.0, 1.5),   # 右上
            ]
            
            for i, (x, z, w, h) in enumerate(standard_positions):
                # 初始化窗户参数
                window_type = 0  # 0=普通, 1=窗框, 2=遮阳
                has_frame = False
                has_shading = False
                
                # 从解决方案中提取真实数据
                if solution:
                    # 检查不同的数据结构格式
                    if hasattr(solution, 'window_types') and i < len(solution.window_types):
                        # FacadeIndividual 格式
                        window_type = solution.window_types[i]
                        has_frame = window_type >= 1
                        has_shading = window_type >= 2
                        
                        self.logger.debug(f"窗户 {i}: 类型={window_type}, 窗框={has_frame}, 遮阳={has_shading}")
                        
                    elif isinstance(solution, dict):
                        # 字典格式的解决方案数据
                        window_types = solution.get('window_types', [])
                        if i < len(window_types):
                            window_type = window_types[i]
                            has_frame = window_type >= 1
                            has_shading = window_type >= 2
                            
                    elif hasattr(solution, 'individual_id'):
                        # ObjectiveResults 格式，尝试获取辅助数据
                        aux_metrics = getattr(solution, 'auxiliary_metrics', {})
                        window_types = aux_metrics.get('window_types', [])
                        if i < len(window_types):
                            window_type = window_types[i]
                            has_frame = window_type >= 1
                            has_shading = window_type >= 2
                            
                            self.logger.debug(f"从Objective结果s提取窗户 {i}: 类型={window_type}, "
                                            f"窗框={has_frame}, 遮阳={has_shading}")
                        else:
                            self.logger.warning(f"Objective结果s中窗户类型数据不足: {len(window_types)} < {i+1}")
                    
                    else:
                        # 如果无法识别格式，使用演示数据
                        self.logger.warning(f"无法识别解决方案数据格式: {type(solution)}")
                        if i < 2:  # 前2个有遮阳
                            window_type = 2
                            has_frame = True
                            has_shading = True
                        elif i < 4:  # 中2个有窗框
                            window_type = 1
                            has_frame = True
                            has_shading = False
                        # 后2个普通窗户
                else:
                    # 无解决方案数据时的演示模式：创建清晰的对比
                    if i < 2:  # 前2个有遮阳
                        window_type = 2
                        has_frame = True
                        has_shading = True
                    elif i < 4:  # 中2个有窗框
                        window_type = 1
                        has_frame = True
                        has_shading = False
                    # 后2个普通窗户
                
                window_data.append((x, z, w, h, window_type, has_frame, has_shading))
            
            # 记录窗户数据用于调试
            self.logger.info(f"窗户数据提取完成: 共{len(window_data)}个窗户")
            for i, (x, z, w, h, wtype, frame, shading) in enumerate(window_data):
                self.logger.debug(f"  窗户{i+1}: 位置=({x:.1f},{z:.1f}), 类型={wtype}, 窗框={frame}, 遮阳={shading}")
            
            return window_data
            
        except Exception as e:
            self.logger.error(f"获取窗户数据失败: {str(e)}")
            # 返回默认的演示数据
            return [
                (1.5, 1.0, 2.0, 1.5, 2, True, True),   # 左下 - 遮阳
                (4.5, 1.0, 2.0, 1.5, 2, True, True),   # 中下 - 遮阳
                (7.5, 1.0, 2.0, 1.5, 1, True, False),  # 右下 - 窗框
                (1.5, 4.0, 2.0, 1.5, 1, True, False),  # 左上 - 窗框
                (4.5, 4.0, 2.0, 1.5, 0, False, False), # 中上 - 普通
                (7.5, 4.0, 2.0, 1.5, 0, False, False), # 右上 - 普通
            ]
    
    def _draw_clear_window(self, ax, x, z, w, h, window_type):
        """绘制清晰的基础窗户（加深颜色，提高对比度）"""
        try:
            # 窗户洞口 - 深色边框突出显示
            opening_depth = 0.08  # 增加洞口深度
            opening = [
                [[x, -opening_depth, z], [x+w, -opening_depth, z], 
                 [x+w, -opening_depth, z+h], [x, -opening_depth, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(opening, alpha=1.0, 
                                               facecolor='#1A1A1A', edgecolor='#000000', linewidth=3))  # 更深更粗
            
            # 窗户玻璃 - 根据类型使用更深的颜色
            glass_colors = {
                0: '#1E40AF',  # 普通窗户 - 深蓝色（大幅加深）
                1: '#1E3A8A',  # 窗框窗户 - 更深蓝色（大幅加深）
                2: '#0F5132'   # 遮阳窗户 - 深绿色（大幅加深）
            }
            
            glass_color = glass_colors.get(window_type, '#1E40AF')
            glass = [
                [[x+0.1, -0.03, z+0.1], [x+w-0.1, -0.03, z+0.1], 
                 [x+w-0.1, -0.03, z+h-0.1], [x+0.1, -0.03, z+h-0.1]]
            ]
            ax.add_collection3d(Poly3DCollection(glass, alpha=0.9,  # 提高透明度
                                               facecolor=glass_color, edgecolor='#000040', linewidth=2.5))  # 更深更粗
            
            # 简单的窗户分隔条 - 使用更深的颜色
            # 水平分隔条
            ax.plot([x, x+w], [-0.02, -0.02], [z+h/2, z+h/2], '#E0E0E0', linewidth=4)  # 更粗更明显
            # 垂直分隔条
            ax.plot([x+w/2, x+w/2], [-0.02, -0.02], [z, z+h], '#E0E0E0', linewidth=4)  # 更粗更明显
            
        except Exception as e:
            self.logger.error(f"绘制清晰窗户失败: {str(e)}")
    
    def _draw_clear_window_frame(self, ax, x, z, w, h):
        """绘制清晰的窗框（修复绘图逻辑，大幅增加尺寸和深度）"""
        try:
            # 根据窗框和遮阳数值比例大幅增加尺寸
            frame_width = self.frame_width      # 使用配置的50cm宽度
            frame_depth = self.frame_depth      # 使用配置的35cm深度
            frame_color = self.colors['window_frame']  # 使用更深的颜色
            
            # 修复窗框绘图逻辑：确保窗框完全包围窗户
            # 上框 - 修复坐标逻辑
            top_frame = [
                [[x-frame_width, -frame_depth, z+h], 
                 [x+w+frame_width, -frame_depth, z+h], 
                 [x+w+frame_width, -frame_depth, z+h+frame_width], 
                 [x-frame_width, -frame_depth, z+h+frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(top_frame, alpha=1.0, 
                                               facecolor=frame_color, edgecolor='#2A1810', linewidth=3))
            
            # 下框 - 修复坐标逻辑
            bottom_frame = [
                [[x-frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, -frame_depth, z], 
                 [x-frame_width, -frame_depth, z]]
            ]
            ax.add_collection3d(Poly3DCollection(bottom_frame, alpha=1.0, 
                                               facecolor=frame_color, edgecolor='#2A1810', linewidth=3))
            
            # 左框 - 修复坐标逻辑，确保正确连接
            left_frame = [
                [[x-frame_width, -frame_depth, z-frame_width], 
                 [x, -frame_depth, z-frame_width], 
                 [x, -frame_depth, z+h+frame_width], 
                 [x-frame_width, -frame_depth, z+h+frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(left_frame, alpha=1.0, 
                                               facecolor=frame_color, edgecolor='#2A1810', linewidth=3))
            
            # 右框 - 修复坐标逻辑，确保正确连接
            right_frame = [
                [[x+w, -frame_depth, z-frame_width], 
                 [x+w+frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, -frame_depth, z+h+frame_width], 
                 [x+w, -frame_depth, z+h+frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(right_frame, alpha=1.0, 
                                               facecolor=frame_color, edgecolor='#2A1810', linewidth=3))
            
            # 添加窗框侧面，增强立体感和深度
            frame_sides = [
                # 上框侧面
                [[x-frame_width, -frame_depth, z+h+frame_width], 
                 [x+w+frame_width, -frame_depth, z+h+frame_width], 
                 [x+w+frame_width, self.wall_thickness+0.05, z+h+frame_width], 
                 [x-frame_width, self.wall_thickness+0.05, z+h+frame_width]],
                # 下框侧面
                [[x-frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, self.wall_thickness+0.05, z-frame_width], 
                 [x-frame_width, self.wall_thickness+0.05, z-frame_width]],
                # 左框侧面
                [[x-frame_width, -frame_depth, z-frame_width], 
                 [x-frame_width, self.wall_thickness+0.05, z-frame_width], 
                 [x-frame_width, self.wall_thickness+0.05, z+h+frame_width], 
                 [x-frame_width, -frame_depth, z+h+frame_width]],
                # 右框侧面
                [[x+w+frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, self.wall_thickness+0.05, z-frame_width], 
                 [x+w+frame_width, self.wall_thickness+0.05, z+h+frame_width], 
                 [x+w+frame_width, -frame_depth, z+h+frame_width]]
            ]
            
            for frame_side in frame_sides:
                ax.add_collection3d(Poly3DCollection([frame_side], alpha=1.0, 
                                                   facecolor='#3D2415', edgecolor='#2A1810', linewidth=2))
            
            # 添加窗框内侧面，完善立体结构
            inner_frame_parts = [
                # 上框内侧
                [[x-frame_width, self.wall_thickness+0.05, z+h], 
                 [x+w+frame_width, self.wall_thickness+0.05, z+h], 
                 [x+w+frame_width, self.wall_thickness+0.05, z+h+frame_width], 
                 [x-frame_width, self.wall_thickness+0.05, z+h+frame_width]],
                # 下框内侧
                [[x-frame_width, self.wall_thickness+0.05, z-frame_width], 
                 [x+w+frame_width, self.wall_thickness+0.05, z-frame_width], 
                 [x+w+frame_width, self.wall_thickness+0.05, z], 
                 [x-frame_width, self.wall_thickness+0.05, z]],
                # 左框内侧
                [[x-frame_width, self.wall_thickness+0.05, z], 
                 [x, self.wall_thickness+0.05, z], 
                 [x, self.wall_thickness+0.05, z+h], 
                 [x-frame_width, self.wall_thickness+0.05, z+h]],
                # 右框内侧
                [[x+w, self.wall_thickness+0.05, z], 
                 [x+w+frame_width, self.wall_thickness+0.05, z], 
                 [x+w+frame_width, self.wall_thickness+0.05, z+h], 
                 [x+w, self.wall_thickness+0.05, z+h]]
            ]
            
            for frame_part in inner_frame_parts:
                ax.add_collection3d(Poly3DCollection([frame_part], alpha=1.0, 
                                                   facecolor='#5D3A25', edgecolor='#3D2415', linewidth=1.5))
            
        except Exception as e:
            self.logger.error(f"绘制清晰窗框失败: {str(e)}")
    
    def _draw_clear_shading(self, ax, x, z, w, h):
        """绘制清晰的遮阳板（按照配置比例大幅增加尺寸和深度）"""
        try:
            # 使用配置的遮阳板参数，按比例大幅增加
            shading_projection = self.shading_projection    # 使用配置的1.2米伸出长度
            shading_thickness = self.shading_thickness      # 使用配置的25cm厚度
            shading_color = self.colors['shading_main']     # 使用更深的绿色
            
            # 遮阳板位置（窗户上方）
            shading_z = z + h + 0.20  # 增加与窗户的距离
            
            # 遮阳板伸出窗户边缘的距离（增加到20cm）
            shading_overhang = 0.20
            
            # 遮阳板主体（大幅增加尺寸，更明显的效果）
            shading_main = [
                [[x-shading_overhang, -shading_projection, shading_z], 
                 [x+w+shading_overhang, -shading_projection, shading_z], 
                 [x+w+shading_overhang, self.wall_thickness+0.08, shading_z+shading_thickness], 
                 [x-shading_overhang, self.wall_thickness+0.08, shading_z+shading_thickness]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_main, alpha=1.0,  # 完全不透明
                                               facecolor=shading_color, edgecolor='#0A3D26', linewidth=3))
            
            # 遮阳板前端面（增强立体感）
            shading_front = [
                [[x-shading_overhang, -shading_projection, shading_z], 
                 [x+w+shading_overhang, -shading_projection, shading_z], 
                 [x+w+shading_overhang, -shading_projection, shading_z+shading_thickness], 
                 [x-shading_overhang, -shading_projection, shading_z+shading_thickness]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_front, alpha=1.0, 
                                               facecolor=shading_color, edgecolor='#0A3D26', linewidth=3))
            
            # 遮阳板左右端面（增强立体感）
            for x_pos in [x-shading_overhang, x+w+shading_overhang]:
                end_face = [
                    [[x_pos, -shading_projection, shading_z], 
                     [x_pos, self.wall_thickness+0.08, shading_z], 
                     [x_pos, self.wall_thickness+0.08, shading_z+shading_thickness], 
                     [x_pos, -shading_projection, shading_z+shading_thickness]]
                ]
                ax.add_collection3d(Poly3DCollection(end_face, alpha=1.0, 
                                                   facecolor='#0D4A2E', edgecolor='#0A3D26', linewidth=2))
            
            # 遮阳板底面（增强立体感）
            shading_bottom = [
                [[x-shading_overhang, -shading_projection, shading_z], 
                 [x+w+shading_overhang, -shading_projection, shading_z], 
                 [x+w+shading_overhang, self.wall_thickness+0.08, shading_z], 
                 [x-shading_overhang, self.wall_thickness+0.08, shading_z]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_bottom, alpha=1.0, 
                                               facecolor='#1A5F3F', edgecolor='#0F5132', linewidth=2))
            
            # 大幅改进的支撑结构（更粗更明显）
            support_color = self.colors['shading_support']  # 使用更深的棕色
            support_thickness = 0.12  # 增加支撑厚度
            support_width = 0.15      # 增加支撑宽度
            
            # 左右支撑柱（更粗更明显）
            for support_x in [x+0.2, x+w-0.2]:  # 距离窗户边缘20cm
                # 主支撑柱
                support = [
                    [[support_x, -shading_projection*0.9, shading_z], 
                     [support_x+support_thickness, -shading_projection*0.9, shading_z], 
                     [support_x+support_thickness, self.wall_thickness+0.02, shading_z+shading_thickness], 
                     [support_x, self.wall_thickness+0.02, shading_z+shading_thickness]]
                ]
                ax.add_collection3d(Poly3DCollection(support, alpha=1.0, 
                                                   facecolor=support_color, edgecolor='#2A1810', linewidth=2))
                
                # 支撑连接件（更厚更明显）
                connector = [
                    [[support_x, self.wall_thickness+0.02, shading_z], 
                     [support_x+support_thickness, self.wall_thickness+0.02, shading_z], 
                     [support_x+support_thickness, self.wall_thickness+0.02, shading_z+shading_thickness], 
                     [support_x, self.wall_thickness+0.02, shading_z+shading_thickness]]
                ]
                ax.add_collection3d(Poly3DCollection(connector, alpha=1.0, 
                                                   facecolor='#5D3A25', edgecolor='#3D2415', linewidth=1.5))
                
                # 增加斜撑支撑（增强结构感）
                diagonal_support = [
                    [[support_x+support_thickness/2, -shading_projection*0.6, shading_z], 
                     [support_x+support_thickness/2, -shading_projection*0.6, shading_z+0.05], 
                     [support_x+support_thickness/2, self.wall_thickness, shading_z+shading_thickness-0.05], 
                     [support_x+support_thickness/2, self.wall_thickness, shading_z+shading_thickness]]
                ]
                ax.add_collection3d(Poly3DCollection(diagonal_support, alpha=1.0, 
                                                   facecolor='#6B4226', edgecolor='#4A2C17', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"绘制清晰遮阳板失败: {str(e)}")
    
    def _add_window_label(self, ax, x, z, w, h, index, window_type):
        """添加窗户标签"""
        try:
            type_names = {0: '普通', 1: '窗框', 2: '遮阳'}
            type_name = type_names.get(window_type, '未知')
            
            # 在窗户中心添加标签
            label_x = x + w/2
            label_z = z + h/2
            
            ax.text(label_x, -0.15, label_z, f'{index+1}\n{type_name}', 
                   fontsize=10, ha='center', va='center', weight='bold',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
            
        except Exception as e:
            self.logger.error(f"添加窗户标签失败: {str(e)}")
    
    def _add_clear_legend(self, ax, window_data):
        """添加清晰的图例"""
        try:
            # 统计窗户类型
            type_counts = {0: 0, 1: 0, 2: 0}
            for _, _, _, _, window_type, _, _ in window_data:
                type_counts[window_type] += 1
            
            # 创建图例文本
            legend_text = "窗户类型统计:\n"
            legend_text += f"普通窗户: {type_counts[0]}个\n"
            legend_text += f"带窗框: {type_counts[1]}个\n"
            legend_text += f"带遮阳板: {type_counts[2]}个"
            
            # 在右上角显示图例
            ax.text2D(0.02, 0.98, legend_text, transform=ax.transAxes, 
                     fontsize=10, verticalalignment='top',
                     bbox=dict(boxstyle='round,pad=0.5', facecolor='lightyellow', alpha=0.9))
            
        except Exception as e:
            self.logger.error(f"添加图例失败: {str(e)}")
    
    def _draw_simple_error(self, ax, error_msg):
        """绘制简单错误提示"""
        try:
            ax.text(self.wall_width/2, 0, self.wall_height/2, 
                   f'渲染错误\n{error_msg[:30]}...', 
                   fontsize=12, ha='center', va='center',
                   bbox=dict(boxstyle='round,pad=0.5', facecolor='red', alpha=0.7))
        except:
            pass
    
    def _validate_and_process_window_data(self, facade_elements, solution):
        """验证和处理窗户数据，确保遮阳板和窗框正确显示"""
        try:
            window_data = []
            
            # 检查是否有真实窗户数据
            if facade_elements and hasattr(facade_elements, 'windows') and facade_elements.windows:
                windows = facade_elements.windows
                self.logger.info(f"使用真实窗户数据，共 {len(windows)} 个窗户")
            else:
                # 使用默认窗户布局（2x3网格）
                windows = self._create_default_windows()
                self.logger.info("使用默认窗户布局")
            
            # 处理每个窗户的数据
            for i, window in enumerate(windows):
                # 获取窗户3D坐标
                x_3d, z_3d, w_3d, h_3d = self._get_window_3d_coords(window, i)
                
                # 获取窗户类型和参数（重点：确保遮阳板和窗框数据正确）
                window_type = 0  # 默认普通窗户
                frame_depth = 0.0
                shading_depth = 0.0
                shading_angle = 30.0
                
                if solution and hasattr(solution, 'window_types') and i < len(solution.window_types):
                    window_type = solution.window_types[i]
                    
                    # 获取窗框深度
                    if hasattr(solution, 'frame_depths') and i < len(solution.frame_depths):
                        frame_depth = solution.frame_depths[i]
                    else:
                        frame_depth = self.frame_depth  # 使用默认值
                    
                    # 获取遮阳板参数
                    if hasattr(solution, 'shading_depths') and i < len(solution.shading_depths):
                        shading_depth = solution.shading_depths[i]
                    else:
                        shading_depth = self.shading_projection  # 使用默认值
                    
                    if hasattr(solution, 'shading_angles') and i < len(solution.shading_angles):
                        shading_angle = solution.shading_angles[i]
                else:
                    # 演示模式：创建多样化的窗户类型
                    if i < 2:  # 前2个窗户有遮阳板
                        window_type = 2
                        shading_depth = 0.8 + i * 0.2
                        shading_angle = 25 + i * 10
                    elif i < 4:  # 第3-4个窗户有窗框
                        window_type = 1
                        frame_depth = 0.08 + i * 0.02
                    # 其余为普通窗户
                
                window_data.append((x_3d, z_3d, w_3d, h_3d, window_type, frame_depth, shading_depth, shading_angle))
                
                # 记录窗户信息用于调试
                self.logger.debug(f"窗户 {i}: 类型={window_type}, 窗框深度={frame_depth:.2f}, 遮阳深度={shading_depth:.2f}")
            
            return window_data
            
        except Exception as e:
            self.logger.error(f"窗户数据验证处理失败: {str(e)}")
            # 返回默认窗户数据
            return self._create_fallback_window_data()
    
    def _create_default_windows(self):
        """创建默认窗户布局"""
        class DefaultWindow:
            def __init__(self, x, y, w, h):
                self.bbox = [x, y, x+w, y+h]
        
        # 2x3网格布局的窗户
        windows = []
        for row in range(2):
            for col in range(3):
                x = 100 + col * 180  # 窗户X位置
                y = 100 + row * 150  # 窗户Y位置
                w = 120  # 窗户宽度
                h = 100  # 窗户高度
                windows.append(DefaultWindow(x, y, w, h))
        
        return windows
    
    def _create_fallback_window_data(self):
        """创建备用窗户数据"""
        fallback_data = []
        positions = [
            (2.0, 1.5, 1.8, 2.0),   # 左下
            (5.0, 1.5, 1.8, 2.0),   # 中下
            (8.0, 1.5, 1.8, 2.0),   # 右下
            (2.0, 4.5, 1.8, 2.0),   # 左上
            (5.0, 4.5, 1.8, 2.0),   # 中上
            (8.0, 4.5, 1.8, 2.0),   # 右上
        ]
        
        for i, (x, z, w, h) in enumerate(positions):
            window_type = 2 if i < 2 else (1 if i < 4 else 0)
            frame_depth = 0.08 if window_type >= 1 else 0.0
            shading_depth = 0.8 if window_type >= 2 else 0.0
            shading_angle = 30.0
            
            fallback_data.append((x, z, w, h, window_type, frame_depth, shading_depth, shading_angle))
        
        return fallback_data

    def _draw_professional_wall_system(self, ax):
        """绘制专业级墙体系统（非立方体，真实建筑构造）"""
        try:
            # === 外墙面（主立面）- 专业砖墙纹理 ===
            outer_wall = [
                [[0, 0, 0], [self.wall_width, 0, 0], 
                 [self.wall_width, 0, self.wall_height], [0, 0, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(outer_wall, alpha=0.95, 
                                               facecolor=self.colors['wall_outer'], 
                                               edgecolor=self.colors['brick_line'], linewidth=1.2))
            
            # === 内墙面 - 室内装饰面 ===
            inner_wall = [
                [[0, self.wall_thickness, 0], [self.wall_width, self.wall_thickness, 0], 
                 [self.wall_width, self.wall_thickness, self.wall_height], [0, self.wall_thickness, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(inner_wall, alpha=0.8, 
                                               facecolor=self.colors['wall_inner'], 
                                               edgecolor='#E0E0E0', linewidth=0.8))
            
            # === 墙体顶面 - 显示墙体厚度和构造 ===
            top_wall = [
                [[0, 0, self.wall_height], [self.wall_width, 0, self.wall_height], 
                 [self.wall_width, self.wall_thickness, self.wall_height], [0, self.wall_thickness, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(top_wall, alpha=0.9, 
                                               facecolor=self.colors['wall_side'], 
                                               edgecolor='#A0A0A0', linewidth=0.8))
            
            # === 墙体左侧面 - 展示墙体厚度 ===
            left_wall = [
                [[0, 0, 0], [0, self.wall_thickness, 0], 
                 [0, self.wall_thickness, self.wall_height], [0, 0, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(left_wall, alpha=0.9, 
                                               facecolor=self.colors['wall_side'], 
                                               edgecolor='#A0A0A0', linewidth=0.8))
            
            # === 墙体右侧面 - 展示墙体厚度 ===
            right_wall = [
                [[self.wall_width, 0, 0], [self.wall_width, self.wall_thickness, 0], 
                 [self.wall_width, self.wall_thickness, self.wall_height], [self.wall_width, 0, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(right_wall, alpha=0.9, 
                                               facecolor=self.colors['wall_side'], 
                                               edgecolor='#A0A0A0', linewidth=0.8))
            
            # === 添加专业砖墙纹理 ===
            self._add_professional_brick_texture(ax)
            
            # === 添加墙体基础和顶部构造细节 ===
            self._add_wall_construction_details(ax)
            
        except Exception as e:
            self.logger.error(f"绘制专业墙体系统失败: {str(e)}")
    
    def _add_professional_brick_texture(self, ax):
        """添加专业级砖墙纹理效果（中国标准砖）"""
        try:
            # 中国标准砖尺寸：240mm×115mm×53mm
            brick_height = 0.053 + 0.010  # 砖高53mm + 灰缝10mm
            brick_length = 0.240          # 砖长240mm
            
            # 水平砖缝线（每层砖）
            for layer, z in enumerate(np.arange(brick_height, self.wall_height, brick_height)):
                if z <= self.wall_height:
                    brick_line_x = [0, self.wall_width]
                    brick_line_y = [0, 0]
                    brick_line_z = [z, z]
                    ax.plot(brick_line_x, brick_line_y, brick_line_z, 
                           color=self.colors['brick_line'], linewidth=0.8, alpha=0.7)
            
            # 垂直砖缝线（错位砌筑）
            for layer in range(int(self.wall_height / brick_height)):
                z_base = layer * brick_height
                z_top = min(z_base + brick_height, self.wall_height)
                
                # 奇偶层错位半砖长度
                offset = brick_length / 2 if layer % 2 else 0
                
                for x in np.arange(offset, self.wall_width + brick_length, brick_length):
                    if 0 <= x <= self.wall_width:
                        brick_line_x = [x, x]
                        brick_line_y = [0, 0]
                        brick_line_z = [z_base, z_top]
                        ax.plot(brick_line_x, brick_line_y, brick_line_z, 
                               color=self.colors['brick_line'], linewidth=0.4, alpha=0.5)
            
            # 添加砖块立体感（选择性绘制部分砖块边缘）
            self._add_brick_depth_effect(ax)
            
        except Exception as e:
            self.logger.error(f"添加专业砖墙纹理失败: {str(e)}")
    
    def _add_brick_depth_effect(self, ax):
        """添加砖块立体感效果"""
        try:
            brick_height = 0.063
            brick_length = 0.240
            depth_offset = 0.005  # 5mm的立体效果
            
            # 每隔几层砖添加立体效果
            for layer in range(0, int(self.wall_height / brick_height), 3):
                z_base = layer * brick_height
                if z_base + brick_height > self.wall_height:
                    continue
                
                offset = brick_length / 2 if layer % 2 else 0
                
                # 每隔几块砖添加立体效果
                for i, x in enumerate(np.arange(offset, self.wall_width, brick_length * 2)):
                    if x + brick_length <= self.wall_width:
                        # 砖块前表面
                        brick_face = [
                            [[x, -depth_offset, z_base], [x + brick_length, -depth_offset, z_base],
                             [x + brick_length, -depth_offset, z_base + brick_height], [x, -depth_offset, z_base + brick_height]]
                        ]
                        ax.add_collection3d(Poly3DCollection(brick_face, alpha=0.3, 
                                                           facecolor='#D3D3D3', edgecolor='none'))
            
        except Exception as e:
            self.logger.error(f"添加砖块立体效果失败: {str(e)}")
    
    def _add_wall_construction_details(self, ax):
        """添加墙体构造细节（基础、顶部等）"""
        try:
            # 墙体基础（稍微突出）
            foundation_height = 0.2
            foundation_projection = 0.05
            
            foundation = [
                [[-foundation_projection, -foundation_projection, 0], 
                 [self.wall_width + foundation_projection, -foundation_projection, 0],
                 [self.wall_width + foundation_projection, self.wall_thickness + foundation_projection, 0],
                 [-foundation_projection, self.wall_thickness + foundation_projection, 0]]
            ]
            ax.add_collection3d(Poly3DCollection(foundation, alpha=0.9, 
                                               facecolor='#A0A0A0', edgecolor='#808080', linewidth=1))
            
            # 墙体顶部压顶
            cap_height = 0.1
            cap_projection = 0.03
            
            cap_top = [
                [[-cap_projection, -cap_projection, self.wall_height], 
                 [self.wall_width + cap_projection, -cap_projection, self.wall_height],
                 [self.wall_width + cap_projection, self.wall_thickness + cap_projection, self.wall_height],
                 [-cap_projection, self.wall_thickness + cap_projection, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(cap_top, alpha=0.9, 
                                               facecolor='#B8B8B8', edgecolor='#909090', linewidth=1))
            
        except Exception as e:
            self.logger.error(f"添加墙体构造细节失败: {str(e)}")
    
    def _get_window_3d_coords(self, window, index):
        """获取窗户的3D坐标"""
        try:
            if hasattr(window, 'bbox'):
                # 使用真实窗户数据
                x_2d = window.bbox[0] / 640  # 规范化到0-1
                y_2d = (480 - window.bbox[3]) / 480  # Y轴翻转
                w_2d = (window.bbox[2] - window.bbox[0]) / 640
                h_2d = (window.bbox[3] - window.bbox[1]) / 480
                
                # 转换为3D坐标（米）
                x_3d = x_2d * self.wall_width  # 缩放到墙体宽度
                z_3d = y_2d * self.wall_height   # Z轴高度，缩放到墙体高度
                w_3d = w_2d * self.wall_width  # 窗户宽度
                h_3d = h_2d * self.wall_height   # 窗户高度
                
                # 确保窗户不超出墙体边界
                x_3d = max(0.2, min(x_3d, self.wall_width - w_3d - 0.2))
                z_3d = max(0.2, min(z_3d, self.wall_height - h_3d - 0.2))
                w_3d = min(w_3d, 2.0)  # 最大窗户宽度2米
                h_3d = min(h_3d, 2.5)  # 最大窗户高度2.5米
            else:
                # 使用默认窗户位置（3x2网格布局）
                positions_3d = [
                    (1.5, 1.0, 1.5, 1.8),  # 左下窗户
                    (4.25, 1.0, 1.5, 1.8), # 中下窗户
                    (7.0, 1.0, 1.5, 1.8),  # 右下窗户
                    (1.5, 3.5, 1.5, 1.8),  # 左上窗户
                    (4.25, 3.5, 1.5, 1.8), # 中上窗户
                    (7.0, 3.5, 1.5, 1.8)   # 右上窗户
                ]
                if index < len(positions_3d):
                    x_3d, z_3d, w_3d, h_3d = positions_3d[index]
                else:
                    x_3d, z_3d, w_3d, h_3d = 2.0, 2.0, 1.5, 1.8
            
            return x_3d, z_3d, w_3d, h_3d
            
        except Exception as e:
            self.logger.error(f"获取窗户3D坐标失败: {str(e)}")
            return 2.0, 2.0, 1.5, 1.8  # 默认值
    
    def _draw_complete_window_structure(self, ax, x, z, w, h, solution, window_index):
        """绘制完整的窗户结构"""
        try:
            # 1. 绘制窗户洞口（在墙体中挖洞的效果）
            self._draw_window_opening(ax, x, z, w, h)
            
            # 2. 绘制窗户玻璃（带透明效果和分隔条）
            self._draw_window_glass(ax, x, z, w, h)
            
            # 3. 绘制窗台
            self._draw_window_sill(ax, x, z, w)
            
            # 4. 根据解决方案绘制窗框和遮阳板
            if solution and hasattr(solution, 'window_types'):
                window_type = solution.window_types[window_index] if window_index < len(solution.window_types) else 0
                
                # 绘制立体窗框
                if window_type >= 1:
                    self._draw_realistic_window_frame(ax, x, z, w, h)
                
                # 绘制立体遮阳板
                if window_type >= 2:
                    self._draw_realistic_shading(ax, x, z, w, h)
            else:
                # 演示用：前3个窗户有窗框，前2个有遮阳板
                if window_index < 3:  # 前3个窗户有窗框
                    self._draw_realistic_window_frame(ax, x, z, w, h)
                if window_index < 2:  # 前2个窗户有遮阳板
                    self._draw_realistic_shading(ax, x, z, w, h)
            
        except Exception as e:
            self.logger.error(f"绘制完整窗户结构失败: {str(e)}")
    
    def _draw_window_opening(self, ax, x, z, w, h):
        """绘制窗户洞口（在墙体中的开口）"""
        try:
            # 洞口内侧面（上）
            top_opening = [
                [[x, 0, z+h], [x+w, 0, z+h], [x+w, self.wall_thickness, z+h], [x, self.wall_thickness, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(top_opening, alpha=0.9, 
                                               facecolor='#AAAAAA', edgecolor='#888888', linewidth=0.5))
            
            # 洞口内侧面（下）
            bottom_opening = [
                [[x, 0, z], [x+w, 0, z], [x+w, self.wall_thickness, z], [x, self.wall_thickness, z]]
            ]
            ax.add_collection3d(Poly3DCollection(bottom_opening, alpha=0.9, 
                                               facecolor='#AAAAAA', edgecolor='#888888', linewidth=0.5))
            
            # 洞口内侧面（左）
            left_opening = [
                [[x, 0, z], [x, self.wall_thickness, z], [x, self.wall_thickness, z+h], [x, 0, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(left_opening, alpha=0.9, 
                                               facecolor='#AAAAAA', edgecolor='#888888', linewidth=0.5))
            
            # 洞口内侧面（右）
            right_opening = [
                [[x+w, 0, z], [x+w, self.wall_thickness, z], [x+w, self.wall_thickness, z+h], [x+w, 0, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(right_opening, alpha=0.9, 
                                               facecolor='#AAAAAA', edgecolor='#888888', linewidth=0.5))
            
        except Exception as e:
            self.logger.error(f"绘制窗户洞口失败: {str(e)}")
    
    def _draw_professional_window_glass(self, ax, x, z, w, h):
        """绘制专业级窗户玻璃系统（双层中空玻璃）"""
        try:
            glass_gap = 0.012  # 中空玻璃间隙12mm
            glass_thickness = 0.006  # 玻璃厚度6mm
            
            # === 外层玻璃（室外侧）===
            outer_glass_pos = -self.window_depth * 0.4
            outer_glass = [
                [[x, outer_glass_pos, z], [x+w, outer_glass_pos, z], 
                 [x+w, outer_glass_pos, z+h], [x, outer_glass_pos, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(outer_glass, alpha=0.5, 
                                               facecolor=self.colors['window_glass'], 
                                               edgecolor='#4682B4', linewidth=1.2))
            
            # === 内层玻璃（室内侧）===
            inner_glass_pos = outer_glass_pos + glass_thickness + glass_gap
            inner_glass = [
                [[x, inner_glass_pos, z], [x+w, inner_glass_pos, z], 
                 [x+w, inner_glass_pos, z+h], [x, inner_glass_pos, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(inner_glass, alpha=0.4, 
                                               facecolor='#B0E0E6', 
                                               edgecolor='#5F9EA0', linewidth=0.8))
            
            # === 中空玻璃密封条 ===
            self._draw_glass_sealant(ax, x, z, w, h, outer_glass_pos, inner_glass_pos)
            
            # === 专业窗户分隔条系统 ===
            self._draw_professional_window_dividers(ax, x, z, w, h, outer_glass_pos, inner_glass_pos)
            
            # === 玻璃反射效果 ===
            self._add_glass_reflection_effect(ax, x, z, w, h, outer_glass_pos)
            
        except Exception as e:
            self.logger.error(f"绘制专业窗户玻璃失败: {str(e)}")
    
    def _draw_glass_sealant(self, ax, x, z, w, h, outer_pos, inner_pos):
        """绘制中空玻璃密封条"""
        try:
            sealant_width = 0.008  # 密封条宽度8mm
            mid_pos = (outer_pos + inner_pos) / 2
            
            # 上密封条
            top_sealant = [
                [[x, mid_pos - sealant_width/2, z+h], [x+w, mid_pos - sealant_width/2, z+h],
                 [x+w, mid_pos + sealant_width/2, z+h], [x, mid_pos + sealant_width/2, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(top_sealant, alpha=1.0, 
                                               facecolor='#2F4F4F', edgecolor='#2F4F4F'))
            
            # 下密封条
            bottom_sealant = [
                [[x, mid_pos - sealant_width/2, z], [x+w, mid_pos - sealant_width/2, z],
                 [x+w, mid_pos + sealant_width/2, z], [x, mid_pos + sealant_width/2, z]]
            ]
            ax.add_collection3d(Poly3DCollection(bottom_sealant, alpha=1.0, 
                                               facecolor='#2F4F4F', edgecolor='#2F4F4F'))
            
            # 左右密封条
            for x_pos in [x, x+w]:
                side_sealant = [
                    [[x_pos, mid_pos - sealant_width/2, z], [x_pos, mid_pos + sealant_width/2, z],
                     [x_pos, mid_pos + sealant_width/2, z+h], [x_pos, mid_pos - sealant_width/2, z+h]]
                ]
                ax.add_collection3d(Poly3DCollection(side_sealant, alpha=1.0, 
                                                   facecolor='#2F4F4F', edgecolor='#2F4F4F'))
            
        except Exception as e:
            self.logger.error(f"绘制玻璃密封条失败: {str(e)}")
    
    def _draw_professional_window_dividers(self, ax, x, z, w, h, outer_pos, inner_pos):
        """绘制专业窗户分隔条系统"""
        try:
            divider_width = 0.025  # 分隔条宽度25mm
            divider_depth = inner_pos - outer_pos + 0.012  # 分隔条深度
            
            # 水平分隔条（中间）
            h_div_z = z + h/2
            h_divider = [
                [[x, outer_pos - 0.006, h_div_z - divider_width/2], 
                 [x+w, outer_pos - 0.006, h_div_z - divider_width/2],
                 [x+w, outer_pos + divider_depth, h_div_z + divider_width/2], 
                 [x, outer_pos + divider_depth, h_div_z + divider_width/2]]
            ]
            ax.add_collection3d(Poly3DCollection(h_divider, alpha=1.0, 
                                               facecolor='#F5F5F5', edgecolor='#D3D3D3', linewidth=1))
            
            # 垂直分隔条（中间）
            v_div_x = x + w/2
            v_divider = [
                [[v_div_x - divider_width/2, outer_pos - 0.006, z], 
                 [v_div_x + divider_width/2, outer_pos - 0.006, z],
                 [v_div_x + divider_width/2, outer_pos + divider_depth, z+h], 
                 [v_div_x - divider_width/2, outer_pos + divider_depth, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(v_divider, alpha=1.0, 
                                               facecolor='#F5F5F5', edgecolor='#D3D3D3', linewidth=1))
            
            # 分隔条端面
            self._draw_divider_end_faces(ax, x, z, w, h, outer_pos, inner_pos, divider_width)
            
        except Exception as e:
            self.logger.error(f"绘制窗户分隔条失败: {str(e)}")
    
    def _draw_divider_end_faces(self, ax, x, z, w, h, outer_pos, inner_pos, divider_width):
        """绘制分隔条端面"""
        try:
            # 水平分隔条端面
            h_div_z = z + h/2
            for x_pos in [x, x+w]:
                end_face = [
                    [[x_pos, outer_pos - 0.006, h_div_z - divider_width/2],
                     [x_pos, outer_pos + (inner_pos - outer_pos + 0.012), h_div_z - divider_width/2],
                     [x_pos, outer_pos + (inner_pos - outer_pos + 0.012), h_div_z + divider_width/2],
                     [x_pos, outer_pos - 0.006, h_div_z + divider_width/2]]
                ]
                ax.add_collection3d(Poly3DCollection(end_face, alpha=1.0, 
                                                   facecolor='#E8E8E8', edgecolor='#C0C0C0'))
            
            # 垂直分隔条端面
            v_div_x = x + w/2
            for z_pos in [z, z+h]:
                end_face = [
                    [[v_div_x - divider_width/2, outer_pos - 0.006, z_pos],
                     [v_div_x + divider_width/2, outer_pos - 0.006, z_pos],
                     [v_div_x + divider_width/2, outer_pos + (inner_pos - outer_pos + 0.012), z_pos],
                     [v_div_x - divider_width/2, outer_pos + (inner_pos - outer_pos + 0.012), z_pos]]
                ]
                ax.add_collection3d(Poly3DCollection(end_face, alpha=1.0, 
                                                   facecolor='#E8E8E8', edgecolor='#C0C0C0'))
            
        except Exception as e:
            self.logger.error(f"绘制分隔条端面失败: {str(e)}")
    
    def _add_glass_reflection_effect(self, ax, x, z, w, h, glass_pos):
        """添加玻璃反射效果"""
        try:
            # 简单的反射高光效果
            highlight_width = w * 0.3
            highlight_height = h * 0.6
            highlight_x = x + w * 0.1
            highlight_z = z + h * 0.2
            
            highlight = [
                [[highlight_x, glass_pos - 0.001, highlight_z], 
                 [highlight_x + highlight_width, glass_pos - 0.001, highlight_z],
                 [highlight_x + highlight_width, glass_pos - 0.001, highlight_z + highlight_height], 
                 [highlight_x, glass_pos - 0.001, highlight_z + highlight_height]]
            ]
            ax.add_collection3d(Poly3DCollection(highlight, alpha=0.2, 
                                               facecolor='white', edgecolor='none'))
            
        except Exception as e:
            self.logger.error(f"添加玻璃反射效果失败: {str(e)}")
    
    def _draw_enhanced_window_sill(self, ax, x, z, w):
        """绘制增强的窗台系统"""
        try:
            sill_color = self.colors['window_sill']
            
            # 窗台主体顶面（带坡度排水）
            sill_top = [
                [[x-0.08, -self.window_sill_depth, z], [x+w+0.08, -self.window_sill_depth, z], 
                 [x+w+0.08, self.wall_thickness+0.03, z-0.01], [x-0.08, self.wall_thickness+0.03, z-0.01]]
            ]
            ax.add_collection3d(Poly3DCollection(sill_top, alpha=1.0, 
                                               facecolor=sill_color, edgecolor='#A0A0A0', linewidth=1.2))
            
            # 窗台前立面
            sill_front = [
                [[x-0.08, -self.window_sill_depth, z-self.window_sill_height], 
                 [x+w+0.08, -self.window_sill_depth, z-self.window_sill_height], 
                 [x+w+0.08, -self.window_sill_depth, z], [x-0.08, -self.window_sill_depth, z]]
            ]
            ax.add_collection3d(Poly3DCollection(sill_front, alpha=1.0, 
                                               facecolor='#B8B8B8', edgecolor='#909090', linewidth=0.8))
            
            # 窗台底面
            sill_bottom = [
                [[x-0.08, -self.window_sill_depth, z-self.window_sill_height], 
                 [x+w+0.08, -self.window_sill_depth, z-self.window_sill_height], 
                 [x+w+0.08, self.wall_thickness+0.03, z-self.window_sill_height-0.01], 
                 [x-0.08, self.wall_thickness+0.03, z-self.window_sill_height-0.01]]
            ]
            ax.add_collection3d(Poly3DCollection(sill_bottom, alpha=0.9, 
                                               facecolor='#A8A8A8', edgecolor='#808080', linewidth=0.5))
            
            # 窗台左右端面
            for x_pos in [x-0.08, x+w+0.08]:
                end_face = [
                    [[x_pos, -self.window_sill_depth, z-self.window_sill_height], 
                     [x_pos, self.wall_thickness+0.03, z-self.window_sill_height-0.01], 
                     [x_pos, self.wall_thickness+0.03, z-0.01], 
                     [x_pos, -self.window_sill_depth, z]]
                ]
                ax.add_collection3d(Poly3DCollection(end_face, alpha=1.0, 
                                                   facecolor='#B0B0B0', edgecolor='#909090', linewidth=0.6))
            
            # 窗台排水槽
            self._draw_sill_drainage(ax, x, z, w)
            
        except Exception as e:
            self.logger.error(f"绘制增强窗台失败: {str(e)}")
    
    def _draw_sill_drainage(self, ax, x, z, w):
        """绘制窗台排水槽"""
        try:
            groove_width = 0.01
            groove_depth = 0.003
            
            # 排水槽
            drainage = [
                [[x-0.05, -self.window_sill_depth+0.02, z-groove_depth], 
                 [x+w+0.05, -self.window_sill_depth+0.02, z-groove_depth], 
                 [x+w+0.05, -self.window_sill_depth+0.02+groove_width, z], 
                 [x-0.05, -self.window_sill_depth+0.02+groove_width, z]]
            ]
            ax.add_collection3d(Poly3DCollection(drainage, alpha=1.0, 
                                               facecolor='#808080', edgecolor='#606060', linewidth=0.3))
            
        except Exception as e:
            self.logger.error(f"绘制窗台排水槽失败: {str(e)}")
    
    def _draw_professional_door(self, ax, door):
        """绘制专业门系统"""
        try:
            # 获取门的坐标
            if hasattr(door, 'bbox'):
                x = door.bbox[0] / 640 * self.wall_width
                z = (480 - door.bbox[3]) / 480 * self.wall_height
                w = (door.bbox[2] - door.bbox[0]) / 640 * self.wall_width
                h = (door.bbox[3] - door.bbox[1]) / 480 * self.wall_height
            else:
                # 默认门位置
                x, z, w, h = 1.0, 0.0, 1.0, 2.2
            
            door_color = self.colors['door']
            
            # 门洞口
            self._draw_window_opening(ax, x, z, w, h)
            
            # 门扇
            door_panel = [
                [[x+0.05, -0.05, z], [x+w-0.05, -0.05, z], 
                 [x+w-0.05, -0.05, z+h], [x+0.05, -0.05, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(door_panel, alpha=1.0, 
                                               facecolor=door_color, edgecolor='#654321', linewidth=1.5))
            
            # 门框
            self._draw_professional_window_frame(ax, x, z, w, h, 0.06)
            
        except Exception as e:
            self.logger.error(f"绘制专业门系统失败: {str(e)}")
    
    def _add_professional_lighting_effects(self, ax):
        """添加专业光影效果"""
        try:
            # 设置光源方向（模拟太阳光）
            ax.view_init(elev=25, azim=45)
            
            # 添加环境光效果（通过调整透明度和颜色）
            # 这里主要通过之前绘制的各个构件的颜色和透明度来实现
            
        except Exception as e:
            self.logger.error(f"添加专业光影效果失败: {str(e)}")
    
    def _add_technical_annotations(self, ax, facade_elements, solution, window_data):
        """添加技术信息标注"""
        try:
            # 统计信息
            total_windows = len(window_data)
            windows_with_frames = sum(1 for _, _, _, _, wtype, _, _, _ in window_data if wtype >= 1)
            windows_with_shading = sum(1 for _, _, _, _, wtype, _, _, _ in window_data if wtype >= 2)
            
            # 在图表底部添加技术信息
            info_text = f"技术参数 Technical Parameters:\n"
            info_text += f"墙体厚度 Wall Thickness: {self.wall_thickness*100:.0f}cm\n"
            info_text += f"窗户总数 Total 窗户s: {total_windows}\n"
            info_text += f"带窗框 With 窗框s: {windows_with_frames}\n"
            info_text += f"带遮阳 With 遮阳: {windows_with_shading}"
            
            ax.text2D(0.02, 0.02, info_text, transform=ax.transAxes, 
                     fontsize=8, verticalalignment='bottom',
                     bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
            
        except Exception as e:
            self.logger.error(f"添加技术标注失败: {str(e)}")
    
    def _draw_error_indicator(self, ax, error_msg):
        """绘制专业错误提示"""
        try:
            ax.text(self.wall_width/2, 0, self.wall_height/2, 
                   f'3D渲染系统错误\n3D Rendering System Error\n\n{error_msg[:50]}...', 
                   fontsize=10, ha='center', va='center',
                   bbox=dict(boxstyle='round,pad=0.8', facecolor='#FFE4E1', 
                            edgecolor='red', linewidth=2, alpha=0.9))
            
        except Exception as e:
            self.logger.error(f"绘制错误提示失败: {str(e)}")    

    def _draw_professional_window_frame(self, ax, x, z, w, h, frame_depth):
        """绘制专业级立体窗框系统"""
        try:
            # 使用传入的frame_depth参数，如果为0则使用默认值
            actual_frame_depth = frame_depth if frame_depth > 0 else self.frame_depth
            frame_color = self.colors['window_frame']
            frame_edge_color = '#654321'
            
            # === 外框架系统（室外侧）===
            outer_frame_pos = -actual_frame_depth
            
            # 上框外侧
            top_frame_outer = [
                [[x-self.frame_width, outer_frame_pos, z+h], 
                 [x+w+self.frame_width, outer_frame_pos, z+h], 
                 [x+w+self.frame_width, outer_frame_pos, z+h+self.frame_width], 
                 [x-self.frame_width, outer_frame_pos, z+h+self.frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(top_frame_outer, alpha=1.0, 
                                               facecolor=frame_color, edgecolor=frame_edge_color, linewidth=1.2))
            
            # 下框外侧
            bottom_frame_outer = [
                [[x-self.frame_width, outer_frame_pos, z-self.frame_width], 
                 [x+w+self.frame_width, outer_frame_pos, z-self.frame_width], 
                 [x+w+self.frame_width, outer_frame_pos, z], 
                 [x-self.frame_width, outer_frame_pos, z]]
            ]
            ax.add_collection3d(Poly3DCollection(bottom_frame_outer, alpha=1.0, 
                                               facecolor=frame_color, edgecolor=frame_edge_color, linewidth=1.2))
            
            # 左框外侧
            left_frame_outer = [
                [[x-self.frame_width, outer_frame_pos, z], [x, outer_frame_pos, z], 
                 [x, outer_frame_pos, z+h], [x-self.frame_width, outer_frame_pos, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(left_frame_outer, alpha=1.0, 
                                               facecolor=frame_color, edgecolor=frame_edge_color, linewidth=1.2))
            
            # 右框外侧
            right_frame_outer = [
                [[x+w, outer_frame_pos, z], [x+w+self.frame_width, outer_frame_pos, z], 
                 [x+w+self.frame_width, outer_frame_pos, z+h], [x+w, outer_frame_pos, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(right_frame_outer, alpha=1.0, 
                                               facecolor=frame_color, edgecolor=frame_edge_color, linewidth=1.2))
            
            # === 内框架系统（室内侧）===
            inner_frame_pos = self.wall_thickness + 0.01
            
            # 上框内侧
            top_frame_inner = [
                [[x-self.frame_width, inner_frame_pos, z+h], 
                 [x+w+self.frame_width, inner_frame_pos, z+h], 
                 [x+w+self.frame_width, inner_frame_pos, z+h+self.frame_width], 
                 [x-self.frame_width, inner_frame_pos, z+h+self.frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(top_frame_inner, alpha=0.95, 
                                               facecolor=frame_color, edgecolor=frame_edge_color, linewidth=0.8))
            
            # 下框内侧
            bottom_frame_inner = [
                [[x-self.frame_width, inner_frame_pos, z-self.frame_width], 
                 [x+w+self.frame_width, inner_frame_pos, z-self.frame_width], 
                 [x+w+self.frame_width, inner_frame_pos, z], 
                 [x-self.frame_width, inner_frame_pos, z]]
            ]
            ax.add_collection3d(Poly3DCollection(bottom_frame_inner, alpha=0.95, 
                                               facecolor=frame_color, edgecolor=frame_edge_color, linewidth=0.8))
            
            # === 框架连接系统（显示厚度和立体感）===
            self._draw_frame_connection_system(ax, x, z, w, h, outer_frame_pos, inner_frame_pos, frame_color)
            
            # === 框架装饰细节 ===
            self._draw_frame_decorative_details(ax, x, z, w, h, outer_frame_pos, frame_color)
            
        except Exception as e:
            self.logger.error(f"绘制专业窗框失败: {str(e)}")
    
    def _draw_frame_connection_system(self, ax, x, z, w, h, outer_pos, inner_pos, frame_color):
        """绘制窗框连接系统"""
        try:
            # 上框架连接面
            top_connection = [
                [[x-self.frame_width, outer_pos, z+h+self.frame_width], 
                 [x+w+self.frame_width, outer_pos, z+h+self.frame_width], 
                 [x+w+self.frame_width, inner_pos, z+h+self.frame_width], 
                 [x-self.frame_width, inner_pos, z+h+self.frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(top_connection, alpha=0.9, 
                                               facecolor='#A0522D', edgecolor='#8B4513', linewidth=0.6))
            
            # 下框架连接面
            bottom_connection = [
                [[x-self.frame_width, outer_pos, z-self.frame_width], 
                 [x+w+self.frame_width, outer_pos, z-self.frame_width], 
                 [x+w+self.frame_width, inner_pos, z-self.frame_width], 
                 [x-self.frame_width, inner_pos, z-self.frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(bottom_connection, alpha=0.9, 
                                               facecolor='#A0522D', edgecolor='#8B4513', linewidth=0.6))
            
            # 左右框架连接面
            for x_pos in [x-self.frame_width, x+w+self.frame_width]:
                side_connection = [
                    [[x_pos, outer_pos, z], [x_pos, inner_pos, z], 
                     [x_pos, inner_pos, z+h], [x_pos, outer_pos, z+h]]
                ]
                ax.add_collection3d(Poly3DCollection(side_connection, alpha=0.9, 
                                                   facecolor='#A0522D', edgecolor='#8B4513', linewidth=0.6))
            
        except Exception as e:
            self.logger.error(f"绘制窗框连接系统失败: {str(e)}")
    
    def _draw_frame_decorative_details(self, ax, x, z, w, h, outer_pos, frame_color):
        """绘制窗框装饰细节"""
        try:
            # 窗框边缘装饰线条
            detail_depth = 0.005
            detail_width = 0.01
            
            # 上框装饰线
            top_detail = [
                [[x-self.frame_width+detail_width, outer_pos-detail_depth, z+h+detail_width], 
                 [x+w+self.frame_width-detail_width, outer_pos-detail_depth, z+h+detail_width], 
                 [x+w+self.frame_width-detail_width, outer_pos, z+h+detail_width], 
                 [x-self.frame_width+detail_width, outer_pos, z+h+detail_width]]
            ]
            ax.add_collection3d(Poly3DCollection(top_detail, alpha=1.0, 
                                               facecolor='#CD853F', edgecolor='#D2691E', linewidth=0.5))
            
            # 窗框角部加强件
            corner_size = 0.03
            for corner_x, corner_z in [(x-self.frame_width, z+h), (x+w+self.frame_width, z+h),
                                      (x-self.frame_width, z), (x+w+self.frame_width, z)]:
                corner = [
                    [[corner_x, outer_pos-detail_depth, corner_z], 
                     [corner_x+corner_size, outer_pos-detail_depth, corner_z], 
                     [corner_x+corner_size, outer_pos, corner_z+corner_size], 
                     [corner_x, outer_pos, corner_z+corner_size]]
                ]
                ax.add_collection3d(Poly3DCollection(corner, alpha=1.0, 
                                                   facecolor='#B8860B', edgecolor='#DAA520', linewidth=0.3))
            
        except Exception as e:
            self.logger.error(f"绘制窗框装饰细节失败: {str(e)}")
    
    def _draw_professional_shading_system(self, ax, x, z, w, h, shading_depth, shading_angle):
        """绘制专业级遮阳板系统"""
        try:
            # 使用传入的参数，如果为0则使用默认值
            actual_shading_depth = shading_depth if shading_depth > 0 else self.shading_projection
            actual_angle = shading_angle if shading_angle > 0 else 30.0
            
            # 遮阳板颜色
            shading_color = self.colors['shading_main']
            support_color = self.colors['shading_support']
            
            # 计算遮阳板位置和角度
            shading_z_pos = z + h + 0.15  # 窗户上方15cm
            angle_rad = np.radians(actual_angle)
            
            # 遮阳板起始和结束位置
            shading_y_start = -actual_shading_depth
            shading_y_end = self.wall_thickness + 0.05
            
            # 考虑角度的高度调整
            height_adjustment = actual_shading_depth * np.tan(angle_rad) * 0.3
            
            # === 遮阳板主体结构（修复尺寸，与窗户对齐）===
            # 遮阳板伸出窗户边缘的距离（减少到10cm）
            shading_overhang = 0.1
            
            # 遮阳板顶面（带角度）
            shading_top = [
                [[x-shading_overhang, shading_y_start, shading_z_pos+self.shading_thickness+height_adjustment], 
                 [x+w+shading_overhang, shading_y_start, shading_z_pos+self.shading_thickness+height_adjustment], 
                 [x+w+shading_overhang, shading_y_end, shading_z_pos+self.shading_thickness], 
                 [x-shading_overhang, shading_y_end, shading_z_pos+self.shading_thickness]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_top, alpha=0.95, 
                                               facecolor=shading_color, edgecolor='#1F5F1F', linewidth=1.5))
            
            # 遮阳板底面（带角度）
            shading_bottom = [
                [[x-shading_overhang, shading_y_start, shading_z_pos+height_adjustment], 
                 [x+w+shading_overhang, shading_y_start, shading_z_pos+height_adjustment], 
                 [x+w+shading_overhang, shading_y_end, shading_z_pos], 
                 [x-shading_overhang, shading_y_end, shading_z_pos]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_bottom, alpha=0.85, 
                                               facecolor='#3CB371', edgecolor='#2E8B57', linewidth=0.8))
            
            # 遮阳板前端面（显示厚度）
            shading_front = [
                [[x-shading_overhang, shading_y_start, shading_z_pos+height_adjustment], 
                 [x+w+shading_overhang, shading_y_start, shading_z_pos+height_adjustment], 
                 [x+w+shading_overhang, shading_y_start, shading_z_pos+self.shading_thickness+height_adjustment], 
                 [x-shading_overhang, shading_y_start, shading_z_pos+self.shading_thickness+height_adjustment]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_front, alpha=1.0, 
                                               facecolor=shading_color, edgecolor='#1F5F1F', linewidth=1.2))
            
            # 遮阳板左右端面
            for x_pos in [x-shading_overhang, x+w+shading_overhang]:
                end_face = [
                    [[x_pos, shading_y_start, shading_z_pos+height_adjustment], 
                     [x_pos, shading_y_end, shading_z_pos], 
                     [x_pos, shading_y_end, shading_z_pos+self.shading_thickness], 
                     [x_pos, shading_y_start, shading_z_pos+self.shading_thickness+height_adjustment]]
                ]
                ax.add_collection3d(Poly3DCollection(end_face, alpha=0.9, 
                                                   facecolor='#228B22', edgecolor='#1F5F1F', linewidth=0.8))
            
            # === 专业支撑结构系统 ===
            self._draw_shading_support_system(ax, x, z, w, h, shading_y_start, shading_y_end, 
                                            shading_z_pos, height_adjustment, support_color)
            
            # === 遮阳板装饰和功能细节 ===
            self._draw_shading_decorative_details(ax, x, z, w, shading_y_start, shading_y_end, 
                                                shading_z_pos, height_adjustment)
            
            # === 投影阴影效果 ===
            self._draw_shading_shadow_effect(ax, x, z, w, h, actual_shading_depth, actual_angle)
            
        except Exception as e:
            self.logger.error(f"绘制专业遮阳板系统失败: {str(e)}")
    
    def _draw_shading_support_system(self, ax, x, z, w, h, y_start, y_end, z_pos, height_adj, support_color):
        """绘制遮阳板支撑系统"""
        try:
            support_thickness = 0.06
            support_width = 0.08
            
            # 主支撑柱（左右两侧）
            for support_x in [x-0.1, x+w+0.1]:
                # 垂直支撑柱
                support_column = [
                    [[support_x-support_thickness/2, y_end-0.05, z_pos], 
                     [support_x+support_thickness/2, y_end-0.05, z_pos], 
                     [support_x+support_thickness/2, y_end-0.05, z_pos+self.shading_thickness+height_adj], 
                     [support_x-support_thickness/2, y_end-0.05, z_pos+self.shading_thickness+height_adj]]
                ]
                ax.add_collection3d(Poly3DCollection(support_column, alpha=1.0, 
                                                   facecolor=support_color, edgecolor='#654321', linewidth=0.8))
                
                # 斜撑支架
                diagonal_support = [
                    [[support_x, y_start+0.2, z_pos+height_adj*0.5], 
                     [support_x, y_end-0.1, z_pos], 
                     [support_x+support_thickness, y_end-0.1, z_pos+support_thickness], 
                     [support_x+support_thickness, y_start+0.2, z_pos+height_adj*0.5+support_thickness]]
                ]
                ax.add_collection3d(Poly3DCollection(diagonal_support, alpha=1.0, 
                                                   facecolor='#A0522D', edgecolor='#8B4513', linewidth=0.6))
            
            # 中间支撑（宽窗户）
            if w > 2.5:
                mid_supports = int(w / 2.5)  # 每2.5米一个中间支撑
                for i in range(1, mid_supports + 1):
                    support_x = x + i * w / (mid_supports + 1)
                    
                    # 中间支撑柱
                    mid_support = [
                        [[support_x-support_thickness/2, y_start+0.3, z_pos+height_adj*0.7], 
                         [support_x+support_thickness/2, y_start+0.3, z_pos+height_adj*0.7], 
                         [support_x+support_thickness/2, y_end-0.1, z_pos+self.shading_thickness*0.5], 
                         [support_x-support_thickness/2, y_end-0.1, z_pos+self.shading_thickness*0.5]]
                    ]
                    ax.add_collection3d(Poly3DCollection(mid_support, alpha=1.0, 
                                                       facecolor=support_color, edgecolor='#654321', linewidth=0.6))
            
            # 连接梁
            connection_beam = [
                [[x-0.2, y_end-0.02, z_pos+self.shading_thickness], 
                 [x+w+0.2, y_end-0.02, z_pos+self.shading_thickness], 
                 [x+w+0.2, y_end+0.02, z_pos+self.shading_thickness+0.04], 
                 [x-0.2, y_end+0.02, z_pos+self.shading_thickness+0.04]]
            ]
            ax.add_collection3d(Poly3DCollection(connection_beam, alpha=1.0, 
                                               facecolor='#8B4513', edgecolor='#654321', linewidth=0.8))
            
        except Exception as e:
            self.logger.error(f"绘制遮阳板支撑系统失败: {str(e)}")
    
    def _draw_shading_decorative_details(self, ax, x, z, w, y_start, y_end, z_pos, height_adj):
        """绘制遮阳板装饰细节"""
        try:
            # 遮阳板边缘装饰条
            edge_thickness = 0.01
            edge_color = '#1F5F1F'
            
            # 前边缘装饰条
            front_edge = [
                [[x-0.3, y_start-edge_thickness, z_pos+height_adj], 
                 [x+w+0.3, y_start-edge_thickness, z_pos+height_adj], 
                 [x+w+0.3, y_start, z_pos+self.shading_thickness+height_adj], 
                 [x-0.3, y_start, z_pos+self.shading_thickness+height_adj]]
            ]
            ax.add_collection3d(Poly3DCollection(front_edge, alpha=1.0, 
                                               facecolor=edge_color, edgecolor='#0F4F0F', linewidth=0.5))
            
            # 排水槽
            drainage_width = 0.02
            drainage_depth = 0.005
            
            drainage_groove = [
                [[x-0.2, y_start+0.1, z_pos+height_adj*0.8-drainage_depth], 
                 [x+w+0.2, y_start+0.1, z_pos+height_adj*0.8-drainage_depth], 
                 [x+w+0.2, y_start+0.1+drainage_width, z_pos+height_adj*0.8], 
                 [x-0.2, y_start+0.1+drainage_width, z_pos+height_adj*0.8]]
            ]
            ax.add_collection3d(Poly3DCollection(drainage_groove, alpha=1.0, 
                                               facecolor='#2F4F4F', edgecolor='#1F3F3F', linewidth=0.3))
            
        except Exception as e:
            self.logger.error(f"绘制遮阳板装饰细节失败: {str(e)}")
    
    def _draw_shading_shadow_effect(self, ax, x, z, w, h, shading_depth, angle):
        """绘制遮阳板投影阴影效果"""
        try:
            # 计算阴影投影
            angle_rad = np.radians(angle)
            shadow_length = shading_depth * np.cos(angle_rad) * 0.8
            shadow_height = shading_depth * np.sin(angle_rad) * 0.3
            
            # 在墙面上绘制阴影
            shadow_z_start = max(0, z + h - shadow_height)
            shadow_z_end = z + h
            
            if shadow_z_start < shadow_z_end:
                shadow_area = [
                    [[x-0.1, 0.001, shadow_z_start], [x+w+0.1, 0.001, shadow_z_start], 
                     [x+w+0.1, 0.001, shadow_z_end], [x-0.1, 0.001, shadow_z_end]]
                ]
                ax.add_collection3d(Poly3DCollection(shadow_area, alpha=0.3, 
                                                   facecolor='gray', edgecolor='none'))
            
        except Exception as e:
            self.logger.error(f"绘制遮阳板阴影效果失败: {str(e)}")
    
    def _draw_default_windows_3d(self, ax, solution):
        """绘制默认窗户（当没有真实数据时）"""
        try:
            # 默认窗户位置（3x2网格）
            default_positions = [
                (1.5, 1.0, 1.5, 1.8),  # 左下
                (4.25, 1.0, 1.5, 1.8), # 中下
                (7.0, 1.0, 1.5, 1.8),  # 右下
                (1.5, 3.5, 1.5, 1.8),  # 左上
                (4.25, 3.5, 1.5, 1.8), # 中上
                (7.0, 3.5, 1.5, 1.8)   # 右上
            ]
            
            for i, (x, z, w, h) in enumerate(default_positions):
                # 绘制完整的窗户结构
                self._draw_complete_window_structure(ax, x, z, w, h, solution, i)
            
        except Exception as e:
            self.logger.error(f"绘制默认窗户失败: {str(e)}")
    
    def _draw_realistic_door(self, ax, door):
        """绘制真实的门"""
        try:
            # 获取门的坐标
            if hasattr(door, 'bbox'):
                x_2d = door.bbox[0] / 640
                z_2d = (480 - door.bbox[3]) / 480
                w_2d = (door.bbox[2] - door.bbox[0]) / 640
                h_2d = (door.bbox[3] - door.bbox[1]) / 480
                
                x_3d = x_2d * self.wall_width
                z_3d = 0  # 门从地面开始
                w_3d = w_2d * self.wall_width
                h_3d = h_2d * self.wall_height
            else:
                # 默认门位置
                x_3d, z_3d, w_3d, h_3d = 8.5, 0, 1.0, 2.2
            
            # 门洞口
            self._draw_window_opening(ax, x_3d, z_3d, w_3d, h_3d)
            
            # 门板
            door_panel = [
                [[x_3d, -0.05, z_3d], [x_3d+w_3d, -0.05, z_3d], 
                 [x_3d+w_3d, -0.05, z_3d+h_3d], [x_3d, -0.05, z_3d+h_3d]]
            ]
            ax.add_collection3d(Poly3DCollection(door_panel, alpha=1.0, 
                                               facecolor='#8B4513', edgecolor='#654321', linewidth=1))
            
            # 门框
            door_frame_width = 0.06
            # 上门框
            top_door_frame = [
                [[x_3d-door_frame_width, -0.08, z_3d+h_3d], 
                 [x_3d+w_3d+door_frame_width, -0.08, z_3d+h_3d], 
                 [x_3d+w_3d+door_frame_width, -0.08, z_3d+h_3d+door_frame_width], 
                 [x_3d-door_frame_width, -0.08, z_3d+h_3d+door_frame_width]]
            ]
            ax.add_collection3d(Poly3DCollection(top_door_frame, alpha=1.0, 
                                               facecolor='#A0522D', edgecolor='#654321', linewidth=0.5))
            
            # 门把手
            handle_x = x_3d + w_3d * 0.8
            handle_z = z_3d + h_3d * 0.5
            handle = [
                [[handle_x, -0.08, handle_z-0.05], [handle_x+0.05, -0.08, handle_z-0.05], 
                 [handle_x+0.05, -0.08, handle_z+0.05], [handle_x, -0.08, handle_z+0.05]]
            ]
            ax.add_collection3d(Poly3DCollection(handle, alpha=1.0, 
                                               facecolor='gold', edgecolor='orange', linewidth=0.5))
            
        except Exception as e:
            self.logger.error(f"绘制门失败: {str(e)}")
    
    def _add_lighting_effects(self, ax):
        """添加光影效果"""
        try:
            # 设置背景颜色
            ax.xaxis.pane.fill = False
            ax.yaxis.pane.fill = False
            ax.zaxis.pane.fill = False
            
            # 设置网格颜色
            ax.xaxis.pane.set_edgecolor('lightgray')
            ax.yaxis.pane.set_edgecolor('lightgray')
            ax.zaxis.pane.set_edgecolor('lightgray')
            ax.grid(True, alpha=0.2)
            
            # 设置背景色
            ax.xaxis.pane.set_facecolor((1.0, 1.0, 1.0, 0.0))
            ax.yaxis.pane.set_facecolor((1.0, 1.0, 1.0, 0.0))
            ax.zaxis.pane.set_facecolor((1.0, 1.0, 1.0, 0.0))
            
        except Exception as e:
            self.logger.error(f"添加光影效果失败: {str(e)}")
    
    def _setup_realistic_3d_view(self, ax):
        """设置专业3D视角和显示效果"""
        try:
            # 设置坐标轴范围（稍微扩大以显示完整结构）
            ax.set_xlim(-0.5, self.wall_width + 0.5)
            ax.set_ylim(-1.5, self.wall_thickness + 0.5)
            ax.set_zlim(-0.2, self.wall_height + 0.5)
            
            # 设置最佳观察视角 - 确保窗户清晰可见
            # elev=20: 适中的仰角，既能看到窗户正面又能看到立体效果
            # azim=-45: 从左前方45度角观察，窗户、窗框、遮阳板都清晰可见
            ax.view_init(elev=20, azim=-45)
            
            # 设置坐标轴标签（双语）
            ax.set_xlabel('宽度 Width (m)', fontsize=9, labelpad=5)
            ax.set_ylabel('深度 Depth (m)', fontsize=9, labelpad=5)
            ax.set_zlabel('高度 Height (m)', fontsize=9, labelpad=5)
            
            # 设置专业的坐标轴刻度
            ax.set_xticks(np.arange(0, self.wall_width + 1, 2))
            ax.set_yticks([0, self.wall_thickness])
            ax.set_zticks(np.arange(0, self.wall_height + 1, 2))
            
            # 设置坐标轴刻度标签
            ax.set_xticklabels([f'{i}' for i in range(0, int(self.wall_width) + 1, 2)], fontsize=7)
            ax.set_yticklabels(['外', '内'], fontsize=7)
            ax.set_zticklabels([f'{i}' for i in range(0, int(self.wall_height) + 1, 2)], fontsize=7)
            
            # 设置专业背景
            ax.xaxis.pane.fill = True
            ax.yaxis.pane.fill = True
            ax.zaxis.pane.fill = True
            
            ax.xaxis.pane.set_facecolor('#F8F8F8')
            ax.yaxis.pane.set_facecolor('#F8F8F8')
            ax.zaxis.pane.set_facecolor('#F8F8F8')
            
            ax.xaxis.pane.set_alpha(0.3)
            ax.yaxis.pane.set_alpha(0.3)
            ax.zaxis.pane.set_alpha(0.3)
            
            # 设置网格样式
            ax.grid(True, alpha=0.3, linewidth=0.5)
            
            # 设置坐标轴线条样式
            ax.xaxis.line.set_color('#CCCCCC')
            ax.yaxis.line.set_color('#CCCCCC')
            ax.zaxis.line.set_color('#CCCCCC')
            
            # 设置纵横比
            ax.set_box_aspect([self.wall_width, 2, self.wall_height])
            
        except Exception as e:
            self.logger.error(f"设置专业3D视角失败: {str(e)}")
    
    def _add_debug_info(self, ax, facade_elements, solution):
        """添加专业调试和技术信息"""
        try:
            info_lines = []
            
            # 基础构件信息
            if facade_elements:
                windows_count = len(getattr(facade_elements, 'windows', []))
                doors_count = len(getattr(facade_elements, 'doors', []))
                info_lines.append(f"构件统计 Components: 窗{windows_count} 门{doors_count}")
            
            # 解决方案技术参数
            if solution and hasattr(solution, 'window_types'):
                window_types = solution.window_types
                type_counts = {0: 0, 1: 0, 2: 0}  # 普通、窗框、遮阳
                for wtype in window_types:
                    if wtype in type_counts:
                        type_counts[wtype] += 1
                
                info_lines.append(f"窗户类型 窗户 Types: 普通{type_counts[0]} 窗框{type_counts[1]} 遮阳{type_counts[2]}")
                
                # 性能指标
                if hasattr(solution, 'energy_consumption'):
                    info_lines.append(f"能耗 Energy: {solution.energy_consumption:.1f} kWh/m²/年")
                if hasattr(solution, 'renovation_cost'):
                    info_lines.append(f"成本 成本: {solution.renovation_cost:,.0f} 元")
            
            # 显示信息
            if info_lines:
                info_text = '\n'.join(info_lines)
                ax.text2D(0.02, 0.98, info_text, transform=ax.transAxes, 
                         fontsize=8, verticalalignment='top',
                         bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
                
                ax.text2D(0.02, 0.98, info_text, transform=ax.transAxes, 
                         fontsize=8, verticalalignment='top', horizontalalignment='left',
                         bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7))
            
        except Exception as e:
            self.logger.error(f"添加调试信息失败: {str(e)}")


def create_enhanced_3d_visualizer():
    """创建增强3D可视化器实例"""
    return Enhanced3DVisualizer()