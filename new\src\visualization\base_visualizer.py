"""
基础可视化器
提供所有可视化图表的基础功能和通用方法
"""

import numpy as np
import matplotlib.pyplot as plt
# 设置中文字体支持
import matplotlib.pyplot as plt
import matplotlib

# 尝试获取系统可用的中文字体
def get_available_chinese_fonts():
    """获取系统可用的中文字体"""
    try:
        import matplotlib.font_manager as fm
        font_names = [f.name for f in fm.fontManager.ttflist]
        
        # 按优先级排列的中文字体
        preferred_fonts = [
            'Microsoft YaHei', 'SimHei', 'SimSun', 'KaiTi', 'FangSong',
            'Arial Unicode MS', 'PingFang SC', 'Hiragino Sans GB',
            'WenQuanYi Micro Hei', 'Noto Sans CJK SC', 'Source Han Sans SC'
        ]
        
        available_fonts = []
        for font in preferred_fonts:
            if font in font_names:
                available_fonts.append(font)
        
        # 添加基础字体作为备选
        available_fonts.extend(['DejaVu Sans', 'Liberation Sans', 'Arial', 'sans-serif'])
        return available_fonts
    except Exception:
        # 如果获取失败，返回基础字体列表
        return ['Arial', 'DejaVu Sans', 'Liberation Sans', 'sans-serif']

# 设置支持Unicode字符的字体列表，优先使用支持中文的字体
fonts = get_available_chinese_fonts()

# 设置matplotlib字体参数，并抑制字体警告
import warnings
warnings.filterwarnings('ignore', message='.*Glyph.*missing from font.*')

matplotlib.rcParams['font.sans-serif'] = fonts
matplotlib.rcParams['axes.unicode_minus'] = False
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['mathtext.fontset'] = 'dejavusans'

plt.rcParams['font.sans-serif'] = fonts
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['mathtext.fontset'] = 'dejavusans'
plt.rcParams['font.size'] = 10
plt.rcParams['axes.titlesize'] = 14
plt.rcParams['axes.labelsize'] = 12
plt.rcParams['xtick.labelsize'] = 10
plt.rcParams['ytick.labelsize'] = 10
plt.rcParams['legend.fontsize'] = 10

import seaborn as sns
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional, Union
from datetime import datetime
import os
from pathlib import Path

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext

from ..core.exceptions import VisualizationError, handle_exception
from ..core.data_structures import VisualizationData
from .chart_config import ChartConfig, create_chart_config


class BaseVisualizer:
    """
    基础可视化器
    
    功能：
    1. 通用图表创建方法
    2. 数据预处理和验证
    3. 图表样式统一管理
    4. 文件保存和导出
    5. 错误处理和日志记录
    """
    
    def __init__(self):
        """初始化基础可视化器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 初始化图表配置
        self.chart_config = create_chart_config()
        
        # 获取可视化配置
        viz_config = self.config.get_section('visualization')
        self.base_config = viz_config.get('base_visualizer', {})
        
        # 输出配置 - 修复路径
        output_config = self.base_config.get('output_settings', {})
        # 使用配置管理器获取正确的输出路径
        base_output_dir = self.config.get_output_directory()
        self.output_dir = os.path.join(base_output_dir, 'charts')
        self.auto_save = output_config.get('auto_save', True)
        
        # 只输出PNG格式
        self.save_formats = ['png']
        
        # 创建输出目录
        self._ensure_output_directory()

        # 文件名中英文映射
        self.filename_mapping = {
            'pareto_frontier_3d': '帕累托前沿3D图',
            'pareto_frontier_2d': '帕累托前沿2D图',
            'optimization_convergence': '优化收敛图',
            'energy_convergence': '能耗收敛图',
            'thermal_convergence': '热工收敛图',
            'cost_convergence': '成本收敛图',
            'solutions_grid': '解决方案网格图',
            'facade_comparison': '立面对比图',
            'enhanced_3d_facade': '增强3D立面图',
            'all_solutions_dashboard': '所有解决方案仪表板',
            'tradeoff_analysis': '权衡分析图',
            'performance_radar': '性能雷达图',
            'comprehensive_analysis': '综合分析图',
            'best_solutions_comparison': '最佳方案对比图',
            'optimization_statistics': '优化统计图',
            'algorithm_performance': '算法性能图'
        }
        
        # 数据验证配置
        validation_config = self.base_config.get('data_validation', {})
        self.check_data_integrity = validation_config.get('check_data_integrity', True)
        self.handle_missing_data = validation_config.get('handle_missing_data', True)
        
        # 强制设置英文字体 - 确保在所有配置后生效
        self._force_english_font()
        
        self.logger.info("基础可视化器初始化完成")
    
    def _force_english_font(self):
        """强制设置英文字体"""
        try:
            # 强制设置matplotlib使用英文字体
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 同时设置seaborn
            import seaborn as sns
            sns.set_style("whitegrid")
            
            self.logger.info("English font settings applied successfully")
        except Exception as e:
            self.logger.error(f"Failed to set English fonts: {e}")
    
    def _ensure_output_directory(self):
        """确保输出目录存在"""
        try:
            Path(self.output_dir).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"输出目录已创建: {self.output_dir}")
        except Exception as e:
            self.logger.error(f"创建输出目录失败: {str(e)}")
    
    @handle_exception
    def validate_data(self, data: Any, required_fields: List[str] = None) -> bool:
        """
        验证数据完整性
        
        Args:
            data: 待验证的数据
            required_fields: 必需字段列表
            
        Returns:
            数据是否有效
            
        Raises:
            VisualizationError: 数据验证失败时抛出
        """
        try:
            if not self.check_data_integrity:
                return True
            
            if data is None:
                raise VisualizationError("数据为空")
            
            # 验证必需字段
            if required_fields:
                if isinstance(data, dict):
                    missing_fields = [field for field in required_fields if field not in data]
                    if missing_fields:
                        raise VisualizationError(f"缺少必需字段: {missing_fields}")
                elif hasattr(data, '__dict__'):
                    missing_fields = [field for field in required_fields 
                                    if not hasattr(data, field)]
                    if missing_fields:
                        raise VisualizationError(f"缺少必需属性: {missing_fields}")
            
            return True
            
        except Exception as e:
            if isinstance(e, VisualizationError):
                raise
            else:
                raise VisualizationError(f"数据验证失败: {str(e)}") from e
    
    def preprocess_data(self, data: Union[Dict, List, np.ndarray, pd.DataFrame]) -> pd.DataFrame:
        """
        数据预处理
        
        Args:
            data: 原始数据
            
        Returns:
            预处理后的DataFrame
        """
        try:
            # 转换为DataFrame
            if isinstance(data, pd.DataFrame):
                df = data.copy()
            elif isinstance(data, dict):
                df = pd.DataFrame(data)
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            elif isinstance(data, np.ndarray):
                df = pd.DataFrame(data)
            else:
                raise VisualizationError(f"不支持的数据类型: {type(data)}")
            
            # 处理缺失值
            if self.handle_missing_data:
                # 数值列用均值填充
                numeric_columns = df.select_dtypes(include=[np.number]).columns
                for col in numeric_columns:
                    if df[col].isnull().any():
                        df[col].fillna(df[col].mean(), inplace=True)
                
                # 分类列用众数填充
                categorical_columns = df.select_dtypes(include=['object']).columns
                for col in categorical_columns:
                    if df[col].isnull().any():
                        mode_value = df[col].mode()
                        if not mode_value.empty:
                            df[col].fillna(mode_value[0], inplace=True)
                        else:
                            df[col].fillna('Unknown', inplace=True)
            
            return df
            
        except Exception as e:
            self.logger.error(f"数据预处理失败: {str(e)}")
            # 返回空DataFrame
            return pd.DataFrame()
    
    def create_figure(self, figsize: Tuple[int, int] = None, 
                     subplot_params: Dict[str, Any] = None,
                     title: str = None, bilingual_title: str = None) -> Tuple[plt.Figure, plt.Axes]:
        """
        创建图形和轴对象
        
        Args:
            figsize: 图形尺寸
            subplot_params: 子图参数
            title: 中文标题
            bilingual_title: 英文标题
            
        Returns:
            图形和轴对象的元组
        """
        try:
            if figsize is None:
                figsize = self.chart_config.get_figure_size()
            
            fig, ax = plt.subplots(figsize=figsize)
            
            # 应用子图参数
            if subplot_params:
                plt.subplots_adjust(**subplot_params)
            
            # 设置标题 - 支持双语
            if title:
                display_title = title
                if bilingual_title:
                    display_title = f"{title}\n{bilingual_title}"
                
                ax.set_title(display_title, fontsize=12, pad=25,
                           fontweight='bold', color='#333333')
            
            return fig, ax
            
        except Exception as e:
            self.logger.error(f"创建图形失败: {str(e)}")
            raise
            # 返回默认图形
            return plt.subplots(figsize=(10, 6))
    
    def create_subplot_figure(self, nrows: int, ncols: int, 
                            figsize: Tuple[int, int] = None) -> Tuple[plt.Figure, np.ndarray]:
        """
        创建多子图图形
        
        Args:
            nrows: 行数
            ncols: 列数
            figsize: 图形尺寸
            
        Returns:
            图形和轴数组的元组
        """
        try:
            if figsize is None:
                # 根据子图数量调整尺寸
                base_size = self.chart_config.get_figure_size()
                figsize = (base_size[0] * ncols * 0.8, base_size[1] * nrows * 0.8)
            
            fig, axes = plt.subplots(nrows, ncols, figsize=figsize)
            
            # 确保axes是数组
            if nrows == 1 and ncols == 1:
                axes = np.array([axes])
            elif nrows == 1 or ncols == 1:
                # 对于1维的情况，保持为1D数组
                axes = np.array(axes).reshape(-1)
            else:
                # 对于2D的情况，确保是2D数组
                axes = np.array(axes)
            
            # 调整子图间距
            plt.tight_layout(pad=3.0)
            
            return fig, axes
            
        except Exception as e:
            self.logger.error(f"创建多子图失败: {str(e)}")
            return plt.subplots(figsize=(12, 8))
    
    def add_title_and_labels(self, ax: plt.Axes, title: str = "", 
                           xlabel: str = "", ylabel: str = "",
                           title_en: str = "", xlabel_en: str = "", ylabel_en: str = ""):
        """
        添加标题和轴标签（支持双语）
        
        Args:
            ax: 轴对象
            title: 中文标题
            xlabel: 中文x轴标签
            ylabel: 中文y轴标签
            title_en: 英文标题
            xlabel_en: 英文x轴标签
            ylabel_en: 英文y轴标签
        """
        try:
            # 处理双语标题
            if title:
                final_title = self.chart_config.get_bilingual_text(title, title_en)
                ax.set_title(final_title, fontsize=self.chart_config.current_font.title_size,
                           fontweight='bold', pad=20)
            
            # 处理双语轴标签
            if xlabel:
                final_xlabel = self.chart_config.get_bilingual_text(xlabel, xlabel_en)
                ax.set_xlabel(final_xlabel, fontsize=self.chart_config.current_font.label_size)
            
            if ylabel:
                final_ylabel = self.chart_config.get_bilingual_text(ylabel, ylabel_en)
                ax.set_ylabel(final_ylabel, fontsize=self.chart_config.current_font.label_size)
            
            # 应用图表样式
            self.chart_config.apply_chart_style(ax)
            
        except Exception as e:
            self.logger.error(f"添加标题和标签失败: {str(e)}")
    
    def add_data_annotations(self, ax: plt.Axes, x_data: np.ndarray, y_data: np.ndarray,
                           annotations: List[str] = None, annotation_style: str = 'value'):
        """
        添加数据注释
        
        Args:
            ax: 轴对象
            x_data: x坐标数据
            y_data: y坐标数据
            annotations: 自定义注释文本
            annotation_style: 注释样式 ('value', 'percentage', 'custom')
        """
        try:
            if annotations is None:
                if annotation_style == 'value':
                    annotations = [f'{y:.1f}' for y in y_data]
                elif annotation_style == 'percentage':
                    total = sum(y_data)
                    annotations = [f'{y/total*100:.1f}%' for y in y_data]
                else:
                    annotations = [str(i) for i in range(len(y_data))]
            
            for x, y, text in zip(x_data, y_data, annotations):
                ax.annotate(text, (x, y), 
                          textcoords="offset points",
                          xytext=(0, 10),
                          ha='center',
                          fontsize=self.chart_config.current_font.annotation_size,
                          color=self.chart_config.current_palette.text,
                          bbox=dict(boxstyle="round,pad=0.3",
                                  facecolor=self.chart_config.current_palette.background,
                                  alpha=0.8,
                                  edgecolor=self.chart_config.current_palette.grid))
                                  
        except Exception as e:
            self.logger.error(f"添加数据注释失败: {str(e)}")
    
    def add_statistical_info(self, ax: plt.Axes, data: np.ndarray, 
                           position: str = 'top_right'):
        """
        添加统计信息
        
        Args:
            ax: 轴对象
            data: 数据数组
            position: 位置 ('top_right', 'top_left', 'bottom_right', 'bottom_left')
        """
        try:
            # 计算统计量
            mean_val = np.mean(data)
            std_val = np.std(data)
            min_val = np.min(data)
            max_val = np.max(data)
            
            # 构建统计信息文本
            stats_text = f'均值: {mean_val:.2f}\n标准差: {std_val:.2f}\n最小值: {min_val:.2f}\n最大值: {max_val:.2f}'
            
            # 确定位置
            position_map = {
                'top_right': (0.95, 0.95),
                'top_left': (0.05, 0.95),
                'bottom_right': (0.95, 0.05),
                'bottom_left': (0.05, 0.05)
            }
            
            x, y = position_map.get(position, (0.95, 0.95))
            ha = 'right' if 'right' in position else 'left'
            va = 'top' if 'top' in position else 'bottom'
            
            # 添加文本框
            ax.text(x, y, stats_text,
                   transform=ax.transAxes,
                   fontsize=self.chart_config.current_font.annotation_size,
                   ha=ha, va=va,
                   bbox=dict(boxstyle="round,pad=0.5",
                           facecolor=self.chart_config.current_palette.background,
                           alpha=0.9,
                           edgecolor=self.chart_config.current_palette.grid))
                           
        except Exception as e:
            self.logger.error(f"添加统计信息失败: {str(e)}")
    
    def add_professional_watermark(self, ax: plt.Axes, text: str = "Building Facade Optimization"):
        """
        添加专业水印
        
        Args:
            ax: 轴对象
            text: 水印文本
        """
        try:
            ax.text(0.5, 0.5, text,
                   transform=ax.transAxes,
                   fontsize=self.chart_config.current_font.title_size * 2,
                   alpha=0.1,
                   ha='center', va='center',
                   rotation=45,
                   color=self.chart_config.current_palette.neutral)
                   
        except Exception as e:
            self.logger.error(f"添加专业水印失败: {str(e)}")
    
    def customize_axis_format(self, ax: plt.Axes, x_format: str = None, y_format: str = None):
        """
        自定义轴格式
        
        Args:
            ax: 轴对象
            x_format: x轴格式
            y_format: y轴格式
        """
        try:
            from matplotlib.ticker import FuncFormatter
            
            if x_format:
                if x_format == 'percentage':
                    ax.xaxis.set_major_formatter(FuncFormatter(lambda x, p: f'{x:.1f}%'))
                elif x_format == 'currency':
                    ax.xaxis.set_major_formatter(FuncFormatter(lambda x, p: f'¥{x:,.0f}'))
                elif x_format == 'scientific':
                    ax.xaxis.set_major_formatter(FuncFormatter(lambda x, p: f'{x:.2e}'))
            
            if y_format:
                if y_format == 'percentage':
                    ax.yaxis.set_major_formatter(FuncFormatter(lambda y, p: f'{y:.1f}%'))
                elif y_format == 'currency':
                    ax.yaxis.set_major_formatter(FuncFormatter(lambda y, p: f'¥{y:,.0f}'))
                elif y_format == 'scientific':
                    ax.yaxis.set_major_formatter(FuncFormatter(lambda y, p: f'{y:.2e}'))
                    
        except Exception as e:
            self.logger.error(f"自定义轴格式失败: {str(e)}")
    
    def save_chart(self, fig: plt.Figure, filename: str, 
                  custom_path: str = None, **kwargs) -> List[str]:
        """
        保存图表
        
        Args:
            fig: 图形对象
            filename: 文件名（不含扩展名）
            custom_path: 自定义保存路径
            **kwargs: 保存参数
            
        Returns:
            保存的文件路径列表
        """
        saved_files = []
        
        try:
            # 确定保存路径
            save_path = custom_path if custom_path else self.output_dir
            Path(save_path).mkdir(parents=True, exist_ok=True)
            
            # 转换为中文文件名
            chinese_filename = self.filename_mapping.get(filename, filename)

            # 添加时间戳
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = f"{chinese_filename}_{timestamp}"
            
            # 保存不同格式
            for fmt in self.save_formats:
                filepath = os.path.join(save_path, f"{base_filename}.{fmt}")
                
                # 保存参数
                save_params = {
                    'dpi': self.chart_config.dpi,
                    'bbox_inches': 'tight',
                    'facecolor': self.chart_config.current_palette.background,
                    'edgecolor': 'none',
                    'transparent': False,
                    'pad_inches': 0.2
                }
                save_params.update(kwargs)
                
                # 保存文件
                fig.savefig(filepath, format=fmt, **save_params)
                saved_files.append(filepath)
                
                self.logger.info(f"图表已保存: {filepath}")
            
            return saved_files
            
        except Exception as e:
            self.logger.error(f"保存图表失败: {str(e)}")
            return []
    
    def create_color_gradient(self, values: np.ndarray, colormap: str = None) -> List[str]:
        """
        创建颜色渐变
        
        Args:
            values: 数值数组
            colormap: 颜色映射名称
            
        Returns:
            颜色列表
        """
        try:
            if colormap is None:
                # 使用自定义颜色映射
                colors = [
                    self.chart_config.current_palette.primary,
                    self.chart_config.current_palette.secondary,
                    self.chart_config.current_palette.accent
                ]
                cmap = self.chart_config.create_custom_colormap(colors)
            else:
                cmap = plt.cm.get_cmap(colormap)
            
            # 安全的归一化数值，避免除零警告
            min_val = np.min(values)
            max_val = np.max(values)
            value_range = max_val - min_val

            if value_range == 0 or np.isclose(value_range, 0):
                # 如果所有值相同，使用中间值
                norm_values = np.full_like(values, 0.5)
            else:
                norm_values = (values - min_val) / value_range
            
            # 生成颜色
            colors = [cmap(val) for val in norm_values]
            
            # 转换为十六进制
            hex_colors = []
            for color in colors:
                if len(color) >= 3:
                    hex_color = '#{:02x}{:02x}{:02x}'.format(
                        int(color[0] * 255),
                        int(color[1] * 255),
                        int(color[2] * 255)
                    )
                    hex_colors.append(hex_color)
                else:
                    hex_colors.append(self.chart_config.current_palette.primary)
            
            return hex_colors
            
        except Exception as e:
            self.logger.error(f"创建颜色渐变失败: {str(e)}")
            return [self.chart_config.current_palette.primary] * len(values)
    
    def add_benchmark_lines(self, ax: plt.Axes, benchmarks: Dict[str, float],
                          line_styles: Dict[str, str] = None):
        """
        添加基准线
        
        Args:
            ax: 轴对象
            benchmarks: 基准值字典 {'name': value}
            line_styles: 线条样式字典
        """
        try:
            default_styles = {
                'excellent': '--',
                'good': '-.',
                'average': ':',
                'poor': '-'
            }
            
            if line_styles is None:
                line_styles = default_styles
            
            colors = self.chart_config.get_color_sequence(len(benchmarks))
            
            for i, (name, value) in enumerate(benchmarks.items()):
                style = line_styles.get(name, '-')
                color = colors[i % len(colors)]
                
                ax.axhline(y=value, color=color, linestyle=style, 
                         linewidth=2, alpha=0.7, label=f'{name}: {value}')
            
            # 添加图例
            ax.legend(loc='best')
            
        except Exception as e:
            self.logger.error(f"添加基准线失败: {str(e)}")
    
    def cleanup_figure(self, fig: plt.Figure):
        """
        清理图形资源
        
        Args:
            fig: 图形对象
        """
        try:
            plt.close(fig)
        except Exception as e:
            self.logger.error(f"清理图形资源失败: {str(e)}")


def create_base_visualizer() -> BaseVisualizer:
    """
    创建基础可视化器实例
    
    Returns:
        配置好的基础可视化器
    """
    return BaseVisualizer()