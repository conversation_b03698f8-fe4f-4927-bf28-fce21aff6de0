"""
收敛监控器
监控NSGA-III优化算法的收敛过程和终止条件
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import ConvergenceError, handle_exception
from ..core.data_structures import (
    ObjectiveResults, PopulationStatistics, ConvergenceMetrics
)
from ..core.utils import MathUtils, TimeUtils


class ConvergenceStatus(Enum):
    """收敛状态枚举"""
    RUNNING = "running"
    CONVERGED = "converged"
    STAGNATED = "stagnated"
    MAX_GENERATIONS = "max_generations"
    MAX_TIME = "max_time"
    USER_TERMINATED = "user_terminated"


@dataclass
class TerminationCriteria:
    """终止条件"""
    max_generations: int = 500
    max_time_seconds: int = 3600  # 1小时
    convergence_threshold: float = 1e-6
    stagnation_generations: int = 50
    min_improvement_ratio: float = 0.01
    target_hypervolume: Optional[float] = None
    target_objectives: Optional[List[float]] = None


class ConvergenceMonitor:
    """
    收敛监控器
    
    功能：
    1. 监控优化过程的收敛状态
    2. 计算收敛指标和性能指标
    3. 判断终止条件
    4. 记录优化历史和统计信息
    5. 提供早停机制
    6. 生成收敛分析报告
    """
    
    def __init__(self):
        """初始化收敛监控器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取收敛监控配置
        optimization_config = self.config.get_section('optimization')
        convergence_config = optimization_config.get('convergence_monitoring', {})
        
        # 大幅优化的终止条件 - 防止过早收敛
        self.termination_criteria = TerminationCriteria(
            max_generations=convergence_config.get('max_generations', 300),  # 进一步增加到300代
            max_time_seconds=convergence_config.get('max_time_seconds', 5400), # 增加到90分钟
            convergence_threshold=convergence_config.get('convergence_threshold', 1e-6), # 更严格的收敛阈值
            stagnation_generations=convergence_config.get('stagnation_generations', 100), # 大幅增加停滞检测代数到100
            min_improvement_ratio=convergence_config.get('min_improvement_ratio', 0.0001), # 进一步降低改进要求
            target_hypervolume=convergence_config.get('target_hypervolume'),
            target_objectives=convergence_config.get('target_objectives')
        )
        
        # 收敛历史记录
        self.convergence_history = []
        self.hypervolume_history = []
        self.diversity_history = []
        self.best_objectives_history = []
        
        # 运行时信息
        self.start_time = None
        self.current_generation = 0
        self.convergence_status = ConvergenceStatus.RUNNING
        self.stagnation_counter = 0
        self.best_known_objectives = [float('inf')] * 3
        self.last_significant_improvement = 0
        
        # 参考点用于计算超体积 - 基于实际数据调整参考点
        # 修复：设置更合理的参考点，确保所有解都能产生正的超体积贡献
        self.reference_point = [80.0, 0.8, 15000.0]  # 能耗, 热工性能, 成本 (确保劣于最差解)
        
        self.logger.info("收敛监控器初始化完成")
    
    def start_monitoring(self) -> None:
        """开始监控"""
        self.start_time = datetime.now()
        self.current_generation = 0
        self.convergence_status = ConvergenceStatus.RUNNING
        self.stagnation_counter = 0
        self.best_known_objectives = [float('inf')] * 3
        self.last_significant_improvement = 0
        
        # 清空历史记录
        self.convergence_history.clear()
        self.hypervolume_history.clear()
        self.diversity_history.clear()
        self.best_objectives_history.clear()
        
        self.logger.info("开始优化收敛监控")
    
    @handle_exception
    def update_convergence_metrics(self, population_stats: PopulationStatistics,
                                 evaluation_results: List[ObjectiveResults]) -> ConvergenceMetrics:
        """
        更新收敛指标
        
        Args:
            population_stats: 种群统计信息
            evaluation_results: 评估结果列表
            
        Returns:
            收敛指标
            
        Raises:
            ConvergenceError: 指标计算失败时抛出
        """
        with LogContext(f"收敛监控 - 第{self.current_generation}代", self.logger):
            try:
                self.current_generation = population_stats.generation
                
                # 计算收敛指标
                convergence_metrics = self._calculate_convergence_metrics(
                    population_stats, evaluation_results
                )
                
                # 更新历史记录
                self._update_history(convergence_metrics, evaluation_results)
                
                # 检查改进情况
                self._check_improvement(convergence_metrics)
                
                # 判断收敛状态
                self._update_convergence_status(convergence_metrics)
                
                # 记录收敛信息
                self.convergence_history.append(convergence_metrics)
                
                self.logger.debug(f"收敛指标更新完成 - 第{self.current_generation}代: "
                                f"超体积={convergence_metrics.hypervolume:.6f}, "
                                f"多样性={convergence_metrics.diversity_metric:.6f}")
                
                return convergence_metrics
                
            except Exception as e:
                raise ConvergenceError(f"收敛指标更新失败: {str(e)}") from e
    
    def should_terminate(self) -> Tuple[bool, ConvergenceStatus]:
        """
        判断是否应该终止优化
        
        Returns:
            (是否终止, 终止原因)
        """
        try:
            # 检查最大代数
            if self.current_generation >= self.termination_criteria.max_generations:
                self.convergence_status = ConvergenceStatus.MAX_GENERATIONS
                return True, self.convergence_status
            
            # 检查时间限制
            if self.start_time:
                elapsed_time = (datetime.now() - self.start_time).total_seconds()
                if elapsed_time >= self.termination_criteria.max_time_seconds:
                    self.convergence_status = ConvergenceStatus.MAX_TIME
                    return True, self.convergence_status
            
            # 检查收敛
            if self._check_convergence():
                self.convergence_status = ConvergenceStatus.CONVERGED
                return True, self.convergence_status
            
            # 检查停滞
            if self._check_stagnation():
                self.convergence_status = ConvergenceStatus.STAGNATED
                return True, self.convergence_status
            
            # 检查目标达成
            if self._check_target_achievement():
                self.convergence_status = ConvergenceStatus.CONVERGED
                return True, self.convergence_status
            
            return False, ConvergenceStatus.RUNNING
            
        except Exception as e:
            self.logger.error(f"终止条件检查失败: {str(e)}")
            return False, ConvergenceStatus.RUNNING
    
    def _calculate_convergence_metrics(self, population_stats: PopulationStatistics,
                                     evaluation_results: List[ObjectiveResults]) -> ConvergenceMetrics:
        """计算收敛指标"""
        try:
            # 过滤可行解
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            
            # 基本指标
            generation = population_stats.generation
            feasibility_ratio = len(feasible_results) / len(evaluation_results) if evaluation_results else 0.0
            
            # 计算超体积
            hypervolume = self._calculate_hypervolume(feasible_results)
            
            # 计算多样性指标
            diversity_metric = self._calculate_diversity_metric(evaluation_results)
            
            # 计算收敛性指标
            convergence_metric = self._calculate_convergence_metric(feasible_results)
            
            # 计算分布均匀性
            distribution_uniformity = self._calculate_distribution_uniformity(feasible_results)
            
            # 计算改进率
            improvement_rate = self._calculate_improvement_rate()
            
            # 计算最佳目标值
            best_objectives = self._get_best_objectives(feasible_results)
            
            # 计算平均目标值
            avg_objectives = self._get_average_objectives(evaluation_results)
            
            # 计算约束违反程度
            constraint_violation = self._calculate_constraint_violation(evaluation_results)
            
            # 计算运行时间
            elapsed_time = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0.0
            
            convergence_metrics = ConvergenceMetrics(
                generation=generation,
                elapsed_time=elapsed_time,
                hypervolume=hypervolume,
                diversity_metric=diversity_metric,
                convergence_metric=convergence_metric,
                distribution_uniformity=distribution_uniformity,
                feasibility_ratio=feasibility_ratio,
                improvement_rate=improvement_rate,
                best_objectives=best_objectives,
                average_objectives=avg_objectives,
                constraint_violation=constraint_violation,
                stagnation_count=self.stagnation_counter
            )
            
            return convergence_metrics
            
        except Exception as e:
            self.logger.error(f"计算收敛指标失败: {str(e)}")
            # 返回默认指标
            return ConvergenceMetrics(
                generation=population_stats.generation,
                elapsed_time=0.0,
                hypervolume=0.0,
                diversity_metric=0.0,
                convergence_metric=1.0,
                distribution_uniformity=0.0,
                feasibility_ratio=0.0,
                improvement_rate=0.0,
                best_objectives=[float('inf')] * 3,
                average_objectives=[float('inf')] * 3,
                constraint_violation=0.0,
                stagnation_count=0
            )
    
    def _calculate_hypervolume(self, feasible_results: List[ObjectiveResults]) -> float:
        """计算超体积指标"""
        try:
            if not feasible_results:
                return 0.0
            
            # 提取目标函数值
            objectives_matrix = np.array([
                [r.energy_consumption, r.thermal_performance, r.renovation_cost]
                for r in feasible_results
            ])
            
            # 找到非支配解
            non_dominated_indices = self._find_non_dominated_solutions(objectives_matrix)
            non_dominated_objectives = objectives_matrix[non_dominated_indices]
            
            # 计算超体积（简化版本，适用于3维）
            hypervolume = self._compute_hypervolume_3d(
                non_dominated_objectives, self.reference_point
            )
            
            return hypervolume
            
        except Exception as e:
            self.logger.warning(f"计算超体积失败: {str(e)}")
            return 0.0
    
    def _compute_hypervolume_3d(self, objectives: np.ndarray, reference_point: List[float]) -> float:
        """计算3维超体积（修复版本）"""
        try:
            if len(objectives) == 0:
                return 0.0

            # 使用固定参考点
            fixed_reference = np.array(reference_point)

            # 找到非支配解
            non_dominated_indices = self._find_non_dominated_solutions(objectives)
            if not non_dominated_indices:
                return 0.0

            non_dominated_objectives = objectives[non_dominated_indices]

            # 修复：使用改进的超体积计算方法
            total_volume = 0.0

            # 对于每个非支配解，计算其超体积贡献
            for i, obj in enumerate(non_dominated_objectives):
                # 计算该解相对于参考点的体积贡献
                volume_contribution = 1.0

                for dim in range(3):
                    obj_value = obj[dim]
                    ref_value = fixed_reference[dim]

                    # 修复：对于热工性能（第2维），值越大越好
                    if dim == 1:  # 热工性能维度
                        if obj_value > 0:
                            improvement = obj_value / ref_value if ref_value > 0 else obj_value
                            volume_contribution *= min(improvement, 2.0)  # 限制最大改进倍数
                        else:
                            volume_contribution = 0.0
                            break
                    else:  # 能耗和成本维度（越小越好）
                        if ref_value > 0 and obj_value < ref_value:
                            improvement = (ref_value - obj_value) / ref_value
                            volume_contribution *= improvement
                        elif obj_value >= ref_value:
                            # 如果解劣于参考点，仍给予小的贡献以避免超体积为0
                            penalty = max(0.01, 1.0 - (obj_value - ref_value) / ref_value)
                            volume_contribution *= penalty
                        else:
                            volume_contribution = 0.0
                            break

                total_volume += volume_contribution

            # 应用缩放因子以获得合理的数值范围
            total_volume *= 10.0  # 减小缩放因子，避免数值过大

            return max(0.001, total_volume)  # 确保最小值不为0

        except Exception as e:
            self.logger.warning(f"超体积计算异常: {str(e)}")
            return 0.001  # 返回小的正值而不是0
    
    def _compute_relative_hypervolume(self, objectives: np.ndarray, reference_point: np.ndarray) -> float:
        """计算相对超体积（当所有解都优于参考点时）"""
        try:
            if len(objectives) == 0:
                return 0.0
            
            # 计算每个解相对于参考点的改进程度
            total_volume = 0.0
            
            # 找到每个维度的最差值作为局部参考点
            worst_values = np.max(objectives, axis=0)
            local_reference = np.maximum(worst_values * 1.1, reference_point * 0.5)
            
            # 按第一个目标函数排序
            sorted_indices = np.argsort(objectives[:, 0])
            sorted_objectives = objectives[sorted_indices]
            
            # 计算每个解的贡献体积
            for i, obj in enumerate(sorted_objectives):
                volume_contribution = 1.0
                
                # 对每个维度计算相对改进
                for dim in range(3):
                    obj_value = obj[dim]
                    local_ref = local_reference[dim]
                    
                    # 计算相对改进（越小越好）
                    if local_ref > 0:
                        improvement = max(0, (local_ref - obj_value) / local_ref)
                    else:
                        improvement = 0.0
                    
                    volume_contribution *= improvement
                
                # 减去被支配的体积
                for j, other_obj in enumerate(sorted_objectives):
                    if i != j:
                        if all(obj[k] >= other_obj[k] for k in range(3)) and any(obj[k] > other_obj[k] for k in range(3)):
                            volume_contribution *= 0.8  # 被支配，减少贡献
                
                total_volume += volume_contribution
            
            # 应用缩放因子
            total_volume *= 0.05
            
            return max(0, total_volume)
            
        except Exception as e:
            self.logger.warning(f"相对超体积计算异常: {str(e)}")
            return 0.0
    
    def _calculate_constraint_violation(self, evaluation_results: List[ObjectiveResults]) -> float:
        """计算平均约束违反程度"""
        try:
            if not evaluation_results:
                return 0.0
            
            # 提取所有解的约束违反程度
            violations = [r.constraint_violations for r in evaluation_results]
            
            # 计算平均约束违反程度
            avg_violation = np.mean(violations) if violations else 0.0
            
            return avg_violation
            
        except Exception as e:
            self.logger.warning(f"计算约束违反程度失败: {str(e)}")
            return 0.0
    
    def _find_non_dominated_solutions(self, objectives: np.ndarray) -> List[int]:
        """找到非支配解的索引"""
        try:
            non_dominated = []
            
            for i in range(len(objectives)):
                is_dominated = False
                
                for j in range(len(objectives)):
                    if i != j:
                        # 检查j是否支配i
                        dominates = True
                        better_in_at_least_one = False
                        
                        for k in range(objectives.shape[1]):
                            if objectives[j, k] > objectives[i, k]:  # j在第k个目标上劣于i
                                dominates = False
                                break
                            elif objectives[j, k] < objectives[i, k]:  # j在第k个目标上优于i
                                better_in_at_least_one = True
                        
                        if dominates and better_in_at_least_one:
                            is_dominated = True
                            break
                
                if not is_dominated:
                    non_dominated.append(i)
            
            return non_dominated
            
        except Exception:
            return list(range(len(objectives)))
    
    def _calculate_diversity_metric(self, evaluation_results: List[ObjectiveResults]) -> float:
        """计算多样性指标"""
        try:
            if len(evaluation_results) < 2:
                return 0.0
            
            # 提取目标函数值
            objectives_matrix = np.array([
                [r.energy_consumption, r.thermal_performance, r.renovation_cost]
                for r in evaluation_results
            ])
            
            # 计算目标函数的标准差
            std_devs = np.std(objectives_matrix, axis=0)
            
            # 归一化标准差
            means = np.mean(objectives_matrix, axis=0)
            normalized_stds = []
            
            for i in range(len(std_devs)):
                if means[i] > 1e-10:
                    normalized_stds.append(std_devs[i] / means[i])
                else:
                    normalized_stds.append(0.0)
            
            # 多样性指标为归一化标准差的平均值
            diversity = np.mean(normalized_stds)
            
            return min(diversity, 1.0)  # 限制在[0,1]范围内
            
        except Exception:
            return 0.0
    
    def _calculate_convergence_metric(self, feasible_results: List[ObjectiveResults]) -> float:
        """计算收敛性指标（修复版本）"""
        try:
            if not feasible_results or len(self.convergence_history) < 2:
                return 1.0  # 初期认为未收敛

            # 修复：使用更合理的收敛性判断方法
            # 方法1：基于超体积变化率
            recent_hypervolumes = [
                metrics.hypervolume for metrics in self.convergence_history[-min(5, len(self.convergence_history)):]
            ]

            hypervolume_convergence = 1.0
            if len(recent_hypervolumes) >= 2:
                # 计算相对变化率
                changes = []
                for i in range(1, len(recent_hypervolumes)):
                    current_hv = recent_hypervolumes[i]
                    previous_hv = recent_hypervolumes[i-1]

                    if previous_hv > 1e-6:  # 避免除零
                        relative_change = abs(current_hv - previous_hv) / previous_hv
                    else:
                        relative_change = 1.0 if current_hv > 1e-6 else 0.0

                    changes.append(relative_change)

                if changes:
                    avg_change = np.mean(changes)
                    # 变化率越小，收敛性越好（值越小）
                    hypervolume_convergence = min(1.0, avg_change * 10)  # 放大变化率

            # 方法2：基于目标函数值的标准差
            objectives_matrix = np.array([
                [r.energy_consumption, r.thermal_performance, r.renovation_cost]
                for r in feasible_results
            ])

            # 计算每个目标的变异系数
            objective_convergence = 0.0
            for dim in range(3):
                values = objectives_matrix[:, dim]
                if len(values) > 1 and np.mean(values) > 1e-6:
                    cv = np.std(values) / np.mean(values)  # 变异系数
                    objective_convergence += cv

            objective_convergence /= 3  # 平均变异系数

            # 综合收敛性指标
            convergence_metric = 0.7 * hypervolume_convergence + 0.3 * min(1.0, objective_convergence)

            return min(convergence_metric, 1.0)

        except Exception as e:
            self.logger.warning(f"计算收敛性指标失败: {str(e)}")
            return 1.0
            
        except Exception:
            return 1.0
    
    def _calculate_distribution_uniformity(self, feasible_results: List[ObjectiveResults]) -> float:
        """计算分布均匀性"""
        try:
            if len(feasible_results) < 3:
                return 0.0
            
            # 提取目标函数值
            objectives_matrix = np.array([
                [r.energy_consumption, r.thermal_performance, r.renovation_cost]
                for r in feasible_results
            ])
            
            # 找到非支配解
            non_dominated_indices = self._find_non_dominated_solutions(objectives_matrix)
            if len(non_dominated_indices) < 3:
                return 0.0
            
            non_dominated_objectives = objectives_matrix[non_dominated_indices]
            
            # 计算相邻解之间的距离
            distances = []
            for i in range(len(non_dominated_objectives)):
                min_distance = float('inf')
                for j in range(len(non_dominated_objectives)):
                    if i != j:
                        distance = np.linalg.norm(non_dominated_objectives[i] - non_dominated_objectives[j])
                        min_distance = min(min_distance, distance)
                distances.append(min_distance)
            
            # 均匀性指标：距离的变异系数的倒数
            if distances:
                mean_distance = np.mean(distances)
                std_distance = np.std(distances)
                
                if mean_distance > 1e-10:
                    coefficient_of_variation = std_distance / mean_distance
                    uniformity = 1.0 / (1.0 + coefficient_of_variation)
                else:
                    uniformity = 0.0
            else:
                uniformity = 0.0
            
            return uniformity
            
        except Exception:
            return 0.0
    
    def _calculate_improvement_rate(self) -> float:
        """计算改进率"""
        try:
            if len(self.best_objectives_history) < 2:
                return 0.0
            
            # 计算最近几代的改进率
            window_size = min(10, len(self.best_objectives_history))
            recent_objectives = self.best_objectives_history[-window_size:]
            
            improvements = 0
            total_comparisons = 0
            
            for i in range(1, len(recent_objectives)):
                for j in range(3):  # 三个目标
                    if recent_objectives[i][j] < recent_objectives[i-1][j]:
                        improvements += 1
                    total_comparisons += 1
            
            if total_comparisons > 0:
                improvement_rate = improvements / total_comparisons
            else:
                improvement_rate = 0.0
            
            return improvement_rate
            
        except Exception:
            return 0.0
    
    def _get_best_objectives(self, feasible_results: List[ObjectiveResults]) -> List[float]:
        """获取最佳目标值"""
        if not feasible_results:
            return [float('inf')] * 3
        
        best_objectives = [
            min(r.energy_consumption for r in feasible_results),
            min(r.thermal_performance for r in feasible_results),
            min(r.renovation_cost for r in feasible_results)
        ]
        
        return best_objectives
    
    def _get_average_objectives(self, evaluation_results: List[ObjectiveResults]) -> List[float]:
        """获取平均目标值"""
        if not evaluation_results:
            return [float('inf')] * 3
        
        avg_objectives = [
            np.mean([r.energy_consumption for r in evaluation_results]),
            np.mean([r.thermal_performance for r in evaluation_results]),
            np.mean([r.renovation_cost for r in evaluation_results])
        ]
        
        return avg_objectives
    
    def _update_history(self, convergence_metrics: ConvergenceMetrics,
                       evaluation_results: List[ObjectiveResults]) -> None:
        """更新历史记录"""
        try:
            # 更新超体积历史
            self.hypervolume_history.append(convergence_metrics.hypervolume)
            
            # 更新多样性历史
            self.diversity_history.append(convergence_metrics.diversity_metric)
            
            # 更新最佳目标历史
            self.best_objectives_history.append(convergence_metrics.best_objectives)
            
            # 限制历史记录长度
            max_history_length = 1000
            if len(self.hypervolume_history) > max_history_length:
                self.hypervolume_history = self.hypervolume_history[-max_history_length:]
                self.diversity_history = self.diversity_history[-max_history_length:]
                self.best_objectives_history = self.best_objectives_history[-max_history_length:]
            
        except Exception as e:
            self.logger.warning(f"更新历史记录失败: {str(e)}")
    
    def _check_improvement(self, convergence_metrics: ConvergenceMetrics) -> None:
        """检查改进情况"""
        try:
            current_best = convergence_metrics.best_objectives
            
            # 检查是否有显著改进
            has_significant_improvement = False
            
            for i in range(3):
                if current_best[i] < self.best_known_objectives[i] * (1 - self.termination_criteria.min_improvement_ratio):
                    has_significant_improvement = True
                    self.best_known_objectives[i] = current_best[i]
            
            if has_significant_improvement:
                self.last_significant_improvement = self.current_generation
                self.stagnation_counter = 0
            else:
                self.stagnation_counter += 1
            
        except Exception as e:
            self.logger.warning(f"检查改进情况失败: {str(e)}")
    
    def _update_convergence_status(self, convergence_metrics: ConvergenceMetrics) -> None:
        """更新收敛状态"""
        try:
            # 如果收敛指标足够小，认为已收敛
            if (convergence_metrics.convergence_metric < self.termination_criteria.convergence_threshold and
                convergence_metrics.feasibility_ratio > 0.8):
                self.convergence_status = ConvergenceStatus.CONVERGED
            elif self.stagnation_counter >= self.termination_criteria.stagnation_generations:
                self.convergence_status = ConvergenceStatus.STAGNATED
            else:
                self.convergence_status = ConvergenceStatus.RUNNING
            
        except Exception as e:
            self.logger.warning(f"更新收敛状态失败: {str(e)}")
    
    def _check_convergence(self) -> bool:
        """
        修复的收敛检查 - 防止过早终止
        """
        # 至少运行250代才开始检查收敛
        if len(self.convergence_history) < 250:
            return False

        # 只有在运行了足够代数后才检查收敛
        if self.current_generation < 280:
            return False

        recent_metrics = self.convergence_history[-10:]  # 增加检查历史长度

        # 检查目标函数改进是否足够小
        if len(recent_metrics) >= 5:
            recent_improvements = []
            for i in range(1, len(recent_metrics)):
                prev_best = recent_metrics[i-1].best_objectives
                curr_best = recent_metrics[i].best_objectives

                if prev_best and curr_best and len(prev_best) == len(curr_best):
                    # 计算相对改进
                    improvements = []
                    for j in range(len(prev_best)):
                        if abs(prev_best[j]) > 1e-10:  # 避免除零
                            rel_improvement = abs(curr_best[j] - prev_best[j]) / abs(prev_best[j])
                            improvements.append(rel_improvement)

                    if improvements:
                        recent_improvements.append(np.mean(improvements))

            # 提高收敛要求：连续多代改进都很小才认为收敛
            if (len(recent_improvements) >= 8 and
                np.mean(recent_improvements) < self.termination_criteria.min_improvement_ratio * 0.1):
                return True

        return False  # 默认不收敛，让算法继续运行
    
    def _check_stagnation(self) -> bool:
        """
        修复的停滞检查 - 防止过早判断停滞
        """
        # 至少运行200代才开始检查停滞
        if self.current_generation < 200:
            return False

        # 进一步提高停滞阈值 - 需要连续停滞更长时间才认为真正停滞
        return self.stagnation_counter >= (self.termination_criteria.stagnation_generations * 2)
    
    def _check_target_achievement(self) -> bool:
        """检查是否达到目标"""
        if not self.termination_criteria.target_objectives:
            return False
        
        if not self.best_objectives_history:
            return False
        
        current_best = self.best_objectives_history[-1]
        target = self.termination_criteria.target_objectives
        
        # 检查是否所有目标都达到或优于目标值
        return all(current_best[i] <= target[i] for i in range(len(target)))
    
    def get_convergence_summary(self) -> Dict[str, Any]:
        """获取收敛总结"""
        try:
            elapsed_time = (datetime.now() - self.start_time).total_seconds() if self.start_time else 0.0
            
            latest_metrics = self.convergence_history[-1] if self.convergence_history else None
            
            summary = {
                'status': self.convergence_status.value,
                'current_generation': self.current_generation,
                'elapsed_time': elapsed_time,
                'stagnation_count': self.stagnation_counter,
                'total_evaluations': self.current_generation * 100,  # 假设种群大小为100
                'best_known_objectives': self.best_known_objectives,
                'convergence_metrics': {
                    'hypervolume': latest_metrics.hypervolume if latest_metrics else 0.0,
                    'diversity': latest_metrics.diversity_metric if latest_metrics else 0.0,
                    'convergence': latest_metrics.convergence_metric if latest_metrics else 1.0,
                    'feasibility_ratio': latest_metrics.feasibility_ratio if latest_metrics else 0.0
                },
                'termination_criteria': {
                    'max_generations': self.termination_criteria.max_generations,
                    'max_time_seconds': self.termination_criteria.max_time_seconds,
                    'convergence_threshold': self.termination_criteria.convergence_threshold,
                    'stagnation_generations': self.termination_criteria.stagnation_generations
                }
            }
            
            return summary
            
        except Exception as e:
            self.logger.error(f"生成收敛总结失败: {str(e)}")
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def generate_convergence_report(self) -> str:
        """生成收敛分析报告"""
        try:
            summary = self.get_convergence_summary()
            
            report = f"""
# 优化收敛分析报告

## 基本信息
- **优化状态**: {summary['status']}
- **当前代数**: {summary['current_generation']}
- **运行时间**: {summary['elapsed_time']:.1f} 秒
- **停滞代数**: {summary['stagnation_count']}
- **总评估次数**: {summary['total_evaluations']}

## 最佳目标值
- **能耗**: {summary['best_known_objectives'][0]:.2f} kWh/m²·year
- **热工性能**: {summary['best_known_objectives'][1]:.4f}
- **改造成本**: {summary['best_known_objectives'][2]:.0f} 元

## 收敛指标
- **超体积**: {summary['convergence_metrics']['hypervolume']:.6f}
- **多样性**: {summary['convergence_metrics']['diversity']:.4f}
- **收敛度**: {summary['convergence_metrics']['convergence']:.6f}
- **可行解比例**: {summary['convergence_metrics']['feasibility_ratio']:.2%}

## 终止条件
- **最大代数**: {summary['termination_criteria']['max_generations']}
- **最大时间**: {summary['termination_criteria']['max_time_seconds']} 秒
- **收敛阈值**: {summary['termination_criteria']['convergence_threshold']:.2e}
- **停滞阈值**: {summary['termination_criteria']['stagnation_generations']} 代

## 收敛趋势分析
"""
            
            # 添加趋势分析
            if len(self.hypervolume_history) >= 10:
                recent_hv = self.hypervolume_history[-10:]
                hv_trend = "上升" if recent_hv[-1] > recent_hv[0] else "下降"
                report += f"- 近10代超体积趋势: {hv_trend}\n"
            
            if len(self.best_objectives_history) >= 10:
                recent_best = self.best_objectives_history[-10:]
                energy_trend = "下降" if recent_best[-1][0] < recent_best[0][0] else "上升"
                report += f"- 近10代最佳能耗趋势: {energy_trend}\n"
            
            report += f"\n---\n*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*\n"
            
            return report
            
        except Exception as e:
            self.logger.error(f"生成收敛报告失败: {str(e)}")
            return f"收敛报告生成失败: {str(e)}"
    
    def get_convergence_history(self) -> List[ConvergenceMetrics]:
        """获取收敛历史"""
        return self.convergence_history.copy()
    
    def reset_monitor(self) -> None:
        """重置监控器"""
        self.convergence_history.clear()
        self.hypervolume_history.clear()
        self.diversity_history.clear()
        self.best_objectives_history.clear()
        
        self.start_time = None
        self.current_generation = 0
        self.convergence_status = ConvergenceStatus.RUNNING
        self.stagnation_counter = 0
        self.best_known_objectives = [float('inf')] * 3
        self.last_significant_improvement = 0


def create_convergence_monitor() -> ConvergenceMonitor:
    """
    创建收敛监控器实例
    
    Returns:
        配置好的收敛监控器
    """
    return ConvergenceMonitor()