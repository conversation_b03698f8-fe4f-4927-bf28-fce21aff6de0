#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化模块图例中文化修复脚本
Fix Chinese Legends in Visualization Modules

功能：
1. 批量替换可视化模块中的英文图例为中文
2. 统一图例格式和字体设置
3. 修复matplotlib中文显示问题
4. 生成修复报告

作者：自动生成
时间：2024
"""

import os
import re
import sys
import logging
from pathlib import Path
from typing import Dict, List, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_chinese_legends.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 英文到中文的映射表
LEGEND_TRANSLATIONS = {
    # 基础性能指标
    'Energy Consumption': '能耗',
    'Energy Performance': '能耗性能',
    'Energy Efficiency': '能效',
    'Energy Usage': '能源使用',
    'Total Energy': '总能耗',
    'Annual Energy': '年能耗',
    'Heating Energy': '供暖能耗',
    'Cooling Energy': '制冷能耗',
    'Lighting Energy': '照明能耗',
    
    # 热工性能
    'Thermal Performance': '热工性能',
    'Thermal Comfort': '热舒适性',
    'Temperature': '温度',
    'Indoor Temperature': '室内温度',
    'Outdoor Temperature': '室外温度',
    'Heat Transfer': '传热',
    'Thermal Bridge': '热桥',
    'Insulation': '保温',
    
    # 采光与照明
    'Daylight': '采光',
    'Daylight Factor': '采光系数',
    'Illuminance': '照度',
    'Natural Light': '自然光',
    'Artificial Light': '人工照明',
    'Lighting': '照明',
    'Luminance': '亮度',
    
    # 成本分析
    'Cost': '成本',
    'Total Cost': '总成本',
    'Initial Cost': '初期成本',
    'Operating Cost': '运营成本',
    'Maintenance Cost': '维护成本',
    'Construction Cost': '建设成本',
    'Material Cost': '材料成本',
    'Labor Cost': '人工成本',
    
    # 建筑构件
    'Window': '窗户',
    'Windows': '窗户',
    'Frame': '窗框',
    'Frames': '窗框',
    'Shading': '遮阳',
    'Shading System': '遮阳系统',
    'Shading Device': '遮阳装置',
    'Sunshade': '遮阳板',
    'Blind': '百叶',
    'Awning': '雨篷',
    'Louver': '百叶窗',
    
    # 窗户类型
    'Regular Window': '普通窗户',
    'Double Glazed': '双层玻璃',
    'Triple Glazed': '三层玻璃',
    'Low-E Glass': 'Low-E玻璃',
    'Casement Window': '平开窗',
    'Sliding Window': '推拉窗',
    'Fixed Window': '固定窗',
    
    # 优化类型
    'Original': '原始设计',
    'Original Design': '原始设计',
    'Energy Opt': '能耗优化',
    'Energy Optimized': '能耗优化',
    'Thermal Opt': '热工优化',
    'Thermal Optimized': '热工优化',
    'Comprehensive': '综合优化',
    'Comprehensive Opt': '综合优化',
    'Multi-objective': '多目标优化',
    
    # 性能评价
    'Performance': '性能',
    'Efficiency': '效率',
    'Rating': '评级',
    'Score': '得分',
    'Index': '指数',
    'Factor': '系数',
    'Ratio': '比值',
    'Value': '数值',
    'Result': '结果',
    
    # 时间单位
    'Annual': '年度',
    'Monthly': '月度',
    'Daily': '日',
    'Hourly': '小时',
    'Hour': '小时',
    'Day': '天',
    'Month': '月',
    'Year': '年',
    
    # 单位
    'kWh': 'kWh',
    'kWh/m²': 'kWh/m²',
    'kWh/m²·a': 'kWh/m²·年',
    'W/m²': 'W/m²',
    'lux': 'lux',
    '°C': '°C',
    'm²': 'm²',
    'm': 'm',
    'mm': 'mm',
    '%': '%',
    
    # 图表类型
    'Bar Chart': '柱状图',
    'Line Chart': '折线图',
    'Pie Chart': '饼图',
    'Scatter Plot': '散点图',
    'Radar Chart': '雷达图',
    'Heatmap': '热力图',
    '3D View': '3D视图',
    'Comparison': '对比',
    'Distribution': '分布',
    'Trend': '趋势',
    
    # 方位和朝向
    'North': '北',
    'South': '南',
    'East': '东',
    'West': '西',
    'Northeast': '东北',
    'Northwest': '西北',
    'Southeast': '东南',
    'Southwest': '西南',
    'Orientation': '朝向',
    'Facade': '立面',
    
    # 材料和属性
    'Material': '材料',
    'Glass': '玻璃',
    'Frame Material': '窗框材料',
    'Aluminum': '铝合金',
    'Wood': '木材',
    'PVC': 'PVC',
    'Steel': '钢材',
    'Concrete': '混凝土',
    'Brick': '砖',
    
    # 常用动词和形容词
    'Optimized': '优化的',
    'Enhanced': '增强的',
    'Improved': '改进的',
    'Advanced': '高级的',
    'Standard': '标准的',
    'Basic': '基础的',
    'High': '高',
    'Medium': '中',
    'Low': '低',
    'Best': '最佳',
    'Good': '良好',
    'Poor': '较差',
    
    # 分析类型
    'Analysis': '分析',
    'Comparison Analysis': '对比分析',
    'Performance Analysis': '性能分析',
    'Cost Analysis': '成本分析',
    'Sensitivity Analysis': '敏感性分析',
    'Optimization': '优化',
    'Simulation': '仿真',
    'Evaluation': '评估',
    'Assessment': '评价',
    
    # 图例标题
    'Legend': '图例',
    'Title': '标题',
    'Label': '标签',
    'Axis': '坐标轴',
    'X-axis': 'X轴',
    'Y-axis': 'Y轴',
    'Z-axis': 'Z轴',
}

class ChineseLegendFixer:
    """中文图例修复器"""
    
    def __init__(self, project_root: str = None):
        """初始化修复器"""
        if project_root is None:
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        self.project_root = Path(project_root)
        self.fixed_files = []
        self.translation_stats = {}
        self.font_fixes = []
        
        logger.info(f"初始化中文图例修复器，项目根目录: {self.project_root}")
    
    def find_visualization_files(self) -> List[Path]:
        """查找所有可视化相关的Python文件"""
        patterns = [
            '**/visualization*.py',
            '**/chart*.py',
            '**/plot*.py',
            '**/visual*.py',
            '**/*3d*.py',
            '**/enhanced*.py'
        ]
        
        files = []
        for pattern in patterns:
            files.extend(self.project_root.glob(pattern))
        
        # 过滤掉非Python文件和测试文件
        py_files = []
        for file in files:
            if (file.suffix == '.py' and 
                'test' not in file.name.lower() and 
                '__pycache__' not in str(file)):
                py_files.append(file)
        
        logger.info(f"找到 {len(py_files)} 个可视化相关文件")
        return py_files
    
    def fix_matplotlib_chinese_support(self, content: str) -> Tuple[str, bool]:
        """修复matplotlib中文支持"""
        chinese_font_setup = '''
# 设置matplotlib中文字体支持
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
'''
        
        # 检查是否已经有中文字体设置
        if 'font.sans-serif' in content or 'SimHei' in content:
            return content, False
        
        # 查找import matplotlib的位置
        import_patterns = [
            r'import matplotlib\.pyplot as plt',
            r'import matplotlib',
            r'from matplotlib import',
        ]
        
        for pattern in import_patterns:
            match = re.search(pattern, content)
            if match:
                # 在import后插入中文字体设置
                insert_pos = content.find('\n', match.end())
                if insert_pos != -1:
                    content = content[:insert_pos] + '\n' + chinese_font_setup + content[insert_pos:]
                    return content, True
        
        # 如果没找到matplotlib import，在文件开头添加
        if 'matplotlib' in content.lower():
            lines = content.split('\n')
            insert_line = 0
            
            # 找到合适的插入位置（在文档字符串和编码声明之后）
            for i, line in enumerate(lines):
                if (line.strip().startswith('"""') or 
                    line.strip().startswith("'''") or
                    line.strip().startswith('# -*- coding')):
                    continue
                elif line.strip() and not line.strip().startswith('#'):
                    insert_line = i
                    break
            
            lines.insert(insert_line, chinese_font_setup)
            return '\n'.join(lines), True
        
        return content, False
    
    def translate_legends_in_content(self, content: str) -> Tuple[str, Dict[str, int]]:
        """翻译内容中的图例文本"""
        translation_count = {}
        modified_content = content
        
        # 翻译图例、标题、标签等
        for english, chinese in LEGEND_TRANSLATIONS.items():
            # 匹配不同的使用场景
            patterns = [
                # 字符串中的直接使用
                rf"'({re.escape(english)})'",
                rf'"({re.escape(english)})"',
                # f字符串中的使用
                rf"f'([^']*{re.escape(english)}[^']*)'",
                rf'f"([^"]*{re.escape(english)}[^"]*)"',
                # 作为参数值
                rf"label\s*=\s*['\"]({re.escape(english)})['\"]",
                rf"title\s*=\s*['\"]({re.escape(english)})['\"]",
                rf"xlabel\s*=\s*['\"]({re.escape(english)})['\"]",
                rf"ylabel\s*=\s*['\"]({re.escape(english)})['\"]",
                rf"legend\s*=\s*['\"]({re.escape(english)})['\"]",
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, modified_content, re.IGNORECASE)
                if matches:
                    if english not in translation_count:
                        translation_count[english] = 0
                    translation_count[english] += len(matches)
                    
                    # 执行替换
                    modified_content = re.sub(
                        pattern, 
                        lambda m: m.group(0).replace(english, chinese),
                        modified_content,
                        flags=re.IGNORECASE
                    )
        
        return modified_content, translation_count
    
    def add_chinese_legend_helpers(self, content: str) -> Tuple[str, bool]:
        """添加中文图例辅助函数"""
        helper_functions = '''
def setup_chinese_fonts():
    """设置中文字体"""
    import matplotlib.pyplot as plt
    import matplotlib
    fonts = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
    matplotlib.rcParams['font.sans-serif'] = fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False

def create_chinese_legend(ax, labels, **kwargs):
    """创建中文图例"""
    setup_chinese_fonts()
    return ax.legend(labels, **kwargs)

def set_chinese_labels(ax, xlabel=None, ylabel=None, title=None):
    """设置中文标签"""
    setup_chinese_fonts()
    if xlabel:
        ax.set_xlabel(xlabel, fontproperties='SimHei')
    if ylabel:
        ax.set_ylabel(ylabel, fontproperties='SimHei')
    if title:
        ax.set_title(title, fontproperties='SimHei')
'''
        
        # 检查是否已经有这些函数
        if 'setup_chinese_fonts' in content:
            return content, False
        
        # 在类定义前添加辅助函数
        class_match = re.search(r'^class\s+\w+', content, re.MULTILINE)
        if class_match:
            insert_pos = class_match.start()
            content = content[:insert_pos] + helper_functions + '\n\n' + content[insert_pos:]
            return content, True
        
        # 如果没有类定义，在文件末尾添加
        content += '\n' + helper_functions
        return content, True
    
    def fix_file(self, file_path: Path) -> Dict:
        """修复单个文件"""
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            modified_content = original_content
            changes_made = []
            
            # 1. 修复matplotlib中文支持
            modified_content, font_fixed = self.fix_matplotlib_chinese_support(modified_content)
            if font_fixed:
                changes_made.append('添加了matplotlib中文字体支持')
                self.font_fixes.append(str(file_path))
            
            # 2. 翻译图例文本
            modified_content, translations = self.translate_legends_in_content(modified_content)
            if translations:
                changes_made.append(f'翻译了图例文本: {translations}')
                for eng, count in translations.items():
                    if eng not in self.translation_stats:
                        self.translation_stats[eng] = 0
                    self.translation_stats[eng] += count
            
            # 3. 添加中文图例辅助函数
            modified_content, helpers_added = self.add_chinese_legend_helpers(modified_content)
            if helpers_added:
                changes_made.append('添加了中文图例辅助函数')
            
            # 如果有修改，写回文件
            if changes_made:
                # 备份原文件
                backup_path = file_path.with_suffix(file_path.suffix + '.backup')
                with open(backup_path, 'w', encoding='utf-8') as f:
                    f.write(original_content)
                
                # 写入修改后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(modified_content)
                
                self.fixed_files.append(str(file_path))
                
                result = {
                    'file': str(file_path),
                    'status': 'success',
                    'changes': changes_made,
                    'backup': str(backup_path)
                }
                
                logger.info(f"修复文件: {file_path.name} - {', '.join(changes_made)}")
                return result
            else:
                return {
                    'file': str(file_path),
                    'status': 'no_changes',
                    'changes': [],
                    'backup': None
                }
        
        except Exception as e:
            error_msg = f"修复文件失败 {file_path}: {str(e)}"
            logger.error(error_msg)
            return {
                'file': str(file_path),
                'status': 'error',
                'error': str(e),
                'changes': [],
                'backup': None
            }
    
    def fix_all_files(self) -> Dict:
        """修复所有可视化文件"""
        logger.info("开始批量修复可视化文件中的中文图例问题")
        
        files = self.find_visualization_files()
        results = []
        
        for file_path in files:
            result = self.fix_file(file_path)
            results.append(result)
        
        # 生成汇总报告
        summary = {
            'total_files': len(files),
            'fixed_files': len([r for r in results if r['status'] == 'success']),
            'no_changes_files': len([r for r in results if r['status'] == 'no_changes']),
            'error_files': len([r for r in results if r['status'] == 'error']),
            'translation_stats': self.translation_stats,
            'font_fixes': len(self.font_fixes),
            'results': results
        }
        
        return summary
    
    def generate_report(self, summary: Dict) -> str:
        """生成修复报告"""
        report = f"""
=== 可视化模块中文图例修复报告 ===
生成时间: {self._get_current_time()}

一、修复统计
- 总文件数: {summary['total_files']}
- 成功修复: {summary['fixed_files']}
- 无需修改: {summary['no_changes_files']}
- 修复失败: {summary['error_files']}
- 字体修复: {summary['font_fixes']}

二、翻译统计
"""
        
        if summary['translation_stats']:
            for english, count in sorted(summary['translation_stats'].items(), key=lambda x: x[1], reverse=True):
                chinese = LEGEND_TRANSLATIONS.get(english, english)
                report += f"- '{english}' -> '{chinese}': {count}次\n"
        else:
            report += "- 未发现需要翻译的英文图例\n"
        
        report += "\n三、详细结果\n"
        for result in summary['results']:
            status_symbol = {'success': '[SUCCESS]', 'no_changes': '[NO_CHANGE]', 'error': '[ERROR]'}[result['status']]
            report += f"{status_symbol} {result['file']}\n"
            
            if result['status'] == 'success':
                for change in result['changes']:
                    report += f"  - {change}\n"
                if result['backup']:
                    report += f"  - 备份文件: {result['backup']}\n"
            elif result['status'] == 'error':
                report += f"  - 错误: {result['error']}\n"
            
            report += "\n"
        
        return report
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def main():
    """主函数"""
    print("可视化模块中文图例修复脚本")
    print("=" * 50)
    
    # 获取项目根目录
    if len(sys.argv) > 1:
        project_root = sys.argv[1]
    else:
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    
    print(f"项目根目录: {project_root}")
    
    # 创建修复器
    fixer = ChineseLegendFixer(project_root)
    
    # 执行修复
    summary = fixer.fix_all_files()
    
    # 生成报告
    report = fixer.generate_report(summary)
    
    # 保存报告
    report_path = os.path.join(project_root, 'chinese_legend_fix_report.txt')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    # 显示结果
    print("\n修复完成！")
    print(f"- 总计文件: {summary['total_files']}")
    print(f"- 成功修复: {summary['fixed_files']}")
    print(f"- 无需修改: {summary['no_changes_files']}")
    print(f"- 修复失败: {summary['error_files']}")
    print(f"- 详细报告: {report_path}")
    
    if summary['fixed_files'] > 0:
        print("\n注意事项：")
        print("1. 原文件已备份为 .backup 文件")
        print("2. 请测试修复后的文件是否正常工作")
        print("3. 如有问题可从备份文件恢复")
        print("4. 确认无问题后可删除备份文件")

if __name__ == '__main__':
    main()