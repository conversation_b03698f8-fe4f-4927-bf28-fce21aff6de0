"""
个体编码器
处理立面优化个体的编码、解码和变异操作
"""

import numpy as np
import random
from typing import Dict, List, Tuple, Any, Optional

from ..core.config import get_config
from ..core.logging_config import get_logger
from ..core.exceptions import IndividualEncodingError, handle_exception
from ..core.data_structures import (
    FacadeIndividual, FacadeElements, RenovationMode, 
    create_facade_individual
)
from ..core.utils import MathUtils


class IndividualEncoder:
    """
    立面个体编码器
    
    功能：
    1. 个体编码和解码
    2. 基于改造模式的约束处理
    3. 变异和交叉操作的支持
    4. 个体合法性验证
    """
    
    def __init__(self, facade_elements: FacadeElements, allow_window_modification: bool = False, renovation_mode: Optional[RenovationMode] = None):
        """
        初始化个体编码器
        
        Args:
            facade_elements: 立面元素数据
            allow_window_modification: 是否允许增减窗户数量
            renovation_mode: 改造模式（可选，如果提供将覆盖自动判断）
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.facade_elements = facade_elements
        self.allow_window_modification = allow_window_modification
        
        # 获取约束配置
        self.constraints = self.config.get_section('optimization_constraints')
        
        # 窗户尺寸约束
        self.window_size_constraints = self.constraints.get('window_size', {})
        self.min_width = self.window_size_constraints.get('min_width', 0.6)
        self.max_width = self.window_size_constraints.get('max_width', 3.0)
        self.min_height = self.window_size_constraints.get('min_height', 0.8)
        self.max_height = self.window_size_constraints.get('max_height', 2.5)
        
        # 窗框和遮阳约束
        self.frame_depth_constraints = self.constraints.get('frame_depth', {})
        self.min_frame_depth = self.frame_depth_constraints.get('min', 0.0)
        self.max_frame_depth = self.frame_depth_constraints.get('max', 0.5)
        
        self.shading_depth_constraints = self.constraints.get('shading_depth', {})
        self.min_shading_depth = self.shading_depth_constraints.get('min', 0.0)
        # 修复：降低遮阳深度最大值，避免成本异常高
        self.max_shading_depth = self.shading_depth_constraints.get('max', 0.8)
        
        self.shading_angle_constraints = self.constraints.get('shading_angle', {})
        self.min_shading_angle = self.shading_angle_constraints.get('min', 0.0)
        self.max_shading_angle = self.shading_angle_constraints.get('max', 90.0)
        
        # 空间约束 - 默认值
        self.minimum_distance = self.constraints.get('minimum_distance', 0.3)
        self.wall_margin = self.constraints.get('wall_margin', 0.2)
        self.min_window_spacing = 0.5  # 默认窗间距
        
        # 确定改造模式 - 优先使用传入的参数
        if renovation_mode is not None:
            self.renovation_mode = renovation_mode
        else:
            self.renovation_mode = self._determine_renovation_mode()
        
        # 严格改造模式的极其宽松约束 - 适应现有紧密布局
        if self.renovation_mode == RenovationMode.STRICT_RENOVATION:
            self.min_window_spacing = 0.05    # 极小间距5cm，适应极其紧密布局
            self.wall_margin = 0.02           # 极小边距2cm
            self.overlap_tolerance = 0.15     # 大幅允许15cm的重叠容忍度
            self.min_width = 0.3              # 进一步减少最小宽度到30cm
            self.min_height = 0.5             # 进一步减少最小高度到50cm
            self.logger.info(f"应用极宽松严格改造模式约束: 窗间距={self.min_window_spacing}m, 边距={self.wall_margin}m, 重叠容忍={self.overlap_tolerance}m")
        else:
            self.overlap_tolerance = 0.0      # 默认不允许重叠
        
        # 获取立面边界
        self.facade_bounds = self._calculate_facade_bounds()
        
        self.logger.info(f"个体编码器初始化完成: 改造模式={self.renovation_mode.value}, 窗户修改模式={'允许' if allow_window_modification else '禁止'}")
    
    def _determine_renovation_mode(self) -> RenovationMode:
        """确定改造模式"""
        # 如果墙体没有任何构件（或只有墙体），则为新建项目
        has_existing_windows = len(self.facade_elements.windows) > 0
        has_existing_doors = len(self.facade_elements.doors) > 0
        has_existing_components = has_existing_windows or has_existing_doors
        
        if not has_existing_components:
            return RenovationMode.NEW_CONSTRUCTION
        else:
            return RenovationMode.RENOVATION
    
    def _calculate_facade_bounds(self) -> Dict[str, float]:
        """计算立面边界"""
        if self.facade_elements.image_shape[0] == 0:
            # 如果没有图像信息，使用默认边界
            return {
                'min_x': self.wall_margin,
                'max_x': 10.0 - self.wall_margin,
                'min_y': self.wall_margin, 
                'max_y': 3.0 - self.wall_margin
            }
        
        # 基于图像尺寸计算边界
        height, width = self.facade_elements.image_shape[:2]
        pixel_ratio = self.facade_elements.pixel_to_meter_ratio
        
        # 转换为米制单位
        facade_width_m = width * pixel_ratio
        facade_height_m = height * pixel_ratio
        
        return {
            'min_x': self.wall_margin,
            'max_x': facade_width_m - self.wall_margin,
            'min_y': self.wall_margin,
            'max_y': facade_height_m - self.wall_margin
        }
    
    @handle_exception
    def create_individual_from_real_data(self, individual_id: str, 
                                        num_windows: Optional[int] = None) -> FacadeIndividual:
        """
        基于真实立面数据创建个体（替代随机生成）
        
        Args:
            individual_id: 个体ID
            num_windows: 窗户数量，如果为None则基于真实数据确定
            
        Returns:
            基于真实数据的立面个体
        """
        try:
            # 基于真实立面数据确定窗户数量
            if num_windows is None:
                num_windows = self._determine_window_count_from_real_data()
            
            # 根据改造模式创建个体
            if self.renovation_mode == RenovationMode.NEW_CONSTRUCTION:
                individual = self._create_individual_based_on_real_facade(individual_id, num_windows)
            elif self.renovation_mode == RenovationMode.STRICT_RENOVATION:
                individual = self._create_strict_renovation_individual_from_real_data(individual_id, num_windows)
            else:
                individual = self._create_renovation_individual_from_real_data(individual_id, num_windows)
            
            # 验证个体合法性
            if not self._validate_individual(individual):
                # 如果个体不合法，进行修复
                individual = self._repair_individual(individual)
            
            self.logger.info(f"基于真实数据创建个体: {individual_id}, 窗户数量: {num_windows}")
            return individual
            
        except Exception as e:
            raise IndividualEncodingError(f"基于真实数据创建个体失败: {str(e)}") from e
    
    # 保持向后兼容性的别名方法
    def create_random_individual(self, individual_id: str, 
                                num_windows: Optional[int] = None) -> FacadeIndividual:
        """向后兼容性方法 - 实际调用基于真实数据的创建方法"""
        self.logger.warning("调用了create_random_individual方法，已重定向到基于真实数据的创建方法")
        return self.create_individual_from_real_data(individual_id, num_windows)
    
    def _determine_window_count_from_real_data(self) -> int:
        """基于真实立面数据确定窗户数量"""
        existing_windows = len(self.facade_elements.windows)
        
        if existing_windows == 0:
            self.logger.warning("未检测到现有窗户，使用最小配置")
            return 1
        
        if self.allow_window_modification:
            # 允许窗户数量变化的策略
            if self.renovation_mode == RenovationMode.NEW_CONSTRUCTION:
                # 第一种：新建项目 - 窗户大小尺寸位置没有约束，可以自由添加
                self.logger.info(f"新建项目：窗户大小尺寸位置没有约束，基于现有{existing_windows}个窗户")
                min_count = max(1, int(existing_windows * 0.5))  # 允许减少50%
                max_count = int(existing_windows * 2.0)          # 允许增加100%
                actual_count = random.randint(min_count, max_count)
                self.logger.info(f"新建项目窗户数量调整为{actual_count}个")
                return actual_count
            elif self.renovation_mode == RenovationMode.MAJOR_RENOVATION:
                # 第二种：改造项目 - 窗户尺寸位置大小可以分别有50%的变化，不能删除原有窗户但可以新建
                self.logger.info(f"改造项目：窗户尺寸位置大小可以有50%变化，基于现有{existing_windows}个窗户")
                min_count = existing_windows  # 不能删除原有窗户
                max_count = int(existing_windows * 1.5)  # 允许新建50%
                actual_count = random.randint(min_count, max_count)
                self.logger.info(f"大幅度改造项目窗户数量调整为{actual_count}个（变化范围±60%）")
                return actual_count
            else:  # RenovationMode.RENOVATION
                # 普通改造项目：严格保持原有窗户数量，不允许增减
                self.logger.info(f"改造项目模式：严格保持原有{existing_windows}个窗户数量，不允许增删")
                return existing_windows
        else:
            # 严格保持原有窗户数量，不允许增减
            self.logger.info(f"严格保持原有{existing_windows}个窗户数量")
            return existing_windows
    
    def _determine_window_count(self) -> int:
        """确定窗户数量（向后兼容性方法）"""
        return self._determine_window_count_from_real_data()
    
    def _determine_window_type_from_real_data(self, window_index: int, existing_frames, existing_shading) -> int:
        """基于真实数据确定窗户类型"""
        # 检查是否有对应的框架或遮阳构件
        has_frame = window_index < len(existing_frames) if existing_frames else False
        has_shading = window_index < len(existing_shading) if existing_shading else False
        
        if has_shading:
            return 2  # 遮阳窗户
        elif has_frame:
            return 1  # 框架窗户
        else:
            # 基于现有构件的比例随机选择
            total_frames = len(existing_frames) if existing_frames else 0
            total_shading = len(existing_shading) if existing_shading else 0
            total_special = total_frames + total_shading
            
            if total_special == 0:
                return 0  # 普通窗户
            
            # 按比例随机选择
            rand_val = random.random()
            frame_ratio = total_frames / (total_frames + total_shading) if total_special > 0 else 0
            
            if rand_val < frame_ratio:
                return 1  # 框架窗户
            elif rand_val < frame_ratio + (total_shading / total_special):
                return 2  # 遮阳窗户
            else:
                return 0  # 普通窗户
    
    def _determine_window_type_from_real_coordinates(self, window_index: int, existing_windows, existing_frames, existing_shading) -> int:
        """基于真实坐标位置确定窗户类型 - 修复版本"""
        if window_index >= len(existing_windows):
            return self._determine_window_type_from_real_data(window_index, existing_frames, existing_shading)
        
        current_window = existing_windows[window_index]
        window_center = current_window.center
        
        # 检查是否有框架与此窗户在空间上关联
        has_associated_frame = False
        if existing_frames:
            for frame in existing_frames:
                if self._is_spatially_associated(window_center, frame.center, current_window.width, current_window.height):
                    has_associated_frame = True
                    break
        
        # 检查是否有遮阳与此窗户在空间上关联
        has_associated_shading = False
        if existing_shading:
            for shading in existing_shading:
                if self._is_spatially_associated(window_center, shading.center, current_window.width, current_window.height):
                    has_associated_shading = True
                    break
        
        # 根据空间关联确定窗户类型
        if has_associated_shading:
            return 2  # 遮阳窗户
        elif has_associated_frame:
            return 1  # 框架窗户
        else:
            return 0  # 普通窗户
    
    def _is_spatially_associated(self, window_center: Tuple[float, float], 
                               component_center: Tuple[float, float],
                               window_width: float, window_height: float) -> bool:
        """判断构件是否与窗户在空间上关联"""
        # 计算距离
        distance = MathUtils.calculate_distance(window_center, component_center)
        
        # 基于窗户尺寸确定关联阈值
        association_threshold = max(window_width, window_height) * 1.5  # 1.5倍窗户最大尺寸
        
        return distance <= association_threshold
    
    def _extract_real_frame_depth(self, window_index: int, existing_frames) -> float:
        """从真实框架数据中提取框架深度"""
        if not existing_frames or window_index >= len(existing_frames):
            return 0.15  # 默认框架深度15cm
        
        frame = existing_frames[window_index]
        
        # 基于框架的几何特征估算深度
        # 假设框架深度与其最小尺寸成比例
        estimated_depth = min(frame.width, frame.height) * 0.3
        
        # 确保在合理范围内
        return max(0.05, min(estimated_depth, 0.5))  # 5cm到50cm之间
    
    def _extract_real_shading_parameters(self, window_index: int, existing_shading) -> Tuple[float, float]:
        """从真实遮阳数据中提取遮阳参数"""
        if not existing_shading or window_index >= len(existing_shading):
            return 0.6, 30.0  # 默认遮阳深度60cm，角度30度
        
        shading = existing_shading[window_index]
        
        # 基于遮阳的几何特征估算深度
        # 遮阳深度通常与其高度相关
        estimated_depth = shading.height * 0.6
        
        # 基于遮阳的宽高比估算角度
        aspect_ratio = shading.width / shading.height if shading.height > 0 else 1.0
        estimated_angle = 20.0 + (aspect_ratio - 1.0) * 15.0  # 基于宽高比调整角度
        
        # 确保在合理范围内
        depth = max(0.2, min(estimated_depth, 1.5))  # 20cm到150cm之间
        angle = max(10.0, min(estimated_angle, 60.0))  # 10度到60度之间
        
        return depth, angle    
 
    def _calculate_average_window_spacing(self, existing_windows) -> float:
        """计算现有窗户的平均间距"""
        if len(existing_windows) < 2:
            return 2.0  # 默认2米间距
        
        spacings = []
        for i in range(len(existing_windows) - 1):
            pos1 = existing_windows[i].center
            pos2 = existing_windows[i + 1].center
            spacing = MathUtils.calculate_distance(pos1, pos2)
            spacings.append(spacing)
        
        avg_spacing = sum(spacings) / len(spacings)
        self.logger.debug(f"计算得到平均窗户间距: {avg_spacing:.2f}米")
        return avg_spacing
    
    def _generate_diverse_position(self, existing_positions, avg_spacing, diversity_factor):
        """生成多样化位置"""
        if not existing_positions:
            center_x = (self.facade_bounds['min_x'] + self.facade_bounds['max_x']) / 2
            center_y = (self.facade_bounds['min_y'] + self.facade_bounds['max_y']) / 2
            return (center_x, center_y)
        
        # 选择参考位置
        ref_pos = random.choice(existing_positions)
        
        # 大幅度位置变化
        angle = random.uniform(0, 2 * np.pi)
        distance = avg_spacing * random.uniform(0.5, 2.0) * diversity_factor  # 增加距离变化
        
        new_x = ref_pos[0] + distance * np.cos(angle)
        new_y = ref_pos[1] + distance * np.sin(angle)
        
        # 确保在边界内
        new_x = MathUtils.clamp(new_x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
        new_y = MathUtils.clamp(new_y, self.facade_bounds['min_y'], self.facade_bounds['max_y'])
        
        return (new_x, new_y)
    
    def _generate_diverse_window_type(self, window_index, existing_windows, existing_frames, existing_shading, type_randomness):
        """生成多样化窗户类型 - 大幅增强版本"""
        # 首先尝试基于现有构件判断
        if window_index < len(existing_windows):
            spatial_type = self._determine_window_type_from_real_coordinates(
                window_index, existing_windows, existing_frames, existing_shading
            )
            
            # 如果检测到特定类型，有一定概率保持，否则随机化
            if spatial_type != 0 and random.random() > type_randomness:
                return spatial_type
        
        # 修复：所有模式都必须满足60%窗框和遮阳生成率约束
        # 确保窗框+遮阳总比例≥60%，普通窗户≤40%

        if self.renovation_mode == RenovationMode.MAJOR_RENOVATION:
            # 大幅度改造模式：最高比例的窗框和遮阳
            type_distribution = [
                (0, 0.25), # 25% 普通窗户（确保≤40%）
                (1, 0.45), # 45% 框架窗户
                (2, 0.30)  # 30% 遮阳窗户（总计75%特殊类型）
            ]

            if type_randomness > 0.6:  # 高随机化时进一步提高
                type_distribution = [
                    (0, 0.20), # 20% 普通窗户
                    (1, 0.50), # 50% 框架窗户
                    (2, 0.30)  # 30% 遮阳窗户（总计80%特殊类型）
                ]
        elif self.renovation_mode == RenovationMode.RENOVATION:
            # 改造模式：确保60%窗框和遮阳
            type_distribution = [
                (0, 0.35), # 35% 普通窗户（确保≤40%）
                (1, 0.40), # 40% 框架窗户
                (2, 0.25)  # 25% 遮阳窗户（总计65%特殊类型）
            ]

            if type_randomness > 0.6:
                type_distribution = [
                    (0, 0.30), # 30% 普通窗户
                    (1, 0.45), # 45% 框架窗户
                    (2, 0.25)  # 25% 遮阳窗户（总计70%特殊类型）
                ]
        elif self.renovation_mode == RenovationMode.STRICT_RENOVATION:
            # 严格改造模式：保守但仍满足60%约束
            type_distribution = [
                (0, 0.40), # 40% 普通窗户（刚好满足≤40%约束）
                (1, 0.35), # 35% 框架窗户
                (2, 0.25)  # 25% 遮阳窗户（总计60%特殊类型）
            ]

            if type_randomness > 0.6:
                type_distribution = [
                    (0, 0.35), # 35% 普通窗户
                    (1, 0.40), # 40% 框架窗户
                    (2, 0.25)  # 25% 遮阳窗户（总计65%特殊类型）
                ]
        else:
            # 新建项目：最高比例
            type_distribution = [
                (0, 0.20), # 20% 普通窗户
                (1, 0.50), # 50% 框架窗户
                (2, 0.30)  # 30% 遮阳窗户（总计80%特殊类型）
            ]
        
        # 基于概率分布选择类型
        rand_val = random.random()
        cumulative_prob = 0.0
        
        for window_type, prob in type_distribution:
            cumulative_prob += prob
            if rand_val <= cumulative_prob:
                return window_type
        
        return 0  # 默认返回普通窗户
    
    def _generate_position_based_on_pattern(self, existing_positions, avg_spacing) -> Tuple[float, float]:
        if not existing_positions:
            # 如果没有现有位置，返回立面中心
            center_x = (self.facade_bounds['min_x'] + self.facade_bounds['max_x']) / 2
            center_y = (self.facade_bounds['min_y'] + self.facade_bounds['max_y']) / 2
            return (center_x, center_y)
        
        # 尝试在现有窗户旁边找到合适位置
        for attempt in range(50):
            # 选择一个现有位置作为参考
            ref_pos = random.choice(existing_positions)
            
            # 在参考位置周围生成新位置
            angle = random.uniform(0, 2 * np.pi)
            distance = avg_spacing * random.uniform(0.8, 1.2)  # ±20%变化
            
            new_x = ref_pos[0] + distance * np.cos(angle)
            new_y = ref_pos[1] + distance * np.sin(angle)
            
            # 检查是否在边界内
            if (self.facade_bounds['min_x'] <= new_x <= self.facade_bounds['max_x'] and
                self.facade_bounds['min_y'] <= new_y <= self.facade_bounds['max_y']):
                
                # 检查与现有位置的距离
                min_distance = min(MathUtils.calculate_distance((new_x, new_y), pos) 
                                 for pos in existing_positions)
                
                if min_distance >= self.minimum_distance:
                    return (new_x, new_y)
        
        # 如果找不到合适位置，返回一个安全位置
        safe_x = (self.facade_bounds['min_x'] + self.facade_bounds['max_x']) / 2
        safe_y = (self.facade_bounds['min_y'] + self.facade_bounds['max_y']) / 2
        return (safe_x, safe_y)
    
    def _create_strict_renovation_individual_from_real_data(self, individual_id: str,
                                                          num_windows: int) -> FacadeIndividual:
        """创建特别严格改造个体 - 只允许水平宽度变化，中轴对称，严禁增删窗户"""
        existing_windows = self.facade_elements.windows
        existing_frames = self.facade_elements.frames
        existing_shading = self.facade_elements.shading
        
        if len(existing_windows) == 0:
            self.logger.error("特别严格改造模式需要现有窗户数据")
            raise IndividualEncodingError("特别严格改造模式需要现有窗户数据")
        
        # 严格限制：窗户数量必须与现有完全一致
        if num_windows != len(existing_windows):
            self.logger.warning(f"特别严格改造模式：强制窗户数量为现有数量{len(existing_windows)}")
            num_windows = len(existing_windows)
        
        window_positions = []
        window_sizes = []
        window_types = []
        frame_depths = []
        shading_depths = []
        shading_angles = []
        
        self.logger.info(f"创建特别严格改造个体: {num_windows}个窗户，仅允许水平宽度中轴对称变化")
        
        # 基于现有窗户创建严格限制的个体
        for i in range(num_windows):
            existing_window = existing_windows[i]
            
            # 第三种：严格改造 - 窗户位置「中心点」只有30%变化
            pixel_center_x, pixel_center_y = existing_window.center
            real_x = pixel_center_x * self.facade_elements.pixel_to_meter_ratio
            real_y = pixel_center_y * self.facade_elements.pixel_to_meter_ratio

            # 位置变化：中心点允许30%变化
            position_variation = 0.3  # 30%变化范围
            max_x_change = existing_window.width * position_variation
            max_y_change = existing_window.height * position_variation

            new_x = real_x + random.uniform(-max_x_change, max_x_change)
            new_y = real_y + random.uniform(-max_y_change, max_y_change)

            # 确保在边界内
            new_x = MathUtils.clamp(new_x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
            new_y = MathUtils.clamp(new_y, self.facade_bounds['min_y'], self.facade_bounds['max_y'])

            window_positions.append((new_x, new_y))
            
            # 尺寸变化：只允许宽度变化，高度严格不变
            base_width = existing_window.width
            base_height = existing_window.height
            
            # 第三种：严格改造 - 窗户只能横向以中轴对称式改造40%
            width_variation = random.uniform(-0.4, 0.4)  # ±40%横向变化
            new_width = base_width * (1 + width_variation)
            new_height = base_height  # 高度严格不变

            # 应用约束并确保在合理范围内
            new_width = MathUtils.clamp(new_width, self.min_width, self.max_width)
            new_width = max(new_width, base_width * 0.6)  # 最小不能小于原宽度的60%
            new_width = min(new_width, base_width * 1.4)  # 最大不能超过原宽度的140%
            
            window_sizes.append((new_width, new_height))
            
            # 窗户类型：基于现有构件确定，但允许增加窗框和遮阳
            window_type = self._determine_window_type_from_real_coordinates(
                i, existing_windows, existing_frames, existing_shading
            )
            
            # 第三种：严格改造 - 遮阳和窗框的宽度符合现实改造
            if window_type == 0 and random.random() < 0.3:  # 30%概率为普通窗户添加窗框或遮阳
                window_type = random.choice([1, 2])  # 随机选择窗框或遮阳
            
            window_types.append(window_type)
            
            # 窗框深度：根据改造模式调整约束
            if window_type == 1:  # 有窗框
                # 第三种：严格改造 - 窗框宽度符合现实改造，不超过窗户宽度的120%
                max_frame_width = new_width * 1.2  # 不超过窗户宽度的120%
                if existing_frames and i < len(existing_frames):
                    base_depth = self._extract_real_frame_depth(i, existing_frames)
                    # 符合现实改造的微调
                    depth_variation = random.uniform(-0.1, 0.1)  # ±10%变化
                    frame_depth = base_depth * (1 + depth_variation)
                else:
                    frame_depth = random.uniform(0.05, 0.25)  # 符合现实改造的框架深度
                # 确保不超过窗户宽度限制
                frame_depth = min(frame_depth, max_frame_width)
                frame_depth = MathUtils.clamp(frame_depth, self.min_frame_depth, self.max_frame_depth)
            else:
                frame_depth = 0.0
            frame_depths.append(frame_depth)
            
            # 遮阳参数：根据改造模式调整约束
            if window_type == 2:  # 有遮阳
                # 第三种：严格改造 - 遮阳宽度符合现实改造，不超过窗户宽度的120%
                max_shading_width = new_width * 1.2  # 不超过窗户宽度的120%
                if existing_shading and i < len(existing_shading):
                    base_shading_depth, base_shading_angle = self._extract_real_shading_parameters(i, existing_shading)
                    # 符合现实改造的调整
                    depth_variation = random.uniform(-0.1, 0.1)  # ±10%变化
                    angle_variation = random.uniform(-0.1, 0.1)  # ±10%变化

                    shading_depth = base_shading_depth * (1 + depth_variation)
                    shading_angle = base_shading_angle * (1 + angle_variation)
                else:
                    shading_depth = random.uniform(0.3, 1.0)  # 符合现实改造的遮阳深度
                    shading_angle = random.uniform(15.0, 45.0)  # 符合现实改造的遮阳角度

                # 确保不超过窗户宽度限制
                shading_depth = min(shading_depth, max_shading_width)
                shading_depth = MathUtils.clamp(shading_depth, self.min_shading_depth, self.max_shading_depth)
                shading_angle = MathUtils.clamp(shading_angle, self.min_shading_angle, self.max_shading_angle)
            else:
                shading_depth = 0.0
                shading_angle = 0.0
            
            shading_depths.append(shading_depth)
            shading_angles.append(shading_angle)
            
            self.logger.debug(f"严格改造窗户{i}: 位置不变({real_x:.2f}, {real_y:.2f}), "
                            f"宽度{base_width:.2f}→{new_width:.2f}m (变化{width_variation*100:+.1f}%), "
                            f"高度不变{new_height:.2f}m, 类型{window_type}")
        
        # 创建严格改造个体
        strict_individual = FacadeIndividual(
            individual_id=individual_id,
            window_positions=window_positions,
            window_sizes=window_sizes,
            window_types=window_types,
            frame_depths=frame_depths,
            shading_depths=shading_depths,
            shading_angles=shading_angles,
            renovation_mode=RenovationMode.STRICT_RENOVATION
        )
        
        # 严格验证：检查重叠（特别严格模式绝不允许重叠）
        if self._check_strict_overlaps(strict_individual):
            self.logger.error("特别严格模式检测到窗户重叠，尝试修复")
            strict_individual = self._repair_strict_individual(strict_individual)
            
            # 再次检查，如果仍有重叠则报错
            if self._check_strict_overlaps(strict_individual):
                raise IndividualEncodingError("特别严格模式无法消除窗户重叠")
        
        self.logger.info(f"特别严格改造个体创建完成: {num_windows}个窗户, "
                        f"框架{sum(1 for wt in window_types if wt == 1)}个, "
                        f"遮阳{sum(1 for wt in window_types if wt == 2)}个, "
                        f"严格保证无重叠")
        
        return strict_individual
    
    def _check_strict_overlaps(self, individual: FacadeIndividual) -> bool:
        """特别严格的重叠检查 - 使用适应的间距参数"""
        positions = individual.window_positions
        sizes = individual.window_sizes
        
        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                x1, y1 = positions[i]
                x2, y2 = positions[j]
                w1, h1 = sizes[i]
                w2, h2 = sizes[j]
                
                # 修复：增强窗户重叠检查，所有模式都严格执行不重叠约束
                min_gap = self.min_window_spacing

                # 计算窗户边界
                left1, right1 = x1 - w1/2, x1 + w1/2
                top1, bottom1 = y1 - h1/2, y1 + h1/2
                left2, right2 = x2 - w2/2, x2 + w2/2
                top2, bottom2 = y2 - h2/2, y2 + h2/2

                # 检查水平和垂直重叠（包含最小间距）
                horizontal_overlap = not (right1 + min_gap <= left2 or right2 + min_gap <= left1)
                vertical_overlap = not (bottom1 + min_gap <= top2 or bottom2 + min_gap <= top1)

                if horizontal_overlap and vertical_overlap:
                    self.logger.warning(f"检测到窗户重叠: 窗户{i}和窗户{j}")
                    return True
        
        return False
    
    def _repair_strict_individual(self, individual: FacadeIndividual) -> FacadeIndividual:
        """修复特别严格改造个体的重叠问题"""
        self.logger.info("修复特别严格改造个体重叠问题")
        
        # 对于严格模式，只能通过微调宽度来解决重叠
        for i in range(len(individual.window_sizes)):
            width, height = individual.window_sizes[i]
            
            # 逐步减小宽度直到无重叠
            reduction_step = 0.05  # 每次减少5cm
            max_reductions = 10    # 最多减少10次（50cm）
            
            for reduction in range(max_reductions):
                test_width = width - reduction * reduction_step
                test_width = max(test_width, self.min_width)  # 不能小于最小宽度
                
                # 测试这个宽度是否消除重叠
                individual.window_sizes[i] = (test_width, height)
                
                if not self._check_strict_overlaps(individual):
                    self.logger.info(f"窗户{i}宽度调整为{test_width:.2f}m以消除重叠")
                    break
            else:
                self.logger.warning(f"窗户{i}无法通过宽度调整消除重叠")
        
        return individual
    
    def _create_renovation_individual_from_real_data(self, individual_id: str,
                                                   num_windows: int) -> FacadeIndividual:
        """基于真实数据创建改造项目个体 - 大幅增强多样化版本"""
        existing_windows = self.facade_elements.windows
        existing_frames = self.facade_elements.frames
        existing_shading = self.facade_elements.shading
        
        window_positions = []
        window_sizes = []
        window_types = []
        frame_depths = []
        shading_depths = []
        shading_angles = []
        
        # 增加多样化参数 - 生成差异化的个体，支持60%变化范围
        diversity_factor = random.uniform(0.4, 1.0)  # 40%-100%变化幅度
        position_variation = random.uniform(0.1, 0.6)  # 10%-60%位置变化
        size_variation = random.uniform(0.2, 0.6)     # 20%-60%尺寸变化
        type_randomness = random.uniform(0.5, 0.9)      # 50%-90%类型随机化
        
        self.logger.info(f"创建多样化改造个体: 多样性{diversity_factor:.2f}, 位置变化{position_variation:.2f}, 尺寸变化{size_variation:.2f}")
        
        # 基于现有窗户创建多样化个体
        for i in range(num_windows):
            if i < len(existing_windows):
                existing_window = existing_windows[i]
                
                # 大幅度位置调整 - 增加多样性
                pixel_center_x, pixel_center_y = existing_window.center
                real_x = pixel_center_x * self.facade_elements.pixel_to_meter_ratio
                real_y = pixel_center_y * self.facade_elements.pixel_to_meter_ratio
                
                # 应用更大的位置变化
                max_position_change_x = existing_window.width * position_variation * diversity_factor
                max_position_change_y = existing_window.height * position_variation * diversity_factor
                
                new_x = real_x + random.uniform(-max_position_change_x, max_position_change_x)
                new_y = real_y + random.uniform(-max_position_change_y, max_position_change_y)
                
                # 确保在边界内
                new_x = MathUtils.clamp(new_x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
                new_y = MathUtils.clamp(new_y, self.facade_bounds['min_y'], self.facade_bounds['max_y'])
                window_positions.append((new_x, new_y))
                
                # 大幅度尺寸调整 - 增加多样性
                base_width = existing_window.width
                base_height = existing_window.height
                
                # 应用更大的尺寸变化
                width_change = size_variation * diversity_factor
                new_width = base_width * (1 + random.uniform(-width_change, width_change))
                new_height = base_height * (1 + random.uniform(-width_change * 0.5, width_change * 0.5))  # 高度变化较小
                
                # 应用约束
                new_width = MathUtils.clamp(new_width, self.min_width, self.max_width)
                new_height = MathUtils.clamp(new_height, self.min_height, self.max_height)
                window_sizes.append((new_width, new_height))
                
                self.logger.debug(f"多样化窗户{i}: 位置变化±{max_position_change_x:.3f}m, 尺寸变化{width_change*100:.1f}%")
                
            else:
                # 为额外窗户生成多样化位置
                if existing_windows:
                    avg_spacing = self._calculate_average_window_spacing(existing_windows)
                    # 增加位置随机性
                    position = self._generate_diverse_position(window_positions, avg_spacing, diversity_factor)
                else:
                    center_x = (self.facade_bounds['min_x'] + self.facade_bounds['max_x']) / 2
                    center_y = (self.facade_bounds['min_y'] + self.facade_bounds['max_y']) / 2
                    # 添加随机偏移
                    offset_x = random.uniform(-1.0, 1.0) * diversity_factor
                    offset_y = random.uniform(-0.5, 0.5) * diversity_factor
                    position = (center_x + offset_x, center_y + offset_y)
                
                window_positions.append(position)
                
                # 多样化尺寸生成
                if existing_windows:
                    avg_width = sum(w.width for w in existing_windows) / len(existing_windows)
                    avg_height = sum(w.height for w in existing_windows) / len(existing_windows)
                    
                    # 大幅度尺寸变化
                    width = avg_width * (1 + random.uniform(-size_variation, size_variation))
                    height = avg_height * (1 + random.uniform(-size_variation * 0.5, size_variation * 0.5))
                else:
                    width = random.uniform(1.0, 2.5)  
                    height = random.uniform(1.0, 2.0) 
                
                width = MathUtils.clamp(width, self.min_width, self.max_width)
                height = MathUtils.clamp(height, self.min_height, self.max_height)
                window_sizes.append((width, height))
            
            # 大幅增强窗户类型多样化 - 关键改进
            window_type = self._generate_diverse_window_type(i, existing_windows, existing_frames, existing_shading, type_randomness)
            window_types.append(window_type)
            
            # 多样化框架深度
            if window_type == 1:  # 有窗框
                if existing_frames and i < len(existing_frames):
                    base_depth = self._extract_real_frame_depth(i, existing_frames)
                    depth_variation = random.uniform(0.2, 0.6) * diversity_factor
                    frame_depth = base_depth * (1 + random.uniform(-depth_variation, depth_variation))
                else:
                    # 随机生成框架深度
                    frame_depth = random.uniform(0.05, 0.4) * diversity_factor + 0.1
                frame_depth = MathUtils.clamp(frame_depth, self.min_frame_depth, self.max_frame_depth)
            else:
                frame_depth = 0.0
            frame_depths.append(frame_depth)
            
            # 多样化遮阳参数
            if window_type == 2:  # 有遮阳
                if existing_shading and i < len(existing_shading):
                    base_shading_depth, base_shading_angle = self._extract_real_shading_parameters(i, existing_shading)
                    depth_variation = random.uniform(0.3, 0.7) * diversity_factor
                    angle_variation = random.uniform(0.2, 0.5) * diversity_factor
                    
                    shading_depth = base_shading_depth * (1 + random.uniform(-depth_variation, depth_variation))
                    shading_angle = base_shading_angle * (1 + random.uniform(-angle_variation, angle_variation))
                else:
                    # 随机生成遮阳参数
                    shading_depth = random.uniform(0.3, 1.2) * diversity_factor + 0.2
                    shading_angle = random.uniform(15.0, 55.0)
                
                shading_depth = MathUtils.clamp(shading_depth, self.min_shading_depth, self.max_shading_depth)
                shading_angle = MathUtils.clamp(shading_angle, self.min_shading_angle, self.max_shading_angle)
            else:
                shading_depth = 0.0
                shading_angle = 0.0
            
            shading_depths.append(shading_depth)
            shading_angles.append(shading_angle)
        
        # 创建多样化个体
        renovation_individual = FacadeIndividual(
            individual_id=individual_id,
            window_positions=window_positions,
            window_sizes=window_sizes,
            window_types=window_types,
            frame_depths=frame_depths,
            shading_depths=shading_depths,
            shading_angles=shading_angles,
            renovation_mode=RenovationMode.RENOVATION
        )
        
        # 验证并修复
        if not self._validate_individual(renovation_individual):
            renovation_individual = self._repair_individual(renovation_individual)
        
        self.logger.info(f"多样化改造个体创建完成: {num_windows}个窗户, "
                        f"框架{sum(1 for wt in window_types if wt == 1)}个, "
                        f"遮阳{sum(1 for wt in window_types if wt == 2)}个, "
                        f"多样性因子{diversity_factor:.2f}")
        
        return renovation_individual 
   
    def _create_individual_based_on_real_facade(self, individual_id: str, 
                                               num_windows: int) -> FacadeIndividual:
        """基于真实立面数据创建新建项目个体 - 修复坐标提取问题"""
        window_positions = []
        window_sizes = []
        window_types = []
        frame_depths = []
        shading_depths = []
        shading_angles = []
        
        existing_windows = self.facade_elements.windows
        existing_frames = self.facade_elements.frames
        existing_shading = self.facade_elements.shading
        
        self.logger.info(f"基于真实立面数据创建个体: 现有窗户{len(existing_windows)}个, 框架{len(existing_frames)}个, 遮阳{len(existing_shading)}个")
        
        for i in range(num_windows):
            if i < len(existing_windows) and existing_windows:
                # 基于现有窗户数据创建 - 修复：正确提取坐标位置
                existing_window = existing_windows[i]
                
                # 修复：使用真实窗户的精确坐标位置（像素坐标转换为米制坐标）
                pixel_center_x, pixel_center_y = existing_window.center
                # 转换为米制坐标
                real_x = pixel_center_x * self.facade_elements.pixel_to_meter_ratio
                real_y = pixel_center_y * self.facade_elements.pixel_to_meter_ratio
                
                # 允许小幅调整以增加多样性（±5%）
                pos_variation_factor = 0.05
                pos_variation_x = existing_window.width * pos_variation_factor
                pos_variation_y = existing_window.height * pos_variation_factor
                
                new_x = real_x + random.uniform(-pos_variation_x, pos_variation_x)
                new_y = real_y + random.uniform(-pos_variation_y, pos_variation_y)
                
                # 确保在边界内
                new_x = MathUtils.clamp(new_x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
                new_y = MathUtils.clamp(new_y, self.facade_bounds['min_y'], self.facade_bounds['max_y'])
                window_positions.append((new_x, new_y))
                
                # 修复：使用真实窗户的精确尺寸（已经是米制单位）
                base_width = existing_window.width
                base_height = existing_window.height
                size_variation = 0.05  # 减少随机化从15%到5%
                
                new_width = base_width * (1 + random.uniform(-size_variation, size_variation))
                new_height = base_height  # 高度不变
                
                # 应用约束
                new_width = MathUtils.clamp(new_width, self.min_width, self.max_width)
                new_height = MathUtils.clamp(new_height, self.min_height, self.max_height)
                window_sizes.append((new_width, new_height))
                
                self.logger.debug(f"基于现有窗户{i}: 像素坐标({pixel_center_x}, {pixel_center_y}) -> 米制坐标({real_x:.2f}, {real_y:.2f}) -> 调整后({new_x:.2f}, {new_y:.2f}), 尺寸({new_width:.2f}x{new_height:.2f})")
                
            else:
                # 为额外窗户生成合理位置（基于现有窗户的分布模式）
                if existing_windows:
                    # 分析现有窗户的分布模式
                    avg_spacing = self._calculate_average_window_spacing(existing_windows)
                    position = self._generate_position_based_on_pattern(window_positions, avg_spacing)
                else:
                    # 如果没有现有窗户，使用立面中心区域
                    center_x = (self.facade_bounds['min_x'] + self.facade_bounds['max_x']) / 2
                    center_y = (self.facade_bounds['min_y'] + self.facade_bounds['max_y']) / 2
                    position = (center_x, center_y)
                
                window_positions.append(position)
                
                # 基于现有窗户的平均尺寸
                if existing_windows:
                    avg_width = sum(w.width for w in existing_windows) / len(existing_windows)
                    avg_height = sum(w.height for w in existing_windows) / len(existing_windows)
                    
                    # 允许±3%变化（减少随机化）
                    width = avg_width * (1 + random.uniform(-0.03, 0.03))
                    height = avg_height  # 高度不变
                else:
                    # 使用标准窗户尺寸
                    width = 1.5  # 1.5米宽
                    height = 1.2  # 1.2米高
                
                # 应用约束
                width = MathUtils.clamp(width, self.min_width, self.max_width)
                height = MathUtils.clamp(height, self.min_height, self.max_height)
                window_sizes.append((width, height))
            
            # 修复：基于真实构件数据确定窗户类型
            window_type = self._determine_window_type_from_real_coordinates(i, existing_windows, existing_frames, existing_shading)
            window_types.append(window_type)
            
            # 修复：基于真实框架数据设置窗框深度
            if window_type == 1:  # 有窗框
                frame_depth = self._extract_real_frame_depth(i, existing_frames)
                frame_depth = MathUtils.clamp(frame_depth, self.min_frame_depth, self.max_frame_depth)
            else:
                frame_depth = 0.0
            frame_depths.append(frame_depth)
            
            # 修复：基于真实遮阳数据设置遮阳深度和角度
            if window_type == 2:  # 有遮阳
                shading_depth, shading_angle = self._extract_real_shading_parameters(i, existing_shading)
                shading_depth = MathUtils.clamp(shading_depth, self.min_shading_depth, self.max_shading_depth)
                shading_angle = MathUtils.clamp(shading_angle, self.min_shading_angle, self.max_shading_angle)
            else:
                shading_depth = 0.0
                shading_angle = 0.0
            shading_depths.append(shading_depth)
            shading_angles.append(shading_angle)
        
        self.logger.info(f"新建项目个体创建完成: {num_windows}个窗户, 基于真实坐标位置")
        
        # 创建可视化图表显示个体生成过程
        self._create_individual_visualization(individual_id, window_positions, window_sizes, window_types)
        
        return FacadeIndividual(
            individual_id=individual_id,
            window_positions=window_positions,
            window_sizes=window_sizes,
            window_types=window_types,
            frame_depths=frame_depths,
            shading_depths=shading_depths,
            shading_angles=shading_angles,
            renovation_mode=RenovationMode.NEW_CONSTRUCTION
        )
    
    def _generate_random_position(self, existing_positions: List[Tuple[float, float]],
                                existing_sizes: List[Tuple[float, float]]) -> Tuple[float, float]:
        """生成不重叠的随机位置"""
        max_attempts = 100
        
        for attempt in range(max_attempts):
            # 随机生成位置
            x = random.uniform(self.facade_bounds['min_x'], self.facade_bounds['max_x'])
            y = random.uniform(self.facade_bounds['min_y'], self.facade_bounds['max_y'])
            
            # 检查是否与现有窗户重叠
            overlaps = False
            for i, (ex_x, ex_y) in enumerate(existing_positions):
                if i < len(existing_sizes):
                    ex_width, ex_height = existing_sizes[i]
                    
                    # 简化的重叠检查（基于中心点距离）
                    distance = MathUtils.calculate_distance((x, y), (ex_x, ex_y))
                    min_distance = max(self.minimum_distance, 
                                     (ex_width + ex_height) / 4)  # 基于平均尺寸的最小距离
                    
                    if distance < min_distance:
                        overlaps = True
                        break
            
            if not overlaps:
                return (x, y)
        
        # 如果多次尝试都失败，返回一个相对安全的位置
        safe_x = (self.facade_bounds['min_x'] + self.facade_bounds['max_x']) / 2
        safe_y = (self.facade_bounds['min_y'] + self.facade_bounds['max_y']) / 2
        return (safe_x, safe_y) 
   
    def copy_individual(self, individual: FacadeIndividual) -> FacadeIndividual:
        """
        复制个体
        
        Args:
            individual: 要复制的个体
            
        Returns:
            复制的个体
        """
        try:
            return FacadeIndividual(
                individual_id=f"{individual.individual_id}_copy",
                window_positions=individual.window_positions.copy(),
                window_sizes=individual.window_sizes.copy(),
                window_types=individual.window_types.copy(),
                frame_depths=individual.frame_depths.copy(),
                shading_depths=individual.shading_depths.copy(),
                shading_angles=individual.shading_angles.copy(),
                renovation_mode=individual.renovation_mode
            )
        except Exception as e:
            self.logger.error(f"复制个体失败: {str(e)}")
            return individual
    
    def mutate_individual(self, individual: FacadeIndividual, 
                         mutation_rate: float = 0.08,
                         mutation_strength: float = 0.02,
                         generation: int = 0) -> FacadeIndividual:
        """
        变异个体 - 支持严格改造模式
        
        Args:
            individual: 原始个体
            mutation_rate: 变异率
            mutation_strength: 变异强度
            generation: 当前代数
            
        Returns:
            变异后的个体
        """
        try:
            # 特别严格改造模式使用专门的变异函数
            if individual.renovation_mode == RenovationMode.STRICT_RENOVATION:
                return self._mutate_strict_renovation_individual(individual, mutation_rate, mutation_strength, generation)
            
            # 原有的变异逻辑
            # 创建个体副本
            mutated = FacadeIndividual(
                individual_id=individual.individual_id + "_mutated",
                window_positions=individual.window_positions.copy(),
                window_sizes=individual.window_sizes.copy(),
                window_types=individual.window_types.copy(),
                frame_depths=individual.frame_depths.copy(),
                shading_depths=individual.shading_depths.copy(),
                shading_angles=individual.shading_angles.copy(),
                renovation_mode=individual.renovation_mode
            )
            
            # 自适应变异：随着代数增加减少变异强度
            adaptive_mutation_rate = mutation_rate * (1.0 - min(generation / 100.0, 0.7))  # 最多减少70%
            adaptive_mutation_strength = mutation_strength * (1.0 - min(generation / 150.0, 0.8))  # 最多减少80%
            
            num_windows = len(mutated.window_positions)
            
            for i in range(num_windows):
                # 位置变异
                if random.random() < adaptive_mutation_rate:
                    pos_x, pos_y = mutated.window_positions[i]
                    width, height = mutated.window_sizes[i]  # 获取当前窗户尺寸
                    
                    # 计算变异范围
                    x_range = (self.facade_bounds['max_x'] - self.facade_bounds['min_x']) * adaptive_mutation_strength
                    y_range = (self.facade_bounds['max_y'] - self.facade_bounds['min_y']) * adaptive_mutation_strength
                    
                    # 应用变异
                    new_x = pos_x + random.uniform(-x_range, x_range)
                    new_y = pos_y + random.uniform(-y_range, y_range)
                    
                    # 应用边界约束
                    new_x = MathUtils.clamp(new_x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
                    new_y = MathUtils.clamp(new_y, self.facade_bounds['min_y'], self.facade_bounds['max_y'])
                    
                    # 检查窗户重叠 - 如果重叠则放弃位置变异
                    if not self._check_window_overlap(new_x, new_y, width, height, i, mutated.window_positions, mutated.window_sizes):
                        mutated.window_positions[i] = (new_x, new_y)
                    else:
                        # 如果新位置会导致重叠，保持原位置
                        self.logger.debug(f"窗户{i}位置变异会导致重叠，保持原位置")
                
                # 尺寸变异 - 严格只允许水平宽度变化，高度保持不变
                if random.random() < adaptive_mutation_rate:
                    width, height = mutated.window_sizes[i]
                    
                    # 计算变异范围 - 只变化宽度
                    width_change = width * adaptive_mutation_strength
                    
                    # 应用变异 - 仅宽度变化，高度保持原样
                    new_width = width + random.uniform(-width_change, width_change)
                    new_height = height  # 高度不变
                    
                    # 应用约束
                    new_width = MathUtils.clamp(new_width, self.min_width, self.max_width)
                    # 高度不需要约束，因为保持不变
                    
                    mutated.window_sizes[i] = (new_width, new_height)
                        
                # 位置变异 - 严格只允许水平X坐标变化，Y坐标保持不变
                if random.random() < adaptive_mutation_rate:
                    pos_x, pos_y = mutated.window_positions[i]
                    width, height = mutated.window_sizes[i]
                    
                    # 计算变异范围 - 只变化X坐标
                    x_range = (self.facade_bounds['max_x'] - self.facade_bounds['min_x']) * adaptive_mutation_strength
                    
                    # 应用变异 - 仅X坐标变化，Y坐标保持原样
                    new_x = pos_x + random.uniform(-x_range, x_range)
                    new_y = pos_y  # Y坐标保持不变
                    
                    # 应用边界约束
                    new_x = MathUtils.clamp(new_x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
                    
                    # 检查窗户重叠 - 如果重叠则放弃位置变异
                    if not self._check_window_overlap(new_x, new_y, width, height, i, mutated.window_positions, mutated.window_sizes):
                        mutated.window_positions[i] = (new_x, new_y)
                    else:
                        # 如果新位置会导致重叠，保持原位置
                        self.logger.debug(f"窗户{i}位置变异会导致重叠，保持原位置")
                
                # 修复：类型变异 - 提高窗框和遮阳生成概率
                if random.random() < adaptive_mutation_rate * 0.7:  # 提高变异概率
                    # 使用加权随机选择，偏向窗框和遮阳
                    type_choices = [0, 1, 1, 1, 2, 2]  # 1和2的概率更高
                    mutated.window_types[i] = random.choice(type_choices)
                
                # 窗框深度变异
                if random.random() < adaptive_mutation_rate:
                    current_depth = mutated.frame_depths[i]
                    depth_change = self.max_frame_depth * adaptive_mutation_strength
                    
                    new_depth = current_depth + random.uniform(-depth_change, depth_change)
                    new_depth = MathUtils.clamp(new_depth, self.min_frame_depth, self.max_frame_depth)
                    
                    mutated.frame_depths[i] = new_depth
                
                # 遮阳深度变异
                if random.random() < adaptive_mutation_rate:
                    current_depth = mutated.shading_depths[i]
                    depth_change = self.max_shading_depth * adaptive_mutation_strength
                    
                    new_depth = current_depth + random.uniform(-depth_change, depth_change)
                    new_depth = MathUtils.clamp(new_depth, self.min_shading_depth, self.max_shading_depth)
                    
                    mutated.shading_depths[i] = new_depth
                
                # 遮阳角度变异
                if random.random() < adaptive_mutation_rate:
                    current_angle = mutated.shading_angles[i]
                    angle_change = self.max_shading_angle * adaptive_mutation_strength
                    
                    new_angle = current_angle + random.uniform(-angle_change, angle_change)
                    new_angle = MathUtils.clamp(new_angle, self.min_shading_angle, self.max_shading_angle)
                    
                    mutated.shading_angles[i] = new_angle
            
            # 验证和修复变异后的个体
            if not self._validate_individual(mutated):
                mutated = self._repair_individual(mutated)
            
            return mutated
            
        except Exception as e:
            self.logger.error(f"个体变异失败: {str(e)}")
            return individual  # 返回原个体    
    
    def _mutate_strict_renovation_individual(self, individual: FacadeIndividual,
                                           mutation_rate: float, mutation_strength: float, 
                                           generation: int) -> FacadeIndividual:
        """特别严格改造模式的变异函数 - 只允许水平宽度变化"""
        try:
            # 创建个体副本
            mutated = FacadeIndividual(
                individual_id=individual.individual_id + "_strict_mutated",
                window_positions=individual.window_positions.copy(),  # 位置严格不变
                window_sizes=individual.window_sizes.copy(),
                window_types=individual.window_types.copy(),
                frame_depths=individual.frame_depths.copy(),
                shading_depths=individual.shading_depths.copy(),
                shading_angles=individual.shading_angles.copy(),
                renovation_mode=RenovationMode.STRICT_RENOVATION
            )
            
            # 自适应变异：随着代数增加减少变异强度
            adaptive_mutation_rate = mutation_rate * (1.0 - min(generation / 200.0, 0.8))
            adaptive_mutation_strength = mutation_strength * (1.0 - min(generation / 300.0, 0.9))
            
            num_windows = len(mutated.window_sizes)
            mutations_applied = 0
            
            for i in range(num_windows):
                # 严格模式：只允许宽度变异，位置和高度严格不变
                if random.random() < adaptive_mutation_rate:
                    width, height = mutated.window_sizes[i]
                    
                    # 极小的宽度变化：±2%以内的微调（更保守）
                    width_change_factor = adaptive_mutation_strength * 0.5  # 进一步减小变异强度
                    width_change = width * width_change_factor * random.uniform(-1.0, 1.0)
                    
                    new_width = width + width_change
                    new_height = height  # 高度严格不变
                    
                    # 应用约束并确保变化合理
                    original_width = width
                    new_width = MathUtils.clamp(new_width, self.min_width, self.max_width)
                    new_width = max(new_width, original_width * 0.9)  # 最小不小于原宽度的90%
                    new_width = min(new_width, original_width * 1.1)  # 最大不超过原宽度的110%
                    
                    # 测试新尺寸是否会导致重叠
                    test_positions = mutated.window_positions.copy()
                    test_sizes = mutated.window_sizes.copy()
                    test_sizes[i] = (new_width, new_height)
                    
                    # 严格重叠检查
                    if not self._check_window_overlap(
                        test_positions[i][0], test_positions[i][1], 
                        new_width, new_height, i, test_positions, test_sizes, min_gap=0.6
                    ):
                        mutated.window_sizes[i] = (new_width, new_height)
                        mutations_applied += 1
                        self.logger.debug(f"严格模式窗户{i}宽度变异: {width:.3f}m → {new_width:.3f}m")
                    else:
                        self.logger.debug(f"严格模式窗户{i}宽度变异被拒绝（会导致重叠）")
                
                # 修复：窗户类型变异 - 大幅提高窗框和遮阳生成概率
                if random.random() < adaptive_mutation_rate * 0.8:  # 提高到80%的变异率
                    original_type = mutated.window_types[i]
                    # 只允许添加窗框或遮阳，不允许删除
                    if original_type == 0:  # 普通窗户可以添加窗框或遮阳
                        # 使用加权选择，偏向窗框
                        choices = [1, 1, 1, 2, 2]  # 窗框概率更高
                        mutated.window_types[i] = random.choice(choices)
                        mutations_applied += 1
                    # 已有窗框或遮阳的保持不变（严格模式不允许删除）
                
                # 窗框和遮阳参数的微调
                if mutated.window_types[i] == 1 and random.random() < adaptive_mutation_rate:
                    current_depth = mutated.frame_depths[i]
                    depth_change = current_depth * adaptive_mutation_strength * random.uniform(-0.5, 0.5)
                    new_depth = current_depth + depth_change
                    new_depth = MathUtils.clamp(new_depth, self.min_frame_depth, self.max_frame_depth)
                    mutated.frame_depths[i] = new_depth
                    mutations_applied += 1
                
                if mutated.window_types[i] == 2 and random.random() < adaptive_mutation_rate:
                    # 遮阳深度微调
                    current_depth = mutated.shading_depths[i]
                    depth_change = current_depth * adaptive_mutation_strength * random.uniform(-0.5, 0.5)
                    new_depth = current_depth + depth_change
                    new_depth = MathUtils.clamp(new_depth, self.min_shading_depth, self.max_shading_depth)
                    mutated.shading_depths[i] = new_depth
                    
                    # 遮阳角度微调
                    current_angle = mutated.shading_angles[i]
                    angle_change = current_angle * adaptive_mutation_strength * random.uniform(-0.5, 0.5)
                    new_angle = current_angle + angle_change
                    new_angle = MathUtils.clamp(new_angle, self.min_shading_angle, self.max_shading_angle)
                    mutated.shading_angles[i] = new_angle
                    mutations_applied += 1
            
            # 最终验证：确保无重叠
            if self._check_strict_overlaps(mutated):
                self.logger.warning("严格模式变异后检测到重叠，尝试修复")
                mutated = self._repair_strict_individual(mutated)
                
                # 如果仍有重叠，返回原始个体
                if self._check_strict_overlaps(mutated):
                    self.logger.warning("严格模式变异修复失败，返回原始个体")
                    return individual
            
            self.logger.debug(f"严格模式变异完成: 应用{mutations_applied}个变异，确保无重叠")
            return mutated
            
        except Exception as e:
            self.logger.error(f"严格模式个体变异失败: {str(e)}")
            return individual  # 返回原个体
   
    def crossover_individuals(self, parent1: FacadeIndividual, 
                            parent2: FacadeIndividual) -> Tuple[FacadeIndividual, FacadeIndividual]:
        """
        交叉操作产生子代个体
        
        Args:
            parent1: 父代个体1
            parent2: 父代个体2
            
        Returns:
            两个子代个体
        """
        try:
            # 确定子代窗户数量
            num_windows_1 = len(parent1.window_positions)
            num_windows_2 = len(parent2.window_positions)
            
            # 交叉窗户数量
            child1_num_windows = random.choice([num_windows_1, num_windows_2, 
                                              (num_windows_1 + num_windows_2) // 2])
            child2_num_windows = random.choice([num_windows_1, num_windows_2,
                                              (num_windows_1 + num_windows_2) // 2])
            
            # 创建子代1
            child1 = self._create_crossover_child(
                parent1, parent2, child1_num_windows, "child1"
            )
            
            # 创建子代2
            child2 = self._create_crossover_child(
                parent2, parent1, child2_num_windows, "child2"
            )
            
            # 验证和修复子代
            if not self._validate_individual(child1):
                child1 = self._repair_individual(child1)
            
            if not self._validate_individual(child2):
                child2 = self._repair_individual(child2)
            
            return child1, child2
            
        except Exception as e:
            self.logger.error(f"个体交叉失败: {str(e)}")
            return parent1, parent2  # 返回父代
    
    def _create_crossover_child(self, primary_parent: FacadeIndividual,
                               secondary_parent: FacadeIndividual,
                               num_windows: int, child_id: str) -> FacadeIndividual:
        """创建交叉子代个体"""
        window_positions = []
        window_sizes = []
        window_types = []
        frame_depths = []
        shading_depths = []
        shading_angles = []
        
        # 交叉点
        crossover_point = random.randint(1, num_windows - 1) if num_windows > 1 else 1
        
        for i in range(num_windows):
            # 选择父代
            if i < crossover_point:
                parent = primary_parent
            else:
                parent = secondary_parent
            
            # 从选定父代获取基因
            if i < len(parent.window_positions):
                window_positions.append(parent.window_positions[i])
                window_sizes.append(parent.window_sizes[i])
                window_types.append(parent.window_types[i])
                frame_depths.append(parent.frame_depths[i])
                shading_depths.append(parent.shading_depths[i])
                shading_angles.append(parent.shading_angles[i])
            else:
                # 如果父代窗户数量不足，使用默认值
                window_positions.append(self._generate_random_position(window_positions, window_sizes))
                window_sizes.append((1.5, 1.2))  # 默认尺寸
                window_types.append(0)  # 普通窗户
                frame_depths.append(0.0)
                shading_depths.append(0.0)
                shading_angles.append(0.0)
        
        return FacadeIndividual(
            individual_id=f"{primary_parent.individual_id}_{child_id}",
            window_positions=window_positions,
            window_sizes=window_sizes,
            window_types=window_types,
            frame_depths=frame_depths,
            shading_depths=shading_depths,
            shading_angles=shading_angles,
            renovation_mode=primary_parent.renovation_mode
        )
    
    def _check_window_overlap(self, x, y, width, height, current_index, window_positions, window_sizes, min_gap=None):
        """严格检查窗户是否与其他窗户重叠 - 适应改造模式版本
        
        Args:
            x, y: 窗户中心坐标
            width, height: 窗户尺寸
            current_index: 当前窗户索引
            window_positions: 所有窗户位置列表
            window_sizes: 所有窗户尺寸列表
            min_gap: 窗户间最小间距（米），如果为None则使用改造模式对应的间距
        
        Returns:
            bool: True表示有重叠，False表示无重叠
        """
        try:
            # 根据改造模式确定最小间距
            if min_gap is None:
                min_gap = self.min_window_spacing
            
            # 对于严格改造模式，使用重叠容忍度
            tolerance = self.overlap_tolerance if hasattr(self, 'overlap_tolerance') else 0.0
            effective_min_gap = max(0, min_gap - tolerance)
            
            # 计算当前窗户的边界（包含安全边距）
            current_left = x - width / 2 - effective_min_gap / 2
            current_right = x + width / 2 + effective_min_gap / 2
            current_top = y - height / 2 - effective_min_gap / 2
            current_bottom = y + height / 2 + effective_min_gap / 2
            
            # 检查与其他所有窗户的重叠
            for i, (other_x, other_y) in enumerate(window_positions):
                if i == current_index:
                    continue  # 跳过自身
                
                if i < len(window_sizes):
                    other_width, other_height = window_sizes[i]
                    
                    # 计算其他窗户的边界（包含安全边距）
                    other_left = other_x - other_width / 2 - effective_min_gap / 2
                    other_right = other_x + other_width / 2 + effective_min_gap / 2
                    other_top = other_y - other_height / 2 - effective_min_gap / 2
                    other_bottom = other_y + other_height / 2 + effective_min_gap / 2
                    
                    # 严格的矩形重叠检查
                    horizontal_overlap = not (current_right <= other_left or current_left >= other_right)
                    vertical_overlap = not (current_bottom <= other_top or current_top >= other_bottom)
                    
                    # 如果水平和垂直都重叠，则存在重叠
                    if horizontal_overlap and vertical_overlap:
                        # 严格改造模式下降低警告级别
                        if self.renovation_mode == RenovationMode.STRICT_RENOVATION:
                            self.logger.debug(f"严格模式检测到窗户重叠: 窗户{current_index}与窗户{i}")
                        else:
                            self.logger.warning(f"检测到窗户重叠: 窗户{current_index}与窗户{i}")
                        return True
                    
                    # 额外检查：中心点距离（对严格改造模式更宽松）
                    center_distance = MathUtils.calculate_distance((x, y), (other_x, other_y))
                    min_center_distance = (width + other_width) / 2 + (height + other_height) / 2 + effective_min_gap
                    
                    if center_distance < min_center_distance:
                        # 严格改造模式下降低警告级别
                        if self.renovation_mode == RenovationMode.STRICT_RENOVATION:
                            self.logger.debug(f"严格模式检测到窗户过近: 窗户{current_index}与窗户{i}距离{center_distance:.3f}m < 最小距离{min_center_distance:.3f}m")
                        else:
                            self.logger.warning(f"检测到窗户过近: 窗户{current_index}与窗户{i}距离{center_distance:.3f}m < 最小距离{min_center_distance:.3f}m")
                        return True
            
            return False  # 无重叠
            
        except Exception as e:
            self.logger.error(f"窗户重叠检查失败: {str(e)}")
            return True  # 出错时保守返回有重叠
    
    def _validate_individual(self, individual: FacadeIndividual) -> bool:
        """验证个体合法性"""
        try:
            # 检查基本数据完整性
            num_windows = len(individual.window_positions)
            if (len(individual.window_sizes) != num_windows or
                len(individual.window_types) != num_windows or
                len(individual.frame_depths) != num_windows or
                len(individual.shading_depths) != num_windows or
                len(individual.shading_angles) != num_windows):
                return False
            
            # 检查位置边界
            for pos in individual.window_positions:
                x, y = pos
                if (x < self.facade_bounds['min_x'] or x > self.facade_bounds['max_x'] or
                    y < self.facade_bounds['min_y'] or y > self.facade_bounds['max_y']):
                    return False
            
            # 检查尺寸约束
            for size in individual.window_sizes:
                width, height = size
                if (width < self.min_width or width > self.max_width or
                    height < self.min_height or height > self.max_height):
                    return False
            
            # 检查重叠
            if self._check_window_overlaps(individual):
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"个体验证失败: {str(e)}")
            return False
    
    def _check_window_overlaps(self, individual: FacadeIndividual) -> bool:
        """检查窗户重叠"""
        positions = individual.window_positions
        sizes = individual.window_sizes
        
        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                pos1, size1 = positions[i], sizes[i]
                pos2, size2 = positions[j], sizes[j]
                
                # 计算距离
                distance = MathUtils.calculate_distance(pos1, pos2)
                min_distance = max(self.minimum_distance, 
                                 (size1[0] + size1[1] + size2[0] + size2[1]) / 8)
                
                if distance < min_distance:
                    return True
        
        return False
    
    def _repair_individual(self, individual: FacadeIndividual) -> FacadeIndividual:
        """修复不合法的个体"""
        try:
            # 修复位置边界
            for i, (x, y) in enumerate(individual.window_positions):
                new_x = MathUtils.clamp(x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
                new_y = MathUtils.clamp(y, self.facade_bounds['min_y'], self.facade_bounds['max_y'])
                individual.window_positions[i] = (new_x, new_y)
            
            # 修复尺寸约束
            for i, (width, height) in enumerate(individual.window_sizes):
                new_width = MathUtils.clamp(width, self.min_width, self.max_width)
                new_height = MathUtils.clamp(height, self.min_height, self.max_height)
                individual.window_sizes[i] = (new_width, new_height)
            
            # 修复重叠问题
            self._fix_overlaps(individual)
            
            return individual
            
        except Exception as e:
            self.logger.error(f"个体修复失败: {str(e)}")
            return individual
    
    def _fix_overlaps(self, individual: FacadeIndividual) -> None:
        """修复窗户重叠问题"""
        positions = individual.window_positions
        sizes = individual.window_sizes
        
        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                pos1, size1 = positions[i], sizes[i]
                pos2, size2 = positions[j], sizes[j]
                
                distance = MathUtils.calculate_distance(pos1, pos2)
                min_distance = max(self.minimum_distance, 
                                 (size1[0] + size1[1] + size2[0] + size2[1]) / 8)
                
                if distance < min_distance:
                    # 移动第二个窗户
                    angle = random.uniform(0, 2 * np.pi)
                    new_x = pos1[0] + min_distance * np.cos(angle)
                    new_y = pos1[1] + min_distance * np.sin(angle)
                    
                    # 确保在边界内
                    new_x = MathUtils.clamp(new_x, self.facade_bounds['min_x'], self.facade_bounds['max_x'])
                    new_y = MathUtils.clamp(new_y, self.facade_bounds['min_y'], self.facade_bounds['max_y'])
                    
                    individual.window_positions[j] = (new_x, new_y)
    
    def _create_individual_visualization(self, individual_id: str, window_positions: List[Tuple[float, float]], 
                                       window_sizes: List[Tuple[float, float]], window_types: List[int]):
        """创建个体可视化图表"""
        try:
            # 延迟导入以避免循环依赖
            from ..visualization.visualization_manager import create_visualization_manager
            
            # 创建可视化管理器
            vm = create_visualization_manager()
            
            # 调用可视化功能
            vm.visualize_initial_population_creation(
                individual_id=individual_id,
                num_windows=len(window_types),
                window_types=window_types,
                window_positions=window_positions,
                window_sizes=window_sizes
            )
            
            self.logger.info(f"已创建个体可视化: {individual_id}")
            
        except Exception as e:
            self.logger.warning(f"创建个体可视化失败: {str(e)}")


def create_individual_encoder(facade_elements: FacadeElements, allow_window_modification: bool = False, renovation_mode: Optional[RenovationMode] = None) -> IndividualEncoder:
    """
    创建个体编码器实例
    
    Args:
        facade_elements: 立面元素数据
        allow_window_modification: 是否允许增减窗户数量
        renovation_mode: 改造模式（可选，如果提供将覆盖自动判断）
        
    Returns:
        配置好的个体编码器
    """
    return IndividualEncoder(facade_elements, allow_window_modification, renovation_mode)