"""
NSGA-III立面优化器主类
整合所有优化组件，提供完整的立面优化解决方案
"""

import numpy as np
import json
from typing import Dict, List, Tuple, Any, Optional, Callable
from datetime import datetime
from pathlib import Path

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext, get_performance_logger, get_optimization_logger
from ..core.exceptions import OptimizationError, handle_exception
from ..core.data_structures import (
    FacadeElements, OrientedClimateData, FacadeIndividual, 
    ObjectiveResults, OptimizationResults, OptimizationSession, RenovationMode
)
from ..core.utils import FileUtils, DataUtils, TimeUtils

from .individual_encoder import IndividualEncoder, create_individual_encoder
from .objective_functions import ObjectiveFunctionEvaluator, create_objective_function_evaluator
from .constraint_handler import ConstraintHandler, create_constraint_handler
from .genetic_operators import GeneticOperators, create_genetic_operators
from .convergence_monitor import ConvergenceMonitor, create_convergence_monitor, ConvergenceStatus


class NSGA3FacadeOptimizer:
    """
    NSGA-III立面优化器主类
    
    功能：
    1. 整合所有优化组件
    2. 管理优化会话和状态
    3. 提供完整的优化流程
    4. 支持优化过程监控和控制
    5. 生成优化结果和报告
    6. 支持断点续传和结果保存
    """
    
    def __init__(self, facade_elements: FacadeElements, 
                 climate_data: OrientedClimateData, 
                 renovation_mode: RenovationMode = RenovationMode.NEW_CONSTRUCTION):
        """
        初始化NSGA-III立面优化器
        
        Args:
            facade_elements: 立面元素数据
            climate_data: 朝向调整后的气候数据
            renovation_mode: 改造模式
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        self.opt_logger = get_optimization_logger()  # 优化算法专用日志器
        
        # 确保优化日志器不输出到终端
        self.opt_logger.propagate = False
        import logging
        for handler in self.opt_logger.handlers[:]:
            if isinstance(handler, logging.StreamHandler) and handler.stream.name == '<stdout>':
                self.opt_logger.removeHandler(handler)
        
        self.facade_elements = facade_elements
        self.climate_data = climate_data
        self.renovation_mode = renovation_mode
        
        # 初始化优化组件
        # 根据改造模式确定窗户修改策略
        if renovation_mode == RenovationMode.NEW_CONSTRUCTION:
            # 新建项目：允许自由添加窗户
            allow_modification = True
        elif renovation_mode == RenovationMode.RENOVATION:
            # 改造项目：不能增删窗户，只能横向调整
            allow_modification = False
        else:  # MAJOR_RENOVATION
            # 大幅度改造：允许自由增删窗户
            allow_modification = True
        
        self.individual_encoder = create_individual_encoder(
            facade_elements, 
            allow_window_modification=allow_modification,
            renovation_mode=renovation_mode
        )
        self.objective_evaluator = create_objective_function_evaluator(facade_elements, climate_data)
        self.constraint_handler = create_constraint_handler(facade_elements)
        self.genetic_operators = create_genetic_operators(
            self.individual_encoder, self.objective_evaluator, self.constraint_handler
        )
        self.convergence_monitor = create_convergence_monitor()
        
        # 根据改造模式调整约束参数
        self.constraint_handler.adjust_constraints_for_renovation_mode(renovation_mode)
        
        # 优化状态
        self.is_running = False
        self.is_paused = False
        self.current_session = None
        self.best_solutions = []
        self.optimization_history = []
        
        # 回调函数
        self.progress_callback: Optional[Callable] = None
        self.generation_callback: Optional[Callable] = None
        
        # 输出配置
        self.output_directory = None
        self.save_intermediate_results = True
        
        self.logger.info(f"NSGA-III立面优化器初始化完成: {climate_data.orientation.value}朝向")
    
    def set_progress_callback(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """设置进度回调函数"""
        self.progress_callback = callback
    
    def set_generation_callback(self, callback: Callable[[int, List[FacadeIndividual], List[ObjectiveResults]], None]) -> None:
        """设置代数回调函数"""
        self.generation_callback = callback
    
    def set_output_directory(self, output_dir: str, save_intermediate: bool = True) -> None:
        """设置输出目录"""
        self.output_directory = Path(output_dir)
        self.save_intermediate_results = save_intermediate
        FileUtils.ensure_directory(self.output_directory)
    
    @handle_exception
    def optimize(self, max_generations: Optional[int] = None,
                max_time_seconds: Optional[int] = None,
                target_objectives: Optional[List[float]] = None) -> OptimizationResults:
        """
        执行立面优化
        
        Args:
            max_generations: 最大进化代数
            max_time_seconds: 最大运行时间（秒）
            target_objectives: 目标值
            
        Returns:
            优化结果
            
        Raises:
            OptimizationError: 优化失败时抛出
        """
        with LogContext("NSGA-III立面优化", self.logger):
            try:
                # 创建优化会话
                session_id = TimeUtils.get_timestamp()
                self.current_session = OptimizationSession(
                    session_id=session_id,
                    facade_elements=self.facade_elements,
                    climate_data=self.climate_data,
                    start_time=datetime.now(),
                    parameters=self._get_optimization_parameters()
                )
                
                # 设置终止条件
                if max_generations:
                    self.convergence_monitor.termination_criteria.max_generations = max_generations
                if max_time_seconds:
                    self.convergence_monitor.termination_criteria.max_time_seconds = max_time_seconds
                if target_objectives:
                    self.convergence_monitor.termination_criteria.target_objectives = target_objectives
                
                # 准备输出目录
                if self.output_directory is None:
                    session_dir = Path(self.config.get_output_directory()) / f"optimization_{session_id}"
                    self.set_output_directory(session_dir)
                
                self.logger.info(f"开始立面优化: 会话ID={session_id}")
                
                # 执行优化主循环
                optimization_result = self._run_optimization_loop()
                
                # 保存最终结果
                self._save_optimization_results(optimization_result)
                
                # 生成优化报告
                report_path = self._generate_optimization_report(optimization_result)
                optimization_result.report_path = str(report_path)
                
                self.logger.info(f"立面优化完成: {optimization_result.termination_reason}, "
                               f"找到 {len(optimization_result.pareto_solutions)} 个帕累托解")
                
                return optimization_result
                
            except Exception as e:
                self.is_running = False
                raise OptimizationError(f"立面优化失败: {str(e)}") from e
    
    def pause_optimization(self) -> None:
        """暂停优化"""
        if self.is_running:
            self.is_paused = True
            self.logger.info("优化已暂停")
    
    def resume_optimization(self) -> None:
        """恢复优化"""
        if self.is_running and self.is_paused:
            self.is_paused = False
            self.logger.info("优化已恢复")
    
    def stop_optimization(self) -> None:
        """停止优化"""
        self.is_running = False
        self.is_paused = False
        self.logger.info("优化已停止")
    
    def _run_optimization_loop(self) -> OptimizationResults:
        """运行优化主循环"""
        try:
            self.is_running = True
            self.convergence_monitor.start_monitoring()
            
            # 初始化种群
            with self.performance_logger.time_operation("种群初始化"):
                population = self.genetic_operators.initialize_population()
                evaluation_results = self.genetic_operators.evaluate_population(population)
            
            generation = 0
            termination_reason = "未知"
            
            # 主进化循环
            while self.is_running:
                # 检查暂停
                while self.is_paused and self.is_running:
                    import time
                    time.sleep(0.1)
                
                if not self.is_running:
                    termination_reason = "用户终止"
                    break
                
                # 计算种群统计
                population_stats = self.genetic_operators._calculate_population_statistics(
                    population, evaluation_results
                )
                
                # 更新收敛监控
                convergence_metrics = self.convergence_monitor.update_convergence_metrics(
                    population_stats, evaluation_results
                )
                
                # 检查终止条件
                should_terminate, status = self.convergence_monitor.should_terminate()
                if should_terminate:
                    termination_reason = status.value
                    break
                
                # 进化一代
                with self.performance_logger.time_operation(f"第{generation + 1}代进化"):
                    population, evaluation_results = self.genetic_operators.evolve_generation(
                        population, evaluation_results
                    )
                
                generation += 1
                
                # 更新最佳解
                self._update_best_solutions(evaluation_results)
                
                # 第二阶段优化：自适应参数更新
                diversity_metric = getattr(convergence_metrics, 'diversity_metric', None)
                self.genetic_operators.update_adaptive_parameters(evaluation_results, diversity_metric)
                
                # 保存中间结果
                if self.save_intermediate_results and generation % 10 == 0:
                    self._save_intermediate_results(generation, population, evaluation_results)
                
                # 调用回调函数
                self._call_callbacks(generation, population, evaluation_results, convergence_metrics)

                # 优化的终端输出 - 显示每一代的三个方面解的数据
                self._print_generation_summary(generation, evaluation_results, convergence_metrics)
            
            # 创建优化结果
            optimization_result = self._create_optimization_results(
                population, evaluation_results, termination_reason
            )
            
            return optimization_result

        finally:
            self.is_running = False
            self.is_paused = False

    def _print_generation_summary(self, generation: int, evaluation_results: List[ObjectiveResults],
                                 convergence_metrics) -> None:
        """
        打印每一代的详细摘要信息到终端
        """
        try:
            # 过滤可行解
            feasible_results = [r for r in evaluation_results if r.is_feasible]

            if not feasible_results:
                print(f"第{generation:3d}代 | 无可行解 | 种群大小: {len(evaluation_results)}")
                return

            # 计算三个目标的最佳值
            best_energy = min(r.energy_consumption for r in feasible_results)
            best_thermal = min(r.thermal_performance for r in feasible_results)
            best_cost = min(r.renovation_cost for r in feasible_results)

            # 计算平均值
            avg_energy = sum(r.energy_consumption for r in feasible_results) / len(feasible_results)
            avg_thermal = sum(r.thermal_performance for r in feasible_results) / len(feasible_results)
            avg_cost = sum(r.renovation_cost for r in feasible_results) / len(feasible_results)

            # 计算收敛指标
            hypervolume = getattr(convergence_metrics, 'hypervolume', 0.0)
            diversity = getattr(convergence_metrics, 'diversity_metric', 0.0)

            # 格式化输出
            print(f"第{generation:3d}代 | "
                  f"能耗: {best_energy:6.1f}({avg_energy:6.1f}) | "
                  f"热工: {best_thermal:.3f}({avg_thermal:.3f}) | "
                  f"成本: {best_cost:6.0f}({avg_cost:6.0f}) | "
                  f"可行解: {len(feasible_results):2d}/{len(evaluation_results):2d} | "
                  f"HV: {hypervolume:.4f} | "
                  f"多样性: {diversity:.3f}")

            # 每代都显示详细信息以观察差异性
            if True:  # 每代都显示
                print("-" * 100)
                print(f"第{generation}代详细统计:")
                print(f"  最佳解 - 能耗: {best_energy:.1f} kWh/m²/year, 热工: {best_thermal:.4f}, 成本: {best_cost:.0f} 元")
                print(f"  平均值 - 能耗: {avg_energy:.1f} kWh/m²/year, 热工: {avg_thermal:.4f}, 成本: {avg_cost:.0f} 元")
                print(f"  收敛指标 - 超体积: {hypervolume:.6f}, 多样性: {diversity:.4f}")
                print("-" * 100)

        except Exception as e:
            # 如果输出失败，使用简化版本
            print(f"第{generation:3d}代 | 输出错误: {str(e)}")

    def _update_best_solutions(self, evaluation_results: List[ObjectiveResults]) -> None:
        """更新最佳解集合"""
        try:
            # 获取可行解
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            if not feasible_results:
                return
            
            # 找到非支配解
            non_dominated_results = self._find_non_dominated_solutions(feasible_results)
            
            # 更新最佳解集合
            for result in non_dominated_results:
                # 检查是否已存在类似解
                is_duplicate = False
                for existing in self.best_solutions:
                    if self._is_similar_solution(result, existing):
                        # 如果新解更好，替换旧解
                        if self._is_better_solution(result, existing):
                            self.best_solutions.remove(existing)
                            self.best_solutions.append(result)
                        is_duplicate = True
                        break
                
                if not is_duplicate:
                    self.best_solutions.append(result)
            
            # 限制最佳解数量
            if len(self.best_solutions) > 100:
                # 保留最好的100个解
                self.best_solutions = sorted(
                    self.best_solutions,
                    key=lambda r: r.energy_consumption + r.thermal_performance + r.renovation_cost / 100000
                )[:100]
            
        except Exception as e:
            self.logger.warning(f"更新最佳解失败: {str(e)}")
    
    def _find_non_dominated_solutions(self, results: List[ObjectiveResults]) -> List[ObjectiveResults]:
        """找到非支配解"""
        non_dominated = []
        
        for i, result1 in enumerate(results):
            is_dominated = False
            
            for j, result2 in enumerate(results):
                if i != j and self._dominates(result2, result1):
                    is_dominated = True
                    break
            
            if not is_dominated:
                non_dominated.append(result1)
        
        return non_dominated
    
    def _dominates(self, result1: ObjectiveResults, result2: ObjectiveResults) -> bool:
        """判断result1是否支配result2"""
        objectives1 = [result1.energy_consumption, result1.thermal_performance, result1.renovation_cost]
        objectives2 = [result2.energy_consumption, result2.thermal_performance, result2.renovation_cost]
        
        better_in_at_least_one = False
        for i in range(len(objectives1)):
            if objectives1[i] > objectives2[i]:
                return False
            elif objectives1[i] < objectives2[i]:
                better_in_at_least_one = True
        
        return better_in_at_least_one
    
    def _is_similar_solution(self, result1: ObjectiveResults, result2: ObjectiveResults) -> bool:
        """判断两个解是否相似"""
        threshold = 0.01  # 1%的差异阈值
        
        objectives1 = [result1.energy_consumption, result1.thermal_performance, result1.renovation_cost]
        objectives2 = [result2.energy_consumption, result2.thermal_performance, result2.renovation_cost]
        
        for i in range(len(objectives1)):
            if objectives2[i] > 0:
                relative_diff = abs(objectives1[i] - objectives2[i]) / objectives2[i]
                if relative_diff > threshold:
                    return False
            elif abs(objectives1[i] - objectives2[i]) > 1e-6:
                return False
        
        return True
    
    def _is_better_solution(self, result1: ObjectiveResults, result2: ObjectiveResults) -> bool:
        """判断解1是否优于解2"""
        # 综合评分比较
        score1 = (result1.energy_consumption * 0.4 + 
                 result1.thermal_performance * 0.3 + 
                 result1.renovation_cost * 0.3 / 100000)
        score2 = (result2.energy_consumption * 0.4 + 
                 result2.thermal_performance * 0.3 + 
                 result2.renovation_cost * 0.3 / 100000)
        
        return score1 < score2
    
    def _save_intermediate_results(self, generation: int, population: List[FacadeIndividual],
                                 evaluation_results: List[ObjectiveResults]) -> None:
        """保存中间结果"""
        try:
            if not self.output_directory:
                return
            
            intermediate_dir = self.output_directory / "intermediate_results"
            FileUtils.ensure_directory(intermediate_dir)
            
            # 保存当前代的最佳解
            best_results = sorted(
                [r for r in evaluation_results if r.is_feasible],
                key=lambda r: r.energy_consumption
            )[:10]  # 保存前10个最佳解
            
            generation_data = {
                'generation': generation,
                'timestamp': datetime.now().isoformat(),
                'best_solutions': [self._result_to_dict(r) for r in best_results],
                'population_size': len(population),
                'feasible_count': len([r for r in evaluation_results if r.is_feasible])
            }
            
            filename = f"generation_{generation:04d}.json"
            DataUtils.save_json(generation_data, intermediate_dir / filename)
            
        except Exception as e:
            self.logger.warning(f"保存中间结果失败: {str(e)}")
    
    def _call_callbacks(self, generation: int, population: List[FacadeIndividual],
                       evaluation_results: List[ObjectiveResults],
                       convergence_metrics) -> None:
        """调用回调函数"""
        try:
            # 进度回调
            if self.progress_callback:
                progress_info = {
                    'generation': generation,
                    'max_generations': self.convergence_monitor.termination_criteria.max_generations,
                    'elapsed_time': convergence_metrics.elapsed_time,
                    'max_time': self.convergence_monitor.termination_criteria.max_time_seconds,
                    'best_objectives': convergence_metrics.best_objectives,
                    'feasible_count': len([r for r in evaluation_results if r.is_feasible]),
                    'total_count': len(evaluation_results),
                    'convergence_metrics': {
                        'hypervolume': convergence_metrics.hypervolume,
                        'diversity': convergence_metrics.diversity_metric,
                        'convergence': convergence_metrics.convergence_metric
                    }
                }
                self.progress_callback(progress_info)
            
            # 代数回调
            if self.generation_callback:
                self.generation_callback(generation, population, evaluation_results)
            
        except Exception as e:
            self.logger.warning(f"回调函数执行失败: {str(e)}")
    
    def _create_optimization_results(self, population: List[FacadeIndividual],
                                   evaluation_results: List[ObjectiveResults],
                                   termination_reason: str) -> OptimizationResults:
        """创建优化结果"""
        try:
            # 获取帕累托最优解
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            pareto_solutions = self._find_non_dominated_solutions(feasible_results)
            
            # 选择代表性解
            representative_solutions = self._select_representative_solutions(pareto_solutions)
            
            # 获取收敛历史
            convergence_history = self.convergence_monitor.get_convergence_history()
            
            # 计算统计信息
            statistics = self._calculate_optimization_statistics(
                evaluation_results, convergence_history
            )
            
            optimization_results = OptimizationResults(
                session_id=self.current_session.session_id if self.current_session else "unknown",
                facade_elements=self.facade_elements,
                climate_data=self.climate_data,
                pareto_solutions=pareto_solutions,
                representative_solutions=representative_solutions,
                best_energy_solution=min(feasible_results, key=lambda r: r.energy_consumption) if feasible_results else None,
                best_thermal_solution=min(feasible_results, key=lambda r: r.thermal_performance) if feasible_results else None,
                best_cost_solution=min(feasible_results, key=lambda r: r.renovation_cost) if feasible_results else None,
                convergence_history=convergence_history,
                optimization_statistics=statistics,
                termination_reason=termination_reason,
                total_generations=self.convergence_monitor.current_generation,
                total_evaluations=self.convergence_monitor.current_generation * len(population),
                computation_time=(datetime.now() - self.current_session.start_time).total_seconds() if self.current_session else 0.0,
                optimization_metadata={
                    'population_size': len(population),
                    'final_feasible_count': len(feasible_results),
                    'pareto_front_size': len(pareto_solutions),
                    'optimization_parameters': self._get_optimization_parameters()
                }
            )
            
            return optimization_results
            
        except Exception as e:
            self.logger.error(f"创建优化结果失败: {str(e)}")
            # 返回基本结果
            return OptimizationResults(
                session_id="error",
                facade_elements=self.facade_elements,
                climate_data=self.climate_data,
                pareto_solutions=[],
                representative_solutions=[],
                convergence_history=[],
                optimization_statistics={},
                termination_reason=f"错误: {str(e)}",
                total_generations=0,
                total_evaluations=0,
                computation_time=0.0,
                optimization_metadata={}
            )
    
    def _select_representative_solutions(self, pareto_solutions: List[ObjectiveResults]) -> List[ObjectiveResults]:
        """选择代表性解"""
        if len(pareto_solutions) <= 5:
            return pareto_solutions
        
        # 使用K-means聚类选择代表性解
        try:
            from sklearn.cluster import KMeans
            
            # 提取目标函数值
            objectives_matrix = np.array([
                [r.energy_consumption, r.thermal_performance, r.renovation_cost]
                for r in pareto_solutions
            ])
            
            # 归一化
            objectives_normalized = (objectives_matrix - objectives_matrix.min(axis=0)) / (
                objectives_matrix.max(axis=0) - objectives_matrix.min(axis=0) + 1e-10
            )
            
            # 检查数据唯一性，避免聚类警告
            unique_points = np.unique(objectives_normalized, axis=0)
            n_unique = len(unique_points)

            # 动态调整聚类数量
            n_clusters = min(max(2, n_unique), min(5, len(pareto_solutions)))

            if n_unique < 2:
                # 数据点太相似，直接返回前5个
                return pareto_solutions[:5]

            try:
                # K-means聚类
                kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
                clusters = kmeans.fit_predict(objectives_normalized)

                # 从每个簇中选择最接近簇中心的解
                representative_solutions = []
                for cluster_id in range(n_clusters):
                    cluster_indices = np.where(clusters == cluster_id)[0]

                    # 检查簇是否为空
                    if len(cluster_indices) == 0:
                        continue

                    cluster_center = kmeans.cluster_centers_[cluster_id]

                    # 找到最接近簇中心的解
                    min_distance = float('inf')
                    best_idx = cluster_indices[0]

                    for idx in cluster_indices:
                        distance = np.linalg.norm(objectives_normalized[idx] - cluster_center)
                        if distance < min_distance:
                            min_distance = distance
                            best_idx = idx

                    representative_solutions.append(pareto_solutions[best_idx])

                # 如果聚类结果不足5个，补充其他解
                if len(representative_solutions) < 5:
                    remaining_solutions = [sol for sol in pareto_solutions if sol not in representative_solutions]
                    representative_solutions.extend(remaining_solutions[:5-len(representative_solutions)])

            except Exception as cluster_error:
                self.logger.warning(f"聚类失败: {str(cluster_error)}")
                # 使用简单的均匀选择
                step = max(1, len(pareto_solutions) // 5)
                representative_solutions = pareto_solutions[::step][:5]
            
            return representative_solutions
            
        except ImportError:
            # 如果没有sklearn，使用简单的均匀选择
            step = len(pareto_solutions) // 5
            return pareto_solutions[::step][:5]
        except Exception as e:
            self.logger.warning(f"选择代表性解失败: {str(e)}")
            return pareto_solutions[:5]
    
    def _calculate_optimization_statistics(self, evaluation_results: List[ObjectiveResults],
                                         convergence_history) -> Dict[str, Any]:
        """计算优化统计信息"""
        try:
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            
            statistics = {
                'final_population_size': len(evaluation_results),
                'final_feasible_count': len(feasible_results),
                'feasibility_ratio': len(feasible_results) / len(evaluation_results) if evaluation_results else 0.0,
                'objective_statistics': {},
                'convergence_statistics': {},
                'performance_statistics': {}
            }
            
            # 目标函数统计
            if feasible_results:
                energy_values = [r.energy_consumption for r in feasible_results]
                thermal_values = [r.thermal_performance for r in feasible_results]
                cost_values = [r.renovation_cost for r in feasible_results]
                
                statistics['objective_statistics'] = {
                    'energy_consumption': {
                        'min': min(energy_values),
                        'max': max(energy_values),
                        'mean': np.mean(energy_values),
                        'std': np.std(energy_values)
                    },
                    'thermal_performance': {
                        'min': min(thermal_values),
                        'max': max(thermal_values),
                        'mean': np.mean(thermal_values),
                        'std': np.std(thermal_values)
                    },
                    'renovation_cost': {
                        'min': min(cost_values),
                        'max': max(cost_values),
                        'mean': np.mean(cost_values),
                        'std': np.std(cost_values)
                    }
                }
            
            # 收敛统计
            if convergence_history:
                hypervolumes = [m.hypervolume for m in convergence_history]
                convergence_values = [m.convergence_metric for m in convergence_history]
                
                statistics['convergence_statistics'] = {
                    'final_hypervolume': hypervolumes[-1] if hypervolumes else 0.0,
                    'hypervolume_improvement': hypervolumes[-1] - hypervolumes[0] if len(hypervolumes) > 1 else 0.0,
                    'final_convergence_metric': convergence_values[-1] if convergence_values else 1.0,
                    'generations_to_convergence': len(convergence_history)
                }
            
            return statistics
            
        except Exception as e:
            self.logger.error(f"计算优化统计失败: {str(e)}")
            return {}
    
    def _get_optimization_parameters(self) -> Dict[str, Any]:
        """获取优化参数"""
        return {
            'algorithm': 'NSGA-III',
            'population_size': self.genetic_operators.params.population_size,
            'crossover_rate': self.genetic_operators.params.crossover_rate,
            'mutation_rate': self.genetic_operators.params.mutation_rate,
            'mutation_strength': self.genetic_operators.params.mutation_strength,
            'tournament_size': self.genetic_operators.params.tournament_size,
            'max_generations': self.convergence_monitor.termination_criteria.max_generations,
            'max_time_seconds': self.convergence_monitor.termination_criteria.max_time_seconds,
            'convergence_threshold': self.convergence_monitor.termination_criteria.convergence_threshold,
            'stagnation_generations': self.convergence_monitor.termination_criteria.stagnation_generations
        }
    
    def _save_optimization_results(self, optimization_results: OptimizationResults) -> None:
        """保存优化结果"""
        try:
            if not self.output_directory:
                return
            
            # 保存主要结果
            results_dict = self._results_to_dict(optimization_results)
            DataUtils.save_json(results_dict, self.output_directory / "optimization_results.json")
            
            # 保存帕累托解
            pareto_data = [self._result_to_dict(r) for r in optimization_results.pareto_solutions]
            DataUtils.save_json(pareto_data, self.output_directory / "pareto_solutions.json")
            
            # 保存收敛历史
            convergence_data = [m.to_dict() for m in optimization_results.convergence_history]
            DataUtils.save_json(convergence_data, self.output_directory / "convergence_history.json")
            
            self.logger.info(f"优化结果已保存到: {self.output_directory}")
            
        except Exception as e:
            self.logger.error(f"保存优化结果失败: {str(e)}")
    
    def _generate_optimization_report(self, optimization_results: OptimizationResults) -> Path:
        """生成优化报告"""
        try:
            report_path = self.output_directory / "optimization_report.md"
            
            report_content = f"""# 立面优化分析报告

## 基本信息
- **会话ID**: {optimization_results.session_id}
- **朝向**: {self.climate_data.orientation.value}
- **终止原因**: {optimization_results.termination_reason}
- **总代数**: {optimization_results.total_generations}
- **总评估次数**: {optimization_results.total_evaluations}
- **计算时间**: {optimization_results.computation_time:.1f} 秒

## 优化结果统计
- **帕累托解数量**: {len(optimization_results.pareto_solutions)}
- **最终可行解数量**: {optimization_results.optimization_statistics.get('final_feasible_count', 0)}
- **可行性比例**: {optimization_results.optimization_statistics.get('feasibility_ratio', 0):.2%}

## 最佳目标值
"""
            
            # 添加最佳解信息
            if optimization_results.best_energy_solution:
                report_content += f"""
### 最佳能耗解
- **能耗**: {optimization_results.best_energy_solution.energy_consumption:.2f} kWh/m²·year
- **热工性能**: {optimization_results.best_energy_solution.thermal_performance:.4f}
- **改造成本**: {optimization_results.best_energy_solution.renovation_cost:.0f} 元
"""
            
            if optimization_results.best_thermal_solution:
                report_content += f"""
### 最佳热工性能解
- **能耗**: {optimization_results.best_thermal_solution.energy_consumption:.2f} kWh/m²·year
- **热工性能**: {optimization_results.best_thermal_solution.thermal_performance:.4f}
- **改造成本**: {optimization_results.best_thermal_solution.renovation_cost:.0f} 元
"""
            
            if optimization_results.best_cost_solution:
                report_content += f"""
### 最佳成本解
- **能耗**: {optimization_results.best_cost_solution.energy_consumption:.2f} kWh/m²·year
- **热工性能**: {optimization_results.best_cost_solution.thermal_performance:.4f}
- **改造成本**: {optimization_results.best_cost_solution.renovation_cost:.0f} 元
"""
            
            # 添加收敛信息
            if optimization_results.convergence_history:
                final_metrics = optimization_results.convergence_history[-1]
                report_content += f"""
## 收敛分析
- **最终超体积**: {final_metrics.hypervolume:.6f}
- **最终多样性**: {final_metrics.diversity_metric:.4f}
- **最终收敛度**: {final_metrics.convergence_metric:.6f}
- **停滞代数**: {final_metrics.stagnation_count}
"""
            
            report_content += f"""
## 输出文件
- `optimization_results.json`: 完整优化结果
- `pareto_solutions.json`: 帕累托最优解集
- `convergence_history.json`: 收敛历史数据
- `intermediate_results/`: 中间结果目录

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            return report_path
            
        except Exception as e:
            self.logger.error(f"生成优化报告失败: {str(e)}")
            return self.output_directory / "report_generation_failed.txt"
    
    def _result_to_dict(self, result: ObjectiveResults) -> Dict[str, Any]:
        """将ObjectiveResults转换为字典"""
        return {
            'individual_id': result.individual_id,
            'energy_consumption': result.energy_consumption,
            'thermal_performance': result.thermal_performance,
            'renovation_cost': result.renovation_cost,
            'constraint_violations': result.constraint_violations,
            'is_feasible': result.is_feasible,
            'auxiliary_metrics': result.auxiliary_metrics,
            'evaluation_timestamp': result.evaluation_timestamp
        }
    
    def _results_to_dict(self, optimization_results: OptimizationResults) -> Dict[str, Any]:
        """将OptimizationResults转换为字典"""
        return {
            'session_id': optimization_results.session_id,
            'termination_reason': optimization_results.termination_reason,
            'total_generations': optimization_results.total_generations,
            'total_evaluations': optimization_results.total_evaluations,
            'computation_time': optimization_results.computation_time,
            'pareto_solutions_count': len(optimization_results.pareto_solutions),
            'representative_solutions_count': len(optimization_results.representative_solutions),
            'optimization_statistics': optimization_results.optimization_statistics,
            'optimization_metadata': optimization_results.optimization_metadata
        }


def create_nsga3_facade_optimizer(facade_elements: FacadeElements,
                                climate_data: OrientedClimateData,
                                renovation_mode: RenovationMode = RenovationMode.NEW_CONSTRUCTION) -> NSGA3FacadeOptimizer:
    """
    创建NSGA-III立面优化器实例 - 禁止增减窗户策略
    
    Args:
        facade_elements: 立面元素数据
        climate_data: 朝向调整后的气候数据
        renovation_mode: 改造模式
        
    Returns:
        配置好的立面优化器
    """
    return NSGA3FacadeOptimizer(facade_elements, climate_data, renovation_mode)


def create_nsga3_facade_optimizer_with_flexible_windows(facade_elements: FacadeElements,
                                                      climate_data: OrientedClimateData,
                                                      renovation_mode: RenovationMode = RenovationMode.NEW_CONSTRUCTION) -> NSGA3FacadeOptimizer:
    """
    创建NSGA-III立面优化器实例 - 允许增减窗户策略
    
    Args:
        facade_elements: 立面元素数据
        climate_data: 朝向调整后的气候数据
        renovation_mode: 改造模式
        
    Returns:
        配置好的立面优化器（允许窗户数量变化）
    """
    optimizer = NSGA3FacadeOptimizer(facade_elements, climate_data, renovation_mode)
    # 重新初始化编码器以允许窗户数量变化
    optimizer.individual_encoder = create_individual_encoder(facade_elements, allow_window_modification=True)
    return optimizer