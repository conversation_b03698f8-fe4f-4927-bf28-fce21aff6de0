# 建筑立面优化系统 / Building Facade Optimization System

基于YOLO识别立面块状图的建筑立面多目标优化系统

Multi-Objective Building Facade Optimization System Based on YOLO Facade Block Recognition

## 项目概述 / Project Overview

本系统是一个完整的建筑立面优化解决方案，通过YOLO目标检测技术识别建筑立面元素，结合EPW气候数据和NSGA-III多目标遗传算法，实现建筑立面的智能化优化设计。系统支持新建建筑和既有建筑改造两种模式，并提供11类专业可视化图表和交互式用户界面。

This system is a comprehensive building facade optimization solution that uses YOLO object detection to identify facade elements, combines EPW climate data with NSGA-III multi-objective genetic algorithm to achieve intelligent optimization design of building facades. The system supports both new construction and building renovation modes, providing 11 types of professional visualization charts and an interactive user interface.

## 主要功能 / Key Features

### 1. 立面数据提取 / Facade Data Extraction
- **YOLO目标检测**: 支持YOLOv8等先进模型识别立面元素
- **元素类型识别**: 墙体、窗户、门、遮阳设施、框架等
- **几何参数计算**: 窗墙比、立面面积、尺寸参数等
- **数据质量验证**: 多维度数据质量评估和验证

### 2. 气候数据处理 / Climate Data Processing  
- **EPW文件解析**: 标准气象数据文件解析和处理
- **朝向调整**: 基于建筑朝向的太阳辐射和风环境调整
- **数据预处理**: 缺失值处理、异常检测、数据平滑

### 3. 多目标优化 / Multi-Objective Optimization
- **NSGA-III算法**: 先进的多目标遗传算法
- **三大目标**: 能耗最小化、热工性能优化、改造成本控制
- **约束处理**: 几何、规范、结构、功能、美学约束
- **收敛监控**: 超体积、多样性、收敛性指标监控

### 4. 性能评估 / Performance Evaluation
- **能耗分析**: 供暖、制冷、照明、通风能耗计算
- **热工性能**: PMV/PPD热舒适性评估
- **成本分析**: 材料、人工、设备成本及全生命周期成本
- **综合评价**: 多准则决策和解决方案排序

### 5. 解决方案选择 / Solution Selection
- **四维度优选**: 能耗最优、热工最优、成本最优、综合最优
- **TOPSIS方法**: 逼近理想解排序技术
- **灰色关联分析**: 多指标关联度评价
- **推荐系统**: 智能解决方案推荐

### 6. 专业可视化 / Professional Visualization
- **11类图表**: 涵盖优化结果、性能分析、对比分析
- **双语支持**: 中英文双语显示
- **多种风格**: 专业、现代、学术、技术四种图表风格
- **高质量输出**: 高分辨率图表和报告生成

## 系统架构 / System Architecture

```
建筑立面优化系统/
├── src/                          # 源代码目录
│   ├── core/                     # 核心模块
│   │   ├── config.py            # 配置管理
│   │   ├── data_structures.py   # 数据结构定义
│   │   ├── exceptions.py        # 异常处理
│   │   ├── logging_config.py    # 日志配置
│   │   └── utils.py             # 工具函数
│   ├── facade_extraction/       # 立面数据提取
│   │   ├── yolo_processor.py    # YOLO处理器
│   │   ├── data_extractor.py    # 数据提取器
│   │   ├── geometry_calculator.py # 几何计算
│   │   └── validator.py         # 数据验证
│   ├── climate_processing/      # 气候数据处理
│   │   ├── epw_parser.py        # EPW文件解析
│   │   ├── orientation_adjuster.py # 朝向调整
│   │   └── climate_processor.py # 气候处理器
│   ├── optimization/            # 优化算法
│   │   ├── nsga3_optimizer.py   # NSGA-III优化器
│   │   ├── individual_encoder.py # 个体编码
│   │   ├── objective_functions.py # 目标函数
│   │   ├── constraint_handler.py # 约束处理
│   │   ├── genetic_operators.py # 遗传算子
│   │   └── convergence_monitor.py # 收敛监控
│   ├── performance_evaluation/ # 性能评估
│   │   ├── energy_analyzer.py   # 能耗分析
│   │   ├── thermal_analyzer.py  # 热工分析
│   │   ├── cost_analyzer.py     # 成本分析
│   │   └── performance_integrator.py # 性能集成
│   ├── solution_selection/     # 解决方案选择
│   │   ├── solution_selector.py # 解决方案选择器
│   │   ├── multi_criteria_ranker.py # 多准则排序
│   │   └── solution_evaluator.py # 解决方案评估
│   └── visualization/          # 可视化系统
│       ├── chart_config.py     # 图表配置
│       ├── base_visualizer.py  # 基础可视化
│       ├── optimization_charts.py # 优化结果图表
│       ├── performance_charts.py # 性能分析图表
│       ├── comparison_charts.py # 对比分析图表
│       ├── report_generator.py # 报告生成
│       └── visualization_manager.py # 可视化管理
├── main.py                     # 主程序入口
├── requirements.txt            # 依赖包列表
└── README.md                   # 项目说明
```

## 安装和运行 / Installation and Usage

### 环境要求 / Requirements
- Python 3.8+
- 8GB+ RAM (推荐16GB)
- GPU支持 (可选，用于YOLO加速)

### 安装步骤 / Installation Steps

1. **克隆项目 / Clone Repository**
```bash
git clone <repository-url>
cd building-facade-optimization
```

2. **创建虚拟环境 / Create Virtual Environment**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **安装依赖 / Install Dependencies**
```bash
pip install -r requirements.txt
```

4. **运行系统 / Run System**
```bash
python main.py
```

### 快速开始 / Quick Start

1. **启动系统**: 运行 `python main.py`
2. **立面数据提取**: 
   - 选择功能1，上传建筑立面图像
   - 系统将自动识别立面元素
3. **气候数据处理**:
   - 选择功能2，导入EPW气候数据文件
   - 设置建筑朝向进行数据调整
4. **执行优化**:
   - 选择功能3，配置优化参数
   - 启动NSGA-III多目标优化
5. **查看结果**:
   - 选择功能4，生成专业可视化报告
   - 获得11类专业图表和优化建议

## 可视化图表 / Visualization Charts

系统提供11类专业可视化图表:

### 优化结果图表 (3类)
1. **3D帕累托前沿图**: 三维目标空间中的帕累托最优解分布
2. **2D帕累托前沿图**: 多个二维视角的帕累托前沿展示
3. **优化收敛过程图**: 目标函数收敛、超体积指标、种群多样性变化

### 性能分析图表 (4类)  
4. **能耗性能分析图**: 供暖、制冷、照明、通风能耗详细分析
5. **热工性能分析图**: PMV/PPD热舒适性分析和分布
6. **成本分析图**: 材料、人工、设备成本瀑布图分析
7. **综合性能雷达图**: 多维度性能指标综合展示

### 对比分析图表 (4类)
8. **多方案对比图**: 不同优化方案的多维度性能对比
9. **基准对比分析图**: 与标准/基准方案的详细对比
10. **敏感性分析图**: 参数变化对优化结果的影响分析  
11. **权衡分析图**: 不同目标之间的权衡关系可视化

## 技术特点 / Technical Features

### 先进算法
- **YOLO v8**: 最新的目标检测算法
- **NSGA-III**: 第三代非支配排序遗传算法
- **自适应参考点**: 动态调整参考点分布
- **约束处理**: 多层次约束违反处理机制

### 高质量可视化
- **专业配色**: 基于色彩学的专业配色方案
- **双语支持**: 完整的中英文双语界面
- **多种输出**: PNG, SVG, PDF等多种格式支持
- **交互功能**: 支持图表交互和动画效果

### 系统优化
- **模块化设计**: 松耦合的模块化架构
- **异常处理**: 完善的异常处理和错误恢复
- **日志系统**: 分级日志记录和监控
- **配置管理**: 灵活的配置文件管理系统

## 配置说明 / Configuration

系统配置文件位于 `src/core/config.py`，主要配置项包括:

- **YOLO模型配置**: 模型路径、检测阈值、NMS参数
- **优化算法参数**: 种群大小、迭代次数、变异概率
- **可视化设置**: 图表风格、颜色主题、输出格式
- **性能参数**: 并行计算、缓存策略、内存管理

## 输入数据格式 / Input Data Format

### 图像数据
- **格式**: JPG, PNG, BMP
- **分辨率**: 建议1080p以上
- **要求**: 清晰的建筑立面图像，包含完整的立面元素

### 气候数据
- **格式**: EPW (EnergyPlus Weather) 文件
- **来源**: EnergyPlus官网、气象数据库
- **内容**: 温度、湿度、太阳辐射、风速等逐时数据

## 输出结果 / Output Results

### 图表文件
- **位置**: outputs/session_YYYYMMDD_HHMMSS/charts/
- **格式**: PNG (默认), SVG, PDF
- **命名**: 按图表类型自动命名

### 报告文件
- **位置**: outputs/session_YYYYMMDD_HHMMSS/reports/
- **格式**: HTML, Markdown
- **内容**: 完整的分析报告和优化建议

### 数据文件
- **优化结果**: JSON格式的帕累托解集
- **性能数据**: 详细的性能分析数据
- **配置信息**: 优化参数和系统配置记录

## 开发指南 / Development Guide

### 添加新的目标函数
1. 在 `optimization/objective_functions.py` 中添加新函数
2. 更新 `data_structures.py` 中的相关数据结构
3. 修改可视化模块以支持新目标的显示

### 扩展可视化图表
1. 在相应的图表模块中添加新图表类型
2. 更新 `visualization_manager.py` 的图表类型列表
3. 添加相应的配置和样式设置

### 集成新的优化算法
1. 继承 `BaseOptimizer` 类创建新的优化器
2. 实现必需的接口方法
3. 更新主程序的算法选择逻辑

## 常见问题 / FAQ

**Q: 系统对硬件有什么要求？**
A: 建议8GB以上内存，GPU可选但能显著加速YOLO处理。

**Q: 支持哪些图像格式？**
A: 支持JPG、PNG、BMP等常见格式，建议使用高分辨率图像。

**Q: 可以处理多个建筑吗？**
A: 当前版本支持单个建筑的立面优化，多建筑功能在开发中。

**Q: 如何获得EPW气候数据？**
A: 可从EnergyPlus官网或相关气象数据库下载。

## 许可证 / License

本项目采用MIT许可证，详见LICENSE文件。

## 贡献 / Contributing

欢迎提交Issue和Pull Request来改进系统。

## 联系方式 / Contact

如有问题或建议，请通过以下方式联系:
- Email: [<EMAIL>]
- GitHub Issues: [repository-issues-url]

## 更新日志 / Changelog

### v1.0.0 (2024-XX-XX)
- 初始版本发布
- 完整的YOLO立面识别功能
- NSGA-III多目标优化算法
- 11类专业可视化图表
- 交互式用户界面

---

*建筑立面优化系统 - 让建筑设计更智能、更高效*

*Building Facade Optimization System - Making architectural design smarter and more efficient*