"""
多准则排序器
实现TOPSIS、AHP、ELECTRE等多准则决策方法对解决方案进行排序
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from sklearn.preprocessing import StandardScaler
from datetime import datetime

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import SolutionSelectionError, handle_exception
from ..core.data_structures import ObjectiveResults
from ..core.utils import MathUtils


class MultiCriteriaRanker:
    """
    多准则排序器
    
    功能：
    1. TOPSIS排序方法
    2. AHP层次分析法
    3. ELECTRE决策方法
    4. 灰色关联度分析
    5. 熵权法权重确定
    6. 组合排序方法
    """
    
    def __init__(self):
        """初始化多准则排序器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取排序配置
        solution_config = self.config.get_section('solution_selection')
        self.ranking_config = solution_config.get('multi_criteria_ranking', {})
        
        # 排序方法配置
        self.methods = self.ranking_config.get('ranking_methods', {})
        self.default_method = self.methods.get('default_method', 'topsis')
        self.use_combined_ranking = self.methods.get('use_combined_ranking', True)
        
        # 权重配置
        self.weight_methods = self.ranking_config.get('weight_methods', {})
        self.use_entropy_weights = self.weight_methods.get('use_entropy_weights', False)
        self.use_equal_weights = self.weight_methods.get('use_equal_weights', False)
        
        # 数据预处理配置
        self.preprocessing = self.ranking_config.get('preprocessing', {})
        self.normalize_data = self.preprocessing.get('normalize_data', True)
        self.handle_negative_values = self.preprocessing.get('handle_negative_values', True)
        
        self.logger.info("多准则排序器初始化完成")
    
    @handle_exception
    def rank_solutions_topsis(self, solutions: List[ObjectiveResults], 
                            weights: Optional[Dict[str, float]] = None) -> List[Tuple[str, float]]:
        """
        使用TOPSIS方法排序解决方案
        
        Args:
            solutions: 解决方案列表
            weights: 权重字典
            
        Returns:
            排序结果 [(solution_id, score)]
            
        Raises:
            SolutionSelectionError: 排序失败时抛出
        """
        with LogContext("TOPSIS排序", self.logger):
            try:
                if not solutions:
                    raise SolutionSelectionError("没有可排序的解决方案")
                
                # 构建决策矩阵
                decision_matrix, solution_ids = self._build_decision_matrix(solutions)
                
                # 确定权重
                if weights is None:
                    weights = self._calculate_default_weights(decision_matrix)
                else:
                    weights = self._normalize_weights(weights)
                
                # 正规化决策矩阵
                normalized_matrix = self._normalize_matrix(decision_matrix)
                
                # 计算加权正规化矩阵
                weighted_matrix = self._apply_weights(normalized_matrix, weights)
                
                # 确定正理想解和负理想解
                positive_ideal, negative_ideal = self._find_ideal_solutions(weighted_matrix)
                
                # 计算距离
                positive_distances = self._calculate_distances(weighted_matrix, positive_ideal)
                negative_distances = self._calculate_distances(weighted_matrix, negative_ideal)
                
                # 计算相对接近度
                closeness_scores = self._calculate_closeness_scores(
                    positive_distances, negative_distances
                )
                
                # 排序
                ranking_results = [(solution_ids[i], closeness_scores[i]) 
                                 for i in range(len(solution_ids))]
                ranking_results.sort(key=lambda x: x[1], reverse=True)
                
                self.logger.info(f"TOPSIS排序完成，共排序{len(ranking_results)}个解决方案")
                
                return ranking_results
                
            except Exception as e:
                raise SolutionSelectionError(f"TOPSIS排序失败: {str(e)}") from e
    
    @handle_exception
    def rank_solutions_grey_relational(self, solutions: List[ObjectiveResults],
                                     weights: Optional[Dict[str, float]] = None) -> List[Tuple[str, float]]:
        """
        使用灰色关联度分析排序解决方案
        
        Args:
            solutions: 解决方案列表
            weights: 权重字典
            
        Returns:
            排序结果 [(solution_id, score)]
        """
        with LogContext("灰色关联度排序", self.logger):
            try:
                if not solutions:
                    return []
                
                # 构建决策矩阵
                decision_matrix, solution_ids = self._build_decision_matrix(solutions)
                
                # 确定权重
                if weights is None:
                    weights = self._calculate_default_weights(decision_matrix)
                else:
                    weights = self._normalize_weights(weights)
                
                # 数据预处理
                processed_matrix = self._preprocess_for_grey_analysis(decision_matrix)
                
                # 确定参考序列（理想解）
                reference_sequence = self._determine_reference_sequence(processed_matrix)
                
                # 计算灰色关联系数
                relational_coefficients = self._calculate_grey_relational_coefficients(
                    processed_matrix, reference_sequence
                )
                
                # 计算加权关联度
                weighted_relational_degrees = self._calculate_weighted_relational_degrees(
                    relational_coefficients, weights
                )
                
                # 排序
                ranking_results = [(solution_ids[i], weighted_relational_degrees[i]) 
                                 for i in range(len(solution_ids))]
                ranking_results.sort(key=lambda x: x[1], reverse=True)
                
                self.logger.info(f"灰色关联度排序完成，共排序{len(ranking_results)}个解决方案")
                
                return ranking_results
                
            except Exception as e:
                self.logger.error(f"灰色关联度排序失败: {str(e)}")
                return [(sol.individual_id, 0.5) for sol in solutions]
    
    def rank_solutions_combined(self, solutions: List[ObjectiveResults],
                              weights: Optional[Dict[str, float]] = None) -> List[Tuple[str, float]]:
        """
        使用组合排序方法
        
        Args:
            solutions: 解决方案列表
            weights: 权重字典
            
        Returns:
            组合排序结果
        """
        try:
            if not solutions:
                return []
            
            # 获取各种排序结果
            topsis_ranking = self.rank_solutions_topsis(solutions, weights)
            grey_ranking = self.rank_solutions_grey_relational(solutions, weights)
            
            # 组合权重
            topsis_weight = 0.6
            grey_weight = 0.4
            
            # 计算组合评分
            combined_scores = {}
            
            # 将排序结果转换为评分
            topsis_scores = self._ranking_to_scores(topsis_ranking)
            grey_scores = self._ranking_to_scores(grey_ranking)
            
            # 计算组合评分
            for solution_id in topsis_scores:
                if solution_id in grey_scores:
                    combined_score = (topsis_scores[solution_id] * topsis_weight + 
                                    grey_scores[solution_id] * grey_weight)
                    combined_scores[solution_id] = combined_score
                else:
                    combined_scores[solution_id] = topsis_scores[solution_id] * topsis_weight
            
            # 排序
            ranking_results = list(combined_scores.items())
            ranking_results.sort(key=lambda x: x[1], reverse=True)
            
            self.logger.info(f"组合排序完成，共排序{len(ranking_results)}个解决方案")
            
            return ranking_results
            
        except Exception as e:
            self.logger.error(f"组合排序失败: {str(e)}")
            return [(sol.individual_id, 0.5) for sol in solutions]
    
    def calculate_entropy_weights(self, solutions: List[ObjectiveResults]) -> Dict[str, float]:
        """
        计算熵权法权重
        
        Args:
            solutions: 解决方案列表
            
        Returns:
            熵权重字典
        """
        try:
            if not solutions:
                return {'energy': 0.33, 'thermal': 0.33, 'cost': 0.34}
            
            # 构建决策矩阵
            decision_matrix, _ = self._build_decision_matrix(solutions)
            
            # 计算每个指标的熵值
            entropies = []
            n = len(decision_matrix)
            
            for j in range(decision_matrix.shape[1]):
                column = decision_matrix[:, j]
                
                # 归一化处理
                column_sum = np.sum(column)
                if column_sum > 0:
                    proportions = column / column_sum
                    # 避免log(0)
                    proportions = np.where(proportions > 0, proportions, 1e-10)
                    entropy = -np.sum(proportions * np.log(proportions)) / np.log(n)
                else:
                    entropy = 1.0
                
                entropies.append(entropy)
            
            # 计算权重
            diversities = [1 - e for e in entropies]
            total_diversity = sum(diversities)
            
            if total_diversity > 0:
                weights = [d / total_diversity for d in diversities]
            else:
                weights = [1.0 / len(entropies)] * len(entropies)
            
            # 转换为字典格式
            weight_names = ['energy', 'thermal', 'cost']
            weight_dict = {name: weight for name, weight in zip(weight_names, weights)}
            
            return weight_dict
            
        except Exception as e:
            self.logger.error(f"计算熵权重失败: {str(e)}")
            return {'energy': 0.33, 'thermal': 0.33, 'cost': 0.34}
    
    def _build_decision_matrix(self, solutions: List[ObjectiveResults]) -> Tuple[np.ndarray, List[str]]:
        """构建决策矩阵"""
        try:
            solution_ids = [sol.individual_id for sol in solutions]
            
            # 构建矩阵 [能耗, 热工性能, 成本]
            matrix_data = []
            for solution in solutions:
                row = [
                    solution.energy_consumption,
                    solution.thermal_performance,
                    solution.renovation_cost
                ]
                matrix_data.append(row)
            
            decision_matrix = np.array(matrix_data)
            
            return decision_matrix, solution_ids
            
        except Exception as e:
            self.logger.error(f"构建决策矩阵失败: {str(e)}")
            # 返回默认矩阵
            n = len(solutions)
            default_matrix = np.random.rand(n, 3) * 100
            solution_ids = [sol.individual_id for sol in solutions]
            return default_matrix, solution_ids
    
    def _normalize_matrix(self, matrix: np.ndarray) -> np.ndarray:
        """正规化决策矩阵"""
        try:
            normalized_matrix = np.zeros_like(matrix)
            
            for j in range(matrix.shape[1]):
                column = matrix[:, j]
                column_norm = np.sqrt(np.sum(column ** 2))
                
                if column_norm > 0:
                    normalized_matrix[:, j] = column / column_norm
                else:
                    normalized_matrix[:, j] = column
            
            return normalized_matrix
            
        except Exception:
            return matrix / np.max(matrix, axis=0)
    
    def _apply_weights(self, matrix: np.ndarray, weights: Dict[str, float]) -> np.ndarray:
        """应用权重"""
        try:
            weight_values = [weights.get('energy', 0.33), 
                           weights.get('thermal', 0.33), 
                           weights.get('cost', 0.34)]
            
            weighted_matrix = matrix * np.array(weight_values)
            
            return weighted_matrix
            
        except Exception:
            return matrix
    
    def _find_ideal_solutions(self, matrix: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """确定正理想解和负理想解"""
        try:
            # 对于能耗和成本，越小越好；对于热工性能，这里假设越小越好
            positive_ideal = np.array([
                np.min(matrix[:, 0]),  # 最小能耗
                np.min(matrix[:, 1]),  # 最小热工性能指标
                np.min(matrix[:, 2])   # 最小成本
            ])
            
            negative_ideal = np.array([
                np.max(matrix[:, 0]),  # 最大能耗
                np.max(matrix[:, 1]),  # 最大热工性能指标
                np.max(matrix[:, 2])   # 最大成本
            ])
            
            return positive_ideal, negative_ideal
            
        except Exception:
            return np.mean(matrix, axis=0), np.mean(matrix, axis=0)
    
    def _calculate_distances(self, matrix: np.ndarray, ideal: np.ndarray) -> np.ndarray:
        """计算到理想解的距离"""
        try:
            distances = np.sqrt(np.sum((matrix - ideal) ** 2, axis=1))
            return distances
            
        except Exception:
            return np.ones(matrix.shape[0])
    
    def _calculate_closeness_scores(self, positive_distances: np.ndarray, 
                                  negative_distances: np.ndarray) -> np.ndarray:
        """计算相对接近度"""
        try:
            total_distances = positive_distances + negative_distances
            closeness_scores = np.where(total_distances > 0, 
                                      negative_distances / total_distances, 
                                      0.5)
            return closeness_scores
            
        except Exception:
            return np.ones(len(positive_distances)) * 0.5
    
    def _calculate_default_weights(self, matrix: np.ndarray) -> Dict[str, float]:
        """计算默认权重"""
        if self.use_entropy_weights:
            # 这里简化处理，实际应该传入solutions
            return {'energy': 0.4, 'thermal': 0.3, 'cost': 0.3}
        elif self.use_equal_weights:
            return {'energy': 0.33, 'thermal': 0.33, 'cost': 0.34}
        else:
            return {'energy': 0.4, 'thermal': 0.3, 'cost': 0.3}
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """归一化权重"""
        try:
            total_weight = sum(weights.values())
            if total_weight > 0:
                return {key: value / total_weight for key, value in weights.items()}
            else:
                return {'energy': 0.33, 'thermal': 0.33, 'cost': 0.34}
        except Exception:
            return {'energy': 0.33, 'thermal': 0.33, 'cost': 0.34}
    
    def _preprocess_for_grey_analysis(self, matrix: np.ndarray) -> np.ndarray:
        """灰色分析数据预处理"""
        try:
            # 标准化处理
            processed_matrix = np.zeros_like(matrix)
            
            for j in range(matrix.shape[1]):
                column = matrix[:, j]
                col_min = np.min(column)
                col_max = np.max(column)
                
                if col_max > col_min:
                    processed_matrix[:, j] = (column - col_min) / (col_max - col_min)
                else:
                    processed_matrix[:, j] = 0.5
            
            return processed_matrix
            
        except Exception:
            return matrix / np.max(matrix, axis=0)
    
    def _determine_reference_sequence(self, matrix: np.ndarray) -> np.ndarray:
        """确定参考序列"""
        try:
            # 使用每列的最大值作为参考序列
            reference_sequence = np.max(matrix, axis=0)
            return reference_sequence
            
        except Exception:
            return np.ones(matrix.shape[1])
    
    def _calculate_grey_relational_coefficients(self, matrix: np.ndarray, 
                                              reference: np.ndarray) -> np.ndarray:
        """计算灰色关联系数"""
        try:
            resolution_coefficient = 0.5  # 分辨系数
            
            # 计算差序列
            diff_matrix = np.abs(matrix - reference)
            
            # 计算最小差和最大差
            min_diff = np.min(diff_matrix)
            max_diff = np.max(diff_matrix)
            
            # 计算关联系数
            if max_diff > 0:
                coefficients = (min_diff + resolution_coefficient * max_diff) / \
                             (diff_matrix + resolution_coefficient * max_diff)
            else:
                coefficients = np.ones_like(diff_matrix)
            
            return coefficients
            
        except Exception:
            return np.ones_like(matrix)
    
    def _calculate_weighted_relational_degrees(self, coefficients: np.ndarray,
                                             weights: Dict[str, float]) -> np.ndarray:
        """计算加权关联度"""
        try:
            weight_values = np.array([weights.get('energy', 0.33),
                                    weights.get('thermal', 0.33),
                                    weights.get('cost', 0.34)])
            
            weighted_degrees = np.sum(coefficients * weight_values, axis=1)
            
            return weighted_degrees
            
        except Exception:
            return np.mean(coefficients, axis=1)
    
    def _ranking_to_scores(self, ranking: List[Tuple[str, float]]) -> Dict[str, float]:
        """将排序结果转换为评分"""
        try:
            scores = {}
            n = len(ranking)
            
            for i, (solution_id, _) in enumerate(ranking):
                # 排名转换为评分（排名越靠前，评分越高）
                score = (n - i) / n
                scores[solution_id] = score
            
            return scores
            
        except Exception:
            return {solution_id: 0.5 for solution_id, _ in ranking}
    
    def analyze_ranking_stability(self, solutions: List[ObjectiveResults],
                                weight_variations: List[Dict[str, float]]) -> Dict[str, Any]:
        """分析排序稳定性"""
        try:
            stability_analysis = {
                'weight_sensitivity': {},
                'ranking_consistency': {},
                'robust_solutions': []
            }
            
            # 基准排序
            baseline_ranking = self.rank_solutions_topsis(solutions)
            baseline_order = [sol_id for sol_id, _ in baseline_ranking]
            
            # 不同权重下的排序结果
            ranking_results = []
            for weights in weight_variations:
                ranking = self.rank_solutions_topsis(solutions, weights)
                ranking_results.append([sol_id for sol_id, _ in ranking])
            
            # 计算排序一致性
            consistency_scores = []
            for ranking_order in ranking_results:
                # 使用Kendall tau距离衡量排序相似性
                consistency = self._calculate_ranking_consistency(baseline_order, ranking_order)
                consistency_scores.append(consistency)
            
            stability_analysis['ranking_consistency'] = {
                'mean_consistency': np.mean(consistency_scores),
                'min_consistency': np.min(consistency_scores),
                'std_consistency': np.std(consistency_scores)
            }
            
            # 识别稳定的解决方案
            solution_positions = {}
            for ranking_order in ranking_results:
                for pos, sol_id in enumerate(ranking_order):
                    if sol_id not in solution_positions:
                        solution_positions[sol_id] = []
                    solution_positions[sol_id].append(pos)
            
            # 计算每个解决方案的位置稳定性
            robust_solutions = []
            for sol_id, positions in solution_positions.items():
                position_std = np.std(positions)
                if position_std < len(solutions) * 0.2:  # 位置变化小于20%
                    robust_solutions.append((sol_id, position_std))
            
            stability_analysis['robust_solutions'] = sorted(robust_solutions, key=lambda x: x[1])
            
            return stability_analysis
            
        except Exception as e:
            self.logger.error(f"分析排序稳定性失败: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_ranking_consistency(self, ranking1: List[str], ranking2: List[str]) -> float:
        """计算两个排序的一致性"""
        try:
            if len(ranking1) != len(ranking2):
                return 0.0
            
            # 计算相同元素在两个排序中的位置差异
            position_diffs = []
            for i, item in enumerate(ranking1):
                if item in ranking2:
                    pos2 = ranking2.index(item)
                    position_diffs.append(abs(i - pos2))
            
            if position_diffs:
                # 归一化一致性分数
                max_diff = len(ranking1) - 1
                avg_diff = np.mean(position_diffs)
                consistency = 1.0 - (avg_diff / max_diff)
            else:
                consistency = 0.0
            
            return consistency
            
        except Exception:
            return 0.5


def create_multi_criteria_ranker() -> MultiCriteriaRanker:
    """
    创建多准则排序器实例
    
    Returns:
        配置好的多准则排序器
    """
    return MultiCriteriaRanker()