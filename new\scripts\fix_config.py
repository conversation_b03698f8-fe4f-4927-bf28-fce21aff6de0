#!/usr/bin/env python3
"""
快速修复配置文件
为现有系统添加必要的配置参数
"""

import yaml
import json
from pathlib import Path


def update_config_files():
    """更新配置文件"""
    project_root = Path(__file__).parent.parent
    
    # 1. 更新主配置文件
    config_file = project_root / "config" / "config.yaml"
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f) or {}
        except:
            config = {}
    else:
        config = {}
    
    # 添加图像处理配置
    if 'image_processing' not in config:
        config['image_processing'] = {}
    
    # 设置像素-米转换比例（临时默认值）
    if 'pixel_to_meter_ratio' not in config['image_processing']:
        config['image_processing']['pixel_to_meter_ratio'] = 0.01
    
    # 添加颜色映射配置
    if 'color_mapping' not in config['image_processing']:
        config['image_processing']['color_mapping'] = {
            'walls': {'min': [180, 180, 180], 'max': [255, 255, 255]},
            'windows': {'min': [0, 100, 200], 'max': [50, 150, 255]},
            'doors': {'min': [240, 240, 0], 'max': [255, 255, 20]},
            'shading': {'min': [0, 200, 0], 'max': [20, 255, 20]},
            'frames': {'min': [0, 0, 0], 'max': [30, 30, 30]}
        }
    
    # 保存配置文件
    config_file.parent.mkdir(parents=True, exist_ok=True)
    with open(config_file, 'w', encoding='utf-8') as f:
        yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ 已更新配置文件: {config_file}")
    
    # 2. 创建JSON格式的配置文件（如果需要）
    json_config_file = project_root / "config" / "config.json"
    with open(json_config_file, 'w', encoding='utf-8') as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"✅ 已创建JSON配置文件: {json_config_file}")
    
    return True


def main():
    """主函数"""
    print("开始修复配置文件...")
    
    try:
        success = update_config_files()
        if success:
            print("✅ 配置文件修复完成")
        else:
            print("❌ 配置文件修复失败")
    except Exception as e:
        print(f"❌ 修复过程出错: {e}")


if __name__ == "__main__":
    main()