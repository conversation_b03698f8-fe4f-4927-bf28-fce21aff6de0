"""
遗传算法操作器
实现NSGA-III算法的种群管理、选择、交叉和变异操作
"""

import numpy as np
import random
import math
from typing import Dict, List, Tuple, Any, Optional, Set
from dataclasses import dataclass, field
from enum import Enum

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext, get_optimization_logger
from ..core.exceptions import GeneticAlgorithmError as GeneticOperationError, handle_exception
from ..core.data_structures import (
    FacadeIndividual, ObjectiveResults, PopulationStatistics, RenovationMode
)
from ..core.utils import MathUtils

from .individual_encoder import IndividualEncoder
from .objective_functions import ObjectiveFunctionEvaluator
from .constraint_handler import ConstraintHandler


class SelectionMethod(Enum):
    """选择方法枚举"""
    NSGA3_SELECTION = "nsga3"
    TOURNAMENT = "tournament"
    ROULETTE = "roulette"
    RANKING = "ranking"


@dataclass
class GeneticParameters:
    """大幅优化的遗传算法参数 - 增强差异性和探索能力"""
    population_size: int = 250  # 从配置文件读取
    tournament_size: int = 2    # 进一步降低选择压力，保持多样性
    crossover_rate: float = 0.7   # 降低交叉率，增加多样性
    mutation_rate: float = 0.6    # 提高基础变异率
    mutation_strength: float = 0.5  # 提高变异强度
    elite_size: int = 5         # 动态计算
    diversity_threshold: float = 0.2  # 提高多样性阈值
    optimization_config: Dict[str, Any] = field(default_factory=dict)  # 完整配置


class GeneticOperators:
    """
    遗传算法操作器
    
    功能：
    1. 种群初始化和管理
    2. NSGA-III非支配排序和拥挤度计算
    3. 父代选择操作
    4. 交叉和变异操作
    5. 环境选择和精英保留
    6. 种群多样性维护
    """
    
    def __init__(self, individual_encoder: IndividualEncoder,
                 objective_evaluator: ObjectiveFunctionEvaluator,
                 constraint_handler: ConstraintHandler):
        """
        初始化遗传算法操作器
        
        Args:
            individual_encoder: 个体编码器
            objective_evaluator: 目标函数评估器
            constraint_handler: 约束处理器
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.opt_logger = get_optimization_logger()  # 优化算法专用日志器
        
        # 确保优化日志器不输出到终端
        self.opt_logger.propagate = False
        import logging
        for handler in self.opt_logger.handlers[:]:
            if isinstance(handler, logging.StreamHandler) and handler.stream.name == '<stdout>':
                self.opt_logger.removeHandler(handler)
        
        self.individual_encoder = individual_encoder
        self.objective_evaluator = objective_evaluator
        self.constraint_handler = constraint_handler
        
        # 获取遗传算法配置
        optimization_config = self.config.get_section('optimization')
        
        # 修复：从配置文件正确读取参数
        self.params = GeneticParameters(
            population_size=optimization_config.get('population_size', 250),     # 从配置文件读取
            tournament_size=optimization_config.get('tournament_size', 3),       
            crossover_rate=optimization_config.get('crossover_rate', 0.8),       # 从配置文件读取
            mutation_rate=optimization_config.get('mutation_rate', 0.4),         # 从配置文件读取
            mutation_strength=optimization_config.get('mutation_strength', 0.4), # 从配置文件读取
            elite_size=max(2, int(optimization_config.get('population_size', 250) * 0.02)), # 2%精英保留
            diversity_threshold=0.15,
            optimization_config=optimization_config  # 传递完整配置
        )
        
        # NSGA-III参考点
        self.reference_points = self._generate_reference_points()
        
        # 种群历史统计
        self.generation_count = 0
        self.population_history = []
        
        # 评估缓存 - 避免重复计算
        self.evaluation_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 多样性维持机制
        diversity_config = self.params.optimization_config.get('diversity_maintenance', {})
        self.diversity_enabled = diversity_config.get('enabled', True)
        self.min_diversity_threshold = diversity_config.get('min_diversity_threshold', 0.1)
        self.diversity_enhancement_rate = diversity_config.get('diversity_enhancement_rate', 0.2)
        self.restart_trigger_generations = diversity_config.get('restart_trigger_generations', 20)
        self.elite_preservation_ratio = diversity_config.get('elite_preservation_ratio', 0.1)
        
        # 多样性维持状态
        self.current_diversity = 1.0
        self.diversity_history = []
        self.low_diversity_generations = 0
        self.elite_solutions = []
        
        # 第二阶段优化：自适应参数控制
        self.adaptive_params_enabled = True
        self.stagnation_count = 0
        self.last_best_fitness = float('inf')
        self.diversity_history = []
        self.base_mutation_rate = self.params.mutation_rate
        self.base_crossover_rate = self.params.crossover_rate
        
        print(f"  [第二阶段优化] 启用自适应参数控制")
        print(f"  [参数] 基础变异率: {self.base_mutation_rate:.3f}")
        print(f"  [参数] 基础交叉率: {self.base_crossover_rate:.3f}")
        
        # 减少日志输出以提速
        pass  # self.opt_logger.info(f"遗传算法操作器初始化完成")
    
    def update_adaptive_parameters(self, evaluation_results: List, diversity_metric: float = None):
        """
        第二阶段优化：自适应参数更新
        根据当前收敛状态动态调整遗传算法参数
        
        Args:
            evaluation_results: 当前代评估结果
            diversity_metric: 多样性指标
        """
        if not self.adaptive_params_enabled:
            return
        
        try:
            # 计算当前最佳适应度
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            if not feasible_results:
                return
            
            current_best_fitness = min(r.energy_consumption for r in feasible_results)
            
            # 检查是否停滞
            improvement = self.last_best_fitness - current_best_fitness
            if improvement < 0.01:  # 改进小于0.01认为停滞
                self.stagnation_count += 1
            else:
                self.stagnation_count = 0
                self.last_best_fitness = current_best_fitness
            
            # 计算多样性指标（如果未提供）
            if diversity_metric is None:
                diversity_metric = self._calculate_diversity_metric(evaluation_results)
            
            self.diversity_history.append(diversity_metric)
            
            # 自适应调整参数
            original_mutation = self.params.mutation_rate
            original_crossover = self.params.crossover_rate
            
            # 大幅增强的停滞处理策略
            if self.stagnation_count > 1:  # 更早开始处理停滞
                # 大幅增加变异率
                stagnation_factor = min(0.8, (self.stagnation_count - 1) * 0.15)
                self.params.mutation_rate = min(0.6, self.base_mutation_rate + stagnation_factor)

                # 停滞时大幅增加变异强度
                if self.stagnation_count > 3:
                    self.params.mutation_strength = min(0.5, self.params.mutation_strength * 1.5)

            # 大幅增强的多样性调整策略
            if diversity_metric < 0.05:  # 多样性严重不足
                self.params.mutation_rate = min(0.8, self.params.mutation_rate + 0.25)
                self.params.crossover_rate = max(0.5, self.params.crossover_rate - 0.2)
                self.params.mutation_strength = min(0.7, self.params.mutation_strength + 0.2)
            elif diversity_metric < 0.12:  # 多样性不足
                self.params.mutation_rate = min(0.7, self.params.mutation_rate + 0.15)
                self.params.crossover_rate = max(0.6, self.params.crossover_rate - 0.1)
                self.params.mutation_strength = min(0.6, self.params.mutation_strength + 0.1)
            elif diversity_metric > 0.25:  # 多样性过高时适度降低变异
                self.params.mutation_rate = max(0.3, self.params.mutation_rate - 0.1)
                self.params.crossover_rate = min(0.8, self.params.crossover_rate + 0.1)
            
            # 记录参数变化
            if (abs(self.params.mutation_rate - original_mutation) > 0.01 or 
                abs(self.params.crossover_rate - original_crossover) > 0.01):
                
                self.opt_logger.info(f"自适应参数调整: 变异率 {original_mutation:.3f}→{self.params.mutation_rate:.3f}, "
                                   f"交叉率 {original_crossover:.3f}→{self.params.crossover_rate:.3f}, "
                                   f"停滞{self.stagnation_count}代, 多样性{diversity_metric:.3f}")
                
        except Exception as e:
            self.opt_logger.warning(f"自适应参数更新失败: {str(e)}")
    
    def _calculate_diversity_metric(self, evaluation_results: List) -> float:
        """
        计算种群多样性指标
        
        Args:
            evaluation_results: 评估结果列表
            
        Returns:
            多样性指标 (0-1, 越大越多样)
        """
        try:
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            if len(feasible_results) < 2:
                return 0.0
            
            # 提取目标值
            objectives = np.array([[r.energy_consumption, r.thermal_performance, r.renovation_cost] 
                                 for r in feasible_results])
            
            # 归一化目标值
            obj_min = np.min(objectives, axis=0)
            obj_max = np.max(objectives, axis=0)
            obj_range = obj_max - obj_min
            
            # 避免除零
            obj_range[obj_range == 0] = 1.0
            normalized_objectives = (objectives - obj_min) / obj_range
            
            # 计算平均距离作为多样性指标
            distances = []
            for i in range(len(normalized_objectives)):
                for j in range(i + 1, len(normalized_objectives)):
                    dist = np.linalg.norm(normalized_objectives[i] - normalized_objectives[j])
                    distances.append(dist)
            
            return np.mean(distances) if distances else 0.0
            
        except Exception as e:
            self.opt_logger.warning(f"多样性计算失败: {str(e)}")
            return 0.0
    
    @handle_exception
    def initialize_population(self) -> List[FacadeIndividual]:
        """
        智能种群初始化 - 使用多样化策略
        
        Returns:
            初始种群个体列表
            
        Raises:
            GeneticOperationError: 种群初始化失败时抛出
        """
        with LogContext("智能种群初始化", self.logger):
            try:
                population = []
                
                # 修复：增强种群初始化多样性，解决收敛过快问题

                # 策略1: 基于真实数据的高质量个体 (15%) - 减少比例，增加多样性
                real_data_count = int(self.params.population_size * 0.15)
                for i in range(real_data_count):
                    individual_id = f"gen0_smart_real{i}"
                    individual = self._create_smart_real_data_individual(individual_id, i)
                    population.append(individual)

                # 策略2: 基于启发式的优化个体 (10%) - 进一步减少比例
                heuristic_count = int(self.params.population_size * 0.10)
                for i in range(heuristic_count):
                    individual_id = f"gen0_smart_heuristic{i}"
                    individual = self._create_enhanced_heuristic_individual(individual_id)
                    population.append(individual)

                # 策略3: 多目标导向的极端个体 (15%) - 保持比例
                extreme_count = int(self.params.population_size * 0.15)
                objectives = ['energy', 'thermal', 'cost']
                for i in range(extreme_count):
                    individual_id = f"gen0_smart_extreme{i}"
                    target_objective = objectives[i % len(objectives)]
                    individual = self._create_objective_oriented_individual(individual_id, target_objective)
                    population.append(individual)

                # 策略4: 大幅增加随机个体比例 (60%) - 解决收敛过快问题
                remaining_count = self.params.population_size - real_data_count - heuristic_count - extreme_count
                for i in range(remaining_count):
                    individual_id = f"gen0_smart_random{i}"
                    # 使用更强的随机性
                    individual = self._create_highly_diverse_random_individual(individual_id, i)
                    population.append(individual)
                
                self.generation_count = 0
                self.opt_logger.info(f"增强多样性种群初始化完成: {len(population)} 个个体 "
                               f"(真实数据:{real_data_count}, 启发式:{heuristic_count}, 极端:{extreme_count}, 高度随机:{remaining_count})")

                return population

            except Exception as e:
                raise GeneticOperationError(f"种群初始化失败: {str(e)}") from e

    def _create_highly_diverse_random_individual(self, individual_id: str, variant_idx: int) -> FacadeIndividual:
        """创建高度多样化的随机个体，解决收敛过快问题"""
        try:
            # 使用不同的随机种子确保高度多样性
            random.seed(hash(individual_id + str(variant_idx)) % 2**32)

            # 创建基础随机个体
            individual = self.individual_encoder.create_random_individual(individual_id)

            # 应用高度随机化变异
            high_mutation_rate = 0.8  # 80%的变异率
            high_mutation_strength = 0.5  # 50%的变异强度

            # 窗户尺寸的高度随机化
            for i in range(len(individual.window_sizes)):
                if random.random() < high_mutation_rate:
                    width, height = individual.window_sizes[i]

                    # 大幅度随机变化
                    width_factor = 1.0 + random.uniform(-high_mutation_strength, high_mutation_strength)
                    height_factor = 1.0 + random.uniform(-high_mutation_strength, high_mutation_strength)

                    new_width = width * width_factor
                    new_height = height * height_factor

                    # 应用约束
                    new_width = max(self.individual_encoder.min_width,
                                   min(new_width, self.individual_encoder.max_width))
                    new_height = max(self.individual_encoder.min_height,
                                    min(new_height, self.individual_encoder.max_height))

                    individual.window_sizes[i] = (new_width, new_height)

            # 窗户类型的高度随机化
            for i in range(len(individual.window_types)):
                if random.random() < high_mutation_rate:
                    individual.window_types[i] = random.randint(0, 4)  # 完全随机的窗户类型

            # 遮阳参数的高度随机化
            if hasattr(individual, 'shading_depths'):
                for i in range(len(individual.shading_depths)):
                    if random.random() < high_mutation_rate:
                        individual.shading_depths[i] = random.uniform(0.0, 1.0)  # 完全随机的遮阳深度

            if hasattr(individual, 'shading_angles'):
                for i in range(len(individual.shading_angles)):
                    if random.random() < high_mutation_rate:
                        individual.shading_angles[i] = random.uniform(0.0, 90.0)  # 完全随机的遮阳角度

            return individual

        except Exception as e:
            self.logger.warning(f"创建高度多样化随机个体失败: {str(e)}")
            # 回退到基础随机个体
            return self.individual_encoder.create_random_individual(individual_id)
    
    def _create_heuristic_individual(self, individual_id: str) -> FacadeIndividual:
        """创建基于启发式规则的个体"""
        try:
            # 基于经验规则创建个体
            # 1. 窗墙比控制在合理范围 (0.3-0.6)
            # 2. 优先选择节能窗户类型
            # 3. 合理的遮阳配置
            
            # 先创建随机个体作为基础
            individual = self.individual_encoder.create_random_individual(individual_id)
            
            # 应用启发式规则调整
            if hasattr(individual, 'window_types'):
                # 70%概率选择节能窗户类型
                for i in range(len(individual.window_types)):
                    if random.random() < 0.7:
                        individual.window_types[i] = 1  # 节能窗户
                    
                    # 为节能窗户添加合理的框架深度
                    if individual.window_types[i] == 1:
                        individual.frame_depths[i] = random.uniform(0.1, 0.3)
            
            return individual
            
        except Exception:
            # 如果启发式创建失败，返回随机个体
            return self._create_truly_random_individual(individual_id, 0)
    
    def _create_truly_random_individual(self, individual_id: str, seed: int) -> FacadeIndividual:
        """创建真正随机的个体 - 根据改造模式生成约束兼容的个体"""
        try:
            # 使用不同的种子确保多样性
            random.seed(seed + 42)  # 固定偏移确保可重现性
            
            # 获取改造模式
            renovation_mode = self.individual_encoder.renovation_mode
            
            # 获取立面边界
            facade_bounds = self.individual_encoder.facade_bounds
            min_x, max_x = facade_bounds['min_x'], facade_bounds['max_x']
            min_y, max_y = facade_bounds['min_y'], facade_bounds['max_y']
            
            # 获取约束参数 - 确保生成的个体满足约束
            min_window_width = getattr(self.constraint_handler, 'min_window_width', 0.6)
            max_window_width = getattr(self.constraint_handler, 'max_window_width', 3.0)
            min_window_height = getattr(self.constraint_handler, 'min_window_height', 0.8) 
            max_window_height = getattr(self.constraint_handler, 'max_window_height', 2.5)
            min_spacing = getattr(self.constraint_handler, 'min_window_spacing', 0.5)
            min_wall_margin = getattr(self.constraint_handler, 'min_wall_margin', 0.2)
            
            # 根据改造模式确定窗户数量和生成策略
            if renovation_mode == RenovationMode.RENOVATION:
                # 改造模式：使用原有窗户数量，不能增删
                existing_windows = len(self.individual_encoder.facade_elements.windows)
                num_windows = existing_windows if existing_windows > 0 else 2
                self.opt_logger.debug(f"改造模式: 保持窗户数量={num_windows}")
            else:
                # 新建或大改模式：允许自由确定窗户数量
                available_width = max_x - min_x - 2 * min_wall_margin
                available_height = max_y - min_y - 2 * min_wall_margin
                max_reasonable_windows = min(6, int(available_width * available_height / (min_window_width * min_window_height * 2)))
                num_windows = random.randint(1, max(1, min(4, max_reasonable_windows)))
                self.opt_logger.debug(f"{renovation_mode.value}模式: 随机窗户数量={num_windows}")
            
            window_positions = []
            window_sizes = []
            window_types = []
            frame_depths = []
            shading_depths = []
            shading_angles = []
            
            # 生成约束兼容的随机窗户
            for i in range(num_windows):
                max_attempts = 100  # 增加尝试次数
                placed = False
                
                for attempt in range(max_attempts):
                    # 根据改造模式决定位置和尺寸生成策略
                    if renovation_mode == RenovationMode.RENOVATION and i < len(self.individual_encoder.facade_elements.windows):
                        # 改造模式：基于原有窗户位置，但允许横向变化
                        original_window = self.individual_encoder.facade_elements.windows[i]
                        base_x, base_y = original_window.center
                        base_width, base_height = original_window.width, original_window.height
                        
                        # 位置：允许小幅横向调整，垂直位置保持相对稳定
                        x = base_x + random.uniform(-0.5, 0.5)  # 横向小幅调整
                        y = base_y + random.uniform(-0.2, 0.2)  # 垂直位置基本保持
                        
                        # 尺寸：只允许宽度变化，高度固定
                        width = base_width * random.uniform(0.7, 1.3)  # 宽度可变化
                        height = base_height  # 高度保持不变
                        
                        self.opt_logger.debug(f"改造模式窗户{i}: 保持高度{height:.2f}m, 调整宽度{width:.2f}m")
                    else:
                        # 新建或大改模式：完全随机
                        x = random.uniform(min_x + min_wall_margin + 0.3, max_x - min_wall_margin - 0.3)
                        y = random.uniform(min_y + min_wall_margin + 0.3, max_y - min_wall_margin - 0.3)
                        
                        # 随机大小 - 严格遵守约束
                        available_width = max_x - min_x - 2 * min_wall_margin
                        available_height = max_y - min_y - 2 * min_wall_margin
                        width = random.uniform(min_window_width, min(max_window_width, available_width / 3))
                        height = random.uniform(min_window_height, min(max_window_height, available_height / 3))
                    
                    # 确保窗户完全在立面边界内
                    if (x - width/2 < min_x + min_wall_margin or 
                        x + width/2 > max_x - min_wall_margin or
                        y - height/2 < min_y + min_wall_margin or 
                        y + height/2 > max_y - min_wall_margin):
                        continue
                    
                    # 检查与现有窗户的冲突
                    valid_position = True
                    for j, (pos_x, pos_y) in enumerate(window_positions):
                        existing_width, existing_height = window_sizes[j]
                        
                        # 检查边界间距
                        x_gap = abs(x - pos_x) - (width + existing_width) / 2
                        y_gap = abs(y - pos_y) - (height + existing_height) / 2
                        
                        if x_gap < min_spacing or y_gap < min_spacing:
                            valid_position = False
                            break
                    
                    if valid_position:
                        window_positions.append((x, y))
                        window_sizes.append((width, height))
                        
                        # 保守的随机类型分配
                        window_type = random.choices([0, 1, 2], weights=[0.6, 0.25, 0.15])[0]
                        window_types.append(window_type)
                        
                        # 合理范围内的深度和角度
                        if window_type == 1:  # 有框窗户
                            frame_depths.append(random.uniform(0.05, 0.15))
                        else:
                            frame_depths.append(0.0)
                            
                        if window_type == 2:  # 遮阳窗户
                            shading_depths.append(random.uniform(0.1, 0.3))
                            shading_angles.append(random.uniform(20, 50))
                        else:
                            shading_depths.append(0.0)
                            shading_angles.append(0.0)
                        
                        placed = True
                        break
                
                # 如果无法放置更多窗户，停止尝试
                if not placed:
                    self.opt_logger.debug(f"受约束限制，停在{len(window_positions)}个窗户")
                    break
            
            # 创建个体
            individual = FacadeIndividual(
                individual_id=individual_id,
                window_positions=window_positions,
                window_sizes=window_sizes,
                window_types=window_types,
                frame_depths=frame_depths,
                shading_depths=shading_depths,
                shading_angles=shading_angles,
                renovation_mode=self.individual_encoder.renovation_mode
            )
            
            self.opt_logger.debug(f"创建约束兼容随机个体: {individual_id}, {len(window_positions)}个窗户")
            
            # 验证个体是否满足基本约束
            if len(window_positions) == 0:
                # 如果无法生成任何窗户，使用基于真实数据的备用方案
                self.opt_logger.warning(f"随机个体{individual_id}无法生成任何窗户，使用真实数据备用方案")
                return self.individual_encoder.create_individual_from_real_data(individual_id)
            
            return individual
            
        except Exception as e:
            self.opt_logger.error(f"创建真正随机个体失败: {str(e)}")
            # 降级到基于真实数据的方法
            return self.individual_encoder.create_individual_from_real_data(individual_id)
    
    def _create_real_data_variant_individual(self, individual_id: str, variant_seed: int) -> FacadeIndividual:
        """创建基于真实数据的变异个体 - 增加多样性"""
        try:
            # 首先创建基于真实数据的个体
            base_individual = self.individual_encoder.create_individual_from_real_data(individual_id)
            
            # 使用变异种子确保多样性
            random.seed(variant_seed + 100)
            
            # 对基础个体进行显著变异
            mutation_rate = 0.6  # 60%的变异率
            mutation_strength = 0.3  # 30%的变异强度
            
            # 复制基础个体
            mutated = FacadeIndividual(
                individual_id=base_individual.individual_id + "_variant",
                window_positions=base_individual.window_positions.copy(),
                window_sizes=base_individual.window_sizes.copy(),
                window_types=base_individual.window_types.copy(),
                frame_depths=base_individual.frame_depths.copy(),
                shading_depths=base_individual.shading_depths.copy(),
                shading_angles=base_individual.shading_angles.copy(),
                renovation_mode=base_individual.renovation_mode
            )
            
            num_windows = len(mutated.window_positions)
            
            for i in range(num_windows):
                # 位置变异 - 大幅变化
                if random.random() < mutation_rate:
                    pos_x, pos_y = mutated.window_positions[i]
                    
                    # 大幅位置变化
                    x_range = (self.individual_encoder.facade_bounds['max_x'] - self.individual_encoder.facade_bounds['min_x']) * 0.3
                    y_range = (self.individual_encoder.facade_bounds['max_y'] - self.individual_encoder.facade_bounds['min_y']) * 0.3
                    
                    new_x = pos_x + random.uniform(-x_range, x_range)
                    new_y = pos_y + random.uniform(-y_range, y_range)
                    
                    # 应用边界约束
                    new_x = max(self.individual_encoder.facade_bounds['min_x'], 
                               min(new_x, self.individual_encoder.facade_bounds['max_x']))
                    new_y = max(self.individual_encoder.facade_bounds['min_y'], 
                               min(new_y, self.individual_encoder.facade_bounds['max_y']))
                    
                    mutated.window_positions[i] = (new_x, new_y)
                
                # 尺寸变异 - 大幅变化
                if random.random() < mutation_rate:
                    width, height = mutated.window_sizes[i]
                    
                    # 大幅尺寸变化
                    new_width = width * (1 + random.uniform(-mutation_strength, mutation_strength))
                    new_height = height * (1 + random.uniform(-mutation_strength, mutation_strength))
                    
                    # 应用约束
                    new_width = max(self.individual_encoder.min_width, 
                                   min(new_width, self.individual_encoder.max_width))
                    new_height = max(self.individual_encoder.min_height, 
                                    min(new_height, self.individual_encoder.max_height))
                    
                    mutated.window_sizes[i] = (new_width, new_height)
                
                # 类型变异
                if random.random() < mutation_rate * 0.5:
                    mutated.window_types[i] = random.randint(0, 2)
                    
                    # 更新对应的深度和角度
                    if mutated.window_types[i] == 1:
                        mutated.frame_depths[i] = random.uniform(0.1, 0.3)
                    else:
                        mutated.frame_depths[i] = 0.0
                    
                    if mutated.window_types[i] == 2:
                        mutated.shading_depths[i] = random.uniform(0.2, 0.5)
                        mutated.shading_angles[i] = random.uniform(15, 75)
                    else:
                        mutated.shading_depths[i] = 0.0
                        mutated.shading_angles[i] = 0.0
            
            self.opt_logger.debug(f"创建真实数据变异个体: {individual_id}, 变异率{mutation_rate*100:.0f}%")
            return mutated
            
        except Exception as e:
            self.opt_logger.error(f"创建真实数据变异个体失败: {str(e)}")
            # 降级到基于真实数据的方法
            return self.individual_encoder.create_individual_from_real_data(individual_id)
    
    def _create_high_mutation_individual(self, individual_id: str, seed: int) -> FacadeIndividual:
        """创建高变异个体 - 在经典个体基础上进行大幅度变异"""
        try:
            # 先创建一个经典个体
            base_individual = self._create_heuristic_individual(individual_id)
            
            # 对关键参数进行大幅度变异
            random.seed(seed + 1000)  # 使用不同种子
            
            # 变异窗户位置（只适用于非改造模式或新增窗户）
            if hasattr(base_individual, 'window_positions'):
                for i in range(len(base_individual.window_positions)):
                    if random.random() < 0.6:  # 60%的窗户进行位置变异
                        x, y = base_individual.window_positions[i]
                        # 大幅度位置变异
                        x += random.uniform(-1.5, 1.5)
                        y += random.uniform(-1.0, 1.0)
                        base_individual.window_positions[i] = (x, y)
            
            # 变异窗户尺寸
            if hasattr(base_individual, 'window_sizes'):
                for i in range(len(base_individual.window_sizes)):
                    if random.random() < 0.8:  # 80%的窗户进行尺寸变异
                        width, height = base_individual.window_sizes[i]
                        # 大幅度尺寸变异
                        width_factor = random.uniform(0.3, 2.0)  # 更大的变化范围
                        height_factor = random.uniform(0.3, 2.0)
                        base_individual.window_sizes[i] = (width * width_factor, height * height_factor)
            
            # 变异窗户类型
            if hasattr(base_individual, 'window_types'):
                for i in range(len(base_individual.window_types)):
                    if random.random() < 0.5:  # 50%的窗户进行类型变异
                        base_individual.window_types[i] = random.randint(0, 2)
            
            # 变异遮阳参数
            if hasattr(base_individual, 'shading_depths'):
                for i in range(len(base_individual.shading_depths)):
                    if random.random() < 0.7:  # 70%的窗户进行遮阳变异
                        base_individual.shading_depths[i] = random.uniform(0.0, 1.0)
            
            return base_individual
            
        except Exception:
            # 如果高变异创建失败，返回随机个体
            return self._create_truly_random_individual(individual_id, seed)
    
    def _create_extreme_individual(self, individual_id: str, target_objective: str) -> FacadeIndividual:
        """创建专注于特定目标的极端个体"""
        try:
            # 首先创建真正随机的个体作为基础
            individual = self._create_truly_random_individual(individual_id, hash(target_objective) % 1000)
            
            if target_objective == 'energy':
                # 专注能耗：最大化节能效果
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 1  # 全部使用节能窗户
                    individual.frame_depths[i] = random.uniform(0.25, 0.4)  # 深框架增强隔热
                    individual.shading_depths[i] = random.uniform(0.3, 0.6)  # 添加遮阳进一步节能
                    individual.shading_angles[i] = random.uniform(30, 60)  # 优化遮阳角度
                    
                # 增加窗户数量最大化采光，同时减少墙体热损失
                if len(individual.window_positions) < 6:
                    # 添加更多窗户
                    for _ in range(6 - len(individual.window_positions)):
                        individual.window_positions.append(individual.window_positions[0])
                        individual.window_sizes.append(individual.window_sizes[0])
                        individual.window_types.append(1)
                        individual.frame_depths.append(0.3)
                        individual.shading_depths.append(0.4)
                        individual.shading_angles.append(45)
                        
            elif target_objective == 'thermal':
                # 专注热工性能：最大化热舒适性
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 2  # 全部使用遮阳窗户
                    individual.shading_depths[i] = random.uniform(0.8, 1.5)  # 深遮阳最大化热控制
                    individual.shading_angles[i] = random.uniform(15, 35)  # 浅角度优化采光与遮阳平衡
                    individual.frame_depths[i] = random.uniform(0.15, 0.25)  # 适中框架深度
                    
                # 优化窗户分布以改善室内热环境
                for i in range(len(individual.window_positions)):
                    # 调整窗户位置，避免过热区域
                    x, y = individual.window_positions[i]
                    # 窗户位置偏下，改善室内温度分层
                    individual.window_positions[i] = (x, y * 0.8)
                    
            elif target_objective == 'cost':
                # 专注成本：最小化改造成本
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 0  # 全部使用普通窗户，最低成本
                    individual.frame_depths[i] = 0.0  # 无窗框，节省成本
                    individual.shading_depths[i] = 0.0  # 无遮阳，节省成本
                    individual.shading_angles[i] = 0.0
                    
                # 减少窗户数量，降低改造成本
                if len(individual.window_positions) > 3:
                    # 只保留3个最主要的窗户
                    individual.window_positions = individual.window_positions[:3]
                    individual.window_sizes = individual.window_sizes[:3]
                    individual.window_types = individual.window_types[:3]
                    individual.frame_depths = individual.frame_depths[:3]
                    individual.shading_depths = individual.shading_depths[:3]
                    individual.shading_angles = individual.shading_angles[:3]
            
            self.opt_logger.debug(f"创建极端个体: {individual_id}, 目标={target_objective}")
            return individual
            
        except Exception as e:
            self.opt_logger.error(f"创建极端个体失败: {str(e)}")
            return self._create_truly_random_individual(individual_id, 999)
    
    @handle_exception
    def evaluate_population(self, population: List[FacadeIndividual]) -> List[ObjectiveResults]:
        """
        评估种群 - 优化版本支持缓存和批量处理
        
        Args:
            population: 待评估的种群
            
        Returns:
            种群评估结果列表
        """
        with LogContext(f"种群评估 - 第{self.generation_count}代", self.logger):
            try:
                evaluation_results = []
                
                # 批量评估以提高效率
                for individual in population:
                    # 检查缓存（基于个体ID）
                    cache_key = self._get_individual_cache_key(individual)
                    cached_result = self._get_cached_evaluation(cache_key)
                    
                    if cached_result is not None:
                        evaluation_results.append(cached_result)
                        continue
                    
                    # 评估目标函数
                    objective_results = self.objective_evaluator.evaluate_objectives(individual)
                    
                    # 检查约束
                    constraint_results = self.constraint_handler.check_constraints(individual)
                    
                    # 更新目标结果中的约束信息
                    objective_results.constraint_violations = constraint_results.total_violation
                    objective_results.is_feasible = constraint_results.is_feasible
                    
                    # 缓存结果
                    self._cache_evaluation(cache_key, objective_results)
                    
                    evaluation_results.append(objective_results)
                
                self.opt_logger.info(f"种群评估完成: {len(evaluation_results)} 个个体")
                return evaluation_results
                
            except Exception as e:
                raise GeneticOperationError(f"种群评估失败: {str(e)}") from e
    
    def nsga3_environmental_selection(self, population: List[FacadeIndividual],
                                    evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """
        改进的NSGA-III环境选择 - 减少选择压力，保持更多多样性
        
        Args:
            population: 当前种群
            evaluation_results: 评估结果
            
        Returns:
            选择后的种群和评估结果
        """
        try:
            # 步骤1: 分离可行解和不可行解
            feasible_indices = [i for i, r in enumerate(evaluation_results) if r.is_feasible]
            infeasible_indices = [i for i, r in enumerate(evaluation_results) if not r.is_feasible]
            
            selected_population = []
            selected_evaluations = []
            
            # 大幅放宽选择策略：保持更多多样性
            min_feasible_ratio = 0.2  # 降低到20%的可行解要求
            target_feasible_count = max(
                int(self.params.population_size * min_feasible_ratio),
                min(len(feasible_indices), int(self.params.population_size * 0.8))  # 最多80%可行解
            )
            
            # 优先处理可行解
            if feasible_indices:
                feasible_results = [evaluation_results[i] for i in feasible_indices]
                fronts = self._fast_non_dominated_sort(feasible_results)
                
                # 重新映射索引
                front_indices_mapped = []
                for front in fronts:
                    mapped_front = [feasible_indices[i] for i in front]
                    front_indices_mapped.append(mapped_front)
                
                # 采用更宽松的前沿选择策略
                for front_level, front_indices in enumerate(front_indices_mapped):
                    if len(selected_population) + len(front_indices) <= target_feasible_count:
                        # 整层都可以加入
                        for idx in front_indices:
                            selected_population.append(population[idx])
                            selected_evaluations.append(evaluation_results[idx])
                    else:
                        # 需要从这一层选择部分个体 - 使用更宽松的策略
                        remaining_slots = target_feasible_count - len(selected_population)
                        if remaining_slots > 0:
                            # 对于前两层，使用多样性优先选择
                            if front_level <= 1:
                                selected_indices = self._diversity_first_selection(
                                    front_indices, evaluation_results, remaining_slots
                                )
                            else:
                                # 后续层使用平衡选择
                                selected_indices = self._balanced_selection(
                                    front_indices, evaluation_results, remaining_slots
                                )
                            
                            for idx in selected_indices:
                                selected_population.append(population[idx])
                                selected_evaluations.append(evaluation_results[idx])
                        break
            
            # 填充剩余位置：更宽松地接受不可行解
            remaining_slots = self.params.population_size - len(selected_population)
            if remaining_slots > 0 and infeasible_indices:
                # 按约束违反程度排序，但允许更多不可行解进入
                infeasible_sorted = sorted(infeasible_indices, 
                                         key=lambda i: evaluation_results[i].constraint_violations)
                
                # 选择约束违反较小的解，但不过分严格
                selection_count = min(remaining_slots, len(infeasible_sorted))
                for i in range(selection_count):
                    idx = infeasible_sorted[i]
                    selected_population.append(population[idx])
                    selected_evaluations.append(evaluation_results[idx])
            
            self.opt_logger.debug(f"改进NSGA-III环境选择完成: {len(selected_population)} 个个体 "
                                f"(可行解: {len([r for r in selected_evaluations if r.is_feasible])})")
            return selected_population, selected_evaluations

        except Exception as e:
            self.logger.error(f"NSGA-III环境选择失败: {str(e)}")
            # 返回原种群的前N个个体作为备选
            return population[:self.params.population_size], evaluation_results[:self.params.population_size]

    def _improved_selection_from_front(self, front_indices: List[int],
                                     evaluation_results: List[ObjectiveResults],
                                     num_select: int) -> List[int]:
        """
        从前沿中改进选择策略
        结合目标函数值和多样性进行选择
        """
        try:
            if len(front_indices) <= num_select:
                return front_indices

            # 计算每个个体的综合评分
            scores = []
            for idx in front_indices:
                result = evaluation_results[idx]

                # 目标函数归一化评分（越小越好）
                obj_score = 0.0
                if result.is_feasible:
                    # 简单的加权评分
                    energy_norm = result.energy_consumption / 200.0  # 假设最大能耗200
                    thermal_norm = result.thermal_performance  # 已经是0-1范围
                    cost_norm = result.renovation_cost / 50000.0  # 假设最大成本50000

                    obj_score = 0.4 * energy_norm + 0.3 * thermal_norm + 0.3 * cost_norm
                else:
                    obj_score = 1.0 + result.constraint_violations  # 不可行解惩罚

                scores.append((idx, obj_score))

            # 按评分排序并选择最好的
            scores.sort(key=lambda x: x[1])
            selected_indices = [idx for idx, _ in scores[:num_select]]

            return selected_indices

        except Exception as e:
            self.logger.error(f"改进前沿选择失败: {str(e)}")
            # 返回前num_select个
            return front_indices[:num_select]
    
    def parent_selection(self, population: List[FacadeIndividual],
                        evaluation_results: List[ObjectiveResults],
                        num_parents: int) -> List[FacadeIndividual]:
        """
        父代选择
        
        Args:
            population: 当前种群
            evaluation_results: 评估结果
            num_parents: 需要选择的父代数量
            
        Returns:
            选择的父代个体列表
        """
        try:
            parents = []
            
            for _ in range(num_parents):
                # 锦标赛选择
                parent = self._tournament_selection(population, evaluation_results)
                parents.append(parent)
            
            return parents
            
        except Exception as e:
            self.logger.error(f"父代选择失败: {str(e)}")
            return random.choices(population, k=num_parents)
    
    def crossover_and_mutation(self, parents: List[FacadeIndividual]) -> List[FacadeIndividual]:
        """
        交叉和变异操作 - 改进版本增强多样性
        
        Args:
            parents: 父代个体列表
            
        Returns:
            子代个体列表
        """
        try:
            offspring = []
            
            # 优化的自适应变异策略 - 平衡探索与开发
            current_diversity = self._calculate_population_diversity_simple(parents)
            generation_factor = min(1.0, self.generation_count / 40.0)  # 减少代数因子

            # 基础自适应变异率
            adaptive_mutation_rate = self.params.mutation_rate
            adaptive_mutation_strength = self.params.mutation_strength

            # 多样性过低时适度增加变异（避免过度变异）
            if current_diversity < self.params.diversity_threshold:
                diversity_factor = (self.params.diversity_threshold - current_diversity) / self.params.diversity_threshold
                adaptive_mutation_rate = min(0.3, self.params.mutation_rate * (1 + diversity_factor * 1.5))
                adaptive_mutation_strength = min(0.25, self.params.mutation_strength * (1 + diversity_factor * 0.8))

            # 改进的代数自适应策略
            if self.generation_count < 8:
                adaptive_mutation_rate *= 1.2  # 早期适度增加探索
            elif self.generation_count > 25:
                adaptive_mutation_rate *= 0.85  # 后期适度减少变异，增加开发

            # 添加收敛状态自适应
            if hasattr(self, 'stagnation_counter') and self.stagnation_counter > 5:
                # 停滞时增加变异以跳出局部最优
                adaptive_mutation_rate *= 1.4
                adaptive_mutation_strength *= 1.3
            
            for i in range(0, len(parents), 2):
                parent1 = parents[i]
                parent2 = parents[min(i + 1, len(parents) - 1)]
                
                # 交叉操作
                if random.random() < self.params.crossover_rate:
                    child1, child2 = self.individual_encoder.crossover_individuals(parent1, parent2)
                else:
                    # 即使不交叉也要复制个体以避免引用问题
                    child1 = self.individual_encoder.copy_individual(parent1)
                    child2 = self.individual_encoder.copy_individual(parent2)
                
                # 变异操作 - 使用智能自适应变异率和强度
                if random.random() < adaptive_mutation_rate:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, adaptive_mutation_rate, adaptive_mutation_strength, self.generation_count
                    )
                
                if random.random() < adaptive_mutation_rate:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, adaptive_mutation_rate, adaptive_mutation_strength, self.generation_count
                    )
                
                # 强化多样性的变异策略
                # 1. 微调变异：高概率小幅度变异
                if random.random() < 0.25:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, 0.4, adaptive_mutation_strength * 0.7, self.generation_count
                    )
                
                if random.random() < 0.25:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, 0.4, adaptive_mutation_strength * 0.7, self.generation_count
                    )
                
                # 2. 探索性变异：中等概率中等幅度变异
                if random.random() < 0.15:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, 0.7, adaptive_mutation_strength * 1.5, self.generation_count
                    )
                
                if random.random() < 0.15:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, 0.7, adaptive_mutation_strength * 1.5, self.generation_count
                    )
                
                # 3. 跳跃变异：适中概率大幅度变异（强化多样性）
                if random.random() < 0.08:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, 0.9, adaptive_mutation_strength * 3.0, self.generation_count
                    )
                
                if random.random() < 0.08:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, 0.9, adaptive_mutation_strength * 3.0, self.generation_count
                    )
                
                # 更新个体ID
                child1.individual_id = f"gen{self.generation_count + 1}_off{len(offspring)}"
                child2.individual_id = f"gen{self.generation_count + 1}_off{len(offspring) + 1}"
                
                offspring.extend([child1, child2])
            
            return offspring
            
        except Exception as e:
            self.logger.error(f"交叉变异操作失败: {str(e)}")
            return parents  # 返回父代作为备选
    
    def _calculate_population_diversity_simple(self, population: List[FacadeIndividual]) -> float:
        """简单计算种群多样性"""
        try:
            if len(population) < 2:
                return 0.0
            
            # 基于个体编码的多样性计算
            total_distance = 0.0
            count = 0
            
            for i in range(len(population)):
                for j in range(i + 1, len(population)):
                    # 计算个体间的汉明距离或欧氏距离
                    distance = self._calculate_individual_distance(population[i], population[j])
                    total_distance += distance
                    count += 1
            
            return total_distance / count if count > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_individual_distance(self, ind1: FacadeIndividual, ind2: FacadeIndividual) -> float:
        """计算两个个体间的距离"""
        try:
            # 简化的距离计算，基于个体的主要属性
            distance = 0.0
            
            # 比较窗户配置
            if hasattr(ind1, 'window_configurations') and hasattr(ind2, 'window_configurations'):
                for i, (config1, config2) in enumerate(zip(ind1.window_configurations, ind2.window_configurations)):
                    if config1 != config2:
                        distance += 1.0
            
            # 比较墙体配置
            if hasattr(ind1, 'wall_configurations') and hasattr(ind2, 'wall_configurations'):
                for i, (config1, config2) in enumerate(zip(ind1.wall_configurations, ind2.wall_configurations)):
                    if config1 != config2:
                        distance += 1.0
            
            return distance
            
        except Exception:
            return 1.0  # 默认距离
    
    def evolve_generation(self, population: List[FacadeIndividual],
                         evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """
        进化一代
        
        Args:
            population: 当前种群
            evaluation_results: 当前评估结果
            
        Returns:
            新一代种群和评估结果
        """
        with LogContext(f"进化第{self.generation_count + 1}代", self.logger):
            try:
                # 父代选择
                parents = self.parent_selection(
                    population, evaluation_results, self.params.population_size
                )
                
                # 交叉变异生成子代
                offspring = self.crossover_and_mutation(parents)
                
                # 评估子代
                offspring_evaluations = self.evaluate_population(offspring)
                
                # 合并父代和子代
                combined_population = population + offspring
                combined_evaluations = evaluation_results + offspring_evaluations
                
                # 环境选择
                next_population, next_evaluations = self.nsga3_environmental_selection(
                    combined_population, combined_evaluations
                )
                
                # 多样性维持机制
                next_population, next_evaluations = self.maintain_diversity(
                    next_population, next_evaluations
                )

                # 强制多样性注入 - 每5代注入高度随机个体
                if self.generation_count % 5 == 0 and self.generation_count > 10:
                    next_population, next_evaluations = self._inject_diversity(
                        next_population, next_evaluations
                    )
                
                # 更新代数
                self.generation_count += 1
                
                # 记录种群统计
                stats = self._calculate_population_statistics(next_population, next_evaluations)
                self.population_history.append(stats)
                
                # 获取缓存统计
                cache_stats = self.get_cache_statistics()
                
                self.opt_logger.info(f"第{self.generation_count}代进化完成: "
                               f"平均能耗={stats.average_objectives[0]:.1f}, "
                               f"可行解={stats.feasible_count}/{len(next_population)}, "
                               f"缓存命中率={cache_stats['hit_rate']:.1f}%")
                
                return next_population, next_evaluations
                
            except Exception as e:
                raise GeneticOperationError(f"进化操作失败: {str(e)}") from e
    
    def _fast_non_dominated_sort(self, evaluation_results: List[ObjectiveResults]) -> List[List[int]]:
        """优化的快速非支配排序 - 减少比较次数"""
        n = len(evaluation_results)
        if n == 0:
            return []
        
        fronts = []
        
        # 预处理：分离可行解和不可行解
        feasible_indices = []
        infeasible_indices = []
        
        for i, result in enumerate(evaluation_results):
            if result.is_feasible:
                feasible_indices.append(i)
            else:
                infeasible_indices.append(i)
        
        # 对可行解进行非支配排序
        if feasible_indices:
            feasible_fronts = self._sort_feasible_solutions(
                [evaluation_results[i] for i in feasible_indices]
            )
            # 重新映射索引
            for front in feasible_fronts:
                mapped_front = [feasible_indices[i] for i in front]
                fronts.append(mapped_front)
        
        # 不可行解按约束违反程度排序，放在最后一层
        if infeasible_indices:
            infeasible_sorted = sorted(infeasible_indices, 
                                     key=lambda i: evaluation_results[i].constraint_violations)
            fronts.append(infeasible_sorted)
        
        return fronts
    
    def _sort_feasible_solutions(self, feasible_results: List[ObjectiveResults]) -> List[List[int]]:
        """对可行解进行快速非支配排序"""
        n = len(feasible_results)
        if n == 0:
            return []
        
        fronts = []
        dominated_solutions = [[] for _ in range(n)]
        domination_count = [0] * n
        
        # 优化的支配关系计算 - 只比较上三角
        for i in range(n):
            for j in range(i + 1, n):
                if self._dominates_objectives_only(feasible_results[i], feasible_results[j]):
                    dominated_solutions[i].append(j)
                    domination_count[j] += 1
                elif self._dominates_objectives_only(feasible_results[j], feasible_results[i]):
                    dominated_solutions[j].append(i)
                    domination_count[i] += 1
        
        # 找到第一层非支配解
        first_front = [i for i in range(n) if domination_count[i] == 0]
        if not first_front:
            return [[i for i in range(n)]]  # 所有解都在同一层
        
        fronts.append(first_front)
        
        # 构建后续层
        current_front = first_front
        while current_front:
            next_front = []
            for i in current_front:
                for j in dominated_solutions[i]:
                    domination_count[j] -= 1
                    if domination_count[j] == 0:
                        next_front.append(j)
            
            if next_front:
                fronts.append(next_front)
            current_front = next_front
        
        return fronts
    
    def _dominates_objectives_only(self, obj1: ObjectiveResults, obj2: ObjectiveResults) -> bool:
        """仅基于目标函数的支配关系判断（用于可行解）"""
        objectives1 = [obj1.energy_consumption, obj1.thermal_performance, obj1.renovation_cost]
        objectives2 = [obj2.energy_consumption, obj2.thermal_performance, obj2.renovation_cost]
        
        better_in_at_least_one = False
        for i in range(len(objectives1)):
            if objectives1[i] > objectives2[i]:  # obj1在第i个目标上劣于obj2
                return False
            elif objectives1[i] < objectives2[i]:  # obj1在第i个目标上优于obj2
                better_in_at_least_one = True
        
        return better_in_at_least_one
    
    def _dominates(self, obj1: ObjectiveResults, obj2: ObjectiveResults) -> bool:
        """判断obj1是否支配obj2"""
        # 考虑约束违反的支配关系
        if obj1.constraint_violations < obj2.constraint_violations:
            return True
        elif obj1.constraint_violations > obj2.constraint_violations:
            return False
        
        # 在可行域内比较目标函数（最小化问题）
        objectives1 = [obj1.energy_consumption, obj1.thermal_performance, obj1.renovation_cost]
        objectives2 = [obj2.energy_consumption, obj2.thermal_performance, obj2.renovation_cost]
        
        # 检查所有目标都不劣于obj2
        better_in_at_least_one = False
        for i in range(len(objectives1)):
            if objectives1[i] > objectives2[i]:  # obj1在第i个目标上劣于obj2
                return False
            elif objectives1[i] < objectives2[i]:  # obj1在第i个目标上优于obj2
                better_in_at_least_one = True
        
        return better_in_at_least_one
    
    def _reference_point_selection(self, front_indices: List[int],
                                 evaluation_results: List[ObjectiveResults],
                                 num_select: int) -> List[int]:
        """基于参考点的选择 - 改进版本增强多样性"""
        try:
            if num_select >= len(front_indices):
                return front_indices
            
            # 归一化目标值
            normalized_objectives = self._normalize_objectives(
                [evaluation_results[i] for i in front_indices]
            )
            
            # 计算每个个体到参考点的距离
            distances_to_refs = []
            for obj in normalized_objectives:
                distances = []
                for ref_point in self.reference_points:
                    distance = self._calculate_perpendicular_distance(obj, ref_point)
                    distances.append(distance)
                distances_to_refs.append(distances)
            
            # 改进的选择策略：平衡距离和多样性
            selected_indices = []
            ref_point_counts = [0] * len(self.reference_points)
            
            # 第一阶段：为每个参考点至少选择一个个体（如果可能）
            used_individuals = set()
            for ref_idx in range(min(len(self.reference_points), num_select)):
                best_idx = -1
                best_distance = float('inf')
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    distance = distances_to_refs[i][ref_idx]
                    if distance < best_distance:
                        best_distance = distance
                        best_idx = i
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    ref_point_counts[ref_idx] += 1
            
            # 第二阶段：填充剩余位置，考虑多样性
            while len(selected_indices) < num_select:
                best_idx = -1
                best_ref_idx = -1
                best_score = float('inf')
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    for ref_idx, distance in enumerate(distances_to_refs[i]):
                        # 综合考虑距离和参考点负载
                        diversity_penalty = ref_point_counts[ref_idx] * 0.2
                        score = distance + diversity_penalty
                        
                        if score < best_score:
                            best_score = score
                            best_idx = i
                            best_ref_idx = ref_idx
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    ref_point_counts[best_ref_idx] += 1
                else:
                    break
            
            return [front_indices[i] for i in selected_indices]
            
        except Exception as e:
            self.logger.error(f"参考点选择失败: {str(e)}")
            # 随机选择作为备选
            return random.sample(front_indices, num_select)
    
    def _enhanced_reference_point_selection(self, front_indices: List[int],
                                          evaluation_results: List[ObjectiveResults],
                                          num_select: int) -> List[int]:
        """增强的参考点选择策略 - 结合多样性和质量"""
        try:
            if num_select >= len(front_indices):
                return front_indices
            
            # 归一化目标值
            normalized_objectives = self._normalize_objectives(
                [evaluation_results[i] for i in front_indices]
            )
            
            # 计算每个个体到参考点的距离
            distances_to_refs = []
            for obj in normalized_objectives:
                distances = []
                for ref_point in self.reference_points:
                    distance = self._calculate_perpendicular_distance(obj, ref_point)
                    distances.append(distance)
                distances_to_refs.append(distances)
            
            # 计算个体间的拥挤距离
            crowding_distances = self._calculate_crowding_distances(normalized_objectives)
            
            # 综合选择策略：平衡参考点关联和拥挤距离
            selected_indices = []
            ref_point_counts = [0] * len(self.reference_points)
            used_individuals = set()
            
            # 第一阶段：为负载最少的参考点选择最近的个体
            for _ in range(min(num_select, len(self.reference_points))):
                # 找到负载最少的参考点
                min_load_refs = [i for i, count in enumerate(ref_point_counts) 
                               if count == min(ref_point_counts)]
                target_ref = random.choice(min_load_refs)
                
                # 为该参考点选择最近且未被选择的个体
                best_idx = -1
                best_distance = float('inf')
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    distance = distances_to_refs[i][target_ref]
                    if distance < best_distance:
                        best_distance = distance
                        best_idx = i
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    ref_point_counts[target_ref] += 1
            
            # 第二阶段：基于拥挤距离选择剩余个体
            while len(selected_indices) < num_select:
                best_idx = -1
                best_score = -1
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    # 综合评分：拥挤距离 + 参考点多样性奖励
                    crowding_score = crowding_distances[i]
                    
                    # 找到该个体最近的参考点
                    nearest_ref = min(range(len(self.reference_points)), 
                                    key=lambda r: distances_to_refs[i][r])
                    
                    # 参考点负载惩罚
                    ref_penalty = ref_point_counts[nearest_ref] * 0.1
                    
                    total_score = crowding_score - ref_penalty
                    
                    if total_score > best_score:
                        best_score = total_score
                        best_idx = i
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    
                    # 更新参考点计数
                    nearest_ref = min(range(len(self.reference_points)), 
                                    key=lambda r: distances_to_refs[best_idx][r])
                    ref_point_counts[nearest_ref] += 1
                else:
                    break
            
            return [front_indices[i] for i in selected_indices]
            
        except Exception as e:
            self.logger.error(f"增强参考点选择失败: {str(e)}")
            return random.sample(front_indices, num_select)
    
    def _diversity_first_selection(self, front_indices: List[int],
                                 evaluation_results: List[ObjectiveResults],
                                 num_select: int) -> List[int]:
        """多样性优先选择策略 - 优先保持解的多样性"""
        try:
            if num_select >= len(front_indices):
                return front_indices
            
            # 归一化目标值
            objectives = []
            for idx in front_indices:
                result = evaluation_results[idx]
                objectives.append([
                    result.energy_consumption,
                    result.thermal_performance, 
                    result.renovation_cost
                ])
            
            objectives = np.array(objectives)
            
            # 标准化目标值
            if len(objectives) > 1:
                objectives_std = (objectives - objectives.min(axis=0)) / (objectives.max(axis=0) - objectives.min(axis=0) + 1e-10)
            else:
                objectives_std = objectives
            
            # 计算拥挤距离
            crowding_distances = self._calculate_crowding_distances_numpy(objectives_std)
            
            # 基于拥挤距离选择
            sorted_indices = sorted(range(len(front_indices)), 
                                  key=lambda i: crowding_distances[i], reverse=True)
            
            selected_local_indices = sorted_indices[:num_select]
            return [front_indices[i] for i in selected_local_indices]
            
        except Exception as e:
            self.logger.warning(f"多样性优先选择失败: {str(e)}")
            return random.sample(front_indices, num_select)
    
    def _balanced_selection(self, front_indices: List[int],
                          evaluation_results: List[ObjectiveResults],
                          num_select: int) -> List[int]:
        """平衡选择策略 - 兼顾质量和多样性"""
        try:
            if num_select >= len(front_indices):
                return front_indices
            
            # 计算质量评分
            quality_scores = []
            for idx in front_indices:
                result = evaluation_results[idx]
                # 简单的质量评分：目标值越小越好
                quality_score = -(result.energy_consumption / 100.0 + 
                                result.thermal_performance + 
                                result.renovation_cost / 10000.0)
                quality_scores.append(quality_score)
            
            # 计算多样性评分
            objectives = []
            for idx in front_indices:
                result = evaluation_results[idx]
                objectives.append([
                    result.energy_consumption,
                    result.thermal_performance, 
                    result.renovation_cost
                ])
            
            objectives = np.array(objectives)
            if len(objectives) > 1:
                objectives_std = (objectives - objectives.min(axis=0)) / (objectives.max(axis=0) - objectives.min(axis=0) + 1e-10)
                diversity_scores = self._calculate_crowding_distances_numpy(objectives_std)
            else:
                diversity_scores = [1.0] * len(objectives)
            
            # 综合评分：质量占40%，多样性占60%
            combined_scores = []
            for i in range(len(front_indices)):
                quality_norm = (quality_scores[i] - min(quality_scores)) / (max(quality_scores) - min(quality_scores) + 1e-10)
                diversity_norm = diversity_scores[i] / (max(diversity_scores) + 1e-10)
                combined_score = 0.4 * quality_norm + 0.6 * diversity_norm
                combined_scores.append(combined_score)
            
            # 基于综合评分选择
            sorted_indices = sorted(range(len(front_indices)), 
                                  key=lambda i: combined_scores[i], reverse=True)
            
            selected_local_indices = sorted_indices[:num_select]
            return [front_indices[i] for i in selected_local_indices]
            
        except Exception as e:
            self.logger.warning(f"平衡选择失败: {str(e)}")
            return random.sample(front_indices, num_select)
    
    def _calculate_crowding_distances_numpy(self, objectives: np.ndarray) -> List[float]:
        """使用numpy计算拥挤距离"""
        try:
            n_points, n_objectives = objectives.shape
            distances = np.zeros(n_points)
            
            for m in range(n_objectives):
                # 按第m个目标排序
                sorted_indices = np.argsort(objectives[:, m])
                
                # 边界点设置为无穷大
                distances[sorted_indices[0]] = float('inf')
                distances[sorted_indices[-1]] = float('inf')
                
                # 计算中间点的拥挤距离
                if n_points > 2:
                    obj_range = objectives[sorted_indices[-1], m] - objectives[sorted_indices[0], m]
                    if obj_range > 0:
                        for i in range(1, n_points - 1):
                            distances[sorted_indices[i]] += (
                                objectives[sorted_indices[i+1], m] - 
                                objectives[sorted_indices[i-1], m]
                            ) / obj_range
            
            return distances.tolist()
            
        except Exception as e:
            self.logger.warning(f"计算拥挤距离失败: {str(e)}")
            return [1.0] * len(objectives)
    
    def maintain_diversity(self, population: List[FacadeIndividual], 
                          evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """
        多样性维持机制
        
        Args:
            population: 当前种群
            evaluation_results: 评估结果
            
        Returns:
            维持多样性后的种群和评估结果
        """
        if not self.diversity_enabled:
            return population, evaluation_results
            
        try:
            # 计算当前种群多样性
            self.current_diversity = self._calculate_population_diversity(evaluation_results)
            self.diversity_history.append(self.current_diversity)
            
            # 检查是否需要多样性增强
            if self.current_diversity < self.min_diversity_threshold:
                self.low_diversity_generations += 1
                
                if self.low_diversity_generations >= self.restart_trigger_generations:
                    # 执行多样性重启
                    return self._diversity_restart(population, evaluation_results)
                else:
                    # 执行多样性增强
                    return self._enhance_diversity(population, evaluation_results)
            else:
                self.low_diversity_generations = 0
                # 更新精英解
                self._update_elite_solutions(evaluation_results)
            
            return population, evaluation_results
            
        except Exception as e:
            self.logger.warning(f"多样性维持失败: {str(e)}")
            return population, evaluation_results
    
    def _calculate_population_diversity(self, evaluation_results: List[ObjectiveResults]) -> float:
        """计算种群多样性指标"""
        try:
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            if len(feasible_results) < 2:
                return 1.0
            
            # 提取目标值
            objectives = []
            for result in feasible_results:
                objectives.append([
                    result.energy_consumption,
                    result.thermal_performance,
                    result.renovation_cost
                ])
            
            objectives = np.array(objectives)
            
            # 标准化目标值
            if len(objectives) > 1:
                obj_ranges = objectives.max(axis=0) - objectives.min(axis=0)
                obj_ranges[obj_ranges == 0] = 1  # 避免除零
                objectives_normalized = (objectives - objectives.min(axis=0)) / obj_ranges
            else:
                objectives_normalized = objectives
            
            # 计算平均距离作为多样性指标
            total_distance = 0.0
            pair_count = 0
            
            for i in range(len(objectives_normalized)):
                for j in range(i + 1, len(objectives_normalized)):
                    distance = np.linalg.norm(objectives_normalized[i] - objectives_normalized[j])
                    total_distance += distance
                    pair_count += 1
            
            diversity = total_distance / pair_count if pair_count > 0 else 0.0
            return min(diversity, 1.0)  # 限制在[0,1]范围内
            
        except Exception as e:
            self.logger.warning(f"计算种群多样性失败: {str(e)}")
            return 0.5
    
    def _enhance_diversity(self, population: List[FacadeIndividual], 
                          evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """多样性增强 - 替换部分相似个体"""
        try:
            # 保留精英解
            elite_count = max(1, int(len(population) * self.elite_preservation_ratio))
            feasible_results = [(i, r) for i, r in enumerate(evaluation_results) if r.is_feasible]
            
            if feasible_results:
                # 按适应度排序选择精英
                feasible_results.sort(key=lambda x: x[1].energy_consumption)
                elite_indices = [idx for idx, _ in feasible_results[:elite_count]]
            else:
                elite_indices = list(range(min(elite_count, len(population))))
            
            # 计算需要替换的个体数量
            replacement_count = int(len(population) * self.diversity_enhancement_rate)
            
            # 选择最相似的个体进行替换
            similarity_scores = self._calculate_similarity_scores(evaluation_results)
            
            # 排除精英解，选择最相似的个体
            replacement_candidates = []
            for i, score in enumerate(similarity_scores):
                if i not in elite_indices:
                    replacement_candidates.append((i, score))
            
            replacement_candidates.sort(key=lambda x: x[1], reverse=True)  # 相似度高的优先替换
            replacement_indices = [idx for idx, _ in replacement_candidates[:replacement_count]]
            
            # 生成新的多样化个体
            new_population = list(population)
            new_evaluations = list(evaluation_results)
            
            for idx in replacement_indices:
                # 生成新个体
                new_individual = self._generate_diverse_individual()
                if new_individual:
                    new_population[idx] = new_individual
                    # 评估新个体
                    new_result = self.objective_evaluator.evaluate_objectives(new_individual)
                    new_evaluations[idx] = new_result
            
            self.logger.debug(f"多样性增强: 替换了 {len(replacement_indices)} 个相似个体")
            return new_population, new_evaluations
            
        except Exception as e:
            self.logger.warning(f"多样性增强失败: {str(e)}")
            return population, evaluation_results
    
    def _diversity_restart(self, population: List[FacadeIndividual], 
                          evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """多样性重启 - 重新初始化大部分种群"""
        try:
            # 保留最优解
            elite_count = max(2, int(len(population) * self.elite_preservation_ratio))
            feasible_results = [(i, r) for i, r in enumerate(evaluation_results) if r.is_feasible]
            
            elite_indices = []
            if feasible_results:
                feasible_results.sort(key=lambda x: x[1].energy_consumption)
                elite_indices = [idx for idx, _ in feasible_results[:elite_count]]
            else:
                elite_indices = list(range(min(elite_count, len(population))))
            
            # 重新初始化其他个体
            new_population = list(population)
            new_evaluations = list(evaluation_results)
            
            restart_count = 0
            for i in range(len(population)):
                if i not in elite_indices:
                    new_individual = self._generate_diverse_individual()
                    if new_individual:
                        new_population[i] = new_individual
                        # 评估新个体
                        new_result = self.objective_evaluator.evaluate_objectives(new_individual)
                        new_evaluations[i] = new_result
                        restart_count += 1
            
            # 重置多样性计数
            self.low_diversity_generations = 0
            
            self.logger.info(f"多样性重启: 重新初始化了 {restart_count} 个个体，保留 {len(elite_indices)} 个精英解")
            return new_population, new_evaluations
            
        except Exception as e:
            self.logger.warning(f"多样性重启失败: {str(e)}")
            return population, evaluation_results
    
    def _calculate_similarity_scores(self, evaluation_results: List[ObjectiveResults]) -> List[float]:
        """计算每个个体与种群的相似度分数"""
        try:
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            if len(feasible_results) < 2:
                return [0.0] * len(evaluation_results)
            
            # 提取目标值
            objectives = []
            for result in evaluation_results:
                if result.is_feasible:
                    objectives.append([
                        result.energy_consumption,
                        result.thermal_performance,
                        result.renovation_cost
                    ])
                else:
                    # 不可行解设置为平均值
                    avg_energy = np.mean([r.energy_consumption for r in feasible_results])
                    avg_thermal = np.mean([r.thermal_performance for r in feasible_results])
                    avg_cost = np.mean([r.renovation_cost for r in feasible_results])
                    objectives.append([avg_energy, avg_thermal, avg_cost])
            
            objectives = np.array(objectives)
            
            # 标准化
            if len(objectives) > 1:
                obj_ranges = objectives.max(axis=0) - objectives.min(axis=0)
                obj_ranges[obj_ranges == 0] = 1
                objectives_norm = (objectives - objectives.min(axis=0)) / obj_ranges
            else:
                objectives_norm = objectives
            
            # 计算每个个体的相似度分数
            similarity_scores = []
            for i in range(len(objectives_norm)):
                total_similarity = 0.0
                for j in range(len(objectives_norm)):
                    if i != j:
                        distance = np.linalg.norm(objectives_norm[i] - objectives_norm[j])
                        similarity = 1.0 / (1.0 + distance)  # 距离越小相似度越高
                        total_similarity += similarity
                
                avg_similarity = total_similarity / (len(objectives_norm) - 1) if len(objectives_norm) > 1 else 0.0
                similarity_scores.append(avg_similarity)
            
            return similarity_scores
            
        except Exception as e:
            self.logger.warning(f"计算相似度分数失败: {str(e)}")
            return [0.5] * len(evaluation_results)
    
    def _generate_diverse_individual(self) -> Optional[FacadeIndividual]:
        """生成多样化的新个体"""
        try:
            # 使用个体编码器生成新个体
            new_individual = self.individual_encoder.create_random_individual()
            
            # 应用多样化变异
            mutated_individual = self.mutate_individual(new_individual, mutation_strength=0.8)
            
            return mutated_individual
            
        except Exception as e:
            self.logger.warning(f"生成多样化个体失败: {str(e)}")
            return None
    
    def _update_elite_solutions(self, evaluation_results: List[ObjectiveResults]):
        """更新精英解集合"""
        try:
            feasible_results = [r for r in evaluation_results if r.is_feasible]
            if not feasible_results:
                return
            
            # 选择当前代的最优解
            current_best = min(feasible_results, key=lambda r: r.energy_consumption)
            
            # 更新精英解集合
            if not self.elite_solutions:
                self.elite_solutions.append(current_best)
            else:
                # 检查是否比现有精英解更好
                existing_best = min(self.elite_solutions, key=lambda r: r.energy_consumption)
                if current_best.energy_consumption < existing_best.energy_consumption:
                    self.elite_solutions.append(current_best)
                    
                    # 限制精英解数量
                    if len(self.elite_solutions) > 10:
                        self.elite_solutions.sort(key=lambda r: r.energy_consumption)
                        self.elite_solutions = self.elite_solutions[:10]
            
        except Exception as e:
            self.logger.warning(f"更新精英解失败: {str(e)}")

    def _inject_diversity(self, population: List[FacadeIndividual],
                         evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """
        强制多样性注入 - 替换部分个体为高度随机个体
        """
        try:
            # 计算需要替换的个体数量（5-10%）
            num_replace = max(1, int(len(population) * 0.08))

            # 选择要替换的个体（选择最相似的个体）
            diversity_scores = []
            for i, result in enumerate(evaluation_results):
                # 计算与其他个体的平均距离
                distances = []
                for j, other_result in enumerate(evaluation_results):
                    if i != j:
                        dist = self._calculate_objective_distance(result, other_result)
                        distances.append(dist)
                avg_distance = np.mean(distances) if distances else 0
                diversity_scores.append((i, avg_distance))

            # 选择多样性最低的个体进行替换
            diversity_scores.sort(key=lambda x: x[1])
            replace_indices = [idx for idx, _ in diversity_scores[:num_replace]]

            # 生成高度随机的替换个体
            new_population = population.copy()
            new_evaluations = evaluation_results.copy()

            for idx in replace_indices:
                # 生成完全随机的个体
                random_individual = self.individual_encoder.generate_random_individual()
                random_individual.individual_id = f"diversity_inject_{self.generation_count}_{idx}"

                # 应用极强变异
                random_individual = self.individual_encoder.apply_extreme_randomization(random_individual)

                new_population[idx] = random_individual
                # 评估结果将在下一轮评估中更新

            self.opt_logger.info(f"多样性注入: 替换了{num_replace}个个体")
            return new_population, new_evaluations

        except Exception as e:
            self.logger.warning(f"多样性注入失败: {str(e)}")
            return population, evaluation_results

    def _calculate_objective_distance(self, result1: ObjectiveResults, result2: ObjectiveResults) -> float:
        """计算两个目标结果之间的欧几里得距离"""
        try:
            obj1 = [result1.energy_consumption, result1.thermal_performance, result1.renovation_cost]
            obj2 = [result2.energy_consumption, result2.thermal_performance, result2.renovation_cost]

            # 归一化
            obj1_norm = np.array(obj1) / (np.array(obj1) + 1e-10)
            obj2_norm = np.array(obj2) / (np.array(obj2) + 1e-10)

            return np.linalg.norm(obj1_norm - obj2_norm)
        except:
            return 0.0

    def _calculate_crowding_distances(self, objectives: List[List[float]]) -> List[float]:
        """计算拥挤距离"""
        try:
            n = len(objectives)
            if n <= 2:
                return [float('inf')] * n
            
            distances = [0.0] * n
            objectives_array = np.array(objectives)
            
            # 对每个目标函数计算拥挤距离
            for m in range(objectives_array.shape[1]):
                # 按第m个目标排序
                sorted_indices = np.argsort(objectives_array[:, m])
                
                # 边界个体设为无穷大
                distances[sorted_indices[0]] = float('inf')
                distances[sorted_indices[-1]] = float('inf')
                
                # 计算中间个体的拥挤距离
                obj_range = objectives_array[sorted_indices[-1], m] - objectives_array[sorted_indices[0], m]
                if obj_range > 1e-10:
                    for i in range(1, n - 1):
                        idx = sorted_indices[i]
                        if distances[idx] != float('inf'):
                            distance_contribution = (objectives_array[sorted_indices[i + 1], m] - 
                                                   objectives_array[sorted_indices[i - 1], m]) / obj_range
                            distances[idx] += distance_contribution
            
            return distances
            
        except Exception:
            return [1.0] * len(objectives)
    
    def _normalize_objectives(self, objective_results: List[ObjectiveResults]) -> List[List[float]]:
        """归一化目标函数值"""
        try:
            objectives_matrix = []
            for obj in objective_results:
                objectives_matrix.append([
                    obj.energy_consumption,
                    obj.thermal_performance,
                    obj.renovation_cost
                ])
            
            objectives_array = np.array(objectives_matrix)
            
            # 找到理想点和最差点
            ideal_point = np.min(objectives_array, axis=0)
            nadir_point = np.max(objectives_array, axis=0)
            
            # 归一化
            normalized = []
            for obj in objectives_array:
                normalized_obj = []
                for i in range(len(obj)):
                    if nadir_point[i] - ideal_point[i] > 1e-10:
                        norm_val = (obj[i] - ideal_point[i]) / (nadir_point[i] - ideal_point[i])
                    else:
                        norm_val = 0.0
                    normalized_obj.append(norm_val)
                normalized.append(normalized_obj)
            
            return normalized
            
        except Exception as e:
            self.logger.error(f"目标函数归一化失败: {str(e)}")
            # 返回原始值作为备选
            return [[obj.energy_consumption, obj.thermal_performance, obj.renovation_cost] 
                   for obj in objective_results]
    
    def _calculate_perpendicular_distance(self, objective: List[float], 
                                        reference_point: List[float]) -> float:
        """计算目标点到参考点的垂直距离"""
        try:
            obj_array = np.array(objective)
            ref_array = np.array(reference_point)
            
            # 计算投影长度
            dot_product = np.dot(obj_array, ref_array)
            ref_norm_sq = np.dot(ref_array, ref_array)
            
            if ref_norm_sq > 1e-10:
                projection_length = dot_product / ref_norm_sq
                projection = projection_length * ref_array
                perpendicular = obj_array - projection
                distance = np.linalg.norm(perpendicular)
            else:
                distance = np.linalg.norm(obj_array)
            
            return distance
            
        except Exception:
            return np.linalg.norm(np.array(objective) - np.array(reference_point))
    
    def _tournament_selection(self, population: List[FacadeIndividual],
                            evaluation_results: List[ObjectiveResults]) -> FacadeIndividual:
        """锦标赛选择"""
        tournament_indices = random.sample(range(len(population)), 
                                         min(self.params.tournament_size, len(population)))
        
        best_idx = tournament_indices[0]
        best_result = evaluation_results[best_idx]
        
        for idx in tournament_indices[1:]:
            current_result = evaluation_results[idx]
            if self._is_better_solution(current_result, best_result):
                best_idx = idx
                best_result = current_result
        
        return population[best_idx]
    
    def _is_better_solution(self, result1: ObjectiveResults, result2: ObjectiveResults) -> bool:
        """判断解1是否优于解2"""
        # 首先比较约束违反
        if result1.constraint_violations < result2.constraint_violations:
            return True
        elif result1.constraint_violations > result2.constraint_violations:
            return False
        
        # 在可行域内，使用支配关系或聚合目标函数
        if self._dominates(result1, result2):
            return True
        elif self._dominates(result2, result1):
            return False
        else:
            # 非支配情况下，使用加权和
            score1 = (result1.energy_consumption * 0.4 + 
                     result1.thermal_performance * 0.3 + 
                     result1.renovation_cost * 0.3 / 100000)  # 成本归一化
            score2 = (result2.energy_consumption * 0.4 + 
                     result2.thermal_performance * 0.3 + 
                     result2.renovation_cost * 0.3 / 100000)
            return score1 < score2
    
    def _generate_reference_points(self) -> List[List[float]]:
        """生成NSGA-III参考点 - 大幅增加参考点密度"""
        try:
            num_objectives = 3  # 三个目标函数
            num_divisions = 20  # 大幅增加分割数以获得更多参考点和更好的多样性

            reference_points = []

            # 生成均匀分布的参考点
            for i in range(num_divisions + 1):
                for j in range(num_divisions + 1 - i):
                    k = num_divisions - i - j
                    point = [i / num_divisions, j / num_divisions, k / num_divisions]
                    reference_points.append(point)
            
            # 添加更多边界和中间参考点以增加多样性
            boundary_points = [
                [1.0, 0.0, 0.0],  # 只关注能耗
                [0.0, 1.0, 0.0],  # 只关注热工性能
                [0.0, 0.0, 1.0],  # 只关注成本
                [0.7, 0.3, 0.0],  # 偏重能耗
                [0.3, 0.7, 0.0],  # 偏重热工性能
                [0.7, 0.0, 0.3],  # 偏重能耗和成本
                [0.3, 0.0, 0.7],  # 偏重成本
                [0.0, 0.7, 0.3],  # 偏重热工性能和成本
                [0.0, 0.3, 0.7],  # 偏重成本
                [0.5, 0.5, 0.0],  # 平衡能耗和热工性能
                [0.5, 0.0, 0.5],  # 平衡能耗和成本
                [0.0, 0.5, 0.5],  # 平衡热工性能和成本
                [1/3, 1/3, 1/3],  # 三目标平衡
                [0.6, 0.2, 0.2],  # 多种权重组合
                [0.2, 0.6, 0.2],
                [0.2, 0.2, 0.6],
                [0.4, 0.4, 0.2],
                [0.4, 0.2, 0.4],
                [0.2, 0.4, 0.4]
            ]
            
            reference_points.extend(boundary_points)

            # 添加随机参考点以进一步增强多样性
            random_points = []
            for _ in range(50):  # 添加50个随机参考点
                # 生成随机权重并归一化
                weights = [random.random() for _ in range(num_objectives)]
                total = sum(weights)
                normalized_weights = [w / total for w in weights]
                random_points.append(normalized_weights)

            reference_points.extend(random_points)

            self.opt_logger.info(f"生成参考点: {len(reference_points)} 个 (包含{len(random_points)}个随机点)")
            return reference_points
            
        except Exception as e:
            self.logger.error(f"生成参考点失败: {str(e)}")
            # 返回默认参考点
            return [[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]]
    
    def _calculate_population_statistics(self, population: List[FacadeIndividual],
                                       evaluation_results: List[ObjectiveResults]) -> PopulationStatistics:
        """计算种群统计信息"""
        try:
            feasible_solutions = []
            infeasible_solutions = []
            
            for result in evaluation_results:
                if result.is_feasible:
                    feasible_solutions.append(result)
                else:
                    infeasible_solutions.append(result)
            
            # 计算平均目标值
            if evaluation_results:
                avg_energy = np.mean([r.energy_consumption for r in evaluation_results])
                avg_thermal = np.mean([r.thermal_performance for r in evaluation_results])
                avg_cost = np.mean([r.renovation_cost for r in evaluation_results])
                avg_violation = np.mean([r.constraint_violations for r in evaluation_results])
            else:
                avg_energy = avg_thermal = avg_cost = avg_violation = 0.0
            
            # 计算多样性指标
            diversity = self._calculate_population_diversity(evaluation_results)
            
            return PopulationStatistics(
                generation=self.generation_count,
                population_size=len(population),
                feasible_count=len(feasible_solutions),
                infeasible_count=len(infeasible_solutions),
                average_objectives=[avg_energy, avg_thermal, avg_cost],
                average_constraint_violation=avg_violation,
                diversity_metric=diversity,
                best_feasible_solution=min(feasible_solutions, 
                                         key=lambda x: x.energy_consumption) if feasible_solutions else None
            )
            
        except Exception as e:
            self.logger.error(f"计算种群统计失败: {str(e)}")
            return PopulationStatistics(
                generation=self.generation_count,
                population_size=len(population),
                feasible_count=0,
                infeasible_count=len(population),
                average_objectives=[0.0, 0.0, 0.0],
                average_constraint_violation=1.0,
                diversity_metric=0.0,
                best_feasible_solution=None
            )
    
    def _calculate_population_diversity(self, evaluation_results: List[ObjectiveResults]) -> float:
        """计算种群多样性"""
        try:
            if len(evaluation_results) < 2:
                return 0.0
            
            # 基于目标空间的多样性计算
            objectives_matrix = np.array([
                [r.energy_consumption, r.thermal_performance, r.renovation_cost]
                for r in evaluation_results
            ])
            
            # 计算所有个体间的平均距离
            total_distance = 0.0
            count = 0
            
            for i in range(len(objectives_matrix)):
                for j in range(i + 1, len(objectives_matrix)):
                    distance = np.linalg.norm(objectives_matrix[i] - objectives_matrix[j])
                    total_distance += distance
                    count += 1
            
            return total_distance / count if count > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def get_population_statistics(self) -> List[PopulationStatistics]:
        """获取种群统计历史"""
        return self.population_history.copy()
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.generation_count = 0
        self.population_history.clear()
        self.evaluation_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
    
    def _get_individual_cache_key(self, individual: FacadeIndividual) -> str:
        """生成个体的缓存键"""
        try:
            # 基于个体的主要属性生成哈希键
            key_components = []
            
            if hasattr(individual, 'window_types'):
                key_components.append(f"wt:{','.join(map(str, individual.window_types))}")
            
            if hasattr(individual, 'wall_configurations'):
                key_components.append(f"wc:{','.join(map(str, individual.wall_configurations))}")
            
            if hasattr(individual, 'frame_depths'):
                key_components.append(f"fd:{','.join(f'{x:.3f}' for x in individual.frame_depths)}")
            
            if hasattr(individual, 'shading_depths'):
                key_components.append(f"sd:{','.join(f'{x:.3f}' for x in individual.shading_depths)}")
            
            if hasattr(individual, 'shading_angles'):
                key_components.append(f"sa:{','.join(f'{x:.1f}' for x in individual.shading_angles)}")
            
            return "|".join(key_components)
            
        except Exception:
            # 如果生成键失败，返回个体ID
            return individual.individual_id
    
    def _get_cached_evaluation(self, cache_key: str) -> Optional[ObjectiveResults]:
        """获取缓存的评估结果"""
        if cache_key in self.evaluation_cache:
            self.cache_hits += 1
            return self.evaluation_cache[cache_key]
        else:
            self.cache_misses += 1
            return None
    
    def _cache_evaluation(self, cache_key: str, result: ObjectiveResults) -> None:
        """缓存评估结果"""
        # 限制缓存大小，避免内存溢出
        if len(self.evaluation_cache) > 1000:
            # 清除最旧的一半缓存
            keys_to_remove = list(self.evaluation_cache.keys())[:500]
            for key in keys_to_remove:
                del self.evaluation_cache[key]
        
        self.evaluation_cache[cache_key] = result
    
    def get_cache_statistics(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': hit_rate,
            'cache_size': len(self.evaluation_cache)
        }


    def _create_smart_real_data_individual(self, individual_id: str, seed: int):
        """
        第二阶段优化：创建基于真实数据的智能个体
        在真实数据基础上应用小幅优化调整
        """
        try:
            # 使用真实数据创建基础个体
            individual = self.individual_encoder.create_individual_from_real_data(individual_id)
            
            # 应用智能优化调整
            individual = self._apply_smart_optimizations(individual, seed)
            
            return individual
        except Exception as e:
            self.opt_logger.warning(f"智能真实数据个体创建失败: {str(e)}")
            return self._create_truly_random_individual(individual_id, seed)
    
    def _create_enhanced_heuristic_individual(self, individual_id: str):
        """
        第二阶段优化：创建增强启发式个体
        应用更多启发式规则提高解质量
        """
        try:
            # 创建随机个体作为基础
            individual = self.individual_encoder.create_random_individual(individual_id)
            
            # 应用增强启发式规则
            
            # 规则1: 优化窗墙比到最佳范围 (0.35-0.55)
            total_window_area = sum(w * h for w, h in individual.window_sizes)
            facade_area = self._estimate_facade_area()
            current_ratio = total_window_area / facade_area if facade_area > 0 else 0
            
            if current_ratio < 0.35 or current_ratio > 0.55:
                target_ratio = 0.45  # 最佳窗墙比
                scale_factor = (target_ratio * facade_area / total_window_area) ** 0.5 if total_window_area > 0 else 1.0
                individual.window_sizes = [(max(0.6, w * scale_factor), max(0.8, h * scale_factor)) 
                                         for w, h in individual.window_sizes]
            
            # 规则2: 智能窗户类型选择
            for i in range(len(individual.window_types)):
                window_area = individual.window_sizes[i][0] * individual.window_sizes[i][1]
                if window_area > 2.0:  # 大窗户优先节能
                    individual.window_types[i] = 1
                elif window_area < 1.0:  # 小窗户考虑成本
                    individual.window_types[i] = 0 if random.random() < 0.6 else 1
                else:  # 中等窗户平衡选择
                    individual.window_types[i] = random.choice([0, 1, 2])
            
            return individual
            
        except Exception as e:
            self.opt_logger.warning(f"增强启发式个体创建失败: {str(e)}")
            return self._create_heuristic_individual(individual_id)
    
    def _create_objective_oriented_individual(self, individual_id: str, target_objective: str):
        """
        第二阶段优化：创建目标导向个体
        更精确地针对特定目标进行优化
        """
        try:
            individual = self.individual_encoder.create_random_individual(individual_id)
            
            if target_objective == 'energy':
                # 能耗导向：极致节能配置
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 1  # 全部节能窗户
                    individual.frame_depths[i] = random.uniform(0.25, 0.4)  # 深框架增强隔热
                    individual.shading_depths[i] = random.uniform(0.3, 0.6)  # 适度遮阳
                    individual.shading_angles[i] = random.uniform(35, 55)  # 优化遮阳角度
                    
            elif target_objective == 'thermal':
                # 热工导向：最大化热舒适性
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 2  # 全部遮阳窗户
                    individual.shading_depths[i] = random.uniform(0.6, 1.2)  # 深遮阳控制热辐射
                    individual.shading_angles[i] = random.uniform(20, 40)  # 浅角度优化采光
                    
            elif target_objective == 'cost':
                # 成本导向：极致成本控制
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 0  # 全部普通窗户
                    individual.frame_depths[i] = 0.0  # 无框架
                    individual.shading_depths[i] = 0.0  # 无遮阳
            
            return individual
            
        except Exception as e:
            self.opt_logger.warning(f"目标导向个体创建失败: {str(e)}")
            return self._create_extreme_individual(individual_id, target_objective)
    
    def _apply_smart_optimizations(self, individual, seed: int):
        """应用智能优化调整"""
        try:
            random.seed(seed + 2000)  # 确保可重现性
            
            # 微调窗户位置避免重叠
            for i in range(len(individual.window_positions)):
                x, y = individual.window_positions[i]
                offset_x = random.uniform(-0.05, 0.05)
                offset_y = random.uniform(-0.05, 0.05)
                individual.window_positions[i] = (x + offset_x, y + offset_y)
            
            return individual
            
        except Exception as e:
            self.opt_logger.warning(f"智能优化调整失败: {str(e)}")
            return individual
    
    def _estimate_facade_area(self) -> float:
        """估算立面面积"""
        try:
            facade_bounds = self.individual_encoder.facade_bounds
            width = facade_bounds['max_x'] - facade_bounds['min_x']
            height = facade_bounds['max_y'] - facade_bounds['min_y']
            return max(10.0, width * height)  # 最小10平方米
        except:
            return 50.0  # 默认50平方米


def create_genetic_operators(individual_encoder: IndividualEncoder,
                           objective_evaluator: ObjectiveFunctionEvaluator,
                           constraint_handler: ConstraintHandler) -> GeneticOperators:
    """
    创建遗传算法操作器实例
    
    Args:
        individual_encoder: 个体编码器
        objective_evaluator: 目标函数评估器
        constraint_handler: 约束处理器
        
    Returns:
        配置好的遗传算法操作器
    """
    return GeneticOperators(individual_encoder, objective_evaluator, constraint_handler)