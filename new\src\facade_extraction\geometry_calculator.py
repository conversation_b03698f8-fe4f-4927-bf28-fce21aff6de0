"""
几何参数计算器 - 重构版本
基于现有geometry_calculator模块，增强功能并提供更精确的计算
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Any, Union, Optional
from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import GeometryCalculationError, handle_exception
from ..core.data_structures import FacadeElements, BuildingElement, ElementType
from ..core.utils import MathUtils, safe_divide


class GeometryCalculator:
    """
    几何参数计算器
    
    功能增强:
    1. 精确的窗墙比计算
    2. 多种开口率计算方法
    3. 立面密度分析
    4. 空间分布特征
    5. 几何形状特征分析
    """
    
    def __init__(self):
        """初始化几何计算器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取配置参数 - 禁止使用默认模拟值
        image_config = self.config.get_section('image_processing')
        self.pixel_to_meter_ratio = image_config.get('pixel_to_meter_ratio')
        if self.pixel_to_meter_ratio is None:
            # 临时使用默认值以保持兼容性，但记录警告
            self.pixel_to_meter_ratio = 0.01
            self.logger.warning("未设置 pixel_to_meter_ratio，使用临时默认值 0.01。建议在配置中明确设置真实测量值")
        
        if self.pixel_to_meter_ratio <= 0:
            raise GeometryCalculationError(f"pixel_to_meter_ratio 必须为正数，当前值: {self.pixel_to_meter_ratio}")
        
        # 几何分析参数
        self.min_window_size = 0.5  # 最小窗户尺寸（米）
        self.min_wall_area = 1.0    # 最小墙体面积（平方米）
        
        self.logger.info("几何参数计算器初始化完成")
    
    @handle_exception
    def calculate_facade_parameters(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """
        计算立面几何参数
        
        Args:
            facade_elements: 立面元素数据
            
        Returns:
            完整的几何参数字典
            
        Raises:
            GeometryCalculationError: 计算失败时抛出
        """
        with LogContext("立面几何参数计算", self.logger):
            try:
                # 基础参数计算
                basic_params = self._calculate_basic_parameters(facade_elements)
                
                # 窗墙比计算
                window_wall_params = self._calculate_window_wall_ratio(facade_elements)
                
                # 开口率计算
                opening_params = self._calculate_opening_ratios(facade_elements)
                
                # 密度参数计算
                density_params = self._calculate_density_parameters(facade_elements)
                
                # 分布特征计算
                distribution_params = self._calculate_distribution_features(facade_elements)
                
                # 形状特征计算
                shape_params = self._calculate_shape_features(facade_elements)
                
                # 空间关系计算
                spatial_params = self._calculate_spatial_relationships(facade_elements)
                
                # 整合所有参数
                geometry_params = {
                    **basic_params,
                    **window_wall_params,
                    **opening_params,
                    **density_params,
                    **distribution_params,
                    **shape_params,
                    **spatial_params,
                    'calculation_metadata': {
                        'pixel_to_meter_ratio': self.pixel_to_meter_ratio,
                        'image_shape': facade_elements.image_shape,
                        'total_elements': len(facade_elements.get_all_elements())
                    }
                }
                
                self.logger.info(f"几何参数计算完成，窗墙比: {geometry_params.get('window_wall_ratio', 0):.3f}")
                
                return geometry_params
                
            except Exception as e:
                raise GeometryCalculationError(f"立面几何参数计算失败: {str(e)}") from e
    
    def _calculate_basic_parameters(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """计算基础参数"""
        basic_params = {
            'element_counts': {
                'walls': len(facade_elements.walls),
                'windows': len(facade_elements.windows), 
                'doors': len(facade_elements.doors),
                'shading': len(facade_elements.shading),
                'frames': len(facade_elements.frames),
                'total': len(facade_elements.get_all_elements())
            },
            'total_areas': {},
            'average_areas': {},
            'area_ratios': {}
        }
        
        # 计算各类型元素的总面积和平均面积
        total_facade_area = 0
        
        for element_type in ElementType:
            elements = facade_elements.get_elements_by_type(element_type)
            if elements:
                areas = [elem.area for elem in elements]
                basic_params['total_areas'][element_type.value] = sum(areas)
                basic_params['average_areas'][element_type.value] = np.mean(areas)
                total_facade_area += sum(areas)
        
        # 计算面积比例
        if total_facade_area > 0:
            for element_type in ElementType:
                area = basic_params['total_areas'].get(element_type.value, 0)
                basic_params['area_ratios'][element_type.value] = area / total_facade_area
        
        basic_params['total_facade_area'] = total_facade_area
        
        return basic_params
    
    def _calculate_window_wall_ratio(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """计算窗墙比相关参数"""
        try:
            window_params = {
                'window_wall_ratio': 0.0,
                'window_wall_ratio_calculation_method': 'none',
                'wall_area_estimation_method': 'none',
                'window_coverage': 0.0
            }
            
            # 计算窗户总面积
            window_area = sum(w.area for w in facade_elements.windows)
            wall_area = sum(w.area for w in facade_elements.walls)
            
            if len(facade_elements.windows) == 0:
                window_params['window_wall_ratio_calculation_method'] = 'no_windows'
                return window_params
            
            # 策略1: 直接墙体面积计算
            if wall_area > 0:
                window_params['window_wall_ratio'] = safe_divide(window_area, wall_area)
                window_params['window_wall_ratio_calculation_method'] = 'direct_wall_area'
                window_params['wall_area_estimation_method'] = 'detected_walls'
                
            # 策略2: 基于窗户分布估算立面面积
            elif len(facade_elements.windows) > 0:
                estimated_wall_area = self._estimate_facade_area_from_windows(facade_elements.windows)
                net_wall_area = estimated_wall_area - window_area
                
                if net_wall_area > 0:
                    window_params['window_wall_ratio'] = safe_divide(window_area, net_wall_area)
                    window_params['window_wall_ratio_calculation_method'] = 'estimated_facade_area'
                    window_params['wall_area_estimation_method'] = 'window_distribution_based'
                    window_params['estimated_facade_area'] = estimated_wall_area
                    window_params['estimated_wall_area'] = net_wall_area
            
            # 策略3: 基于实际检测到的所有建筑元素计算立面面积
            if window_params['window_wall_ratio'] == 0.0:
                # 计算所有检测到的建筑元素的总面积
                all_elements = facade_elements.get_all_elements()
                if all_elements:
                    total_detected_area = sum(element.area for element in all_elements)
                    
                    # 使用检测到的总面积作为立面面积
                    if total_detected_area > 0:
                        # 从总检测面积中减去窗户面积得到墙体面积
                        net_wall_area = total_detected_area - window_area
                        
                        if net_wall_area > 0:
                            window_params['window_wall_ratio'] = safe_divide(window_area, net_wall_area)
                            window_params['window_wall_ratio_calculation_method'] = 'detected_elements_based'
                            window_params['wall_area_estimation_method'] = 'detected_elements'
                            window_params['total_detected_area'] = total_detected_area
                            window_params['estimated_facade_area'] = total_detected_area
            
            # 计算窗户覆盖率（相对于检测到的立面）
            if 'estimated_facade_area' in window_params:
                window_params['window_coverage'] = safe_divide(
                    window_area, window_params['estimated_facade_area'])
            
            return window_params
            
        except Exception as e:
            self.logger.error(f"窗墙比计算失败: {str(e)}")
            return {'window_wall_ratio': 0.0, 'error': str(e)}
    
    def _estimate_facade_area_from_windows(self, windows: List[BuildingElement]) -> float:
        """基于窗户分布估算立面面积"""
        if not windows:
            return 0.0
        
        # 找到窗户的边界
        min_x = min(w.bbox[0] for w in windows)
        min_y = min(w.bbox[1] for w in windows)
        max_x = max(w.bbox[2] for w in windows)
        max_y = max(w.bbox[3] for w in windows)
        
        # 计算窗户的平均尺寸作为边距参考
        avg_width = np.mean([w.width for w in windows])
        avg_height = np.mean([w.height for w in windows])
        
        # 智能边距计算
        horizontal_margin = max(avg_width * 0.5, 0.5)  # 至少0.5米
        vertical_margin = max(avg_height * 0.3, 0.3)   # 至少0.3米
        
        # 估算立面尺寸
        facade_width = (max_x - min_x) * self.pixel_to_meter_ratio + 2 * horizontal_margin
        facade_height = (max_y - min_y) * self.pixel_to_meter_ratio + 2 * vertical_margin
        
        return facade_width * facade_height
    
    def _calculate_opening_ratios(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """计算各种开口率"""
        opening_params = {
            'window_opening_ratio': 0.0,
            'door_opening_ratio': 0.0,
            'total_opening_ratio': 0.0,
            'shading_ratio': 0.0
        }
        
        try:
            # 估算总立面面积
            total_facade_area = self._estimate_total_facade_area(facade_elements)
            
            if total_facade_area > 0:
                window_area = sum(w.area for w in facade_elements.windows)
                door_area = sum(d.area for d in facade_elements.doors)
                shading_area = sum(s.area for s in facade_elements.shading)
                
                opening_params['window_opening_ratio'] = safe_divide(window_area, total_facade_area)
                opening_params['door_opening_ratio'] = safe_divide(door_area, total_facade_area)
                opening_params['total_opening_ratio'] = safe_divide(
                    window_area + door_area, total_facade_area)
                opening_params['shading_ratio'] = safe_divide(shading_area, total_facade_area)
                opening_params['estimated_facade_area'] = total_facade_area
            
            return opening_params
            
        except Exception as e:
            self.logger.error(f"开口率计算失败: {str(e)}")
            return opening_params
    
    def _estimate_total_facade_area(self, facade_elements: FacadeElements) -> float:
        """估算总立面面积"""
        # 方法1: 基于检测到的墙体面积
        wall_area = sum(w.area for w in facade_elements.walls)
        if wall_area > 0:
            return wall_area
        
        # 方法2: 基于窗户分布估算
        if facade_elements.windows:
            return self._estimate_facade_area_from_windows(facade_elements.windows)
        
        # 方法3: 基于图像尺寸
        if facade_elements.image_shape[0] > 0:
            image_height, image_width = facade_elements.image_shape[:2]
            return (image_height * image_width * 
                   (facade_elements.pixel_to_meter_ratio ** 2) * 0.8)
        
        return 0.0
    
    def _calculate_density_parameters(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """计算密度参数"""
        density_params = {
            'window_density': 0.0,
            'element_density': 0.0,
            'area_efficiency': 0.0
        }
        
        try:
            total_facade_area = self._estimate_total_facade_area(facade_elements)
            
            if total_facade_area > 0:
                window_count = len(facade_elements.windows)
                total_elements = len(facade_elements.get_all_elements())
                
                # 窗户密度 (个/m²)
                density_params['window_density'] = safe_divide(window_count, total_facade_area)
                
                # 总元素密度 (个/m²)
                density_params['element_density'] = safe_divide(total_elements, total_facade_area)
                
                # 面积利用效率
                used_area = sum(elem.area for elem in facade_elements.get_all_elements())
                density_params['area_efficiency'] = safe_divide(used_area, total_facade_area)
            
            return density_params
            
        except Exception as e:
            self.logger.error(f"密度参数计算失败: {str(e)}")
            return density_params
    
    def _calculate_distribution_features(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """计算分布特征"""
        distribution_params = {
            'window_distribution': {},
            'vertical_distribution': {},
            'horizontal_distribution': {},
            'clustering_metrics': {}
        }
        
        try:
            if not facade_elements.windows:
                return distribution_params
            
            # 提取窗户中心点
            window_centers = [w.center for w in facade_elements.windows]
            x_coords = [c[0] for c in window_centers]
            y_coords = [c[1] for c in window_centers]
            
            # 水平分布分析
            distribution_params['horizontal_distribution'] = {
                'mean_x': np.mean(x_coords) * self.pixel_to_meter_ratio,
                'std_x': np.std(x_coords) * self.pixel_to_meter_ratio,
                'range_x': (max(x_coords) - min(x_coords)) * self.pixel_to_meter_ratio,
                'uniformity_x': self._calculate_uniformity(x_coords)
            }
            
            # 垂直分布分析
            distribution_params['vertical_distribution'] = {
                'mean_y': np.mean(y_coords) * self.pixel_to_meter_ratio,
                'std_y': np.std(y_coords) * self.pixel_to_meter_ratio,
                'range_y': (max(y_coords) - min(y_coords)) * self.pixel_to_meter_ratio,
                'uniformity_y': self._calculate_uniformity(y_coords)
            }
            
            # 聚类度量
            distribution_params['clustering_metrics'] = self._calculate_clustering_metrics(
                window_centers
            )
            
            return distribution_params
            
        except Exception as e:
            self.logger.error(f"分布特征计算失败: {str(e)}")
            return distribution_params
    
    def _calculate_uniformity(self, coordinates: List[float]) -> float:
        """计算坐标的均匀性（0-1，1表示完全均匀）"""
        if len(coordinates) < 2:
            return 1.0
        
        coords = sorted(coordinates)
        distances = [coords[i+1] - coords[i] for i in range(len(coords)-1)]
        
        if not distances:
            return 1.0
        
        mean_distance = np.mean(distances)
        if mean_distance == 0:
            return 1.0
        
        # 使用变异系数的倒数作为均匀性度量
        cv = np.std(distances) / mean_distance
        return 1.0 / (1.0 + cv)
    
    def _calculate_clustering_metrics(self, centers: List[Tuple[float, float]]) -> Dict[str, float]:
        """计算聚类度量"""
        if len(centers) < 2:
            return {'average_distance': 0.0, 'clustering_coefficient': 0.0}
        
        # 计算所有点对之间的距离
        distances = []
        for i in range(len(centers)):
            for j in range(i+1, len(centers)):
                dist = MathUtils.calculate_distance(centers[i], centers[j])
                distances.append(dist * self.pixel_to_meter_ratio)
        
        avg_distance = np.mean(distances)
        
        # 计算聚类系数（基于距离方差）
        distance_variance = np.var(distances)
        clustering_coeff = distance_variance / (avg_distance ** 2) if avg_distance > 0 else 0
        
        return {
            'average_distance': avg_distance,
            'min_distance': min(distances),
            'max_distance': max(distances),
            'clustering_coefficient': clustering_coeff
        }
    
    def _calculate_shape_features(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """计算形状特征"""
        shape_params = {
            'window_shapes': {},
            'size_variations': {},
            'aspect_ratios': {}
        }
        
        try:
            # 窗户形状分析
            if facade_elements.windows:
                window_aspects = [w.aspect_ratio for w in facade_elements.windows]
                window_areas = [w.area for w in facade_elements.windows]
                
                shape_params['window_shapes'] = {
                    'aspect_ratio_mean': np.mean(window_aspects),
                    'aspect_ratio_std': np.std(window_aspects),
                    'aspect_ratio_range': max(window_aspects) - min(window_aspects)
                }
                
                shape_params['size_variations'] = {
                    'area_cv': np.std(window_areas) / np.mean(window_areas) if np.mean(window_areas) > 0 else 0,
                    'size_uniformity': 1.0 / (1.0 + np.std(window_areas) / np.mean(window_areas)) if np.mean(window_areas) > 0 else 0
                }
            
            # 整体立面比例
            if facade_elements.image_shape[0] > 0:
                height, width = facade_elements.image_shape[:2]
                facade_aspect = width / height
                
                shape_params['facade_proportions'] = {
                    'overall_aspect_ratio': facade_aspect,
                    'facade_orientation': 'horizontal' if facade_aspect > 1.2 else 'vertical' if facade_aspect < 0.8 else 'square'
                }
            
            return shape_params
            
        except Exception as e:
            self.logger.error(f"形状特征计算失败: {str(e)}")
            return shape_params
    
    def _calculate_spatial_relationships(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """计算空间关系"""
        spatial_params = {
            'window_spacing': {},
            'alignment_metrics': {},
            'proximity_analysis': {}
        }
        
        try:
            windows = facade_elements.windows
            if len(windows) < 2:
                return spatial_params
            
            # 窗户间距分析
            horizontal_spacings = []
            vertical_spacings = []
            
            for i, w1 in enumerate(windows):
                for j, w2 in enumerate(windows):
                    if i >= j:
                        continue
                    
                    # 计算中心点距离
                    dx = abs(w1.center[0] - w2.center[0]) * self.pixel_to_meter_ratio
                    dy = abs(w1.center[1] - w2.center[1]) * self.pixel_to_meter_ratio
                    
                    horizontal_spacings.append(dx)
                    vertical_spacings.append(dy)
            
            if horizontal_spacings:
                spatial_params['window_spacing'] = {
                    'horizontal_mean': np.mean(horizontal_spacings),
                    'horizontal_std': np.std(horizontal_spacings),
                    'vertical_mean': np.mean(vertical_spacings),
                    'vertical_std': np.std(vertical_spacings)
                }
            
            # 对齐度分析
            spatial_params['alignment_metrics'] = self._calculate_alignment_metrics(windows)
            
            return spatial_params
            
        except Exception as e:
            self.logger.error(f"空间关系计算失败: {str(e)}")
            return spatial_params
    
    def _calculate_alignment_metrics(self, windows: List[BuildingElement]) -> Dict[str, float]:
        """计算窗户对齐度"""
        if len(windows) < 2:
            return {'horizontal_alignment': 0.0, 'vertical_alignment': 0.0}
        
        # 提取窗户的边界
        left_edges = [w.bbox[0] for w in windows]
        right_edges = [w.bbox[2] for w in windows]
        top_edges = [w.bbox[1] for w in windows]
        bottom_edges = [w.bbox[3] for w in windows]
        
        # 计算对齐度（基于边界位置的标准差）
        horizontal_alignment = 1.0 / (1.0 + np.std(left_edges) + np.std(right_edges))
        vertical_alignment = 1.0 / (1.0 + np.std(top_edges) + np.std(bottom_edges))
        
        return {
            'horizontal_alignment': horizontal_alignment,
            'vertical_alignment': vertical_alignment,
            'overall_alignment': (horizontal_alignment + vertical_alignment) / 2
        }
    
    def validate_element_constraints(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """
        验证元素约束条件
        
        Args:
            facade_elements: 立面元素
            
        Returns:
            约束验证结果
        """
        validation_result = {
            'is_valid': True,
            'violations': [],
            'warnings': [],
            'constraint_checks': {}
        }
        
        try:
            # 获取约束配置
            constraints = self.config.get_section('optimization_constraints')
            
            # 检查窗墙比约束
            wwr_constraint = constraints.get('window_wall_ratio', {})
            if wwr_constraint:
                geometry_params = self.calculate_facade_parameters(facade_elements)
                wwr = geometry_params.get('window_wall_ratio', 0)
                
                min_wwr = wwr_constraint.get('min', 0)
                max_wwr = wwr_constraint.get('max', 1)
                
                if not (min_wwr <= wwr <= max_wwr):
                    validation_result['violations'].append(
                        f"窗墙比 {wwr:.3f} 超出范围 [{min_wwr}, {max_wwr}]"
                    )
                    validation_result['is_valid'] = False
                
                validation_result['constraint_checks']['window_wall_ratio'] = {
                    'value': wwr,
                    'min_allowed': min_wwr,
                    'max_allowed': max_wwr,
                    'is_valid': min_wwr <= wwr <= max_wwr
                }
            
            # 检查窗户尺寸约束
            window_size_constraint = constraints.get('window_size', {})
            if window_size_constraint and facade_elements.windows:
                min_width = window_size_constraint.get('min_width', 0)
                max_width = window_size_constraint.get('max_width', float('inf'))
                min_height = window_size_constraint.get('min_height', 0)
                max_height = window_size_constraint.get('max_height', float('inf'))
                
                for window in facade_elements.windows:
                    if not (min_width <= window.width <= max_width):
                        validation_result['violations'].append(
                            f"窗户 {window.element_id} 宽度 {window.width:.2f}m 超出范围 [{min_width}, {max_width}]"
                        )
                        validation_result['is_valid'] = False
                    
                    if not (min_height <= window.height <= max_height):
                        validation_result['violations'].append(
                            f"窗户 {window.element_id} 高度 {window.height:.2f}m 超出范围 [{min_height}, {max_height}]"
                        )
                        validation_result['is_valid'] = False
            
            # 检查窗户重叠
            overlap_violations = self._check_element_overlaps(facade_elements.windows)
            validation_result['violations'].extend(overlap_violations)
            if overlap_violations:
                validation_result['is_valid'] = False
            
            # 检查边界约束
            boundary_violations = self._check_boundary_constraints(facade_elements)
            validation_result['violations'].extend(boundary_violations)
            if boundary_violations:
                validation_result['is_valid'] = False
            
            self.logger.info(f"约束验证完成: {'通过' if validation_result['is_valid'] else '失败'}")
            if validation_result['violations']:
                self.logger.warning(f"发现 {len(validation_result['violations'])} 个约束违反")
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"约束验证失败: {str(e)}")
            return {
                'is_valid': False,
                'violations': [f"验证过程失败: {str(e)}"],
                'warnings': [],
                'constraint_checks': {}
            }
    
    def _check_element_overlaps(self, elements: List[BuildingElement]) -> List[str]:
        """检查元素重叠"""
        violations = []
        
        for i, elem1 in enumerate(elements):
            for j, elem2 in enumerate(elements):
                if i >= j:
                    continue
                
                # 检查边界框重叠
                if self._rectangles_overlap(elem1.bbox, elem2.bbox):
                    violations.append(
                        f"元素重叠: {elem1.element_id} 与 {elem2.element_id}"
                    )
        
        return violations
    
    def _rectangles_overlap(self, rect1: Tuple[float, float, float, float], 
                          rect2: Tuple[float, float, float, float]) -> bool:
        """检查两个矩形是否重叠"""
        x1_min, y1_min, x1_max, y1_max = rect1
        x2_min, y2_min, x2_max, y2_max = rect2
        
        return not (x1_max <= x2_min or x2_max <= x1_min or 
                   y1_max <= y2_min or y2_max <= y1_min)
    
    def _check_boundary_constraints(self, facade_elements: FacadeElements) -> List[str]:
        """检查边界约束"""
        violations = []
        
        if facade_elements.image_shape[0] == 0:
            return violations
        
        image_height, image_width = facade_elements.image_shape[:2]
        
        # 获取边界约束配置
        constraints = self.config.get_section('optimization_constraints')
        wall_margin = constraints.get('wall_margin', 0.2)  # 米
        wall_margin_pixels = wall_margin / facade_elements.pixel_to_meter_ratio
        
        for element in facade_elements.get_all_elements():
            x_min, y_min, x_max, y_max = element.bbox
            
            # 检查是否超出图像边界
            if (x_min < wall_margin_pixels or 
                y_min < wall_margin_pixels or 
                x_max > image_width - wall_margin_pixels or 
                y_max > image_height - wall_margin_pixels):
                
                violations.append(
                    f"元素 {element.element_id} 超出边界约束（边距: {wall_margin}m）"
                )
        
        return violations


def create_geometry_calculator() -> GeometryCalculator:
    """
    创建几何计算器实例
    
    Returns:
        配置好的几何计算器实例
    """
    return GeometryCalculator()