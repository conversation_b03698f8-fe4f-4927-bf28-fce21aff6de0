"""
立面色块图像处理器 - 处理预分割的立面识别色块图
直接处理已经经过立面识别后产生的色块图像，无需YOLO模型分割
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import ImageProcessingError, handle_exception
from ..core.utils import FileUtils, ValidationUtils
from ..core.data_structures import BuildingElement, ElementType, FacadeElements


class ColorBlockImageProcessor:
    """
    立面色块图像处理器
    
    功能：
    1. 处理预分割的立面识别色块图像
    2. 精确的颜色区域识别
    3. 智能建筑元素提取
    4. 几何特征分析
    5. 详细的处理统计信息
    """
    
    def __init__(self):
        """初始化立面色块图像处理器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取图像处理配置
        self.image_config = self.config.get_section('image_processing')
        self.color_mapping = self.image_config.get('color_mapping', {})
        
        # 颜色匹配配置
        color_matching = self.image_config.get('color_matching', {})
        self.matching_strategy = color_matching.get('strategy', 'range')
        self.color_tolerance = color_matching.get('tolerance', 15)
        self.use_hsv = color_matching.get('use_hsv', False)
        
        # 预处理配置
        preprocessing = self.image_config.get('preprocessing', {})
        self.noise_filter_kernel = preprocessing.get('noise_filter_kernel', 3)
        self.min_contour_area = preprocessing.get('min_contour_area', 100)
        self.max_contour_area = preprocessing.get('max_contour_area', 50000)
        
        # 像素到米的转换比例 - 禁止使用默认模拟值
        self.pixel_to_meter_ratio = self.image_config.get('pixel_to_meter_ratio')
        if self.pixel_to_meter_ratio is None:
            # 临时使用默认值以保持兼容性，但记录警告
            self.pixel_to_meter_ratio = 0.01
            self.logger.warning("未设置 pixel_to_meter_ratio，使用临时默认值 0.01。建议在配置中明确设置真实测量值")
        
        if self.pixel_to_meter_ratio <= 0:
            raise ImageProcessingError(f"pixel_to_meter_ratio 必须为正数，当前值: {self.pixel_to_meter_ratio}")
        
        self.logger.info(f"像素-米转换比例设置为: {self.pixel_to_meter_ratio}")
"""
立面色块图像处理器 - 处理预分割的立面识别色块图
直接处理已经经过立面识别后产生的色块图像，无需YOLO模型分割
"""

import cv2
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import ImageProcessingError, handle_exception
from ..core.utils import FileUtils, ValidationUtils
from ..core.data_structures import BuildingElement, ElementType, FacadeElements


class ColorBlockImageProcessor:
    """
    立面色块图像处理器
    
    功能：
    1. 处理预分割的立面识别色块图像
    2. 精确的颜色区域识别
    3. 智能建筑元素提取
    4. 几何特征分析
    5. 详细的处理统计信息
    """
    
    def __init__(self):
        """初始化立面色块图像处理器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取图像处理配置
        self.image_config = self.config.get_section('image_processing')
        self.color_mapping = self.image_config.get('color_mapping', {})
        
        # 颜色匹配配置
        color_matching = self.image_config.get('color_matching', {})
        self.matching_strategy = color_matching.get('strategy', 'range')
        self.color_tolerance = color_matching.get('tolerance', 15)
        self.use_hsv = color_matching.get('use_hsv', False)
        
        # 预处理配置
        preprocessing = self.image_config.get('preprocessing', {})
        self.noise_filter_kernel = preprocessing.get('noise_filter_kernel', 3)
        self.min_contour_area = preprocessing.get('min_contour_area', 100)
        self.max_contour_area = preprocessing.get('max_contour_area', 50000)
        
        # 像素到米的转换比例 - 禁止使用默认模拟值
        self.pixel_to_meter_ratio = self.image_config.get('pixel_to_meter_ratio')
        if self.pixel_to_meter_ratio is None:
            # 临时使用默认值以保持兼容性，但记录警告
            self.pixel_to_meter_ratio = 0.01
            self.logger.warning("未设置 pixel_to_meter_ratio，使用临时默认值 0.01。建议在配置中明确设置真实测量值")
        
        if self.pixel_to_meter_ratio <= 0:
            raise ImageProcessingError(f"pixel_to_meter_ratio 必须为正数，当前值: {self.pixel_to_meter_ratio}")
        
        self.logger.info(f"像素-米转换比例设置为: {self.pixel_to_meter_ratio}")
        self.logger.info("立面色块图像处理器初始化完成")
    
    @handle_exception
    def process_color_block_image(self, image_path: str) -> FacadeElements:
        """
        处理立面色块图像，返回结构化的立面元素数据
        
        Args:
            image_path: 色块图像文件路径
            
        Returns:
            FacadeElements: 结构化的立面元素数据
            
        Raises:
            ImageProcessingError: 图像处理失败时抛出
        """
        with LogContext("立面色块图像处理", self.logger):
            # 验证输入
            self._validate_input(image_path)
            
            # 加载图像
            image = self._load_image(image_path)
            self.logger.info(f"成功加载图像: {image_path}, 尺寸: {image.shape}")
            
            # 图像预处理
            processed_image = self._preprocess_image(image)
            
            # 颜色区域识别
            color_regions = self._identify_color_regions(processed_image)
            
            # 提取建筑元素
            building_elements = self._extract_building_elements(color_regions, image.shape)
            
            # 创建FacadeElements结构
            facade_elements = self._create_facade_elements(building_elements, image.shape)
            
            # 验证结果
            self._validate_extraction_result(facade_elements)
            
            self.logger.info(f"成功处理图像，提取到立面元素: "
                           f"墙体{len(facade_elements.walls)}个, "
                           f"窗户{len(facade_elements.windows)}个, "
                           f"门{len(facade_elements.doors)}个")
            
            return facade_elements
    
    def process_segmentation_image(self, image_path: str) -> FacadeElements:
        """向后兼容性方法别名"""
        return self.process_color_block_image(image_path)
    
    def _validate_input(self, image_path: str) -> None:
        """验证输入参数"""
        if not ValidationUtils.validate_file_exists(image_path):
            raise ImageProcessingError(f"图像文件不存在: {image_path}")
        
        if not FileUtils.is_image_file(image_path):
            raise ImageProcessingError(f"不支持的图像格式: {image_path}")
    
    def _load_image(self, image_path: str) -> np.ndarray:
        """
        加载图像文件，支持中文路径
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像数组 (BGR格式)
        """
        try:
            # 保存当前图像路径
            self.current_image_path = image_path
            
            # 使用支持中文路径的方法加载图像
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            nparr = np.frombuffer(image_data, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if image is None:
                raise ImageProcessingError(f"无法解码图像文件: {image_path}")
            
            return image
            
        except Exception as e:
            raise ImageProcessingError(f"图像加载失败: {str(e)}") from e
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        图像预处理 - 增强版本
        
        Args:
            image: 原始图像
            
        Returns:
            预处理后的图像
        """
        try:
            processed = image.copy()
            
            # 噪声过滤
            if self.noise_filter_kernel > 1:
                processed = cv2.medianBlur(processed, self.noise_filter_kernel)
                self.logger.debug(f"应用中值滤波，核大小: {self.noise_filter_kernel}")
            
            # 可选的额外预处理步骤
            # 1. 对比度增强
            lab = cv2.cvtColor(processed, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
            l = clahe.apply(l)
            processed = cv2.merge([l, a, b])
            processed = cv2.cvtColor(processed, cv2.COLOR_LAB2BGR)
            
            # 2. 高斯平滑
            processed = cv2.GaussianBlur(processed, (3, 3), 0)
            
            self.logger.debug("图像预处理完成：噪声过滤、对比度增强、高斯平滑")
            
            return processed
            
        except Exception as e:
            raise ImageProcessingError(f"图像预处理失败: {str(e)}") from e
    
    def _identify_color_regions(self, image: np.ndarray) -> Dict[str, np.ndarray]:
        """
        识别不同颜色区域 - 增强版本
        
        Args:
            image: 预处理后的图像
            
        Returns:
            按元素类型分组的二值掩码
        """
        try:
            color_regions = {}
            
            self.logger.info(f"开始识别颜色区域，图像形状: {image.shape}")
            
            # 遍历每种建筑元素类型
            for element_type, color_config in self.color_mapping.items():
                if element_type == 'background':
                    continue  # 跳过背景
                
                self.logger.info(f"处理元素类型: {element_type}, 颜色配置: {color_config}")
                
                # 创建颜色掩码
                mask = self._create_enhanced_color_mask(image, color_config, element_type)
                
                mask_pixels = np.sum(mask > 0)
                if mask_pixels > 0:
                    color_regions[element_type] = mask
                    self.logger.info(f"检测到 {element_type}: {mask_pixels} 个像素")
                else:
                    self.logger.warning(f"未检测到 {element_type}, 颜色范围: {color_config}")
            
            # 智能墙体检测
            if 'walls' not in color_regions:
                self.logger.warning("未检测到墙体，启动智能墙体检测")
                wall_mask = self._intelligent_wall_detection(image, color_regions)
                if wall_mask is not None and np.sum(wall_mask > 0) > 0:
                    color_regions['walls'] = wall_mask
                    self.logger.info(f"智能墙体检测成功，检测到 {np.sum(wall_mask > 0)} 个墙体像素")
            
            self.logger.info(f"总共识别到 {len(color_regions)} 种元素类型")
            return color_regions
            
        except Exception as e:
            raise ImageProcessingError(f"颜色区域识别失败: {str(e)}") from e
    
    def _create_enhanced_color_mask(self, image: np.ndarray, color_config: Any, 
                                  element_type: str) -> np.ndarray:
        """
        创建增强的颜色掩码
        
        Args:
            image: 输入图像
            color_config: 颜色配置
            element_type: 元素类型
            
        Returns:
            二值掩码
        """
        try:
            if isinstance(color_config, dict) and 'min' in color_config and 'max' in color_config:
                # 范围匹配策略
                mask = self._create_range_color_mask(image, color_config)
            elif isinstance(color_config, list) and len(color_config) == 3:
                # 精确匹配策略（兼容旧配置）
                bgr_color = [color_config[2], color_config[1], color_config[0]]  # RGB -> BGR
                mask = self._create_exact_color_mask(image, bgr_color, self.color_tolerance)
            else:
                self.logger.warning(f"无效的颜色配置格式: {element_type}")
                return np.zeros(image.shape[:2], dtype=np.uint8)
            
            # 应用形态学操作优化掩码
            mask = self._apply_morphological_operations(mask, element_type)
            
            return mask
            
        except Exception as e:
            raise ImageProcessingError(f"颜色掩码创建失败: {str(e)}") from e
    
    def _create_range_color_mask(self, image: np.ndarray, color_config: Dict) -> np.ndarray:
        """创建范围颜色掩码"""
        min_rgb = color_config.get('min', [0, 0, 0])
        max_rgb = color_config.get('max', [255, 255, 255])
        
        # RGB -> BGR转换
        lower_bound = np.array([min_rgb[2], min_rgb[1], min_rgb[0]], dtype=np.uint8)
        upper_bound = np.array([max_rgb[2], max_rgb[1], max_rgb[0]], dtype=np.uint8)
        
        self.logger.info(f"颜色检测范围 BGR: {lower_bound} - {upper_bound}")
        
        # 创建掩码
        mask = cv2.inRange(image, lower_bound, upper_bound)
        initial_pixels = np.sum(mask > 0)
        
        # 如果检测失败，尝试扩大范围
        if initial_pixels == 0:
            tolerance = 30  # 增加容错范围
            expanded_min = np.maximum(lower_bound - tolerance, [0, 0, 0])
            expanded_max = np.minimum(upper_bound + tolerance, [255, 255, 255])
            mask = cv2.inRange(image, expanded_min, expanded_max)
            
            expanded_pixels = np.sum(mask > 0)
            if expanded_pixels > 0:
                self.logger.info(f"使用扩展范围检测成功: {expanded_pixels} 像素")
            else:
                self.logger.warning(f"扩展范围仍未检测到像素，BGR范围: {expanded_min} - {expanded_max}")
        else:
            self.logger.info(f"初始范围检测成功: {initial_pixels} 像素")
        
        return mask
    
    def _create_exact_color_mask(self, image: np.ndarray, target_color: List[int], 
                                tolerance: int = 10) -> np.ndarray:
        """创建精确颜色掩码"""
        lower_bound = np.array([max(0, c - tolerance) for c in target_color])
        upper_bound = np.array([min(255, c + tolerance) for c in target_color])
        
        return cv2.inRange(image, lower_bound, upper_bound)
    
    def _apply_morphological_operations(self, mask: np.ndarray, element_type: str) -> np.ndarray:
        """
        应用形态学操作优化掩码
        
        Args:
            mask: 输入掩码
            element_type: 元素类型
            
        Returns:
            优化后的掩码
        """
        # 根据元素类型选择不同的核大小
        if element_type == 'walls':
            kernel_size = 5  # 墙体使用较大的核
        elif element_type == 'windows':
            kernel_size = 3  # 窗户使用中等的核
        else:
            kernel_size = 3  # 其他元素使用标准核
        
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        
        # 开运算去除小噪声
        mask = cv2.morphologyEx(mask, cv2.MORPH_OPEN, kernel)
        
        # 闭运算填充小空洞
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        return mask
    
    def _intelligent_wall_detection(self, image: np.ndarray, 
                                  existing_regions: Dict[str, np.ndarray]) -> Optional[np.ndarray]:
        """
        智能墙体检测 - 增强版本
        
        Args:
            image: 输入图像
            existing_regions: 已检测到的其他区域
            
        Returns:
            墙体掩码或None
        """
        try:
            self.logger.info("开始智能墙体检测")
            
            # 策略1: 基于反向掩码
            wall_mask = np.ones(image.shape[:2], dtype=np.uint8) * 255
            
            # 排除已检测的非墙体区域
            for region_type, mask in existing_regions.items():
                if region_type in ['windows', 'doors', 'shading', 'frames']:
                    wall_mask = cv2.bitwise_and(wall_mask, cv2.bitwise_not(mask))
            
            # 策略2: 多层次颜色范围检测
            wall_color_ranges = [
                {'min': [80, 80, 80], 'max': [180, 180, 180]},      # 基础灰色
                {'min': [180, 180, 180], 'max': [255, 255, 255]},   # 白色到浅灰
                {'min': [40, 40, 40], 'max': [120, 120, 120]},      # 深灰到中灰
                {'min': [200, 200, 180], 'max': [255, 255, 220]},   # 米色/象牙色
                {'min': [180, 190, 180], 'max': [220, 230, 220]}    # 浅绿灰色
            ]
            
            combined_mask = np.zeros(image.shape[:2], dtype=np.uint8)
            for color_range in wall_color_ranges:
                range_mask = self._create_range_color_mask(image, color_range)
                combined_mask = cv2.bitwise_or(combined_mask, range_mask)
            
            # 策略3: 基于纹理特征的检测
            texture_mask = self._detect_wall_by_texture(image)
            
            # 策略4: 基于边缘密度的检测
            edge_mask = self._detect_wall_by_edges(image)
            
            # 综合所有策略
            wall_mask = cv2.bitwise_and(wall_mask, 
                                      cv2.bitwise_or(combined_mask, 
                                                   cv2.bitwise_or(texture_mask, edge_mask)))
            
            # 形态学优化
            kernel = np.ones((7, 7), np.uint8)
            wall_mask = cv2.morphologyEx(wall_mask, cv2.MORPH_CLOSE, kernel)
            
            # 去除小噪声
            kernel = np.ones((3, 3), np.uint8)
            wall_mask = cv2.morphologyEx(wall_mask, cv2.MORPH_OPEN, kernel)
            
            # 验证结果
            wall_pixels = np.sum(wall_mask > 0)
            total_pixels = image.shape[0] * image.shape[1]
            wall_ratio = wall_pixels / total_pixels
            
            if wall_ratio > 0.05 and wall_pixels > 1000:  # 至少5%的像素且绝对数量大于1000
                self.logger.info(f"智能墙体检测成功: {wall_pixels} 像素 ({wall_ratio:.2%})")
                return wall_mask
            else:
                self.logger.warning(f"智能墙体检测失败: 像素过少 ({wall_pixels})")
                return None
                
        except Exception as e:
            self.logger.error(f"智能墙体检测失败: {str(e)}")
            return None
    
    def _detect_wall_by_texture(self, image: np.ndarray) -> np.ndarray:
        """基于纹理特征检测墙体"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 计算局部方差（纹理特征）
            kernel = np.ones((9, 9), np.float32) / 81
            mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
            sqr_mean = cv2.filter2D((gray.astype(np.float32))**2, -1, kernel)
            variance = sqr_mean - (mean**2)
            
            # 墙体通常具有低方差（相对均匀的纹理）
            texture_mask = (variance < np.percentile(variance, 30)).astype(np.uint8) * 255
            
            return texture_mask
            
        except Exception as e:
            self.logger.warning(f"纹理检测失败: {str(e)}")
            return np.zeros(image.shape[:2], dtype=np.uint8)
    
    def _detect_wall_by_edges(self, image: np.ndarray) -> np.ndarray:
        """基于边缘密度检测墙体"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 边缘检测
            edges = cv2.Canny(gray, 50, 150)
            
            # 计算局部边缘密度
            kernel = np.ones((15, 15), np.float32) / 225
            edge_density = cv2.filter2D(edges.astype(np.float32), -1, kernel)
            
            # 墙体通常具有低边缘密度
            edge_mask = (edge_density < np.percentile(edge_density, 40)).astype(np.uint8) * 255
            
            return edge_mask
            
        except Exception as e:
            self.logger.warning(f"边缘密度检测失败: {str(e)}")
            return np.zeros(image.shape[:2], dtype=np.uint8)
    
    def _extract_building_elements(self, color_regions: Dict[str, np.ndarray], 
                                 image_shape: Tuple[int, int, int]) -> Dict[str, List[Dict[str, Any]]]:
        """
        从颜色区域提取建筑元素
        
        Args:
            color_regions: 颜色区域掩码字典
            image_shape: 图像形状
            
        Returns:
            按元素类型分组的建筑元素数据
        """
        try:
            building_elements = {}
            
            for element_type, mask in color_regions.items():
                elements = self._extract_elements_from_mask(mask, element_type, image_shape)
                
                if elements:
                    building_elements[element_type] = elements
                    self.logger.debug(f"从 {element_type} 掩码中提取到 {len(elements)} 个元素")
            
            return building_elements
            
        except Exception as e:
            raise ImageProcessingError(f"建筑元素提取失败: {str(e)}") from e
    
    def _extract_elements_from_mask(self, mask: np.ndarray, element_type: str, image_shape: Tuple[int, int, int]) -> List[Dict[str, Any]]:
        """
        从单个掩码中提取元素 - 增强版本
        
        Args:
            mask: 二值掩码
            element_type: 元素类型
            
        Returns:
            元素数据列表
        """
        try:
            elements = []
            
            # 查找轮廓
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # 动态设置面积阈值
            min_area, max_area = self._get_area_thresholds(element_type)
            
            for i, contour in enumerate(contours):
                area = cv2.contourArea(contour)
                
                # 面积过滤
                if area < min_area or area > max_area:
                    continue
                
                # 提取几何特征
                element_data = self._extract_element_features(contour, element_type, i, image_shape)
                
                if element_data:
                    elements.append(element_data)
            
            return elements
            
        except Exception as e:
            raise ImageProcessingError(f"从掩码提取元素失败: {str(e)}") from e
    
    def _get_area_thresholds(self, element_type: str) -> Tuple[float, float]:
        """获取元素类型对应的面积阈值"""
        thresholds = {
            'walls': (1000, 500000),    # 墙体面积较大
            'windows': (100, 50000),    # 窗户面积中等
            'doors': (500, 20000),      # 门面积中等
            'shading': (50, 10000),     # 遮阳面积较小
            'frames': (20, 5000)        # 窗框面积很小
        }
        
        return thresholds.get(element_type, (self.min_contour_area, self.max_contour_area))
    
    def _extract_element_features(self, contour: np.ndarray, element_type: str, index: int, image_shape: Tuple[int, int, int]) -> Optional[Dict[str, Any]]:
        """
        提取元素的详细特征
        
        Args:
            contour: 轮廓
            element_type: 元素类型
            index: 元素索引
            
        Returns:
            元素特征字典
        """
        try:
            # 基础几何特征
            area = cv2.contourArea(contour)
            perimeter = cv2.arcLength(contour, True)
            x, y, w, h = cv2.boundingRect(contour)
            
            # 质心计算
            moments = cv2.moments(contour)
            if moments['m00'] != 0:
                cx = int(moments['m10'] / moments['m00'])
                cy = int(moments['m01'] / moments['m00'])
                # 垂直镜像：将图像坐标系（顶部为0）转换为建筑坐标系（底部为0）
                cy = image_shape[0] - cy  # 翻转Y坐标
            else:
                cx, cy = x + w // 2, y + h // 2
                # 垂直镜像：对默认中心点也进行翻转
                cy = image_shape[0] - cy  # 翻转Y坐标
            
            # 高级几何特征
            aspect_ratio = w / h if h > 0 else 0.0
            extent = area / (w * h) if (w * h) > 0 else 0.0
            
            # 凸包相关特征
            hull = cv2.convexHull(contour)
            hull_area = cv2.contourArea(hull)
            solidity = area / hull_area if hull_area > 0 else 0.0
            
            # 拟合椭圆（如果点数足够）
            ellipse_features = {}
            if len(contour) >= 5:
                try:
                    ellipse = cv2.fitEllipse(contour)
                    ellipse_features = {
                        'ellipse_center': ellipse[0],
                        'ellipse_axes': ellipse[1], 
                        'ellipse_angle': ellipse[2]
                    }
                except:
                    pass
            
            # 圆度计算
            circularity = (4 * np.pi * area) / (perimeter * perimeter) if perimeter > 0 else 0.0
            
            # 创建元素数据
            # 垂直镜像：对边界框坐标也进行翻转
            y_flipped = image_shape[0] - y - h  # 翻转Y坐标并调整高度
            element_data = {
                'element_id': f"{element_type}_{index}",
                'element_type': element_type,
                'bbox': (x, y_flipped, x + w, y_flipped + h),
                'center': (cx, cy),
                'area': float(area),
                'perimeter': float(perimeter),
                'width': w,
                'height': h,
                'aspect_ratio': aspect_ratio,
                'extent': extent,
                'solidity': solidity,
                'circularity': circularity,
                'contour': contour.tolist(),
                'moments': {k: float(v) for k, v in moments.items()},
                **ellipse_features
            }
            
            # 元素特定的特征提取
            element_data.update(self._extract_type_specific_features(contour, element_type))
            
            return element_data
            
        except Exception as e:
            self.logger.warning(f"提取元素特征失败: {str(e)}")
            return None
    
    def _extract_type_specific_features(self, contour: np.ndarray, element_type: str) -> Dict[str, Any]:
        """提取元素类型特定的特征"""
        features = {}
        
        if element_type == 'windows':
            # 窗户特定特征
            features.update({
                'is_rectangular': self._is_rectangular(contour),
                'corner_count': len(cv2.approxPolyDP(contour, 0.02 * cv2.arcLength(contour, True), True)),
                'window_type_hint': self._classify_window_shape(contour)
            })
        
        elif element_type == 'doors':
            # 门特定特征
            features.update({
                'is_arched': self._is_arched(contour),
                'is_rectangular': self._is_rectangular(contour)
            })
        
        elif element_type == 'walls':
            # 墙体特定特征  
            features.update({
                'wall_orientation': self._estimate_wall_orientation(contour),
                'surface_roughness': self._estimate_surface_roughness(contour)
            })
        
        return features
    
    def _is_rectangular(self, contour: np.ndarray) -> bool:
        """判断轮廓是否为矩形"""
        epsilon = 0.02 * cv2.arcLength(contour, True)
        approx = cv2.approxPolyDP(contour, epsilon, True)
        return len(approx) == 4
    
    def _is_arched(self, contour: np.ndarray) -> bool:
        """判断轮廓是否为拱形"""
        # 简化判断：检查顶部是否弯曲
        hull = cv2.convexHull(contour)
        hull_area = cv2.contourArea(hull)
        contour_area = cv2.contourArea(contour)
        
        # 如果凸包面积远大于轮廓面积，可能是拱形
        return (hull_area / contour_area) > 1.2 if contour_area > 0 else False
    
    def _classify_window_shape(self, contour: np.ndarray) -> str:
        """分类窗户形状"""
        area = cv2.contourArea(contour)
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        
        if self._is_rectangular(contour):
            if 0.8 <= aspect_ratio <= 1.2:
                return 'square'
            elif aspect_ratio > 1.2:
                return 'horizontal_rectangular'
            else:
                return 'vertical_rectangular'
        else:
            if len(cv2.approxPolyDP(contour, 0.02 * cv2.arcLength(contour, True), True)) > 8:
                return 'circular'
            else:
                return 'irregular'
    
    def _estimate_wall_orientation(self, contour: np.ndarray) -> str:
        """估计墙体朝向"""
        x, y, w, h = cv2.boundingRect(contour)
        
        if w > h * 2:
            return 'horizontal'
        elif h > w * 2:
            return 'vertical'
        else:
            return 'mixed'
    
    def _estimate_surface_roughness(self, contour: np.ndarray) -> float:
        """估计表面粗糙度"""
        perimeter = cv2.arcLength(contour, True)
        hull = cv2.convexHull(contour)
        hull_perimeter = cv2.arcLength(hull, True)
        
        return perimeter / hull_perimeter if hull_perimeter > 0 else 1.0
    
    def _create_facade_elements(self, building_elements: Dict[str, List[Dict[str, Any]]], 
                              image_shape: Tuple[int, int, int]) -> FacadeElements:
        """
        创建结构化的FacadeElements对象
        
        Args:
            building_elements: 提取的建筑元素数据
            image_shape: 图像形状
            
        Returns:
            FacadeElements对象
        """
        facade_elements = FacadeElements(
            image_shape=image_shape,
            pixel_to_meter_ratio=self.pixel_to_meter_ratio,
            original_image_path=getattr(self, 'current_image_path', None)
        )
        
        # 转换墙体元素
        for wall_data in building_elements.get('walls', []):
            wall_element = self._create_wall_element(wall_data)
            facade_elements.walls.append(wall_element)
        
        # 转换窗户元素
        for window_data in building_elements.get('windows', []):
            window_element = self._create_window_element(window_data)
            facade_elements.windows.append(window_element)
        
        # 转换门元素
        for door_data in building_elements.get('doors', []):
            door_element = self._create_building_element(door_data, ElementType.DOOR)
            facade_elements.doors.append(door_element)
        
        # 转换遮阳元素
        for shading_data in building_elements.get('shading', []):
            shading_element = self._create_building_element(shading_data, ElementType.SHADING)
            facade_elements.shading.append(shading_element)
        
        # 转换窗框元素
        for frame_data in building_elements.get('frames', []):
            frame_element = self._create_building_element(frame_data, ElementType.FRAME)
            facade_elements.frames.append(frame_element)
        
        return facade_elements
    
    def _create_wall_element(self, wall_data: Dict[str, Any]):
        """创建墙体元素"""
        from ..core.data_structures import WallElement
        
        return WallElement(
            element_id=wall_data['element_id'],
            element_type=ElementType.WALL,
            bbox=wall_data['bbox'],
            center=wall_data['center'],
            area=wall_data['area'] * (self.pixel_to_meter_ratio ** 2),
            perimeter=wall_data['perimeter'] * self.pixel_to_meter_ratio,
            width=wall_data['width'] * self.pixel_to_meter_ratio,
            height=wall_data['height'] * self.pixel_to_meter_ratio,
            aspect_ratio=wall_data['aspect_ratio'],
            properties=wall_data
        )
    
    def _create_window_element(self, window_data: Dict[str, Any]):
        """创建窗户元素"""
        from ..core.data_structures import WindowElement
        
        return WindowElement(
            element_id=window_data['element_id'],
            element_type=ElementType.WINDOW,
            bbox=window_data['bbox'],
            center=window_data['center'],
            area=window_data['area'] * (self.pixel_to_meter_ratio ** 2),
            perimeter=window_data['perimeter'] * self.pixel_to_meter_ratio,
            width=window_data['width'] * self.pixel_to_meter_ratio,
            height=window_data['height'] * self.pixel_to_meter_ratio,
            aspect_ratio=window_data['aspect_ratio'],
            properties=window_data
        )
    
    def _create_building_element(self, element_data: Dict[str, Any], element_type: ElementType) -> BuildingElement:
        """创建一般建筑元素"""
        return BuildingElement(
            element_id=element_data['element_id'],
            element_type=element_type,
            bbox=element_data['bbox'],
            center=element_data['center'],
            area=element_data['area'] * (self.pixel_to_meter_ratio ** 2),
            perimeter=element_data['perimeter'] * self.pixel_to_meter_ratio,
            width=element_data['width'] * self.pixel_to_meter_ratio,
            height=element_data['height'] * self.pixel_to_meter_ratio,
            aspect_ratio=element_data['aspect_ratio'],
            properties=element_data
        )
    
    def _validate_extraction_result(self, facade_elements: FacadeElements) -> None:
        """验证提取结果的合理性"""
        total_elements = len(facade_elements.get_all_elements())
        
        if total_elements == 0:
            raise ImageProcessingError("未检测到任何建筑元素")
        
        # 检查是否有基本的墙体或窗户
        has_walls = len(facade_elements.walls) > 0
        has_windows = len(facade_elements.windows) > 0
        
        if not (has_walls or has_windows):
            self.logger.warning("既未检测到墙体也未检测到窗户，结果可能不准确")
        
        # 检查窗墙比是否合理
        if has_walls and has_windows:
            wall_area = sum(w.area for w in facade_elements.walls)
            window_area = sum(w.area for w in facade_elements.windows)
            
            if wall_area > 0:
                wwr = window_area / wall_area
                if wwr > 1.0:
                    self.logger.warning(f"窗墙比异常高: {wwr:.2f}")
        
        self.logger.info(f"提取结果验证通过: 总计{total_elements}个元素")
    
    def save_debug_visualization(self, image_path: str, facade_elements: FacadeElements, 
                               output_dir: str = "output/debug") -> None:
        """
        保存调试可视化图像
        
        Args:
            image_path: 原始图像路径
            facade_elements: 提取的立面元素
            output_dir: 输出目录
        """
        try:
            FileUtils.ensure_directory(output_dir)
            
            # 加载原始图像
            original_image = self._load_image(image_path)
            if original_image is None:
                return
            
            # 创建标注图像
            annotated_image = original_image.copy()
            
            # 定义颜色
            colors = {
                ElementType.WALL: (128, 128, 128),      # 灰色
                ElementType.WINDOW: (255, 0, 0),        # 蓝色
                ElementType.DOOR: (0, 255, 0),          # 绿色
                ElementType.SHADING: (0, 0, 255),       # 红色
                ElementType.FRAME: (255, 255, 0)        # 青色
            }
            
            # 绘制所有元素
            all_elements = facade_elements.get_all_elements()
            for element in all_elements:
                color = colors.get(element.element_type, (255, 255, 255))
                
                # 绘制边界框
                x1, y1, x2, y2 = element.bbox
                cv2.rectangle(annotated_image, (int(x1), int(y1)), (int(x2), int(y2)), color, 2)
                
                # 绘制中心点 
                cx, cy = element.center
                cv2.circle(annotated_image, (int(cx), int(cy)), 3, color, -1)
                
                # 添加标签
                label = f"{element.element_type.value}_{element.element_id.split('_')[-1]}"
                cv2.putText(annotated_image, label, (int(x1), int(y1) - 10),
                          cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            # 保存图像
            output_file = Path(output_dir) / f"debug_{Path(image_path).stem}.png"
            
            success, encoded_img = cv2.imencode('.png', annotated_image)
            if success:
                with open(output_file, 'wb') as f:
                    f.write(encoded_img.tobytes())
                
                self.logger.info(f"调试图像已保存: {output_file}")
            else:
                self.logger.error("图像编码失败")
                
        except Exception as e:
            self.logger.error(f"保存调试图像失败: {str(e)}")
    
    def get_processing_statistics(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Args:
            facade_elements: 立面元素
            
        Returns:
            统计信息字典
        """
        try:
            all_elements = facade_elements.get_all_elements()
            
            stats = {
                'total_elements': len(all_elements),
                'element_counts': {
                    'walls': len(facade_elements.walls),
                    'windows': len(facade_elements.windows),
                    'doors': len(facade_elements.doors),
                    'shading': len(facade_elements.shading),
                    'frames': len(facade_elements.frames)
                },
                'total_areas': {},
                'average_areas': {},
                'size_distributions': {}
            }
            
            # 按类型统计面积
            for element_type in ElementType:
                elements = facade_elements.get_elements_by_type(element_type)
                if elements:
                    areas = [elem.area for elem in elements]
                    stats['total_areas'][element_type.value] = sum(areas)
                    stats['average_areas'][element_type.value] = np.mean(areas)
                    stats['size_distributions'][element_type.value] = {
                        'min': min(areas),
                        'max': max(areas),
                        'std': np.std(areas),
                        'median': np.median(areas)
                    }
            
            # 计算窗墙比
            if facade_elements.walls and facade_elements.windows:
                wall_area = sum(w.area for w in facade_elements.walls)
                window_area = sum(w.area for w in facade_elements.windows)
                stats['window_wall_ratio'] = window_area / wall_area if wall_area > 0 else 0
            
            return stats
            
        except Exception as e:
            self.logger.error(f"统计信息计算失败: {str(e)}")
            return {}


def create_color_block_processor() -> ColorBlockImageProcessor:
    """
    创建立面色块图像处理器实例
    
    Returns:
        配置好的处理器实例
    """
    return ColorBlockImageProcessor()


def create_yolo_segmentation_processor() -> ColorBlockImageProcessor:
    """
    创建YOLO分割处理器实例（兼容性别名）
    
    Returns:
        配置好的处理器实例
    """
    return ColorBlockImageProcessor()