"""
约束处理器
处理立面优化过程中的各种约束条件
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Any, Optional, Set
from enum import Enum

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import ConstraintViolationError as ConstraintError, handle_exception
from ..core.data_structures import (
    FacadeIndividual, FacadeElements, RenovationMode, 
    ConstraintViolation, ConstraintResults
)
from ..core.utils import MathUtils


class ConstraintType(Enum):
    """约束类型枚举"""
    GEOMETRIC = "geometric"
    BUILDING_CODE = "building_code"
    STRUCTURAL = "structural"
    FUNCTIONAL = "functional"
    AESTHETIC = "aesthetic"


class ConstraintSeverity(Enum):
    """约束严重性枚举"""
    CRITICAL = "critical"      # 严重违反，个体不可行
    MAJOR = "major"           # 主要违反，需要修复
    MINOR = "minor"           # 轻微违反，可以容忍
    WARNING = "warning"       # 警告，不影响可行性


class ConstraintHandler:
    """
    约束处理器
    
    功能：
    1. 定义和管理各种约束条件
    2. 检查个体约束违反情况
    3. 计算约束违反度和惩罚
    4. 提供约束修复建议
    5. 支持多层次约束管理
    """
    
    def __init__(self, facade_elements: FacadeElements):
        """
        初始化约束处理器
        
        Args:
            facade_elements: 立面元素数据
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.facade_elements = facade_elements
        
        # 获取约束配置
        optimization_config = self.config.get_section('optimization')
        constraints_config = self.config.get_section('constraints')
        
        self.constraint_config = optimization_config.get('constraint_handling', {})
        
        # 几何约束参数 - 使用新的配置
        window_size_config = constraints_config.get('window_size', {})
        self.min_window_width = window_size_config.get('min_width', 0.4)
        self.max_window_width = window_size_config.get('max_width', 3.5)
        self.min_window_height = window_size_config.get('min_height', 0.4)
        self.max_window_height = window_size_config.get('max_height', 3.0)
        
        # 窗户间距约束
        window_spacing_config = constraints_config.get('window_spacing', {})
        self.min_window_spacing = window_spacing_config.get('min_horizontal', 0.3)
        self.min_vertical_spacing = window_spacing_config.get('min_vertical', 0.3)
        self.overlap_tolerance = window_spacing_config.get('overlap_tolerance', 0.05)

        # 创建几何约束字典，用于改造模式调整
        self.geometric_constraints = {
            'min_window_width': self.min_window_width,
            'min_window_height': self.min_window_height,
            'min_window_spacing': self.min_window_spacing
        }

        # 创建建筑规范约束字典
        self.building_code_constraints = {
            'min_wall_margin': constraints_config.get('wall_margin', 0.15)
        }

        # 通用约束
        self.min_window_area = 0.16  # 0.4 * 0.4
        self.max_window_area = 10.5  # 3.5 * 3.0
        
        # 严格改造模式的极其宽松约束 - 适应极其紧密的现有窗户布局
        self.strict_renovation_constraints = {
            'min_window_spacing': 0.05,       # 极小间距5cm，适应极其紧密布局
            'min_wall_margin': 0.02,          # 极小边距2cm
            'allow_overlap_tolerance': 0.15,  # 大幅允许15cm的重叠容忍度
            'min_window_width': 0.3,          # 进一步减少最小宽度到30cm
            'min_window_height': 0.5          # 进一步减少最小高度到50cm
        }
        
        # 建筑规范约束 - 使用新的配置
        window_wall_ratio_config = constraints_config.get('window_wall_ratio', {})
        self.max_window_wall_ratio = window_wall_ratio_config.get('max', 0.85)
        self.min_window_wall_ratio = window_wall_ratio_config.get('min', 0.08)
        self.min_wall_margin = constraints_config.get('wall_margin', 0.15)
        self.max_opening_width = window_size_config.get('max_width', 3.5)
        
        # 结构约束
        self.structural_constraints = self.constraint_config.get('structural', {})
        self.min_wall_pier_width = self.structural_constraints.get('min_wall_pier_width', 0.8)
        self.max_lintel_span = self.structural_constraints.get('max_lintel_span', 3.5)
        
        # 功能约束
        self.functional_constraints = self.constraint_config.get('functional', {})
        self.min_daylight_factor = self.functional_constraints.get('min_daylight_factor', 0.02)
        self.max_glare_risk = self.functional_constraints.get('max_glare_risk', 0.4)
        
        # 立面边界
        self.facade_bounds = self._calculate_facade_bounds()
        
        # 约束权重
        self.constraint_weights = {
            ConstraintType.GEOMETRIC: 1.0,
            ConstraintType.BUILDING_CODE: 2.0,
            ConstraintType.STRUCTURAL: 3.0,
            ConstraintType.FUNCTIONAL: 1.5,
            ConstraintType.AESTHETIC: 0.5
        }
        
        self.logger.info("约束处理器初始化完成")
    
    def adjust_constraints_for_renovation_mode(self, renovation_mode: RenovationMode) -> None:
        """根据改造模式调整约束参数"""
        if renovation_mode == RenovationMode.STRICT_RENOVATION:
            # 应用宽松的严格改造约束
            self.min_window_spacing = self.strict_renovation_constraints['min_window_spacing']
            self.min_wall_margin = self.strict_renovation_constraints['min_wall_margin']
            self.min_window_width = self.strict_renovation_constraints['min_window_width']
            self.min_window_height = self.strict_renovation_constraints['min_window_height']
            
            self.logger.info(f"已调整为严格改造模式约束: 窗间距={self.min_window_spacing}m, "
                           f"边距={self.min_wall_margin}m, 最小尺寸={self.min_window_width}x{self.min_window_height}m")
        elif renovation_mode == RenovationMode.MAJOR_RENOVATION:
            # 大幅度改造模式：支持60%变化范围，宽松约束以支持窗户增删和遮阳窗框
            self.min_window_spacing = 0.3  # 适中间距30cm
            self.min_wall_margin = 0.15    # 适中边距15cm
            self.min_window_width = 0.4    # 较小最小宽度40cm
            self.min_window_height = 0.6   # 较小最小高度60cm
            
            # 放宽窗墙比约束以支持更多窗户
            self.max_window_wall_ratio = 0.8  # 提高到80%
            
            self.logger.info(f"已调整为大幅度改造模式约束: 窗间距={self.min_window_spacing}m, "
                           f"边距={self.min_wall_margin}m, 最小尺寸={self.min_window_width}x{self.min_window_height}m, "
                           f"支持60%变化范围和窗户增删")
        else:
            # 恢复默认约束
            self.min_window_spacing = self.geometric_constraints.get('min_window_spacing', 0.5)
            self.min_wall_margin = self.building_code_constraints.get('min_wall_margin', 0.2)
            self.min_window_width = self.geometric_constraints.get('min_window_width', 0.6)
            self.min_window_height = self.geometric_constraints.get('min_window_height', 0.8)
            
            self.logger.info(f"已恢复默认约束参数")
    
    @handle_exception
    def check_constraints(self, individual: FacadeIndividual) -> ConstraintResults:
        """
        检查个体的所有约束
        
        Args:
            individual: 待检查的立面个体
            
        Returns:
            约束检查结果
            
        Raises:
            ConstraintError: 约束检查失败时抛出
        """
        # 类型检查和兼容性处理
        if hasattr(individual, 'individual_id'):
            individual_id = individual.individual_id
        else:
            individual_id = "unknown_individual"
            
        with LogContext(f"约束检查 - {individual_id}", self.logger, level='DEBUG'):
            try:
                violations = []
                
                # 几何约束检查
                geometric_violations = self._check_geometric_constraints(individual)
                violations.extend(geometric_violations)
                
                # 建筑规范约束检查
                building_code_violations = self._check_building_code_constraints(individual)
                violations.extend(building_code_violations)
                
                # 结构约束检查
                structural_violations = self._check_structural_constraints(individual)
                violations.extend(structural_violations)
                
                # 功能约束检查
                functional_violations = self._check_functional_constraints(individual)
                violations.extend(functional_violations)
                
                # 美学约束检查
                aesthetic_violations = self._check_aesthetic_constraints(individual)
                violations.extend(aesthetic_violations)
                
                # 计算总约束违反度
                total_violation = self._calculate_total_violation(violations)
                
                # 判断个体可行性
                is_feasible = self._evaluate_feasibility(violations)
                
                # 生成修复建议
                repair_suggestions = self._generate_repair_suggestions(violations)
                
                constraint_results = ConstraintResults(
                    individual_id=individual_id,
                    violations=violations,
                    total_violation=total_violation,
                    is_feasible=is_feasible,
                    repair_suggestions=repair_suggestions,
                    constraint_summary=self._generate_constraint_summary(violations)
                )
                
                self.logger.debug(f"约束检查完成 {individual_id}: "
                                f"违反数={len(violations)}, 可行={is_feasible}, "
                                f"总违反度={total_violation:.3f}")
                
                return constraint_results
                
            except Exception as e:
                raise ConstraintError(f"约束检查失败: {str(e)}") from e
    
    def _check_geometric_constraints(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查几何约束"""
        violations = []
        
        for i in range(len(individual.window_positions)):
            window_id = f"window_{i}"
            position = individual.window_positions[i]
            size = individual.window_sizes[i]
            width, height = size
            
            # 窗户尺寸约束（降低严重性）
            if width < self.min_window_width:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MINOR,  # 从MAJOR改为MINOR
                    description=f"窗户{i}宽度不足: {width:.2f}m < {self.min_window_width}m",
                    violation_value=self.min_window_width - width,
                    affected_elements=[window_id]
                ))
            
            if width > self.max_window_width:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MINOR,  # 从MAJOR改为MINOR
                    description=f"窗户{i}宽度过大: {width:.2f}m > {self.max_window_width}m",
                    violation_value=width - self.max_window_width,
                    affected_elements=[window_id]
                ))
            
            if height < self.min_window_height:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MINOR,  # 从MAJOR改为MINOR
                    description=f"窗户{i}高度不足: {height:.2f}m < {self.min_window_height}m",
                    violation_value=self.min_window_height - height,
                    affected_elements=[window_id]
                ))
            
            if height > self.max_window_height:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MINOR,  # 从MAJOR改为MINOR
                    description=f"窗户{i}高度过大: {height:.2f}m > {self.max_window_height}m",
                    violation_value=height - self.max_window_height,
                    affected_elements=[window_id]
                ))
            
            # 窗户面积约束
            area = width * height
            if area < self.min_window_area:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MINOR,
                    description=f"窗户{i}面积不足: {area:.2f}m² < {self.min_window_area}m²",
                    violation_value=self.min_window_area - area,
                    affected_elements=[window_id]
                ))
            
            if area > self.max_window_area:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MAJOR,
                    description=f"窗户{i}面积过大: {area:.2f}m² > {self.max_window_area}m²",
                    violation_value=area - self.max_window_area,
                    affected_elements=[window_id]
                ))
            
            # 窗户位置边界约束
            x, y = position
            half_width, half_height = width / 2, height / 2
            
            if x - half_width < self.facade_bounds['min_x']:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MAJOR,  # 从CRITICAL改为MAJOR
                    description=f"窗户{i}超出左边界",
                    violation_value=self.facade_bounds['min_x'] - (x - half_width),
                    affected_elements=[window_id]
                ))
            
            if x + half_width > self.facade_bounds['max_x']:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MAJOR,  # 从CRITICAL改为MAJOR
                    description=f"窗户{i}超出右边界",
                    violation_value=(x + half_width) - self.facade_bounds['max_x'],
                    affected_elements=[window_id]
                ))
            
            if y - half_height < self.facade_bounds['min_y']:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MAJOR,  # 从CRITICAL改为MAJOR
                    description=f"窗户{i}超出下边界",
                    violation_value=self.facade_bounds['min_y'] - (y - half_height),
                    affected_elements=[window_id]
                ))
            
            if y + half_height > self.facade_bounds['max_y']:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.GEOMETRIC,
                    severity=ConstraintSeverity.MAJOR,  # 从CRITICAL改为MAJOR
                    description=f"窗户{i}超出上边界",
                    violation_value=(y + half_height) - self.facade_bounds['max_y'],
                    affected_elements=[window_id]
                ))
        
        # 窗户间距约束
        spacing_violations = self._check_window_spacing(individual)
        violations.extend(spacing_violations)
        
        # 窗户重叠约束
        overlap_violations = self._check_window_overlaps(individual)
        violations.extend(overlap_violations)
        
        return violations
    
    def _check_window_spacing(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查窗户间距约束 - 根据改造模式调整严格程度"""
        violations = []
        
        # 根据改造模式调整最小间距要求
        min_spacing = self.min_window_spacing
        severity = ConstraintSeverity.MAJOR
        
        if hasattr(individual, 'renovation_mode'):
            if individual.renovation_mode == RenovationMode.MAJOR_RENOVATION:
                min_spacing = max(0.1, self.min_window_spacing * 0.2)  # 第一阶段优化：减少80%间距要求，进一步放宽
                severity = ConstraintSeverity.WARNING  # 降低严重性，减少警告输出
            elif individual.renovation_mode == RenovationMode.STRICT_RENOVATION:
                min_spacing = self.strict_renovation_constraints.get('min_window_spacing', 0.02)  # 进一步放宽到2cm
                severity = ConstraintSeverity.WARNING  # 进一步降低严重性
        
        for i in range(len(individual.window_positions)):
            for j in range(i + 1, len(individual.window_positions)):
                pos1 = individual.window_positions[i]
                pos2 = individual.window_positions[j]
                size1 = individual.window_sizes[i]
                size2 = individual.window_sizes[j]
                
                # 计算窗户边缘距离
                edge_distance = self._calculate_window_edge_distance(
                    pos1, size1, pos2, size2
                )
                
                if edge_distance < min_spacing:
                    # 只有在违反程度较大时才记录为违反
                    violation_ratio = (min_spacing - edge_distance) / min_spacing
                    if violation_ratio > 0.1:  # 只有违反超过10%时才记录
                        violations.append(ConstraintViolation(
                            constraint_type=ConstraintType.GEOMETRIC,
                            severity=severity,
                            description=f"窗户{i}与窗户{j}距离{edge_distance:.3f}m < 最小距离{min_spacing:.3f}m",
                            violation_value=min_spacing - edge_distance,
                            affected_elements=[f"window_{i}", f"window_{j}"]
                        ))
                        
                        # 减少日志输出频率
                        if violation_ratio > 0.3:  # 只有严重违反时才输出警告
                            self.logger.warning(f"检测到窗户过近: 窗户{i}与窗户{j}距离{edge_distance:.3f}m < 最小距离{min_spacing:.3f}m")
        
        return violations
    
    def _check_window_overlaps(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """
        优化的窗户重叠检查 - 大幅减少不必要的警告
        """
        violations = []

        # 如果窗户数量少于2个，直接返回
        if len(individual.window_positions) < 2:
            return violations

        for i in range(len(individual.window_positions)):
            for j in range(i + 1, len(individual.window_positions)):
                pos1 = individual.window_positions[i]
                pos2 = individual.window_positions[j]
                size1 = individual.window_sizes[i]
                size2 = individual.window_sizes[j]

                # 使用更精确的重叠检查
                overlap_area = self._calculate_window_overlap_area_precise(pos1, size1, pos2, size2)

                # 大幅放宽容忍度，减少误报
                base_tolerance = 0.5  # 基础容忍度50cm²
                tolerance = base_tolerance
                severity = ConstraintSeverity.WARNING  # 默认为警告级别

                # 根据改造模式调整容忍度
                if hasattr(individual, 'renovation_mode'):
                    if individual.renovation_mode == RenovationMode.RENOVATION:
                        tolerance = 1.0  # 改造模式容忍度100cm²
                    elif individual.renovation_mode == RenovationMode.STRICT_RENOVATION:
                        tolerance = 1.5  # 严格改造模式容忍度150cm²

                # 只有在显著重叠时才记录违反
                if overlap_area > tolerance:
                    # 进一步筛选：只有重叠面积超过较大窗户面积的5%时才认为是真正的重叠
                    area1 = size1[0] * size1[1]
                    area2 = size2[0] * size2[1]
                    larger_area = max(area1, area2)
                    overlap_ratio = overlap_area / larger_area

                    if overlap_ratio > 0.05:  # 重叠面积超过较大窗户的5%
                        violations.append(ConstraintViolation(
                            constraint_type=ConstraintType.GEOMETRIC,
                            severity=severity,
                            description=f"窗户{i}与窗户{j}显著重叠{overlap_area:.2f}m²",
                            violation_value=overlap_area - tolerance,
                            affected_elements=[f"window_{i}", f"window_{j}"]
                        ))

                        # 大幅减少日志输出 - 只有非常严重的重叠才输出到终端
                        if overlap_area > tolerance * 2:  # 重叠面积超过容忍度2倍才输出
                            self.logger.debug(f"检测到显著窗户重叠: 窗户{i}与窗户{j}, 重叠面积{overlap_area:.2f}m²")

        return violations

    def _calculate_window_overlap_area_precise(self, pos1: Tuple[float, float], size1: Tuple[float, float],
                                             pos2: Tuple[float, float], size2: Tuple[float, float]) -> float:
        """
        精确计算两个窗户的重叠面积
        """
        try:
            x1, y1 = pos1
            w1, h1 = size1
            x2, y2 = pos2
            w2, h2 = size2

            # 计算窗户边界（中心点坐标转换为边界坐标）
            left1, right1 = x1 - w1/2, x1 + w1/2
            bottom1, top1 = y1 - h1/2, y1 + h1/2

            left2, right2 = x2 - w2/2, x2 + w2/2
            bottom2, top2 = y2 - h2/2, y2 + h2/2

            # 计算重叠区域
            overlap_left = max(left1, left2)
            overlap_right = min(right1, right2)
            overlap_bottom = max(bottom1, bottom2)
            overlap_top = min(top1, top2)

            # 检查是否有重叠
            if overlap_left >= overlap_right or overlap_bottom >= overlap_top:
                return 0.0

            # 计算重叠面积
            overlap_width = overlap_right - overlap_left
            overlap_height = overlap_top - overlap_bottom
            overlap_area = overlap_width * overlap_height

            # 添加数值稳定性检查
            if overlap_area < 1e-6:  # 小于0.001mm²的重叠认为是数值误差
                return 0.0

            return overlap_area

        except Exception as e:
            self.logger.warning(f"计算窗户重叠面积失败: {str(e)}")
            return 0.0

    def _calculate_window_overlap_area(self, pos1: Tuple[float, float], size1: Tuple[float, float],
                                      pos2: Tuple[float, float], size2: Tuple[float, float]) -> float:
        """计算两个窗户的重叠面积"""
        try:
            x1, y1 = pos1
            w1, h1 = size1
            x2, y2 = pos2
            w2, h2 = size2
            
            # 计算窗户边界
            left1, right1 = x1 - w1/2, x1 + w1/2
            bottom1, top1 = y1 - h1/2, y1 + h1/2
            
            left2, right2 = x2 - w2/2, x2 + w2/2
            bottom2, top2 = y2 - h2/2, y2 + h2/2
            
            # 计算重叠区域
            overlap_left = max(left1, left2)
            overlap_right = min(right1, right2)
            overlap_bottom = max(bottom1, bottom2)
            overlap_top = min(top1, top2)
            
            # 如果有重叠，计算重叠面积
            if overlap_left < overlap_right and overlap_bottom < overlap_top:
                overlap_width = overlap_right - overlap_left
                overlap_height = overlap_top - overlap_bottom
                return overlap_width * overlap_height
            else:
                return 0.0
                
        except Exception as e:
            self.logger.warning(f"计算窗户重叠面积失败: {str(e)}")
            return 0.0
    
    def _check_building_code_constraints(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查建筑规范约束"""
        violations = []
        
        # 窗墙比约束
        window_wall_ratio = self._calculate_window_wall_ratio(individual)
        
        if window_wall_ratio > self.max_window_wall_ratio:
            violations.append(ConstraintViolation(
                constraint_type=ConstraintType.BUILDING_CODE,
                severity=ConstraintSeverity.CRITICAL,
                description=f"窗墙比超标: {window_wall_ratio:.3f} > {self.max_window_wall_ratio}",
                violation_value=window_wall_ratio - self.max_window_wall_ratio,
                affected_elements=["facade"]
            ))
        
        if window_wall_ratio < self.min_window_wall_ratio:
            violations.append(ConstraintViolation(
                constraint_type=ConstraintType.BUILDING_CODE,
                severity=ConstraintSeverity.MINOR,
                description=f"窗墙比不足: {window_wall_ratio:.3f} < {self.min_window_wall_ratio}",
                violation_value=self.min_window_wall_ratio - window_wall_ratio,
                affected_elements=["facade"]
            ))
        
        # 单个开口最大宽度约束
        for i, (width, height) in enumerate(individual.window_sizes):
            if width > self.max_opening_width:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.BUILDING_CODE,
                    severity=ConstraintSeverity.MAJOR,
                    description=f"窗户{i}开口宽度超标: {width:.2f}m > {self.max_opening_width}m",
                    violation_value=width - self.max_opening_width,
                    affected_elements=[f"window_{i}"]
                ))
        
        return violations
    
    def _check_structural_constraints(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查结构约束"""
        violations = []
        
        # 墙体支撑柱宽度约束
        wall_pier_violations = self._check_wall_pier_widths(individual)
        violations.extend(wall_pier_violations)
        
        # 过梁跨度约束
        lintel_violations = self._check_lintel_spans(individual)
        violations.extend(lintel_violations)
        
        return violations
    
    def _check_wall_pier_widths(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查墙体支撑柱宽度"""
        violations = []
        
        if len(individual.window_positions) < 2:
            return violations
        
        # 按水平位置排序窗户
        windows_with_indices = list(enumerate(individual.window_positions))
        windows_with_indices.sort(key=lambda x: x[1][0])  # 按x坐标排序
        
        for i in range(len(windows_with_indices) - 1):
            current_idx, current_pos = windows_with_indices[i]
            next_idx, next_pos = windows_with_indices[i + 1]
            
            current_size = individual.window_sizes[current_idx]
            next_size = individual.window_sizes[next_idx]
            
            # 计算墙体支撑柱宽度
            pier_width = self._calculate_wall_pier_width(
                current_pos, current_size, next_pos, next_size
            )
            
            if pier_width < self.min_wall_pier_width:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.STRUCTURAL,
                    severity=ConstraintSeverity.CRITICAL,
                    description=f"墙体支撑柱宽度不足: {pier_width:.2f}m < {self.min_wall_pier_width}m",
                    violation_value=self.min_wall_pier_width - pier_width,
                    affected_elements=[f"window_{current_idx}", f"window_{next_idx}"]
                ))
        
        return violations
    
    def _check_lintel_spans(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查过梁跨度约束"""
        violations = []
        
        for i, (width, height) in enumerate(individual.window_sizes):
            if width > self.max_lintel_span:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.STRUCTURAL,
                    severity=ConstraintSeverity.MAJOR,
                    description=f"窗户{i}过梁跨度过大: {width:.2f}m > {self.max_lintel_span}m",
                    violation_value=width - self.max_lintel_span,
                    affected_elements=[f"window_{i}"]
                ))
        
        return violations
    
    def _check_functional_constraints(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查功能约束"""
        violations = []
        
        # 采光要求约束
        daylight_factor = self._calculate_daylight_factor(individual)
        if daylight_factor < self.min_daylight_factor:
            violations.append(ConstraintViolation(
                constraint_type=ConstraintType.FUNCTIONAL,
                severity=ConstraintSeverity.MAJOR,
                description=f"采光系数不足: {daylight_factor:.3f} < {self.min_daylight_factor}",
                violation_value=self.min_daylight_factor - daylight_factor,
                affected_elements=["facade"]
            ))
        
        # 眩光风险约束
        glare_risk = self._calculate_glare_risk(individual)
        if glare_risk > self.max_glare_risk:
            violations.append(ConstraintViolation(
                constraint_type=ConstraintType.FUNCTIONAL,
                severity=ConstraintSeverity.MINOR,
                description=f"眩光风险过高: {glare_risk:.3f} > {self.max_glare_risk}",
                violation_value=glare_risk - self.max_glare_risk,
                affected_elements=["facade"]
            ))
        
        return violations
    
    def _check_aesthetic_constraints(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查美学约束"""
        violations = []
        
        # 窗户尺寸比例约束
        proportion_violations = self._check_window_proportions(individual)
        violations.extend(proportion_violations)
        
        # 立面对称性约束
        symmetry_violations = self._check_facade_symmetry(individual)
        violations.extend(symmetry_violations)
        
        return violations
    
    def _check_window_proportions(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查窗户比例约束"""
        violations = []
        
        # 理想宽高比范围
        ideal_ratio_range = [1.2, 2.0]  # 1.2:1 到 2:1
        
        for i, (width, height) in enumerate(individual.window_sizes):
            ratio = width / height if height > 0 else float('inf')
            
            if ratio < ideal_ratio_range[0]:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.AESTHETIC,
                    severity=ConstraintSeverity.WARNING,
                    description=f"窗户{i}过高: 宽高比 {ratio:.2f} < {ideal_ratio_range[0]}",
                    violation_value=ideal_ratio_range[0] - ratio,
                    affected_elements=[f"window_{i}"]
                ))
            elif ratio > ideal_ratio_range[1]:
                violations.append(ConstraintViolation(
                    constraint_type=ConstraintType.AESTHETIC,
                    severity=ConstraintSeverity.WARNING,
                    description=f"窗户{i}过宽: 宽高比 {ratio:.2f} > {ideal_ratio_range[1]}",
                    violation_value=ratio - ideal_ratio_range[1],
                    affected_elements=[f"window_{i}"]
                ))
        
        return violations
    
    def _check_facade_symmetry(self, individual: FacadeIndividual) -> List[ConstraintViolation]:
        """检查立面对称性"""
        violations = []
        
        # 简化的对称性检查
        if len(individual.window_positions) < 2:
            return violations
        
        # 计算立面中心线
        center_x = (self.facade_bounds['min_x'] + self.facade_bounds['max_x']) / 2
        
        # 检查窗户是否大致对称分布
        left_windows = sum(1 for pos, _ in zip(individual.window_positions, individual.window_sizes) if pos[0] < center_x)
        right_windows = len(individual.window_positions) - left_windows
        
        asymmetry = abs(left_windows - right_windows)
        if asymmetry > 2 and len(individual.window_positions) > 4:  # 允许小幅不对称
            violations.append(ConstraintViolation(
                constraint_type=ConstraintType.AESTHETIC,
                severity=ConstraintSeverity.WARNING,
                description=f"立面不对称: 左侧{left_windows}个窗户，右侧{right_windows}个窗户",
                violation_value=asymmetry / len(individual.window_positions),
                affected_elements=["facade"]
            ))
        
        return violations
    
    def _calculate_facade_bounds(self) -> Dict[str, float]:
        """计算立面边界"""
        if self.facade_elements.image_shape[0] == 0:
            return {
                'min_x': self.min_wall_margin,
                'max_x': 10.0 - self.min_wall_margin,
                'min_y': self.min_wall_margin,
                'max_y': 3.0 - self.min_wall_margin
            }
        
        height, width = self.facade_elements.image_shape[:2]
        pixel_ratio = self.facade_elements.pixel_to_meter_ratio
        
        facade_width_m = width * pixel_ratio
        facade_height_m = height * pixel_ratio
        
        return {
            'min_x': self.min_wall_margin,
            'max_x': facade_width_m - self.min_wall_margin,
            'min_y': self.min_wall_margin,
            'max_y': facade_height_m - self.min_wall_margin
        }
    
    def _calculate_window_wall_ratio(self, individual: FacadeIndividual) -> float:
        """计算窗墙比"""
        total_window_area = sum(w * h for w, h in individual.window_sizes)
        facade_area = ((self.facade_bounds['max_x'] - self.facade_bounds['min_x']) *
                      (self.facade_bounds['max_y'] - self.facade_bounds['min_y']))
        return total_window_area / facade_area if facade_area > 0 else 0.0
    
    def _calculate_window_edge_distance(self, pos1: Tuple[float, float], size1: Tuple[float, float],
                                      pos2: Tuple[float, float], size2: Tuple[float, float]) -> float:
        """计算两个窗户边缘的最短距离"""
        x1, y1 = pos1
        w1, h1 = size1
        x2, y2 = pos2
        w2, h2 = size2
        
        # 窗户边界
        x1_min, x1_max = x1 - w1/2, x1 + w1/2
        y1_min, y1_max = y1 - h1/2, y1 + h1/2
        x2_min, x2_max = x2 - w2/2, x2 + w2/2
        y2_min, y2_max = y2 - h2/2, y2 + h2/2
        
        # 计算最短距离
        dx = max(0, max(x1_min - x2_max, x2_min - x1_max))
        dy = max(0, max(y1_min - y2_max, y2_min - y1_max))
        
        return math.sqrt(dx*dx + dy*dy)
    
    def _windows_overlap(self, individual: FacadeIndividual, i: int, j: int) -> bool:
        """检查两个窗户是否重叠"""
        pos1 = individual.window_positions[i]
        size1 = individual.window_sizes[i]
        pos2 = individual.window_positions[j]
        size2 = individual.window_sizes[j]
        
        x1, y1 = pos1
        w1, h1 = size1
        x2, y2 = pos2
        w2, h2 = size2
        
        # 窗户边界
        x1_min, x1_max = x1 - w1/2, x1 + w1/2
        y1_min, y1_max = y1 - h1/2, y1 + h1/2
        x2_min, x2_max = x2 - w2/2, x2 + w2/2
        y2_min, y2_max = y2 - h2/2, y2 + h2/2
        
        # 检查重叠
        return not (x1_max <= x2_min or x2_max <= x1_min or y1_max <= y2_min or y2_max <= y1_min)
    
    def _calculate_wall_pier_width(self, pos1: Tuple[float, float], size1: Tuple[float, float],
                                 pos2: Tuple[float, float], size2: Tuple[float, float]) -> float:
        """计算墙体支撑柱宽度"""
        x1, y1 = pos1
        w1, h1 = size1
        x2, y2 = pos2
        w2, h2 = size2
        
        # 确保x1 < x2
        if x1 > x2:
            x1, x2 = x2, x1
            w1, w2 = w2, w1
        
        # 计算支撑柱宽度
        pier_width = (x2 - w2/2) - (x1 + w1/2)
        return max(0, pier_width)
    
    def _calculate_daylight_factor(self, individual: FacadeIndividual) -> float:
        """计算采光系数"""
        window_wall_ratio = self._calculate_window_wall_ratio(individual)
        # 简化的采光系数计算
        return min(window_wall_ratio / 0.3, 1.0)  # 30%窗墙比对应100%采光
    
    def _calculate_glare_risk(self, individual: FacadeIndividual) -> float:
        """计算眩光风险"""
        window_wall_ratio = self._calculate_window_wall_ratio(individual)
        
        # 计算遮阳覆盖率
        shading_coverage = sum(
            1.0 if wt == 2 else 0.0 
            for wt in individual.window_types
        ) / len(individual.window_types) if individual.window_types else 0.0
        
        # 眩光风险与窗墙比正相关，与遮阳覆盖率负相关
        base_risk = window_wall_ratio
        shading_reduction = shading_coverage * 0.6  # 遮阳最多减少60%眩光风险
        
        return max(0, base_risk - shading_reduction)
    
    def _calculate_total_violation(self, violations: List[ConstraintViolation]) -> float:
        """计算总约束违反度"""
        total_violation = 0.0
        
        for violation in violations:
            # 根据约束类型和严重性计算权重
            type_weight = self.constraint_weights.get(violation.constraint_type, 1.0)
            
            severity_multiplier = {
                ConstraintSeverity.CRITICAL: 10.0,
                ConstraintSeverity.MAJOR: 5.0,
                ConstraintSeverity.MINOR: 2.0,
                ConstraintSeverity.WARNING: 1.0
            }.get(violation.severity, 1.0)
            
            weighted_violation = violation.violation_value * type_weight * severity_multiplier
            total_violation += weighted_violation
        
        return total_violation
    
    def _evaluate_feasibility(self, violations: List[ConstraintViolation]) -> bool:
        """评估个体可行性（极其宽松的条件以适应严格改造模式）"""
        # 计算严重违反数量
        critical_violations = sum(1 for v in violations if v.severity == ConstraintSeverity.CRITICAL)
        if critical_violations > 10:  # 进一步放宽，允许最多10个严重违反
            return False
        
        # 极其宽松的主要违反限制
        major_violations = sum(1 for v in violations if v.severity == ConstraintSeverity.MAJOR)
        if major_violations > 50:  # 从20进一步增加到50
            return False
        
        # 极其宽松的总违反度阈值
        total_violation = self._calculate_total_violation(violations)
        if total_violation > 1000.0:  # 从200进一步增加到1000
            return False
        
        return True
    
    def _generate_repair_suggestions(self, violations: List[ConstraintViolation]) -> List[str]:
        """生成修复建议"""
        suggestions = []
        
        # 按约束类型分组违反
        violation_groups = {}
        for violation in violations:
            constraint_type = violation.constraint_type
            if constraint_type not in violation_groups:
                violation_groups[constraint_type] = []
            violation_groups[constraint_type].append(violation)
        
        # 为每种类型的违反生成建议
        for constraint_type, type_violations in violation_groups.items():
            if constraint_type == ConstraintType.GEOMETRIC:
                suggestions.extend(self._generate_geometric_repair_suggestions(type_violations))
            elif constraint_type == ConstraintType.BUILDING_CODE:
                suggestions.extend(self._generate_building_code_repair_suggestions(type_violations))
            elif constraint_type == ConstraintType.STRUCTURAL:
                suggestions.extend(self._generate_structural_repair_suggestions(type_violations))
            elif constraint_type == ConstraintType.FUNCTIONAL:
                suggestions.extend(self._generate_functional_repair_suggestions(type_violations))
            elif constraint_type == ConstraintType.AESTHETIC:
                suggestions.extend(self._generate_aesthetic_repair_suggestions(type_violations))
        
        return suggestions
    
    def _generate_geometric_repair_suggestions(self, violations: List[ConstraintViolation]) -> List[str]:
        """生成几何约束修复建议"""
        suggestions = []
        
        for violation in violations:
            if "宽度不足" in violation.description:
                suggestions.append("增加窗户宽度")
            elif "宽度过大" in violation.description:
                suggestions.append("减少窗户宽度")
            elif "高度不足" in violation.description:
                suggestions.append("增加窗户高度")
            elif "高度过大" in violation.description:
                suggestions.append("减少窗户高度")
            elif "面积不足" in violation.description:
                suggestions.append("增加窗户尺寸")
            elif "面积过大" in violation.description:
                suggestions.append("减少窗户尺寸")
            elif "超出边界" in violation.description:
                suggestions.append("调整窗户位置到合法范围内")
            elif "间距不足" in violation.description:
                suggestions.append("增加窗户间距")
            elif "重叠" in violation.description:
                suggestions.append("调整窗户位置避免重叠")
        
        return list(set(suggestions))  # 去重
    
    def _generate_building_code_repair_suggestions(self, violations: List[ConstraintViolation]) -> List[str]:
        """生成建筑规范修复建议"""
        suggestions = []
        
        for violation in violations:
            if "窗墙比超标" in violation.description:
                suggestions.append("减少窗户数量或尺寸以降低窗墙比")
            elif "窗墙比不足" in violation.description:
                suggestions.append("增加窗户数量或尺寸以提高窗墙比")
            elif "开口宽度超标" in violation.description:
                suggestions.append("分割大窗户为多个小窗户")
        
        return suggestions
    
    def _generate_structural_repair_suggestions(self, violations: List[ConstraintViolation]) -> List[str]:
        """生成结构约束修复建议"""
        suggestions = []
        
        for violation in violations:
            if "支撑柱宽度不足" in violation.description:
                suggestions.append("增加窗户间距或减少窗户宽度")
            elif "过梁跨度过大" in violation.description:
                suggestions.append("减少窗户宽度或添加中间支撑")
        
        return suggestions
    
    def _generate_functional_repair_suggestions(self, violations: List[ConstraintViolation]) -> List[str]:
        """生成功能约束修复建议"""
        suggestions = []
        
        for violation in violations:
            if "采光系数不足" in violation.description:
                suggestions.append("增加窗户面积以改善采光")
            elif "眩光风险过高" in violation.description:
                suggestions.append("添加遮阳设施或减少窗户面积")
        
        return suggestions
    
    def _generate_aesthetic_repair_suggestions(self, violations: List[ConstraintViolation]) -> List[str]:
        """生成美学约束修复建议"""
        suggestions = []
        
        for violation in violations:
            if "过高" in violation.description:
                suggestions.append("调整窗户宽高比，增加宽度或减少高度")
            elif "过宽" in violation.description:
                suggestions.append("调整窗户宽高比，减少宽度或增加高度")
            elif "不对称" in violation.description:
                suggestions.append("调整窗户布局以提高对称性")
        
        return suggestions
    
    def _generate_constraint_summary(self, violations: List[ConstraintViolation]) -> Dict[str, Any]:
        """生成约束检查总结"""
        summary = {
            'total_violations': len(violations),
            'by_type': {},
            'by_severity': {},
            'most_violated_elements': {}
        }
        
        # 按类型统计
        for violation in violations:
            constraint_type = violation.constraint_type.value
            if constraint_type not in summary['by_type']:
                summary['by_type'][constraint_type] = 0
            summary['by_type'][constraint_type] += 1
        
        # 按严重性统计
        for violation in violations:
            severity = violation.severity.value
            if severity not in summary['by_severity']:
                summary['by_severity'][severity] = 0
            summary['by_severity'][severity] += 1
        
        # 统计最常违反的元素
        element_violations = {}
        for violation in violations:
            for element in violation.affected_elements:
                if element not in element_violations:
                    element_violations[element] = 0
                element_violations[element] += 1
        
        # 取前5个最常违反的元素
        summary['most_violated_elements'] = dict(
            sorted(element_violations.items(), key=lambda x: x[1], reverse=True)[:5]
        )
        
        return summary


def create_constraint_handler(facade_elements: FacadeElements) -> ConstraintHandler:
    """
    创建约束处理器实例
    
    Args:
        facade_elements: 立面元素数据
        
    Returns:
        配置好的约束处理器
    """
    return ConstraintHandler(facade_elements)