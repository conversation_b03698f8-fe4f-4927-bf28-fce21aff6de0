"""
核心数据结构定义
定义了整个建筑立面优化系统中使用的主要数据结构
"""

from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any, Union
import numpy as np
from enum import Enum
import json
from datetime import datetime


class ElementType(Enum):
    """建筑元素类型枚举"""
    WALL = "walls"
    WINDOW = "windows"
    DOOR = "doors"
    SHADING = "shading"
    FRAME = "frames"


class RenovationMode(Enum):
    """改造模式枚举"""
    NEW_CONSTRUCTION = "new_construction"  # 新建项目：自由添加窗户大小位置和窗框或者遮阳
    RENOVATION = "renovation"              # 改造项目：不能增删窗户，窗户只能横向变化大小，高度不变，生成窗框或者遮阳
    MAJOR_RENOVATION = "major_renovation"  # 大幅度改造：可以自由增删窗户，原有窗户可以自由变化
    STRICT_RENOVATION = "strict_renovation"  # 特别严格改造：只允许窗户水平宽度变化，中轴对称，严禁增删窗户，严禁重叠


class Orientation(Enum):
    """建筑朝向枚举"""
    SOUTH = "south"
    SOUTHEAST = "southeast"
    SOUTHWEST = "southwest"
    EAST = "east"
    WEST = "west"
    NORTH = "north"
    NORTHEAST = "northeast"
    NORTHWEST = "northwest"


@dataclass
class BuildingElement:
    """建筑元素基础数据结构"""
    element_id: str
    element_type: ElementType
    bbox: Tuple[float, float, float, float]  # (x_min, y_min, x_max, y_max)
    center: Tuple[float, float]              # (cx, cy)
    area: float
    perimeter: float
    width: float
    height: float
    aspect_ratio: float
    properties: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'element_id': self.element_id,
            'element_type': self.element_type.value,
            'bbox': self.bbox,
            'center': self.center,
            'area': self.area,
            'perimeter': self.perimeter,
            'width': self.width,
            'height': self.height,
            'aspect_ratio': self.aspect_ratio,
            'properties': self.properties
        }


@dataclass
class WindowElement(BuildingElement):
    """窗户元素数据结构"""
    window_type: int = 0                     # 0: 普通窗户, 1: 带窗框, 2: 带遮阳
    frame_depth: float = 0.0                 # 窗框伸出深度（米）
    shading_depth: float = 0.0               # 遮阳伸出深度（米）
    shading_angle: float = 0.0               # 遮阳角度（度）
    glazing_ratio: float = 0.8               # 玻璃比例
    u_value: float = 2.5                     # 传热系数 W/(m²·K)
    
    def __post_init__(self):
        if self.element_type != ElementType.WINDOW:
            self.element_type = ElementType.WINDOW


@dataclass
class WallElement(BuildingElement):
    """墙体元素数据结构"""
    wall_type: str = "concrete"              # 墙体类型
    thickness: float = 0.2                   # 墙体厚度（米）
    u_value: float = 1.5                     # 传热系数 W/(m²·K)
    thermal_mass: float = 500.0              # 热质量 J/(m²·K)
    
    def __post_init__(self):
        if self.element_type != ElementType.WALL:
            self.element_type = ElementType.WALL


@dataclass
class FacadeElements:
    """立面元素集合"""
    walls: List[WallElement] = field(default_factory=list)
    windows: List[WindowElement] = field(default_factory=list)
    doors: List[BuildingElement] = field(default_factory=list)
    shading: List[BuildingElement] = field(default_factory=list)
    frames: List[BuildingElement] = field(default_factory=list)
    image_shape: Tuple[int, int, int] = (0, 0, 0)  # (height, width, channels)
    pixel_to_meter_ratio: float = 0.01
    original_image_path: Optional[str] = None  # 原始图像文件路径
    building_orientation: Optional[Orientation] = None  # 建筑朝向
    individual_id: Optional[str] = None  # 添加个体ID属性以兼容性能分析器
    
    def get_all_elements(self) -> List[BuildingElement]:
        """获取所有元素的列表"""
        return self.walls + self.windows + self.doors + self.shading + self.frames
    
    def get_elements_by_type(self, element_type: ElementType) -> List[BuildingElement]:
        """根据类型获取元素"""
        type_mapping = {
            ElementType.WALL: self.walls,
            ElementType.WINDOW: self.windows,
            ElementType.DOOR: self.doors,
            ElementType.SHADING: self.shading,
            ElementType.FRAME: self.frames
        }
        return type_mapping.get(element_type, [])
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'walls': [w.to_dict() for w in self.walls],
            'windows': [w.to_dict() for w in self.windows],
            'doors': [d.to_dict() for d in self.doors],
            'shading': [s.to_dict() for s in self.shading],
            'frames': [f.to_dict() for f in self.frames],
            'image_shape': self.image_shape,
            'pixel_to_meter_ratio': self.pixel_to_meter_ratio
        }


@dataclass
class HourlyClimateData:
    """小时气候数据"""
    hour: int                                # 小时索引 (0-8759)
    month: int                              # 月份 (1-12)
    day: int                                # 日期 (1-31)
    time: int                               # 小时 (0-23)
    dry_bulb_temperature: float             # 干球温度 (°C)
    dew_point_temperature: float            # 露点温度 (°C)
    relative_humidity: float                # 相对湿度 (%)
    atmospheric_pressure: float             # 大气压 (Pa)
    wind_speed: float                       # 风速 (m/s)
    wind_direction: float                   # 风向 (°)
    direct_normal_irradiance: float         # 法向直射辐射 (W/m²)
    diffuse_horizontal_irradiance: float    # 水平散射辐射 (W/m²)
    global_horizontal_irradiance: float     # 水平总辐射 (W/m²)
    sky_cover: float                        # 云量 (0-10)


@dataclass
class OrientedSolarData:
    """朝向调整后的太阳辐射数据"""
    orientation: Orientation
    hourly_irradiance: List[float]          # 8760小时朝向相关的太阳辐射
    peak_irradiance: float                   # 峰值辐射
    daily_totals: List[float]               # 365天的日总辐射
    seasonal_averages: Dict[str, float]     # 四季平均辐射


@dataclass
class OrientedWindData:
    """朝向调整后的风数据"""
    orientation: Orientation
    hourly_wind_effects: List[float]        # 8760小时朝向相关的风影响系数
    prevailing_direction_factor: float      # 主导风向影响因子
    seasonal_patterns: Dict[str, float]     # 季节性风向模式


@dataclass
class OrientedClimateData:
    """朝向调整后的气候数据"""
    orientation: Orientation
    hourly_data: List[HourlyClimateData]
    solar_data: OrientedSolarData
    wind_data: OrientedWindData
    location_info: Dict[str, Any] = field(default_factory=dict)
    
    def get_seasonal_data(self, season: str) -> List[HourlyClimateData]:
        """获取季节性数据"""
        season_months = {
            'spring': [3, 4, 5],
            'summer': [6, 7, 8], 
            'autumn': [9, 10, 11],
            'winter': [12, 1, 2]
        }
        
        months = season_months.get(season, [])
        return [data for data in self.hourly_data if data.month in months]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于JSON序列化）"""
        return {
            'orientation': self.orientation.value,
            'location_info': self.location_info,
            'data_count': len(self.hourly_data),
            'solar_data': {
                'orientation': self.solar_data.orientation.value,
                'peak_irradiance': self.solar_data.peak_irradiance,
                'seasonal_averages': self.solar_data.seasonal_averages,
                'daily_totals_count': len(self.solar_data.daily_totals)
            },
            'wind_data': {
                'orientation': self.wind_data.orientation.value,
                'prevailing_direction_factor': self.wind_data.prevailing_direction_factor,
                'seasonal_patterns': self.wind_data.seasonal_patterns
            },
            'hourly_data_summary': {
                'temperature_range': [
                    min(h.dry_bulb_temperature for h in self.hourly_data),
                    max(h.dry_bulb_temperature for h in self.hourly_data)
                ] if self.hourly_data else [0, 0],
                'humidity_range': [
                    min(h.relative_humidity for h in self.hourly_data),
                    max(h.relative_humidity for h in self.hourly_data)
                ] if self.hourly_data else [0, 0]
            }
        }


@dataclass
class FacadeIndividual:
    """立面优化个体编码"""
    individual_id: str
    
    # 窗户参数
    window_positions: List[Tuple[float, float]]  # 窗户位置 (x, y)
    window_sizes: List[Tuple[float, float]]      # 窗户尺寸 (width, height)
    window_types: List[int]                      # 窗户类型 (0: 普通, 1: 窗框, 2: 遮阳)
    
    # 窗框和遮阳参数
    frame_depths: List[float]                    # 窗框伸出深度
    shading_depths: List[float]                  # 遮阳伸出深度
    shading_angles: List[float]                  # 遮阳角度
    
    # 改造模式
    renovation_mode: RenovationMode
    
    # 目标函数值
    energy_consumption: float = 0.0              # 能耗指标 (kWh/m²/year)
    thermal_performance: float = 0.0             # 热工性能指标
    renovation_cost: float = 0.0                 # 改造成本指标 (元/m²)
    
    # 约束违反度
    constraint_violations: Dict[str, float] = field(default_factory=dict)
    
    # 辅助信息
    generation: int = 0                          # 代数
    rank: int = 0                               # Pareto排名
    crowding_distance: float = 0.0              # 拥挤距离
    
    def get_objectives(self) -> Tuple[float, float, float]:
        """获取三个目标函数值"""
        return (self.energy_consumption, -self.thermal_performance, self.renovation_cost)
    
    def is_dominated_by(self, other: 'FacadeIndividual') -> bool:
        """判断是否被其他个体支配"""
        obj1 = self.get_objectives()
        obj2 = other.get_objectives()
        
        # 至少在一个目标上更差，且在所有目标上不更好
        worse_in_any = any(obj1[i] > obj2[i] for i in range(3))
        better_in_any = any(obj1[i] < obj2[i] for i in range(3))
        
        return worse_in_any and not better_in_any
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'individual_id': self.individual_id,
            'window_positions': self.window_positions,
            'window_sizes': self.window_sizes,
            'window_types': self.window_types,
            'frame_depths': self.frame_depths,
            'shading_depths': self.shading_depths,
            'shading_angles': self.shading_angles,
            'renovation_mode': self.renovation_mode.value,
            'energy_consumption': self.energy_consumption,
            'thermal_performance': self.thermal_performance,
            'renovation_cost': self.renovation_cost,
            'constraint_violations': self.constraint_violations,
            'generation': self.generation,
            'rank': self.rank,
            'crowding_distance': self.crowding_distance
        }


@dataclass
class ConvergenceHistory:
    """算法收敛历史数据"""
    generations: List[int] = field(default_factory=list)
    hypervolume_values: List[float] = field(default_factory=list)
    spacing_values: List[float] = field(default_factory=list)
    spread_values: List[float] = field(default_factory=list)
    best_objectives: List[Tuple[float, float, float]] = field(default_factory=list)
    population_diversity: List[float] = field(default_factory=list)


@dataclass
class BestSolutions:
    """四维度最佳解决方案"""
    comprehensive_best: FacadeIndividual
    energy_best: FacadeIndividual
    performance_best: FacadeIndividual
    cost_best: FacadeIndividual
    
    def get_all_solutions(self) -> List[FacadeIndividual]:
        """获取所有最佳解决方案"""
        return [
            self.comprehensive_best,
            self.energy_best,
            self.performance_best,
            self.cost_best
        ]


@dataclass
class OptimizationResults:
    """优化结果数据结构"""
    pareto_solutions: List[FacadeIndividual]
    convergence_history: ConvergenceHistory
    best_solutions: BestSolutions
    total_generations: int
    total_evaluations: int
    computation_time: float
    optimization_config: Dict[str, Any] = field(default_factory=dict)
    
    def get_pareto_front(self) -> List[FacadeIndividual]:
        """获取帕累托前沿解"""
        return [ind for ind in self.pareto_solutions if ind.rank == 0]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'pareto_solutions': [sol.to_dict() for sol in self.pareto_solutions],
            'convergence_history': {
                'generations': self.convergence_history.generations,
                'hypervolume_values': self.convergence_history.hypervolume_values,
                'spacing_values': self.convergence_history.spacing_values,
                'spread_values': self.convergence_history.spread_values,
                'best_objectives': self.convergence_history.best_objectives,
                'population_diversity': self.convergence_history.population_diversity
            },
            'best_solutions': {
                'comprehensive_best': self.best_solutions.comprehensive_best.to_dict(),
                'energy_best': self.best_solutions.energy_best.to_dict(),
                'performance_best': self.best_solutions.performance_best.to_dict(),
                'cost_best': self.best_solutions.cost_best.to_dict()
            },
            'total_generations': self.total_generations,
            'total_evaluations': self.total_evaluations,
            'computation_time': self.computation_time,
            'optimization_config': self.optimization_config
        }


@dataclass
class EnergyBreakdown:
    """能耗分解数据"""
    heating_energy: float                    # 供暖能耗 (kWh/m²/year)
    cooling_energy: float                    # 制冷能耗 (kWh/m²/year)
    lighting_energy: float                   # 照明能耗 (kWh/m²/year)
    ventilation_energy: float                # 通风能耗 (kWh/m²/year)
    total_energy: float                      # 总能耗 (kWh/m²/year)
    monthly_breakdown: List[float]           # 月度能耗分解
    daily_profiles: List[List[float]]        # 日负荷曲线（365天x24小时）


@dataclass
class ThermalPerformanceMetrics:
    """热工性能指标"""
    thermal_transmittance: float             # 传热系数 W/(m²·K)
    thermal_bridge_effect: float            # 热桥效应系数
    thermal_inertia: float                   # 热惰性时间常数 (hours)
    comfort_hours: float                     # 舒适小时数 (hours/year)
    operative_temperature_range: Tuple[float, float]  # 操作温度范围 (°C)
    seasonal_performance: Dict[str, float]   # 季节性性能指标


@dataclass
class CostBreakdown:
    """成本分解数据"""
    material_cost: float                     # 材料成本 (元)
    labor_cost: float                        # 人工成本 (元)
    equipment_cost: float                    # 设备成本 (元)
    maintenance_cost: float                  # 维护成本 (元/year)
    total_initial_cost: float                # 总初始成本 (元)
    lifecycle_cost: float                    # 生命周期成本 (元)
    cost_per_area: float                     # 单位面积成本 (元/m²)
    baseline_energy_cost: float = 0.0        # 基准能源成本 (元/year) - 修复可视化错误
    energy_savings: float = 0.0              # 能源节约 (元/year)
    payback_period: float = 0.0              # 投资回收期 (年)


@dataclass
class VisualizationData:
    """可视化数据结构"""
    facade_elements: FacadeElements
    climate_data: OrientedClimateData
    optimization_results: OptimizationResults
    energy_breakdown: EnergyBreakdown
    thermal_metrics: ThermalPerformanceMetrics
    cost_breakdown: CostBreakdown
    
    # 统计分析数据
    correlation_matrix: Optional[np.ndarray] = None
    clustering_results: Optional[Dict[str, Any]] = None
    pca_results: Optional[Dict[str, Any]] = None
    
    # 原始图像数据
    original_image: Optional[np.ndarray] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于JSON序列化）"""
        result = {
            'facade_elements': self.facade_elements.to_dict(),
            'orientation': self.climate_data.orientation.value,
            'optimization_results': self.optimization_results.to_dict(),
            'energy_breakdown': {
                'heating_energy': self.energy_breakdown.heating_energy,
                'cooling_energy': self.energy_breakdown.cooling_energy,
                'lighting_energy': self.energy_breakdown.lighting_energy,
                'ventilation_energy': self.energy_breakdown.ventilation_energy,
                'total_energy': self.energy_breakdown.total_energy,
                'monthly_breakdown': self.energy_breakdown.monthly_breakdown
            },
            'thermal_metrics': {
                'thermal_transmittance': self.thermal_metrics.thermal_transmittance,
                'thermal_bridge_effect': self.thermal_metrics.thermal_bridge_effect,
                'thermal_inertia': self.thermal_metrics.thermal_inertia,
                'comfort_hours': self.thermal_metrics.comfort_hours,
                'operative_temperature_range': self.thermal_metrics.operative_temperature_range,
                'seasonal_performance': self.thermal_metrics.seasonal_performance
            },
            'cost_breakdown': {
                'material_cost': self.cost_breakdown.material_cost,
                'labor_cost': self.cost_breakdown.labor_cost,
                'equipment_cost': self.cost_breakdown.equipment_cost,
                'maintenance_cost': self.cost_breakdown.maintenance_cost,
                'total_initial_cost': self.cost_breakdown.total_initial_cost,
                'lifecycle_cost': self.cost_breakdown.lifecycle_cost,
                'cost_per_area': self.cost_breakdown.cost_per_area,
                'baseline_energy_cost': self.cost_breakdown.baseline_energy_cost,
                'energy_savings': self.cost_breakdown.energy_savings,
                'payback_period': self.cost_breakdown.payback_period
            }
        }
        
        if self.correlation_matrix is not None:
            result['correlation_matrix'] = self.correlation_matrix.tolist()
        if self.clustering_results is not None:
            result['clustering_results'] = self.clustering_results
        if self.pca_results is not None:
            result['pca_results'] = self.pca_results
            
        # 原始图像数据（转换为可序列化格式）
        if self.original_image is not None:
            result['original_image_shape'] = self.original_image.shape
            result['has_original_image'] = True
        else:
            result['has_original_image'] = False
            
        return result


def create_empty_facade_elements() -> FacadeElements:
    """创建空的立面元素结构"""
    return FacadeElements()


def create_facade_individual(individual_id: str, 
                           num_windows: int,
                           renovation_mode: RenovationMode = RenovationMode.NEW_CONSTRUCTION) -> FacadeIndividual:
    """创建立面个体"""
    return FacadeIndividual(
        individual_id=individual_id,
        window_positions=[(0.0, 0.0)] * num_windows,
        window_sizes=[(1.0, 1.0)] * num_windows,
        window_types=[0] * num_windows,
        frame_depths=[0.0] * num_windows,
        shading_depths=[0.0] * num_windows,
        shading_angles=[0.0] * num_windows,
        renovation_mode=renovation_mode
    )


def parse_orientation(orientation_str: str) -> Orientation:
    """解析朝向字符串"""
    # 如果已经是Orientation枚举类型，直接返回
    if isinstance(orientation_str, Orientation):
        return orientation_str
    else:
        # 处理字符串类型
        orientation_map = {
            'south': Orientation.SOUTH,
            'southeast': Orientation.SOUTHEAST,
            'southwest': Orientation.SOUTHWEST,
            'east': Orientation.EAST,
            'west': Orientation.WEST,
            'north': Orientation.NORTH,
            'northeast': Orientation.NORTHEAST,
            'northwest': Orientation.NORTHWEST,
            '南': Orientation.SOUTH,
            '东南': Orientation.SOUTHEAST,
            '西南': Orientation.SOUTHWEST,
            '东': Orientation.EAST,
            '西': Orientation.WEST,
            '北': Orientation.NORTH,
            '东北': Orientation.NORTHEAST,
            '西北': Orientation.NORTHWEST,
        }
        
        return orientation_map.get(orientation_str.lower(), Orientation.SOUTH)


# 优化相关数据结构

class ConstraintType(Enum):
    """约束类型"""
    GEOMETRY = "geometry"          # 几何约束
    ENERGY = "energy"              # 能源约束
    COST = "cost"                  # 成本约束
    THERMAL = "thermal"            # 热工约束
    REGULATORY = "regulatory"      # 规范约束

class ConstraintSeverity(Enum):
    """约束严重程度"""
    NONE = "none"                  # 无违反
    MINOR = "minor"                # 轻微违反
    MAJOR = "major"                # 重大违反
    CRITICAL = "critical"          # 致命违反

@dataclass
class ObjectiveResults:
    """目标函数评估结果"""
    individual_id: str
    energy_consumption: float = 0.0         # 能耗 (kWh/m²/year)
    thermal_performance: float = 0.0        # 热工性能指标 (0-1, 越小越好)
    renovation_cost: float = 0.0            # 改造成本 (元)
    constraint_violations: float = 0.0      # 约束违反度
    is_feasible: bool = True                # 是否可行
    auxiliary_metrics: Dict[str, float] = field(default_factory=dict)
    evaluation_timestamp: str = ""
    
    # 添加窗户设计参数用于可视化
    window_positions: List[Tuple[float, float]] = field(default_factory=list)  # 窗户位置
    window_sizes: List[Tuple[float, float]] = field(default_factory=list)      # 窗户尺寸
    window_types: List[int] = field(default_factory=list)                      # 窗户类型
    frame_depths: List[float] = field(default_factory=list)                    # 窗框深度
    shading_depths: List[float] = field(default_factory=list)                  # 遮阳深度
    shading_angles: List[float] = field(default_factory=list)                  # 遮阳角度


@dataclass
class ConstraintViolation:
    """约束违反记录"""
    constraint_type: 'ConstraintType'       # 约束类型
    severity: 'ConstraintSeverity'          # 严重程度
    description: str                        # 违反描述
    violation_value: float                  # 违反程度
    affected_elements: List[str]            # 受影响的元素
    
    def __post_init__(self):
        # 处理 constraint_type 字段
        if isinstance(self.constraint_type, str):
            try:
                self.constraint_type = ConstraintType(self.constraint_type)
            except ValueError:
                self.constraint_type = ConstraintType.GEOMETRY  # 默认值
        
        # 处理 severity 字段
        if isinstance(self.severity, str):
            try:
                self.severity = ConstraintSeverity(self.severity)
            except ValueError:
                self.severity = ConstraintSeverity.MINOR  # 默认值


@dataclass
class ConstraintResults:
    """约束检查结果"""
    individual_id: str
    violations: List[ConstraintViolation]
    def __post_init__(self):
        # 如果violations列表中的元素是字典，尝试转换为ConstraintViolation对象
        if self.violations and isinstance(self.violations[0], dict):
            converted_violations = []
            for violation_dict in self.violations:
                violation_dict['constraint_type'] = ConstraintType(violation_dict.get('constraint_type', 'none'))
                violation_dict['severity'] = ConstraintSeverity(violation_dict.get('severity', 'none'))
                converted_violations.append(ConstraintViolation(**violation_dict))
            self.violations = converted_violations
    
    total_violation: float
    is_feasible: bool
    repair_suggestions: List[str]
    constraint_summary: Dict[str, Any]


@dataclass
class PopulationStatistics:
    """种群统计信息"""
    generation: int
    population_size: int
    feasible_count: int
    infeasible_count: int
    average_objectives: List[float]
    average_constraint_violation: float
    diversity_metric: float
    best_feasible_solution: Optional[ObjectiveResults]


@dataclass
class ConvergenceMetrics:
    """收敛指标"""
    generation: int
    elapsed_time: float
    hypervolume: float
    diversity_metric: float
    convergence_metric: float
    distribution_uniformity: float
    feasibility_ratio: float
    improvement_rate: float
    best_objectives: List[float]
    average_objectives: List[float]
    constraint_violation: float
    stagnation_count: int
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'generation': self.generation,
            'elapsed_time': self.elapsed_time,
            'hypervolume': self.hypervolume,
            'diversity_metric': self.diversity_metric,
            'convergence_metric': self.convergence_metric,
            'distribution_uniformity': self.distribution_uniformity,
            'feasibility_ratio': self.feasibility_ratio,
            'improvement_rate': self.improvement_rate,
            'best_objectives': self.best_objectives,
            'average_objectives': self.average_objectives,
            'constraint_violation': self.constraint_violation,
            'stagnation_count': self.stagnation_count
        }


@dataclass
class OptimizationSession:
    """优化会话信息"""
    session_id: str
    facade_elements: FacadeElements
    climate_data: OrientedClimateData
    start_time: datetime
    end_time: Optional[datetime] = None
    parameters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class OptimizationResults:
    """完整优化结果"""
    session_id: str
    facade_elements: FacadeElements
    climate_data: OrientedClimateData
    pareto_solutions: List[ObjectiveResults]
    representative_solutions: List[ObjectiveResults]
    best_energy_solution: Optional[ObjectiveResults] = None
    best_thermal_solution: Optional[ObjectiveResults] = None
    best_cost_solution: Optional[ObjectiveResults] = None
    convergence_history: List[ConvergenceMetrics] = field(default_factory=list)
    optimization_statistics: Dict[str, Any] = field(default_factory=dict)
    termination_reason: str = ""
    total_generations: int = 0
    total_evaluations: int = 0
    computation_time: float = 0.0
    optimization_metadata: Dict[str, Any] = field(default_factory=dict)
    report_path: Optional[str] = None