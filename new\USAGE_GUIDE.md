# 建筑立面优化系统使用指南 / Building Facade Optimization System Usage Guide

## 系统简介 / System Overview

建筑立面优化系统已经简化为自动化流程，只需输入必要参数即可一键完成所有处理步骤。

The Building Facade Optimization System has been simplified to an automated workflow that completes all processing steps with just the required input parameters.

## 简化的工作流程 / Simplified Workflow

### 输入要求 / Input Requirements

1. **立面色块图 / Facade Color Block Image**
   - 已经过立面识别处理的色块图像
   - 支持格式：PNG, JPG, JPEG
   - Pre-processed facade recognition color block image
   - Supported formats: PNG, JPG, JPEG

2. **EPW气候数据文件 / EPW Climate Data File**
   - 标准的EPW格式气象数据文件
   - 包含全年逐时气候数据
   - Standard EPW format weather data file
   - Contains hourly climate data for a full year

3. **建筑朝向 / Building Orientation**
   - 8个方向可选：北、东北、东、东南、南、西南、西、西北
   - 8 orientations available: North, Northeast, East, Southeast, South, Southwest, West, Northwest

4. **改造模式 / Renovation Mode**
   - 新建（New Construction）
   - 改造（Renovation）

### 运行步骤 / Running Steps

#### 方法1：运行主程序 / Method 1: Run Main Program

```bash
python main.py
```

系统将引导您输入以下信息：
The system will guide you to input the following information:

1. **步骤1/4**：输入立面色块图路径
2. **步骤2/4**：输入EPW文件路径  
3. **步骤3/4**：选择建筑朝向（1-8）
4. **步骤4/4**：选择改造模式（1-2）

#### 方法2：运行简化版本 / Method 2: Run Simplified Version

```bash
python main_simplified.py
```

这是一个独立的简化版本，功能相同但代码更精简。
This is a standalone simplified version with the same functionality but cleaner code.

### 自动处理流程 / Automated Processing Pipeline

确认输入参数后，系统将自动执行以下5个步骤：
After confirming input parameters, the system will automatically execute the following 5 steps:

1. **🔍 立面数据提取 / Facade Data Extraction**
   - 处理色块图像，识别建筑元素
   - Process color block images and identify building elements

2. **🌡️ 气候数据处理 / Climate Data Processing**
   - 解析EPW文件，根据朝向调整气候数据
   - Parse EPW file and adjust climate data based on orientation

3. **⚡ 多目标优化 / Multi-Objective Optimization**
   - 使用NSGA-III算法进行多目标优化
   - Use NSGA-III algorithm for multi-objective optimization
   - 优化目标：能耗、热工性能、改造成本
   - Optimization objectives: Energy consumption, thermal performance, renovation cost

4. **🎯 解决方案选择 / Solution Selection**
   - 从帕累托最优解中选择推荐方案
   - Select recommended solutions from Pareto optimal solutions

5. **📊 可视化和报告生成 / Visualization and Reporting**
   - 生成11类专业图表
   - 创建综合分析报告
   - Generate 11 types of professional charts
   - Create comprehensive analysis reports

### 输出结果 / Output Results

处理完成后，系统将生成以下输出：
After processing, the system will generate the following outputs:

```
outputs/
├── charts/                 # 可视化图表 / Visualization Charts
│   ├── optimization/       # 优化结果图表（3类）
│   ├── performance/        # 性能分析图表（4类）
│   └── comparison/         # 对比分析图表（4类）
├── reports/               # 分析报告 / Analysis Reports
│   ├── comprehensive_report.html
│   ├── comprehensive_report.md
│   └── comprehensive_report.pdf
└── facade_extraction_*/   # 立面提取详细结果
```

### 图表类型 / Chart Types

系统生成11类专业图表：
The system generates 11 types of professional charts:

#### 优化结果图表 / Optimization Result Charts (3类)
1. 3D帕累托前沿图 / 3D Pareto Front
2. 2D帕累托前沿投影 / 2D Pareto Front Projections  
3. 收敛过程分析 / Convergence Analysis

#### 性能分析图表 / Performance Analysis Charts (4类)
4. 能耗分析雷达图 / Energy Consumption Radar Chart
5. 热工性能分析 / Thermal Performance Analysis
6. 成本分析饼图 / Cost Analysis Pie Chart
7. 综合性能对比 / Comprehensive Performance Comparison

#### 对比分析图表 / Comparison Analysis Charts (4类)
8. 多方案对比 / Multi-Solution Comparison
9. 基准对比分析 / Baseline Comparison Analysis
10. 敏感性分析 / Sensitivity Analysis
11. 权衡分析 / Trade-off Analysis

### 系统特点 / System Features

1. **一键式操作 / One-Click Operation**
   - 输入参数后自动完成所有处理
   - Automatic completion of all processing after parameter input

2. **专业级可视化 / Professional Visualization**  
   - 11类精美图表，满足学术和工程需求
   - 11 types of beautiful charts meeting academic and engineering needs

3. **双语支持 / Bilingual Support**
   - 完整的中英文界面和输出
   - Complete Chinese-English interface and output

4. **色块图处理 / Color Block Processing**
   - 直接处理预分割的立面识别色块图
   - Direct processing of pre-segmented facade recognition color blocks
   - 无需YOLO模型，提高处理效率
   - No YOLO model required, improved processing efficiency

5. **多目标优化 / Multi-Objective Optimization**
   - 先进的NSGA-III遗传算法
   - Advanced NSGA-III genetic algorithm
   - 考虑能耗、热工、成本三大目标
   - Considers energy, thermal, and cost objectives

### 故障排除 / Troubleshooting

#### 常见问题 / Common Issues

1. **导入错误 / Import Errors**
   ```bash
   python simple_import_test.py  # 测试所有导入
   ```

2. **依赖包缺失 / Missing Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **文件路径问题 / File Path Issues**
   - 确保输入的文件路径存在且可访问
   - 支持中文路径
   - Ensure input file paths exist and are accessible
   - Chinese paths are supported

4. **内存不足 / Insufficient Memory**
   - 大型图像可能需要更多内存
   - 建议使用较小尺寸的输入图像
   - Large images may require more memory
   - Recommend using smaller input images

### 性能建议 / Performance Recommendations

1. **图像尺寸 / Image Size**
   - 推荐分辨率：1024x768 到 2048x1536
   - Recommended resolution: 1024x768 to 2048x1536

2. **优化参数 / Optimization Parameters**
   - 默认参数已针对一般情况优化
   - 复杂建筑可能需要更多代数
   - Default parameters are optimized for general cases
   - Complex buildings may require more generations

3. **并行处理 / Parallel Processing**
   - 系统自动使用多核处理器
   - System automatically uses multi-core processors

### 技术支持 / Technical Support

如有问题，请检查：
If you encounter issues, please check:

1. Python版本 ≥ 3.8
2. 所需依赖包已安装
3. 输入文件格式正确
4. 系统日志文件（logs/目录）

---

**系统版本 / System Version**: v1.0  
**最后更新 / Last Updated**: 2025-07-28  
**状态 / Status**: ✅ 完全就绪 / Fully Ready