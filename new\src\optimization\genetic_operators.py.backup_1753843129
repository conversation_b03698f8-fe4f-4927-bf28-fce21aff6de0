"""
遗传算法操作器
实现NSGA-III算法的种群管理、选择、交叉和变异操作
"""

import numpy as np
import random
import math
from typing import Dict, List, Tuple, Any, Optional, Set
from dataclasses import dataclass
from enum import Enum

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext, get_optimization_logger
from ..core.exceptions import GeneticAlgorithmError as GeneticOperationError, handle_exception
from ..core.data_structures import (
    FacadeIndividual, ObjectiveResults, PopulationStatistics
)
from ..core.utils import MathUtils

from .individual_encoder import IndividualEncoder
from .objective_functions import ObjectiveFunctionEvaluator
from .constraint_handler import ConstraintHandler


class SelectionMethod(Enum):
    """选择方法枚举"""
    NSGA3_SELECTION = "nsga3"
    TOURNAMENT = "tournament"
    ROULETTE = "roulette"
    RANKING = "ranking"


@dataclass
class GeneticParameters:
    """遗传算法参数"""
    population_size: int = 100
    tournament_size: int = 3
    crossover_rate: float = 0.9
    mutation_rate: float = 0.1
    mutation_strength: float = 0.1
    elite_size: int = 10
    diversity_threshold: float = 0.01


class GeneticOperators:
    """
    遗传算法操作器
    
    功能：
    1. 种群初始化和管理
    2. NSGA-III非支配排序和拥挤度计算
    3. 父代选择操作
    4. 交叉和变异操作
    5. 环境选择和精英保留
    6. 种群多样性维护
    """
    
    def __init__(self, individual_encoder: IndividualEncoder,
                 objective_evaluator: ObjectiveFunctionEvaluator,
                 constraint_handler: ConstraintHandler):
        """
        初始化遗传算法操作器
        
        Args:
            individual_encoder: 个体编码器
            objective_evaluator: 目标函数评估器
            constraint_handler: 约束处理器
        """
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.opt_logger = get_optimization_logger()  # 优化算法专用日志器
        
        # 确保优化日志器不输出到终端
        self.opt_logger.propagate = False
        import logging
        for handler in self.opt_logger.handlers[:]:
            if isinstance(handler, logging.StreamHandler) and handler.stream.name == '<stdout>':
                self.opt_logger.removeHandler(handler)
        
        self.individual_encoder = individual_encoder
        self.objective_evaluator = objective_evaluator
        self.constraint_handler = constraint_handler
        
        # 获取遗传算法配置
        optimization_config = self.config.get_section('optimization')
        genetic_config = optimization_config.get('genetic_operators', {})
        
        # 优化遗传算法参数以提高收敛性能和解的质量 - 第三次优化
        self.params = GeneticParameters(
            population_size=400,  # 增加到400，为3目标问题提供充足的搜索空间
            tournament_size=genetic_config.get('tournament_size', 7),    # 进一步增加选择压力以加速收敛
            crossover_rate=genetic_config.get('crossover_rate', 0.98),   # 最大化交叉率以促进信息交换
            mutation_rate=genetic_config.get('mutation_rate', 0.60),     # 进一步增加变异率以提高探索能力
            mutation_strength=genetic_config.get('mutation_strength', 0.20), # 增加变异强度以跳出局部最优
            elite_size=genetic_config.get('elite_size', 80),             # 大幅增加精英数量以保持优秀解
            diversity_threshold=genetic_config.get('diversity_threshold', 0.005) # 进一步降低多样性阈值以保持最高多样性
        )
        
        # NSGA-III参考点
        self.reference_points = self._generate_reference_points()
        
        # 种群历史统计
        self.generation_count = 0
        self.population_history = []
        
        # 评估缓存 - 避免重复计算
        self.evaluation_cache = {}
        self.cache_hits = 0
        self.cache_misses = 0
        
        self.opt_logger.info(f"遗传算法操作器初始化完成: 种群大小={self.params.population_size}, "
                        f"参考点数量={len(self.reference_points)}")
    
    @handle_exception
    def initialize_population(self) -> List[FacadeIndividual]:
        """
        智能种群初始化 - 使用多样化策略
        
        Returns:
            初始种群个体列表
            
        Raises:
            GeneticOperationError: 种群初始化失败时抛出
        """
        with LogContext("智能种群初始化", self.logger):
            try:
                population = []
                
                # 策略1: 完全随机个体 (30%) - 保持基础多样性
                random_count = int(self.params.population_size * 0.3)
                for i in range(random_count):
                    individual_id = f"gen0_random{i}"
                    individual = self._create_truly_random_individual(individual_id, i)
                    population.append(individual)
                
                # 策略2: 基于启发式的个体 (25%) - 基于经验规则
                heuristic_count = int(self.params.population_size * 0.25)
                for i in range(heuristic_count):
                    individual_id = f"gen0_heuristic{i}"
                    individual = self._create_heuristic_individual(individual_id)
                    population.append(individual)
                
                # 策略3: 极端化个体 (25%) - 专注于单一目标，增加范围
                extreme_count = int(self.params.population_size * 0.25)
                objectives = ['energy', 'thermal', 'cost']
                for i in range(extreme_count):
                    individual_id = f"gen0_extreme{i}"
                    target_objective = objectives[i % len(objectives)]
                    individual = self._create_extreme_individual(individual_id, target_objective)
                    population.append(individual)
                
                # 策略4: 高变异个体 (20%) - 最大化差异性
                high_mutation_count = self.params.population_size - random_count - heuristic_count - extreme_count
                for i in range(high_mutation_count):
                    individual_id = f"gen0_highmut{i}"
                    individual = self._create_high_mutation_individual(individual_id, i)
                    population.append(individual)
                
                self.generation_count = 0
                self.opt_logger.info(f"智能种群初始化完成: {len(population)} 个个体 "
                               f"(随机:{random_count}, 启发式:{heuristic_count}, 极端:{extreme_count})")
                
                return population
                
            except Exception as e:
                raise GeneticOperationError(f"种群初始化失败: {str(e)}") from e
    
    def _create_heuristic_individual(self, individual_id: str) -> FacadeIndividual:
        """创建基于启发式规则的个体"""
        try:
            # 基于经验规则创建个体
            # 1. 窗墙比控制在合理范围 (0.3-0.6)
            # 2. 优先选择节能窗户类型
            # 3. 合理的遮阳配置
            
            # 先创建随机个体作为基础
            individual = self.individual_encoder.create_random_individual(individual_id)
            
            # 应用启发式规则调整
            if hasattr(individual, 'window_types'):
                # 70%概率选择节能窗户类型
                for i in range(len(individual.window_types)):
                    if random.random() < 0.7:
                        individual.window_types[i] = 1  # 节能窗户
                    
                    # 为节能窗户添加合理的框架深度
                    if individual.window_types[i] == 1:
                        individual.frame_depths[i] = random.uniform(0.1, 0.3)
            
            return individual
            
        except Exception:
            # 如果启发式创建失败，返回随机个体
            return self._create_truly_random_individual(individual_id, 0)
    
    def _create_truly_random_individual(self, individual_id: str, seed: int) -> FacadeIndividual:
        """创建真正随机的个体 - 根据改造模式生成约束兼容的个体"""
        try:
            # 使用不同的种子确保多样性
            random.seed(seed + 42)  # 固定偏移确保可重现性
            
            # 获取改造模式
            renovation_mode = self.individual_encoder.renovation_mode
            
            # 获取立面边界
            facade_bounds = self.individual_encoder.facade_bounds
            min_x, max_x = facade_bounds['min_x'], facade_bounds['max_x']
            min_y, max_y = facade_bounds['min_y'], facade_bounds['max_y']
            
            # 获取约束参数 - 确保生成的个体满足约束
            min_window_width = getattr(self.constraint_handler, 'min_window_width', 0.6)
            max_window_width = getattr(self.constraint_handler, 'max_window_width', 3.0)
            min_window_height = getattr(self.constraint_handler, 'min_window_height', 0.8) 
            max_window_height = getattr(self.constraint_handler, 'max_window_height', 2.5)
            min_spacing = getattr(self.constraint_handler, 'min_window_spacing', 0.5)
            min_wall_margin = getattr(self.constraint_handler, 'min_wall_margin', 0.2)
            
            # 根据改造模式确定窗户数量和生成策略
            if renovation_mode == RenovationMode.RENOVATION:
                # 改造模式：使用原有窗户数量，不能增删
                existing_windows = len(self.individual_encoder.facade_elements.windows)
                num_windows = existing_windows if existing_windows > 0 else 2
                self.opt_logger.debug(f"改造模式: 保持窗户数量={num_windows}")
            else:
                # 新建或大改模式：允许自由确定窗户数量
                available_width = max_x - min_x - 2 * min_wall_margin
                available_height = max_y - min_y - 2 * min_wall_margin
                max_reasonable_windows = min(6, int(available_width * available_height / (min_window_width * min_window_height * 2)))
                num_windows = random.randint(1, max(1, min(4, max_reasonable_windows)))
                self.opt_logger.debug(f"{renovation_mode.value}模式: 随机窗户数量={num_windows}")
            
            window_positions = []
            window_sizes = []
            window_types = []
            frame_depths = []
            shading_depths = []
            shading_angles = []
            
            # 生成约束兼容的随机窗户
            for i in range(num_windows):
                max_attempts = 100  # 增加尝试次数
                placed = False
                
                for attempt in range(max_attempts):
                    # 根据改造模式决定位置和尺寸生成策略
                    if renovation_mode == RenovationMode.RENOVATION and i < len(self.individual_encoder.facade_elements.windows):
                        # 改造模式：基于原有窗户位置，但允许横向变化
                        original_window = self.individual_encoder.facade_elements.windows[i]
                        base_x, base_y = original_window.center
                        base_width, base_height = original_window.width, original_window.height
                        
                        # 位置：允许小幅横向调整，垂直位置保持相对稳定
                        x = base_x + random.uniform(-0.5, 0.5)  # 横向小幅调整
                        y = base_y + random.uniform(-0.2, 0.2)  # 垂直位置基本保持
                        
                        # 尺寸：只允许宽度变化，高度固定
                        width = base_width * random.uniform(0.7, 1.3)  # 宽度可变化
                        height = base_height  # 高度保持不变
                        
                        self.opt_logger.debug(f"改造模式窗户{i}: 保持高度{height:.2f}m, 调整宽度{width:.2f}m")
                    else:
                        # 新建或大改模式：完全随机
                        x = random.uniform(min_x + min_wall_margin + 0.3, max_x - min_wall_margin - 0.3)
                        y = random.uniform(min_y + min_wall_margin + 0.3, max_y - min_wall_margin - 0.3)
                        
                        # 随机大小 - 严格遵守约束
                        available_width = max_x - min_x - 2 * min_wall_margin
                        available_height = max_y - min_y - 2 * min_wall_margin
                        width = random.uniform(min_window_width, min(max_window_width, available_width / 3))
                        height = random.uniform(min_window_height, min(max_window_height, available_height / 3))
                    
                    # 确保窗户完全在立面边界内
                    if (x - width/2 < min_x + min_wall_margin or 
                        x + width/2 > max_x - min_wall_margin or
                        y - height/2 < min_y + min_wall_margin or 
                        y + height/2 > max_y - min_wall_margin):
                        continue
                    
                    # 检查与现有窗户的冲突
                    valid_position = True
                    for j, (pos_x, pos_y) in enumerate(window_positions):
                        existing_width, existing_height = window_sizes[j]
                        
                        # 检查边界间距
                        x_gap = abs(x - pos_x) - (width + existing_width) / 2
                        y_gap = abs(y - pos_y) - (height + existing_height) / 2
                        
                        if x_gap < min_spacing or y_gap < min_spacing:
                            valid_position = False
                            break
                    
                    if valid_position:
                        window_positions.append((x, y))
                        window_sizes.append((width, height))
                        
                        # 保守的随机类型分配
                        window_type = random.choices([0, 1, 2], weights=[0.6, 0.25, 0.15])[0]
                        window_types.append(window_type)
                        
                        # 合理范围内的深度和角度
                        if window_type == 1:  # 有框窗户
                            frame_depths.append(random.uniform(0.05, 0.15))
                        else:
                            frame_depths.append(0.0)
                            
                        if window_type == 2:  # 遮阳窗户
                            shading_depths.append(random.uniform(0.1, 0.3))
                            shading_angles.append(random.uniform(20, 50))
                        else:
                            shading_depths.append(0.0)
                            shading_angles.append(0.0)
                        
                        placed = True
                        break
                
                # 如果无法放置更多窗户，停止尝试
                if not placed:
                    self.opt_logger.debug(f"受约束限制，停在{len(window_positions)}个窗户")
                    break
            
            # 创建个体
            individual = FacadeIndividual(
                individual_id=individual_id,
                window_positions=window_positions,
                window_sizes=window_sizes,
                window_types=window_types,
                frame_depths=frame_depths,
                shading_depths=shading_depths,
                shading_angles=shading_angles,
                renovation_mode=self.individual_encoder.renovation_mode
            )
            
            self.opt_logger.debug(f"创建约束兼容随机个体: {individual_id}, {len(window_positions)}个窗户")
            
            # 验证个体是否满足基本约束
            if len(window_positions) == 0:
                # 如果无法生成任何窗户，使用基于真实数据的备用方案
                self.opt_logger.warning(f"随机个体{individual_id}无法生成任何窗户，使用真实数据备用方案")
                return self.individual_encoder.create_individual_from_real_data(individual_id)
            
            return individual
            
        except Exception as e:
            self.opt_logger.error(f"创建真正随机个体失败: {str(e)}")
            # 降级到基于真实数据的方法
            return self.individual_encoder.create_individual_from_real_data(individual_id)
    
    def _create_real_data_variant_individual(self, individual_id: str, variant_seed: int) -> FacadeIndividual:
        """创建基于真实数据的变异个体 - 增加多样性"""
        try:
            # 首先创建基于真实数据的个体
            base_individual = self.individual_encoder.create_individual_from_real_data(individual_id)
            
            # 使用变异种子确保多样性
            random.seed(variant_seed + 100)
            
            # 对基础个体进行显著变异
            mutation_rate = 0.6  # 60%的变异率
            mutation_strength = 0.3  # 30%的变异强度
            
            # 复制基础个体
            mutated = FacadeIndividual(
                individual_id=base_individual.individual_id + "_variant",
                window_positions=base_individual.window_positions.copy(),
                window_sizes=base_individual.window_sizes.copy(),
                window_types=base_individual.window_types.copy(),
                frame_depths=base_individual.frame_depths.copy(),
                shading_depths=base_individual.shading_depths.copy(),
                shading_angles=base_individual.shading_angles.copy(),
                renovation_mode=base_individual.renovation_mode
            )
            
            num_windows = len(mutated.window_positions)
            
            for i in range(num_windows):
                # 位置变异 - 大幅变化
                if random.random() < mutation_rate:
                    pos_x, pos_y = mutated.window_positions[i]
                    
                    # 大幅位置变化
                    x_range = (self.individual_encoder.facade_bounds['max_x'] - self.individual_encoder.facade_bounds['min_x']) * 0.3
                    y_range = (self.individual_encoder.facade_bounds['max_y'] - self.individual_encoder.facade_bounds['min_y']) * 0.3
                    
                    new_x = pos_x + random.uniform(-x_range, x_range)
                    new_y = pos_y + random.uniform(-y_range, y_range)
                    
                    # 应用边界约束
                    new_x = max(self.individual_encoder.facade_bounds['min_x'], 
                               min(new_x, self.individual_encoder.facade_bounds['max_x']))
                    new_y = max(self.individual_encoder.facade_bounds['min_y'], 
                               min(new_y, self.individual_encoder.facade_bounds['max_y']))
                    
                    mutated.window_positions[i] = (new_x, new_y)
                
                # 尺寸变异 - 大幅变化
                if random.random() < mutation_rate:
                    width, height = mutated.window_sizes[i]
                    
                    # 大幅尺寸变化
                    new_width = width * (1 + random.uniform(-mutation_strength, mutation_strength))
                    new_height = height * (1 + random.uniform(-mutation_strength, mutation_strength))
                    
                    # 应用约束
                    new_width = max(self.individual_encoder.min_width, 
                                   min(new_width, self.individual_encoder.max_width))
                    new_height = max(self.individual_encoder.min_height, 
                                    min(new_height, self.individual_encoder.max_height))
                    
                    mutated.window_sizes[i] = (new_width, new_height)
                
                # 类型变异
                if random.random() < mutation_rate * 0.5:
                    mutated.window_types[i] = random.randint(0, 2)
                    
                    # 更新对应的深度和角度
                    if mutated.window_types[i] == 1:
                        mutated.frame_depths[i] = random.uniform(0.1, 0.3)
                    else:
                        mutated.frame_depths[i] = 0.0
                    
                    if mutated.window_types[i] == 2:
                        mutated.shading_depths[i] = random.uniform(0.2, 0.5)
                        mutated.shading_angles[i] = random.uniform(15, 75)
                    else:
                        mutated.shading_depths[i] = 0.0
                        mutated.shading_angles[i] = 0.0
            
            self.opt_logger.debug(f"创建真实数据变异个体: {individual_id}, 变异率{mutation_rate*100:.0f}%")
            return mutated
            
        except Exception as e:
            self.opt_logger.error(f"创建真实数据变异个体失败: {str(e)}")
            # 降级到基于真实数据的方法
            return self.individual_encoder.create_individual_from_real_data(individual_id)
    
    def _create_high_mutation_individual(self, individual_id: str, seed: int) -> FacadeIndividual:
        """创建高变异个体 - 在经典个体基础上进行大幅度变异"""
        try:
            # 先创建一个经典个体
            base_individual = self._create_heuristic_individual(individual_id)
            
            # 对关键参数进行大幅度变异
            random.seed(seed + 1000)  # 使用不同种子
            
            # 变异窗户位置（只适用于非改造模式或新增窗户）
            if hasattr(base_individual, 'window_positions'):
                for i in range(len(base_individual.window_positions)):
                    if random.random() < 0.6:  # 60%的窗户进行位置变异
                        x, y = base_individual.window_positions[i]
                        # 大幅度位置变异
                        x += random.uniform(-1.5, 1.5)
                        y += random.uniform(-1.0, 1.0)
                        base_individual.window_positions[i] = (x, y)
            
            # 变异窗户尺寸
            if hasattr(base_individual, 'window_sizes'):
                for i in range(len(base_individual.window_sizes)):
                    if random.random() < 0.8:  # 80%的窗户进行尺寸变异
                        width, height = base_individual.window_sizes[i]
                        # 大幅度尺寸变异
                        width_factor = random.uniform(0.3, 2.0)  # 更大的变化范围
                        height_factor = random.uniform(0.3, 2.0)
                        base_individual.window_sizes[i] = (width * width_factor, height * height_factor)
            
            # 变异窗户类型
            if hasattr(base_individual, 'window_types'):
                for i in range(len(base_individual.window_types)):
                    if random.random() < 0.5:  # 50%的窗户进行类型变异
                        base_individual.window_types[i] = random.randint(0, 2)
            
            # 变异遮阳参数
            if hasattr(base_individual, 'shading_depths'):
                for i in range(len(base_individual.shading_depths)):
                    if random.random() < 0.7:  # 70%的窗户进行遮阳变异
                        base_individual.shading_depths[i] = random.uniform(0.0, 1.0)
            
            return base_individual
            
        except Exception:
            # 如果高变异创建失败，返回随机个体
            return self._create_truly_random_individual(individual_id, seed)
    
    def _create_extreme_individual(self, individual_id: str, target_objective: str) -> FacadeIndividual:
        """创建专注于特定目标的极端个体"""
        try:
            # 首先创建真正随机的个体作为基础
            individual = self._create_truly_random_individual(individual_id, hash(target_objective) % 1000)
            
            if target_objective == 'energy':
                # 专注能耗：最大化节能效果
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 1  # 全部使用节能窗户
                    individual.frame_depths[i] = random.uniform(0.25, 0.4)  # 深框架增强隔热
                    individual.shading_depths[i] = random.uniform(0.3, 0.6)  # 添加遮阳进一步节能
                    individual.shading_angles[i] = random.uniform(30, 60)  # 优化遮阳角度
                    
                # 增加窗户数量最大化采光，同时减少墙体热损失
                if len(individual.window_positions) < 6:
                    # 添加更多窗户
                    for _ in range(6 - len(individual.window_positions)):
                        individual.window_positions.append(individual.window_positions[0])
                        individual.window_sizes.append(individual.window_sizes[0])
                        individual.window_types.append(1)
                        individual.frame_depths.append(0.3)
                        individual.shading_depths.append(0.4)
                        individual.shading_angles.append(45)
                        
            elif target_objective == 'thermal':
                # 专注热工性能：最大化热舒适性
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 2  # 全部使用遮阳窗户
                    individual.shading_depths[i] = random.uniform(0.8, 1.5)  # 深遮阳最大化热控制
                    individual.shading_angles[i] = random.uniform(15, 35)  # 浅角度优化采光与遮阳平衡
                    individual.frame_depths[i] = random.uniform(0.15, 0.25)  # 适中框架深度
                    
                # 优化窗户分布以改善室内热环境
                for i in range(len(individual.window_positions)):
                    # 调整窗户位置，避免过热区域
                    x, y = individual.window_positions[i]
                    # 窗户位置偏下，改善室内温度分层
                    individual.window_positions[i] = (x, y * 0.8)
                    
            elif target_objective == 'cost':
                # 专注成本：最小化改造成本
                for i in range(len(individual.window_types)):
                    individual.window_types[i] = 0  # 全部使用普通窗户，最低成本
                    individual.frame_depths[i] = 0.0  # 无窗框，节省成本
                    individual.shading_depths[i] = 0.0  # 无遮阳，节省成本
                    individual.shading_angles[i] = 0.0
                    
                # 减少窗户数量，降低改造成本
                if len(individual.window_positions) > 3:
                    # 只保留3个最主要的窗户
                    individual.window_positions = individual.window_positions[:3]
                    individual.window_sizes = individual.window_sizes[:3]
                    individual.window_types = individual.window_types[:3]
                    individual.frame_depths = individual.frame_depths[:3]
                    individual.shading_depths = individual.shading_depths[:3]
                    individual.shading_angles = individual.shading_angles[:3]
            
            self.opt_logger.debug(f"创建极端个体: {individual_id}, 目标={target_objective}")
            return individual
            
        except Exception as e:
            self.opt_logger.error(f"创建极端个体失败: {str(e)}")
            return self._create_truly_random_individual(individual_id, 999)
    
    @handle_exception
    def evaluate_population(self, population: List[FacadeIndividual]) -> List[ObjectiveResults]:
        """
        评估种群 - 优化版本支持缓存和批量处理
        
        Args:
            population: 待评估的种群
            
        Returns:
            种群评估结果列表
        """
        with LogContext(f"种群评估 - 第{self.generation_count}代", self.logger):
            try:
                evaluation_results = []
                
                # 批量评估以提高效率
                for individual in population:
                    # 检查缓存（基于个体ID）
                    cache_key = self._get_individual_cache_key(individual)
                    cached_result = self._get_cached_evaluation(cache_key)
                    
                    if cached_result is not None:
                        evaluation_results.append(cached_result)
                        continue
                    
                    # 评估目标函数
                    objective_results = self.objective_evaluator.evaluate_objectives(individual)
                    
                    # 检查约束
                    constraint_results = self.constraint_handler.check_constraints(individual)
                    
                    # 更新目标结果中的约束信息
                    objective_results.constraint_violations = constraint_results.total_violation
                    objective_results.is_feasible = constraint_results.is_feasible
                    
                    # 缓存结果
                    self._cache_evaluation(cache_key, objective_results)
                    
                    evaluation_results.append(objective_results)
                
                self.opt_logger.info(f"种群评估完成: {len(evaluation_results)} 个个体")
                return evaluation_results
                
            except Exception as e:
                raise GeneticOperationError(f"种群评估失败: {str(e)}") from e
    
    def nsga3_environmental_selection(self, population: List[FacadeIndividual],
                                    evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """
        改进的NSGA-III环境选择 - 增强多样性保持
        
        Args:
            population: 当前种群
            evaluation_results: 评估结果
            
        Returns:
            选择后的种群和评估结果
        """
        try:
            # 步骤1: 分离可行解和不可行解
            feasible_indices = [i for i, r in enumerate(evaluation_results) if r.is_feasible]
            infeasible_indices = [i for i, r in enumerate(evaluation_results) if not r.is_feasible]
            
            selected_population = []
            selected_evaluations = []
            
            # 优先处理可行解
            if feasible_indices:
                feasible_results = [evaluation_results[i] for i in feasible_indices]
                fronts = self._fast_non_dominated_sort(feasible_results)
                
                # 重新映射索引
                front_indices_mapped = []
                for front in fronts:
                    mapped_front = [feasible_indices[i] for i in front]
                    front_indices_mapped.append(mapped_front)
                
                # 选择前若干层非支配解
                for front_indices in front_indices_mapped:
                    if len(selected_population) + len(front_indices) <= self.params.population_size:
                        # 整层都可以加入
                        for idx in front_indices:
                            selected_population.append(population[idx])
                            selected_evaluations.append(evaluation_results[idx])
                    else:
                        # 需要从这一层选择部分个体
                        remaining_slots = self.params.population_size - len(selected_population)
                        if remaining_slots > 0:
                            # 使用增强的参考点选择以提高解的质量
                            selected_indices = self._enhanced_reference_point_selection(
                                front_indices, evaluation_results, remaining_slots
                            )
                            
                            for idx in selected_indices:
                                selected_population.append(population[idx])
                                selected_evaluations.append(evaluation_results[idx])
                        break
            
            # 如果还有空位且有不可行解，选择约束违反最小的
            remaining_slots = self.params.population_size - len(selected_population)
            if remaining_slots > 0 and infeasible_indices:
                # 按约束违反程度排序
                infeasible_sorted = sorted(infeasible_indices, 
                                         key=lambda i: evaluation_results[i].constraint_violations)
                
                for i in range(min(remaining_slots, len(infeasible_sorted))):
                    idx = infeasible_sorted[i]
                    selected_population.append(population[idx])
                    selected_evaluations.append(evaluation_results[idx])
            
            self.opt_logger.debug(f"改进NSGA-III环境选择完成: {len(selected_population)} 个个体")
            return selected_population, selected_evaluations
            
        except Exception as e:
            self.logger.error(f"NSGA-III环境选择失败: {str(e)}")
            # 返回原种群的前N个个体作为备选
            return population[:self.params.population_size], evaluation_results[:self.params.population_size]
    
    def parent_selection(self, population: List[FacadeIndividual],
                        evaluation_results: List[ObjectiveResults],
                        num_parents: int) -> List[FacadeIndividual]:
        """
        父代选择
        
        Args:
            population: 当前种群
            evaluation_results: 评估结果
            num_parents: 需要选择的父代数量
            
        Returns:
            选择的父代个体列表
        """
        try:
            parents = []
            
            for _ in range(num_parents):
                # 锦标赛选择
                parent = self._tournament_selection(population, evaluation_results)
                parents.append(parent)
            
            return parents
            
        except Exception as e:
            self.logger.error(f"父代选择失败: {str(e)}")
            return random.choices(population, k=num_parents)
    
    def crossover_and_mutation(self, parents: List[FacadeIndividual]) -> List[FacadeIndividual]:
        """
        交叉和变异操作 - 改进版本增强多样性
        
        Args:
            parents: 父代个体列表
            
        Returns:
            子代个体列表
        """
        try:
            offspring = []
            
            # 智能自适应变异率：根据种群多样性和代数动态调整
            current_diversity = self._calculate_population_diversity_simple(parents)
            generation_factor = min(1.0, self.generation_count / 50.0)  # 代数因子
            
            # 基础自适应变异率
            adaptive_mutation_rate = self.params.mutation_rate
            adaptive_mutation_strength = self.params.mutation_strength
            
            # 多样性过低时增加变异
            if current_diversity < self.params.diversity_threshold:
                diversity_factor = (self.params.diversity_threshold - current_diversity) / self.params.diversity_threshold
                adaptive_mutation_rate = min(0.4, self.params.mutation_rate * (1 + diversity_factor * 2))
                adaptive_mutation_strength = min(0.4, self.params.mutation_strength * (1 + diversity_factor))
            
            # 早期代数增加探索，后期代数增加开发
            if self.generation_count < 10:
                adaptive_mutation_rate *= 1.3  # 早期增加探索
            elif self.generation_count > 30:
                adaptive_mutation_rate *= 0.8  # 后期减少变异，增加开发
            
            for i in range(0, len(parents), 2):
                parent1 = parents[i]
                parent2 = parents[min(i + 1, len(parents) - 1)]
                
                # 交叉操作
                if random.random() < self.params.crossover_rate:
                    child1, child2 = self.individual_encoder.crossover_individuals(parent1, parent2)
                else:
                    # 即使不交叉也要复制个体以避免引用问题
                    child1 = self.individual_encoder.copy_individual(parent1)
                    child2 = self.individual_encoder.copy_individual(parent2)
                
                # 变异操作 - 使用智能自适应变异率和强度
                if random.random() < adaptive_mutation_rate:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, adaptive_mutation_rate, adaptive_mutation_strength, self.generation_count
                    )
                
                if random.random() < adaptive_mutation_rate:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, adaptive_mutation_rate, adaptive_mutation_strength, self.generation_count
                    )
                
                # 强化多样性的变异策略
                # 1. 微调变异：高概率小幅度变异
                if random.random() < 0.25:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, 0.4, adaptive_mutation_strength * 0.7, self.generation_count
                    )
                
                if random.random() < 0.25:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, 0.4, adaptive_mutation_strength * 0.7, self.generation_count
                    )
                
                # 2. 探索性变异：中等概率中等幅度变异
                if random.random() < 0.15:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, 0.7, adaptive_mutation_strength * 1.5, self.generation_count
                    )
                
                if random.random() < 0.15:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, 0.7, adaptive_mutation_strength * 1.5, self.generation_count
                    )
                
                # 3. 跳跃变异：适中概率大幅度变异（强化多样性）
                if random.random() < 0.08:
                    child1 = self.individual_encoder.mutate_individual(
                        child1, 0.9, adaptive_mutation_strength * 3.0, self.generation_count
                    )
                
                if random.random() < 0.08:
                    child2 = self.individual_encoder.mutate_individual(
                        child2, 0.9, adaptive_mutation_strength * 3.0, self.generation_count
                    )
                
                # 更新个体ID
                child1.individual_id = f"gen{self.generation_count + 1}_off{len(offspring)}"
                child2.individual_id = f"gen{self.generation_count + 1}_off{len(offspring) + 1}"
                
                offspring.extend([child1, child2])
            
            return offspring
            
        except Exception as e:
            self.logger.error(f"交叉变异操作失败: {str(e)}")
            return parents  # 返回父代作为备选
    
    def _calculate_population_diversity_simple(self, population: List[FacadeIndividual]) -> float:
        """简单计算种群多样性"""
        try:
            if len(population) < 2:
                return 0.0
            
            # 基于个体编码的多样性计算
            total_distance = 0.0
            count = 0
            
            for i in range(len(population)):
                for j in range(i + 1, len(population)):
                    # 计算个体间的汉明距离或欧氏距离
                    distance = self._calculate_individual_distance(population[i], population[j])
                    total_distance += distance
                    count += 1
            
            return total_distance / count if count > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def _calculate_individual_distance(self, ind1: FacadeIndividual, ind2: FacadeIndividual) -> float:
        """计算两个个体间的距离"""
        try:
            # 简化的距离计算，基于个体的主要属性
            distance = 0.0
            
            # 比较窗户配置
            if hasattr(ind1, 'window_configurations') and hasattr(ind2, 'window_configurations'):
                for i, (config1, config2) in enumerate(zip(ind1.window_configurations, ind2.window_configurations)):
                    if config1 != config2:
                        distance += 1.0
            
            # 比较墙体配置
            if hasattr(ind1, 'wall_configurations') and hasattr(ind2, 'wall_configurations'):
                for i, (config1, config2) in enumerate(zip(ind1.wall_configurations, ind2.wall_configurations)):
                    if config1 != config2:
                        distance += 1.0
            
            return distance
            
        except Exception:
            return 1.0  # 默认距离
    
    def evolve_generation(self, population: List[FacadeIndividual],
                         evaluation_results: List[ObjectiveResults]) -> Tuple[List[FacadeIndividual], List[ObjectiveResults]]:
        """
        进化一代
        
        Args:
            population: 当前种群
            evaluation_results: 当前评估结果
            
        Returns:
            新一代种群和评估结果
        """
        with LogContext(f"进化第{self.generation_count + 1}代", self.logger):
            try:
                # 父代选择
                parents = self.parent_selection(
                    population, evaluation_results, self.params.population_size
                )
                
                # 交叉变异生成子代
                offspring = self.crossover_and_mutation(parents)
                
                # 评估子代
                offspring_evaluations = self.evaluate_population(offspring)
                
                # 合并父代和子代
                combined_population = population + offspring
                combined_evaluations = evaluation_results + offspring_evaluations
                
                # 环境选择
                next_population, next_evaluations = self.nsga3_environmental_selection(
                    combined_population, combined_evaluations
                )
                
                # 更新代数
                self.generation_count += 1
                
                # 记录种群统计
                stats = self._calculate_population_statistics(next_population, next_evaluations)
                self.population_history.append(stats)
                
                # 获取缓存统计
                cache_stats = self.get_cache_statistics()
                
                self.opt_logger.info(f"第{self.generation_count}代进化完成: "
                               f"平均能耗={stats.average_objectives[0]:.1f}, "
                               f"可行解={stats.feasible_count}/{len(next_population)}, "
                               f"缓存命中率={cache_stats['hit_rate']:.1f}%")
                
                return next_population, next_evaluations
                
            except Exception as e:
                raise GeneticOperationError(f"进化操作失败: {str(e)}") from e
    
    def _fast_non_dominated_sort(self, evaluation_results: List[ObjectiveResults]) -> List[List[int]]:
        """优化的快速非支配排序 - 减少比较次数"""
        n = len(evaluation_results)
        if n == 0:
            return []
        
        fronts = []
        
        # 预处理：分离可行解和不可行解
        feasible_indices = []
        infeasible_indices = []
        
        for i, result in enumerate(evaluation_results):
            if result.is_feasible:
                feasible_indices.append(i)
            else:
                infeasible_indices.append(i)
        
        # 对可行解进行非支配排序
        if feasible_indices:
            feasible_fronts = self._sort_feasible_solutions(
                [evaluation_results[i] for i in feasible_indices]
            )
            # 重新映射索引
            for front in feasible_fronts:
                mapped_front = [feasible_indices[i] for i in front]
                fronts.append(mapped_front)
        
        # 不可行解按约束违反程度排序，放在最后一层
        if infeasible_indices:
            infeasible_sorted = sorted(infeasible_indices, 
                                     key=lambda i: evaluation_results[i].constraint_violations)
            fronts.append(infeasible_sorted)
        
        return fronts
    
    def _sort_feasible_solutions(self, feasible_results: List[ObjectiveResults]) -> List[List[int]]:
        """对可行解进行快速非支配排序"""
        n = len(feasible_results)
        if n == 0:
            return []
        
        fronts = []
        dominated_solutions = [[] for _ in range(n)]
        domination_count = [0] * n
        
        # 优化的支配关系计算 - 只比较上三角
        for i in range(n):
            for j in range(i + 1, n):
                if self._dominates_objectives_only(feasible_results[i], feasible_results[j]):
                    dominated_solutions[i].append(j)
                    domination_count[j] += 1
                elif self._dominates_objectives_only(feasible_results[j], feasible_results[i]):
                    dominated_solutions[j].append(i)
                    domination_count[i] += 1
        
        # 找到第一层非支配解
        first_front = [i for i in range(n) if domination_count[i] == 0]
        if not first_front:
            return [[i for i in range(n)]]  # 所有解都在同一层
        
        fronts.append(first_front)
        
        # 构建后续层
        current_front = first_front
        while current_front:
            next_front = []
            for i in current_front:
                for j in dominated_solutions[i]:
                    domination_count[j] -= 1
                    if domination_count[j] == 0:
                        next_front.append(j)
            
            if next_front:
                fronts.append(next_front)
            current_front = next_front
        
        return fronts
    
    def _dominates_objectives_only(self, obj1: ObjectiveResults, obj2: ObjectiveResults) -> bool:
        """仅基于目标函数的支配关系判断（用于可行解）"""
        objectives1 = [obj1.energy_consumption, obj1.thermal_performance, obj1.renovation_cost]
        objectives2 = [obj2.energy_consumption, obj2.thermal_performance, obj2.renovation_cost]
        
        better_in_at_least_one = False
        for i in range(len(objectives1)):
            if objectives1[i] > objectives2[i]:  # obj1在第i个目标上劣于obj2
                return False
            elif objectives1[i] < objectives2[i]:  # obj1在第i个目标上优于obj2
                better_in_at_least_one = True
        
        return better_in_at_least_one
    
    def _dominates(self, obj1: ObjectiveResults, obj2: ObjectiveResults) -> bool:
        """判断obj1是否支配obj2"""
        # 考虑约束违反的支配关系
        if obj1.constraint_violations < obj2.constraint_violations:
            return True
        elif obj1.constraint_violations > obj2.constraint_violations:
            return False
        
        # 在可行域内比较目标函数（最小化问题）
        objectives1 = [obj1.energy_consumption, obj1.thermal_performance, obj1.renovation_cost]
        objectives2 = [obj2.energy_consumption, obj2.thermal_performance, obj2.renovation_cost]
        
        # 检查所有目标都不劣于obj2
        better_in_at_least_one = False
        for i in range(len(objectives1)):
            if objectives1[i] > objectives2[i]:  # obj1在第i个目标上劣于obj2
                return False
            elif objectives1[i] < objectives2[i]:  # obj1在第i个目标上优于obj2
                better_in_at_least_one = True
        
        return better_in_at_least_one
    
    def _reference_point_selection(self, front_indices: List[int],
                                 evaluation_results: List[ObjectiveResults],
                                 num_select: int) -> List[int]:
        """基于参考点的选择 - 改进版本增强多样性"""
        try:
            if num_select >= len(front_indices):
                return front_indices
            
            # 归一化目标值
            normalized_objectives = self._normalize_objectives(
                [evaluation_results[i] for i in front_indices]
            )
            
            # 计算每个个体到参考点的距离
            distances_to_refs = []
            for obj in normalized_objectives:
                distances = []
                for ref_point in self.reference_points:
                    distance = self._calculate_perpendicular_distance(obj, ref_point)
                    distances.append(distance)
                distances_to_refs.append(distances)
            
            # 改进的选择策略：平衡距离和多样性
            selected_indices = []
            ref_point_counts = [0] * len(self.reference_points)
            
            # 第一阶段：为每个参考点至少选择一个个体（如果可能）
            used_individuals = set()
            for ref_idx in range(min(len(self.reference_points), num_select)):
                best_idx = -1
                best_distance = float('inf')
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    distance = distances_to_refs[i][ref_idx]
                    if distance < best_distance:
                        best_distance = distance
                        best_idx = i
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    ref_point_counts[ref_idx] += 1
            
            # 第二阶段：填充剩余位置，考虑多样性
            while len(selected_indices) < num_select:
                best_idx = -1
                best_ref_idx = -1
                best_score = float('inf')
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    for ref_idx, distance in enumerate(distances_to_refs[i]):
                        # 综合考虑距离和参考点负载
                        diversity_penalty = ref_point_counts[ref_idx] * 0.2
                        score = distance + diversity_penalty
                        
                        if score < best_score:
                            best_score = score
                            best_idx = i
                            best_ref_idx = ref_idx
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    ref_point_counts[best_ref_idx] += 1
                else:
                    break
            
            return [front_indices[i] for i in selected_indices]
            
        except Exception as e:
            self.logger.error(f"参考点选择失败: {str(e)}")
            # 随机选择作为备选
            return random.sample(front_indices, num_select)
    
    def _enhanced_reference_point_selection(self, front_indices: List[int],
                                          evaluation_results: List[ObjectiveResults],
                                          num_select: int) -> List[int]:
        """增强的参考点选择策略 - 结合多样性和质量"""
        try:
            if num_select >= len(front_indices):
                return front_indices
            
            # 归一化目标值
            normalized_objectives = self._normalize_objectives(
                [evaluation_results[i] for i in front_indices]
            )
            
            # 计算每个个体到参考点的距离
            distances_to_refs = []
            for obj in normalized_objectives:
                distances = []
                for ref_point in self.reference_points:
                    distance = self._calculate_perpendicular_distance(obj, ref_point)
                    distances.append(distance)
                distances_to_refs.append(distances)
            
            # 计算个体间的拥挤距离
            crowding_distances = self._calculate_crowding_distances(normalized_objectives)
            
            # 综合选择策略：平衡参考点关联和拥挤距离
            selected_indices = []
            ref_point_counts = [0] * len(self.reference_points)
            used_individuals = set()
            
            # 第一阶段：为负载最少的参考点选择最近的个体
            for _ in range(min(num_select, len(self.reference_points))):
                # 找到负载最少的参考点
                min_load_refs = [i for i, count in enumerate(ref_point_counts) 
                               if count == min(ref_point_counts)]
                target_ref = random.choice(min_load_refs)
                
                # 为该参考点选择最近且未被选择的个体
                best_idx = -1
                best_distance = float('inf')
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    distance = distances_to_refs[i][target_ref]
                    if distance < best_distance:
                        best_distance = distance
                        best_idx = i
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    ref_point_counts[target_ref] += 1
            
            # 第二阶段：基于拥挤距离选择剩余个体
            while len(selected_indices) < num_select:
                best_idx = -1
                best_score = -1
                
                for i, front_idx in enumerate(front_indices):
                    if i in used_individuals:
                        continue
                    
                    # 综合评分：拥挤距离 + 参考点多样性奖励
                    crowding_score = crowding_distances[i]
                    
                    # 找到该个体最近的参考点
                    nearest_ref = min(range(len(self.reference_points)), 
                                    key=lambda r: distances_to_refs[i][r])
                    
                    # 参考点负载惩罚
                    ref_penalty = ref_point_counts[nearest_ref] * 0.1
                    
                    total_score = crowding_score - ref_penalty
                    
                    if total_score > best_score:
                        best_score = total_score
                        best_idx = i
                
                if best_idx >= 0:
                    selected_indices.append(best_idx)
                    used_individuals.add(best_idx)
                    
                    # 更新参考点计数
                    nearest_ref = min(range(len(self.reference_points)), 
                                    key=lambda r: distances_to_refs[best_idx][r])
                    ref_point_counts[nearest_ref] += 1
                else:
                    break
            
            return [front_indices[i] for i in selected_indices]
            
        except Exception as e:
            self.logger.error(f"增强参考点选择失败: {str(e)}")
            return random.sample(front_indices, num_select)
    
    def _calculate_crowding_distances(self, objectives: List[List[float]]) -> List[float]:
        """计算拥挤距离"""
        try:
            n = len(objectives)
            if n <= 2:
                return [float('inf')] * n
            
            distances = [0.0] * n
            objectives_array = np.array(objectives)
            
            # 对每个目标函数计算拥挤距离
            for m in range(objectives_array.shape[1]):
                # 按第m个目标排序
                sorted_indices = np.argsort(objectives_array[:, m])
                
                # 边界个体设为无穷大
                distances[sorted_indices[0]] = float('inf')
                distances[sorted_indices[-1]] = float('inf')
                
                # 计算中间个体的拥挤距离
                obj_range = objectives_array[sorted_indices[-1], m] - objectives_array[sorted_indices[0], m]
                if obj_range > 1e-10:
                    for i in range(1, n - 1):
                        idx = sorted_indices[i]
                        if distances[idx] != float('inf'):
                            distance_contribution = (objectives_array[sorted_indices[i + 1], m] - 
                                                   objectives_array[sorted_indices[i - 1], m]) / obj_range
                            distances[idx] += distance_contribution
            
            return distances
            
        except Exception:
            return [1.0] * len(objectives)
    
    def _normalize_objectives(self, objective_results: List[ObjectiveResults]) -> List[List[float]]:
        """归一化目标函数值"""
        try:
            objectives_matrix = []
            for obj in objective_results:
                objectives_matrix.append([
                    obj.energy_consumption,
                    obj.thermal_performance,
                    obj.renovation_cost
                ])
            
            objectives_array = np.array(objectives_matrix)
            
            # 找到理想点和最差点
            ideal_point = np.min(objectives_array, axis=0)
            nadir_point = np.max(objectives_array, axis=0)
            
            # 归一化
            normalized = []
            for obj in objectives_array:
                normalized_obj = []
                for i in range(len(obj)):
                    if nadir_point[i] - ideal_point[i] > 1e-10:
                        norm_val = (obj[i] - ideal_point[i]) / (nadir_point[i] - ideal_point[i])
                    else:
                        norm_val = 0.0
                    normalized_obj.append(norm_val)
                normalized.append(normalized_obj)
            
            return normalized
            
        except Exception as e:
            self.logger.error(f"目标函数归一化失败: {str(e)}")
            # 返回原始值作为备选
            return [[obj.energy_consumption, obj.thermal_performance, obj.renovation_cost] 
                   for obj in objective_results]
    
    def _calculate_perpendicular_distance(self, objective: List[float], 
                                        reference_point: List[float]) -> float:
        """计算目标点到参考点的垂直距离"""
        try:
            obj_array = np.array(objective)
            ref_array = np.array(reference_point)
            
            # 计算投影长度
            dot_product = np.dot(obj_array, ref_array)
            ref_norm_sq = np.dot(ref_array, ref_array)
            
            if ref_norm_sq > 1e-10:
                projection_length = dot_product / ref_norm_sq
                projection = projection_length * ref_array
                perpendicular = obj_array - projection
                distance = np.linalg.norm(perpendicular)
            else:
                distance = np.linalg.norm(obj_array)
            
            return distance
            
        except Exception:
            return np.linalg.norm(np.array(objective) - np.array(reference_point))
    
    def _tournament_selection(self, population: List[FacadeIndividual],
                            evaluation_results: List[ObjectiveResults]) -> FacadeIndividual:
        """锦标赛选择"""
        tournament_indices = random.sample(range(len(population)), 
                                         min(self.params.tournament_size, len(population)))
        
        best_idx = tournament_indices[0]
        best_result = evaluation_results[best_idx]
        
        for idx in tournament_indices[1:]:
            current_result = evaluation_results[idx]
            if self._is_better_solution(current_result, best_result):
                best_idx = idx
                best_result = current_result
        
        return population[best_idx]
    
    def _is_better_solution(self, result1: ObjectiveResults, result2: ObjectiveResults) -> bool:
        """判断解1是否优于解2"""
        # 首先比较约束违反
        if result1.constraint_violations < result2.constraint_violations:
            return True
        elif result1.constraint_violations > result2.constraint_violations:
            return False
        
        # 在可行域内，使用支配关系或聚合目标函数
        if self._dominates(result1, result2):
            return True
        elif self._dominates(result2, result1):
            return False
        else:
            # 非支配情况下，使用加权和
            score1 = (result1.energy_consumption * 0.4 + 
                     result1.thermal_performance * 0.3 + 
                     result1.renovation_cost * 0.3 / 100000)  # 成本归一化
            score2 = (result2.energy_consumption * 0.4 + 
                     result2.thermal_performance * 0.3 + 
                     result2.renovation_cost * 0.3 / 100000)
            return score1 < score2
    
    def _generate_reference_points(self) -> List[List[float]]:
        """生成NSGA-III参考点 - 改进版本增加多样性"""
        try:
            num_objectives = 3  # 三个目标函数
            num_divisions = 12  # 增加分割数以获得更多参考点和更好的多样性
            
            reference_points = []
            
            # 生成均匀分布的参考点
            for i in range(num_divisions + 1):
                for j in range(num_divisions + 1 - i):
                    k = num_divisions - i - j
                    point = [i / num_divisions, j / num_divisions, k / num_divisions]
                    reference_points.append(point)
            
            # 添加更多边界和中间参考点以增加多样性
            boundary_points = [
                [1.0, 0.0, 0.0],  # 只关注能耗
                [0.0, 1.0, 0.0],  # 只关注热工性能
                [0.0, 0.0, 1.0],  # 只关注成本
                [0.7, 0.3, 0.0],  # 偏重能耗
                [0.3, 0.7, 0.0],  # 偏重热工性能
                [0.7, 0.0, 0.3],  # 偏重能耗和成本
                [0.3, 0.0, 0.7],  # 偏重成本
                [0.0, 0.7, 0.3],  # 偏重热工性能和成本
                [0.0, 0.3, 0.7],  # 偏重成本
                [0.5, 0.5, 0.0],  # 平衡能耗和热工性能
                [0.5, 0.0, 0.5],  # 平衡能耗和成本
                [0.0, 0.5, 0.5],  # 平衡热工性能和成本
                [1/3, 1/3, 1/3],  # 三目标平衡
                [0.6, 0.2, 0.2],  # 多种权重组合
                [0.2, 0.6, 0.2],
                [0.2, 0.2, 0.6],
                [0.4, 0.4, 0.2],
                [0.4, 0.2, 0.4],
                [0.2, 0.4, 0.4]
            ]
            
            reference_points.extend(boundary_points)
            
            self.opt_logger.info(f"生成参考点: {len(reference_points)} 个")
            return reference_points
            
        except Exception as e:
            self.logger.error(f"生成参考点失败: {str(e)}")
            # 返回默认参考点
            return [[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]]
    
    def _calculate_population_statistics(self, population: List[FacadeIndividual],
                                       evaluation_results: List[ObjectiveResults]) -> PopulationStatistics:
        """计算种群统计信息"""
        try:
            feasible_solutions = []
            infeasible_solutions = []
            
            for result in evaluation_results:
                if result.is_feasible:
                    feasible_solutions.append(result)
                else:
                    infeasible_solutions.append(result)
            
            # 计算平均目标值
            if evaluation_results:
                avg_energy = np.mean([r.energy_consumption for r in evaluation_results])
                avg_thermal = np.mean([r.thermal_performance for r in evaluation_results])
                avg_cost = np.mean([r.renovation_cost for r in evaluation_results])
                avg_violation = np.mean([r.constraint_violations for r in evaluation_results])
            else:
                avg_energy = avg_thermal = avg_cost = avg_violation = 0.0
            
            # 计算多样性指标
            diversity = self._calculate_population_diversity(evaluation_results)
            
            return PopulationStatistics(
                generation=self.generation_count,
                population_size=len(population),
                feasible_count=len(feasible_solutions),
                infeasible_count=len(infeasible_solutions),
                average_objectives=[avg_energy, avg_thermal, avg_cost],
                average_constraint_violation=avg_violation,
                diversity_metric=diversity,
                best_feasible_solution=min(feasible_solutions, 
                                         key=lambda x: x.energy_consumption) if feasible_solutions else None
            )
            
        except Exception as e:
            self.logger.error(f"计算种群统计失败: {str(e)}")
            return PopulationStatistics(
                generation=self.generation_count,
                population_size=len(population),
                feasible_count=0,
                infeasible_count=len(population),
                average_objectives=[0.0, 0.0, 0.0],
                average_constraint_violation=1.0,
                diversity_metric=0.0,
                best_feasible_solution=None
            )
    
    def _calculate_population_diversity(self, evaluation_results: List[ObjectiveResults]) -> float:
        """计算种群多样性"""
        try:
            if len(evaluation_results) < 2:
                return 0.0
            
            # 基于目标空间的多样性计算
            objectives_matrix = np.array([
                [r.energy_consumption, r.thermal_performance, r.renovation_cost]
                for r in evaluation_results
            ])
            
            # 计算所有个体间的平均距离
            total_distance = 0.0
            count = 0
            
            for i in range(len(objectives_matrix)):
                for j in range(i + 1, len(objectives_matrix)):
                    distance = np.linalg.norm(objectives_matrix[i] - objectives_matrix[j])
                    total_distance += distance
                    count += 1
            
            return total_distance / count if count > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def get_population_statistics(self) -> List[PopulationStatistics]:
        """获取种群统计历史"""
        return self.population_history.copy()
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.generation_count = 0
        self.population_history.clear()
        self.evaluation_cache.clear()
        self.cache_hits = 0
        self.cache_misses = 0
    
    def _get_individual_cache_key(self, individual: FacadeIndividual) -> str:
        """生成个体的缓存键"""
        try:
            # 基于个体的主要属性生成哈希键
            key_components = []
            
            if hasattr(individual, 'window_types'):
                key_components.append(f"wt:{','.join(map(str, individual.window_types))}")
            
            if hasattr(individual, 'wall_configurations'):
                key_components.append(f"wc:{','.join(map(str, individual.wall_configurations))}")
            
            if hasattr(individual, 'frame_depths'):
                key_components.append(f"fd:{','.join(f'{x:.3f}' for x in individual.frame_depths)}")
            
            if hasattr(individual, 'shading_depths'):
                key_components.append(f"sd:{','.join(f'{x:.3f}' for x in individual.shading_depths)}")
            
            if hasattr(individual, 'shading_angles'):
                key_components.append(f"sa:{','.join(f'{x:.1f}' for x in individual.shading_angles)}")
            
            return "|".join(key_components)
            
        except Exception:
            # 如果生成键失败，返回个体ID
            return individual.individual_id
    
    def _get_cached_evaluation(self, cache_key: str) -> Optional[ObjectiveResults]:
        """获取缓存的评估结果"""
        if cache_key in self.evaluation_cache:
            self.cache_hits += 1
            return self.evaluation_cache[cache_key]
        else:
            self.cache_misses += 1
            return None
    
    def _cache_evaluation(self, cache_key: str, result: ObjectiveResults) -> None:
        """缓存评估结果"""
        # 限制缓存大小，避免内存溢出
        if len(self.evaluation_cache) > 1000:
            # 清除最旧的一半缓存
            keys_to_remove = list(self.evaluation_cache.keys())[:500]
            for key in keys_to_remove:
                del self.evaluation_cache[key]
        
        self.evaluation_cache[cache_key] = result
    
    def get_cache_statistics(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        total_requests = self.cache_hits + self.cache_misses
        hit_rate = (self.cache_hits / total_requests * 100) if total_requests > 0 else 0
        
        return {
            'cache_hits': self.cache_hits,
            'cache_misses': self.cache_misses,
            'hit_rate': hit_rate,
            'cache_size': len(self.evaluation_cache)
        }


def create_genetic_operators(individual_encoder: IndividualEncoder,
                           objective_evaluator: ObjectiveFunctionEvaluator,
                           constraint_handler: ConstraintHandler) -> GeneticOperators:
    """
    创建遗传算法操作器实例
    
    Args:
        individual_encoder: 个体编码器
        objective_evaluator: 目标函数评估器
        constraint_handler: 约束处理器
        
    Returns:
        配置好的遗传算法操作器
    """
    return GeneticOperators(individual_encoder, objective_evaluator, constraint_handler)