#!/usr/bin/env python3
"""
简化的建筑立面优化主程序
重新设计，避免配置和初始化问题
"""

import os
import sys
from pathlib import Path
from datetime import datetime
import traceback

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 基础导入
from src.core.config import setup_config, get_config
from src.core.logging_config import get_logger
from src.core.data_structures import Orientation, RenovationMode

def setup_environment():
    """设置环境和配置"""
    try:
        # 1. 设置配置文件
        config_path = project_root / "config" / "unified_config.yaml"
        if config_path.exists():
            setup_config(str(config_path))
            print(f"✓ 已加载配置文件: {config_path}")
        else:
            print(f"⚠ 配置文件不存在: {config_path}, 使用默认配置")
        
        # 2. 获取logger
        logger = get_logger(__name__)
        logger.info("环境设置完成")
        
        return True, logger
    except Exception as e:
        print(f"❌ 环境设置失败: {e}")
        traceback.print_exc()
        return False, None

def run_optimization():
    """运行优化流程"""
    print("=" * 60)
    print("🏢 建筑立面优化系统")
    print("=" * 60)
    
    # 1. 设置环境
    success, logger = setup_environment()
    if not success:
        return False
    
    try:
        # 2. 设置输入文件路径
        image_path = "04_simplified_blocks.png"
        epw_path = "CHN_Liaoning.Shenyang.543420_CSWD.epw"
        orientation = Orientation.SOUTH
        renovation_mode = RenovationMode.RENOVATION
        
        # 验证文件存在
        if not os.path.exists(image_path):
            logger.error(f"图像文件不存在: {image_path}")
            return False
        
        if not os.path.exists(epw_path):
            logger.error(f"EPW文件不存在: {epw_path}")
            return False
        
        print(f"📁 输入文件:")
        print(f"   图像: {image_path}")
        print(f"   气候: {epw_path}")
        print(f"   朝向: {orientation.value}")
        print(f"   模式: {renovation_mode.value}")
        print()
        
        # 3. 步骤1: 图像处理
        print("🔍 步骤 1/5: 图像处理与立面元素提取")
        from src.facade_extraction.yolo_segmentation_processor import ColorBlockImageProcessor

        yolo_processor = ColorBlockImageProcessor()
        facade_elements = yolo_processor.process_color_block_image(image_path=image_path)
        
        if not facade_elements:
            logger.error("立面元素提取失败")
            return False
        
        facade_elements.building_orientation = orientation
        logger.info(f"立面元素提取成功: 墙体{len(facade_elements.walls)}, 窗户{len(facade_elements.windows)}, 门{len(facade_elements.doors)}")
        print(f"✓ 提取到立面元素: 墙体{len(facade_elements.walls)}, 窗户{len(facade_elements.windows)}, 门{len(facade_elements.doors)}")
        
        # 4. 步骤2: 气候数据处理
        print("\n🌤️  步骤 2/5: 气候数据处理")
        from src.climate_processing.climate_data_processor import ClimateDataProcessor

        climate_processor = ClimateDataProcessor()
        climate_data = climate_processor.process_epw_file(epw_path, orientation)
        
        if not climate_data:
            logger.error("气候数据处理失败")
            return False
        
        logger.info("气候数据处理完成")
        print("✓ 气候数据处理完成")
        
        # 5. 步骤3: 多目标优化
        print("\n🎯 步骤 3/5: 多目标优化")
        from src.optimization.nsga3_optimizer import create_nsga3_facade_optimizer
        
        optimizer = create_nsga3_facade_optimizer(
            facade_elements=facade_elements,
            climate_data=climate_data,
            renovation_mode=renovation_mode
        )
        
        if not optimizer:
            logger.error("优化器创建失败")
            return False
        
        # 运行优化
        optimization_results = optimizer.optimize(
            max_generations=50,  # 减少代数以便快速测试
            max_time_seconds=1800  # 30分钟
        )
        
        if not optimization_results or not optimization_results.pareto_solutions:
            logger.error("优化失败或无有效解")
            return False
        
        logger.info(f"优化完成: 找到{len(optimization_results.pareto_solutions)}个Pareto最优解")
        print(f"✓ 优化完成: 找到{len(optimization_results.pareto_solutions)}个Pareto最优解")
        
        # 6. 步骤4: 解选择
        print("\n📊 步骤 4/5: 解选择")
        from src.solution_selection.solution_selector import SolutionSelector
        
        solution_selector = SolutionSelector()
        selected_solutions = solution_selector.select_four_dimension_solutions(
            optimization_results=optimization_results
        )
        
        logger.info(f"解选择完成: 选择了{len(selected_solutions)}个代表性解")
        print(f"✓ 解选择完成: 选择了{len(selected_solutions)}个代表性解")
        
        # 7. 步骤5: 结果可视化
        print("\n📈 步骤 5/5: 结果可视化")
        from src.visualization.visualization_manager import VisualizationManager
        from src.core.data_structures import VisualizationData, EnergyBreakdown, ThermalMetrics, CostBreakdown
        
        # 创建输出目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(f"outputs/simple_run_{timestamp}")
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建可视化数据
        visualization_data = VisualizationData(
            facade_elements=facade_elements,
            climate_data=climate_data,
            optimization_results=optimization_results,
            energy_breakdown=EnergyBreakdown(
                heating=50.0, cooling=30.0, lighting=15.0, ventilation=5.0
            ),
            thermal_metrics=ThermalMetrics(
                avg_temperature=22.0, comfort_hours=6000, thermal_comfort_score=0.8
            ),
            cost_breakdown=CostBreakdown(
                material_cost=10000.0, labor_cost=5000.0, equipment_cost=2000.0
            ),
            selected_solutions=selected_solutions
        )
        
        # 生成可视化
        visualization_manager = VisualizationManager()
        visualization_manager.generate_comprehensive_report(
            visualization_data=visualization_data,
            output_directory=str(output_dir)
        )
        
        logger.info(f"可视化完成，结果保存到: {output_dir}")
        print(f"✓ 可视化完成，结果保存到: {output_dir}")
        
        print("\n" + "=" * 60)
        print("🎉 优化流程完成！")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        logger.error(f"优化流程失败: {e}")
        print(f"❌ 优化流程失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        success = run_optimization()
        if success:
            print("\n✅ 程序执行成功")
            return 0
        else:
            print("\n❌ 程序执行失败")
            return 1
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
        return 1
    except Exception as e:
        print(f"\n💥 程序异常: {e}")
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
