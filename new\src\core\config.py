"""
配置管理系统
管理整个建筑立面优化系统的配置参数
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path
import logging


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认配置
        """
        self.config_path = config_path
        self._config = self._load_default_config()
        
        if config_path and os.path.exists(config_path):
            self._load_config_file(config_path)
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            # 图像处理配置
            'image_processing': {
                'color_mapping': {
                    'walls': {'min': [100, 100, 100], 'max': [160, 160, 160]},    # 灰色墙体
                    'windows': {'min': [240, 0, 240], 'max': [255, 30, 255]},      # 洋红色窗户，扩大绿色通道范围  
                    'doors': {'min': [240, 240, 0], 'max': [255, 255, 20]},        # 黄色门
                    'shading': {'min': [0, 200, 0], 'max': [20, 255, 20]},         # 绿色植物/遮阳
                    'frames': {'min': [0, 0, 0], 'max': [30, 30, 30]}              # 黑色窗框边界
                },
                'color_matching': {
                    'strategy': 'range',  # 'range' 或 'exact'
                    'tolerance': 25,      # 增加容错范围
                    'use_hsv': False
                },
                'preprocessing': {
                    'noise_filter_kernel': 3,
                    'min_contour_area': 100,
                    'max_contour_area': 50000
                },
                'pixel_to_meter_ratio': 0.01
            },
            
            # 气候数据处理配置
            'climate_processing': {
                'epw_encoding': 'utf-8',
                'solar_radiation_threshold': 50.0,  # W/m²
                'wind_speed_threshold': 20.0,      # m/s
                'orientation_adjustment': {
                    'solar_factor': {
                        'south': 1.0,
                        'southeast': 0.9,
                        'southwest': 0.9,
                        'east': 0.7,
                        'west': 0.7,
                        'north': 0.3,
                        'northeast': 0.5,
                        'northwest': 0.5
                    },
                    'wind_factor': {
                        'south': 0.8,
                        'southeast': 0.9,
                        'southwest': 0.9,
                        'east': 1.0,
                        'west': 1.0,
                        'north': 0.6,
                        'northeast': 0.8,
                        'northwest': 0.8
                    }
                }
            },
            
            # 遗传算法配置
            'genetic_algorithm': {
                'population_size': 100,
                'max_generations': 100,
                'crossover_probability': 0.9,
                'mutation_probability': 0.1,
                'mutation_strength': 0.1,
                'reference_points': 12,  # NSGA-III参考点数量
                'convergence_threshold': 1e-6,
                'stagnation_generations': 20
            },
            
            # 优化约束配置
            'optimization_constraints': {
                'window_wall_ratio': {'min': 0.1, 'max': 0.8},
                'window_size': {'min_width': 0.6, 'max_width': 3.0, 
                               'min_height': 0.8, 'max_height': 2.5},
                'frame_depth': {'min': 0.0, 'max': 0.5},
                'shading_depth': {'min': 0.0, 'max': 1.5},
                'shading_angle': {'min': 0.0, 'max': 90.0},
                'minimum_distance': 0.3,  # 窗户间最小距离
                'wall_margin': 0.2        # 窗户距墙边最小距离
            },
            
            # 性能评估配置
            'performance_evaluation': {
                'energy_calculation': {
                    'heating_setpoint': 20.0,      # °C
                    'cooling_setpoint': 26.0,      # °C
                    'heating_cop': 3.0,             # 供暖系统COP
                    'cooling_cop': 4.0,             # 制冷系统COP
                    'lighting_power_density': 10.0, # W/m²
                    'equipment_power_density': 8.0,  # W/m²
                    'occupancy_schedule': 'standard_office'
                },
                'thermal_properties': {
                    'wall_u_value': 1.5,           # W/(m²·K)
                    'window_u_value': 2.5,         # W/(m²·K)
                    'wall_thermal_mass': 500.0,    # J/(m²·K)
                    'window_g_value': 0.6,         # 太阳得热系数
                    'infiltration_rate': 0.5       # ACH
                },
                'comfort_criteria': {
                    'temperature_range': [18.0, 28.0],  # °C
                    'humidity_range': [30.0, 70.0],     # %
                    'air_velocity_max': 0.25            # m/s
                }
            },
            
            # 成本评估配置
            'cost_evaluation': {
                'material_costs': {
                    'window_per_m2': 800.0,        # 元/m²
                    'frame_per_m': 200.0,          # 元/m
                    'shading_per_m2': 300.0,       # 元/m²
                    'installation_factor': 1.3     # 安装费用系数
                },
                'labor_costs': {
                    'window_installation_per_unit': 500.0,  # 元/窗
                    'frame_installation_per_m': 100.0,      # 元/m
                    'shading_installation_per_m2': 150.0    # 元/m²
                },
                'lifecycle_parameters': {
                    'design_life': 30,             # 年
                    'discount_rate': 0.05,         # 5%
                    'maintenance_rate': 0.02       # 年维护费率
                }
            },
            
            # 可视化配置
            'visualization': {
                'figure_size': (12, 8),
                'dpi': 300,
                'color_schemes': {
                    'default': {
                        'primary': '#E74C3C',      # 红色
                        'secondary': '#3498DB',    # 蓝色
                        'accent': '#2ECC71',       # 绿色
                        'background': '#F8F9FA',   # 浅灰色
                        'text': '#2C3E50'          # 深蓝灰色
                    },
                    'professional': {
                        'primary': '#34495E',      # 深蓝灰色
                        'secondary': '#95A5A6',    # 中灰色
                        'accent': '#E67E22',       # 橙色
                        'background': '#FFFFFF',   # 白色
                        'text': '#2C3E50'          # 深蓝灰色
                    },
                    'academic': {
                        'primary': '#8E44AD',      # 紫色
                        'secondary': '#16A085',    # 青绿色
                        'accent': '#F39C12',       # 黄色
                        'background': '#FAFAFA',   # 极浅灰色
                        'text': '#34495E'          # 深蓝灰色
                    }
                },
                'fonts': {
                    'title': {'family': 'SimHei', 'size': 16, 'weight': 'bold'},
                    'label': {'family': 'SimHei', 'size': 12, 'weight': 'normal'},
                    'text': {'family': 'SimHei', 'size': 10, 'weight': 'normal'}
                },
                'language': 'bilingual',  # 'chinese', 'english', 'bilingual'
                'output_formats': ['png']
            },
            
            # 输出配置
            'output': {
                'base_directory': 'output',
                'create_timestamp_folders': True,
                'chart_subdirectories': [
                    '01_algorithm_convergence',
                    '02_pareto_frontier_analysis', 
                    '03_thermal_performance_analysis',
                    '04_energy_breakdown_analysis',
                    '05_best_solutions_comparison',
                    '06_solutions_grid_layout',
                    '07_3d_solutions',
                    '08_comprehensive_performance_radar',
                    '09_solution_clustering_analysis',
                    '10_parameter_correlation_analysis'
                ],
                'save_intermediate_results': True,
                'compression_level': 6
            },
            
            # 日志配置
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_output': True,
                'console_output': True,
                'log_directory': 'logs',
                'max_file_size': '10MB',
                'backup_count': 5
            },
            
            # 系统配置
            'system': {
                'max_memory_usage': 4.0,       # GB
                'parallel_processes': 4,
                'cache_enabled': True,
                'cache_size': 1000,            # MB
                'temp_directory': 'temp'
            }
        }
    
    def _load_config_file(self, config_path: str) -> None:
        """从文件加载配置"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.endswith('.yaml') or config_path.endswith('.yml'):
                    file_config = yaml.safe_load(f)
                else:
                    # 假设是JSON格式
                    import json
                    file_config = json.load(f)
            
            # 递归更新配置
            self._deep_update(self._config, file_config)
            logging.info(f"已加载配置文件: {config_path}")
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {e}")
    
    def _deep_update(self, base_dict: Dict[str, Any], update_dict: Dict[str, Any]) -> None:
        """递归更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键，如 'image_processing.color_mapping'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key.split('.')
        current = self._config
        
        try:
            for k in keys:
                current = current[k]
            return current
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """
        设置配置值
        
        Args:
            key: 配置键，支持点号分隔的嵌套键
            value: 配置值
        """
        keys = key.split('.')
        current = self._config
        
        # 创建嵌套字典路径
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置最终值
        current[keys[-1]] = value
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        获取配置节
        
        Args:
            section: 节名称
            
        Returns:
            配置节字典
        """
        return self.get(section, {})
    
    def save_config(self, output_path: str) -> None:
        """
        保存配置到文件
        
        Args:
            output_path: 输出文件路径
        """
        try:
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                if output_path.endswith('.yaml') or output_path.endswith('.yml'):
                    yaml.dump(self._config, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                else:
                    import json
                    json.dump(self._config, f, ensure_ascii=False, indent=2)
            
            logging.info(f"配置已保存到: {output_path}")
            
        except Exception as e:
            logging.error(f"保存配置文件失败: {e}")
    
    def validate_config(self) -> bool:
        """
        验证配置的完整性和合理性
        
        Returns:
            True如果配置有效
        """
        try:
            # 检查必需的配置节
            required_sections = [
                'image_processing',
                'climate_processing', 
                'genetic_algorithm',
                'optimization_constraints',
                'performance_evaluation',
                'cost_evaluation',
                'visualization'
            ]
            
            for section in required_sections:
                if section not in self._config:
                    logging.error(f"缺少必需的配置节: {section}")
                    return False
            
            # 验证数值范围
            ga_config = self.get_section('genetic_algorithm')
            if ga_config.get('population_size', 0) < 10:
                logging.error("种群大小必须至少为10")
                return False
            
            if ga_config.get('max_generations', 0) < 1:
                logging.error("最大代数必须至少为1")
                return False
            
            # 验证约束参数
            constraints = self.get_section('optimization_constraints')
            wwr = constraints.get('window_wall_ratio', {})
            if wwr.get('min', 0) >= wwr.get('max', 1):
                logging.error("窗墙比最小值必须小于最大值")
                return False
            
            logging.info("配置验证通过")
            return True
            
        except Exception as e:
            logging.error(f"配置验证失败: {e}")
            return False
    
    def get_output_directory(self, create: bool = True) -> str:
        """
        获取输出目录路径
        
        Args:
            create: 是否创建目录
            
        Returns:
            输出目录路径
        """
        base_dir = self.get('output.base_directory', 'output')
        
        if self.get('output.create_timestamp_folders', True):
            import datetime
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
            output_dir = os.path.join(base_dir, timestamp)
        else:
            output_dir = base_dir
        
        if create:
            os.makedirs(output_dir, exist_ok=True)
        
        return output_dir
    
    def get_chart_output_path(self, chart_name: str) -> str:
        """
        获取图表输出路径
        
        Args:
            chart_name: 图表名称
            
        Returns:
            图表输出路径
        """
        output_dir = self.get_output_directory()
        chart_dir = os.path.join(output_dir, 'charts', chart_name)
        os.makedirs(chart_dir, exist_ok=True)
        return chart_dir


# 全局配置实例
config_manager = ConfigManager()


def setup_config(config_path: Optional[str] = None) -> ConfigManager:
    """
    设置全局配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        配置管理器实例
    """
    global config_manager
    config_manager = ConfigManager(config_path)
    return config_manager


def get_config() -> ConfigManager:
    """获取全局配置管理器"""
    return config_manager