"""
核心模块
提供系统的基础设施和核心功能
"""

from .config import Config<PERSON>ana<PERSON>, config_manager, setup_config, get_config
from .data_structures import *
from .exceptions import *
from .logging_config import *
from .utils import *

__all__ = [
    # 配置管理
    'ConfigManager', 'config_manager', 'setup_config', 'get_config',
    
    # 数据结构
    'ElementType', 'RenovationMode', 'Orientation',
    'BuildingElement', 'WindowElement', 'WallElement', 'FacadeElements',
    'HourlyClimateData', 'OrientedSolarData', 'OrientedWindData', 'OrientedClimateData',
    'FacadeIndividual', 'ConvergenceHistory', 'BestSolutions', 'OptimizationResults',
    'EnergyBreakdown', 'ThermalPerformanceMetrics', 'CostBreakdown', 'VisualizationData',
    
    # 异常处理
    'FacadeOptimizationError', 'ConfigurationError', 'DataValidationError',
    'handle_exception', 'ErrorContext',
    
    # 日志系统
    'setup_logging', 'get_logger', 'get_performance_logger', 'LogContext',
    
    # 工具函数
    'FileUtils', 'DataUtils', 'ValidationUtils', 'HashUtils', 'TimeUtils', 'MathUtils', 'MemoryUtils'
]