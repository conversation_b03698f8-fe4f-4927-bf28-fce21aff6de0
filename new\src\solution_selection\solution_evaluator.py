"""
解决方案评估器
对选定的解决方案进行详细评估和比较分析
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime
import json

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import SolutionSelectionError, handle_exception
from ..core.data_structures import (
    ObjectiveResults, FacadeIndividual, OrientedClimateData,
    RenovationMode, EnergyBreakdown, ThermalPerformanceMetrics, CostBreakdown
)
from ..performance_evaluation import create_performance_integrator


class SolutionEvaluator:
    """
    解决方案评估器
    
    功能：
    1. 解决方案详细评估
    2. 多维度对比分析
    3. 风险评估
    4. 实施可行性分析
    5. 经济效益分析
    6. 生成评估报告
    """
    
    def __init__(self):
        """初始化解决方案评估器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取评估配置
        solution_config = self.config.get_section('solution_selection')
        self.evaluation_config = solution_config.get('solution_evaluation', {})
        
        # 评估指标配置
        self.evaluation_metrics = self.evaluation_config.get('evaluation_metrics', {})
        self.technical_weight = self.evaluation_metrics.get('technical_weight', 0.4)
        self.economic_weight = self.evaluation_metrics.get('economic_weight', 0.3)
        self.environmental_weight = self.evaluation_metrics.get('environmental_weight', 0.2)
        self.social_weight = self.evaluation_metrics.get('social_weight', 0.1)
        
        # 风险评估配置
        self.risk_config = self.evaluation_config.get('risk_assessment', {})
        self.technical_risk_factors = self.risk_config.get('technical_risk_factors', [])
        self.economic_risk_factors = self.risk_config.get('economic_risk_factors', [])
        
        # 基准值配置
        self.benchmarks = self.evaluation_config.get('benchmarks', {})
        self.energy_benchmark = self.benchmarks.get('energy_benchmark', 100)
        self.cost_benchmark = self.benchmarks.get('cost_benchmark', 1000)
        self.comfort_benchmark = self.benchmarks.get('comfort_benchmark', 6500)
        
        self.logger.info("解决方案评估器初始化完成")
    
    @handle_exception
    def comprehensive_solution_evaluation(self, solutions: Dict[str, ObjectiveResults],
                                        climate_data: OrientedClimateData,
                                        facade_elements,
                                        facade_area: float,
                                        renovation_mode: RenovationMode) -> Dict[str, Any]:
        """
        综合解决方案评估
        
        Args:
            solutions: 解决方案字典
            climate_data: 气候数据
            facade_elements: 立面元素
            facade_area: 立面面积
            renovation_mode: 改造模式
            
        Returns:
            综合评估结果
            
        Raises:
            SolutionSelectionError: 评估失败时抛出
        """
        with LogContext("综合解决方案评估", self.logger):
            try:
                comprehensive_evaluation = {
                    'evaluation_timestamp': datetime.now().isoformat(),
                    'solutions_count': len(solutions),
                    'individual_evaluations': {},
                    'comparative_analysis': {},
                    'risk_assessment': {},
                    'feasibility_analysis': {},
                    'economic_analysis': {},
                    'recommendations': {}
                }
                
                # 初始化性能集成器
                performance_integrator = create_performance_integrator(
                    climate_data, facade_elements, facade_area, renovation_mode
                )
                
                # 逐个评估解决方案
                solution_performances = {}
                for solution_name, solution in solutions.items():
                    # 创建虚拟的FacadeIndividual（简化处理）
                    individual = self._create_virtual_individual(solution, renovation_mode)
                    
                    # 详细性能分析
                    performance_analysis = performance_integrator.comprehensive_performance_analysis(individual)
                    solution_performances[solution_name] = performance_analysis
                    
                    # 单个解决方案评估
                    individual_evaluation = self._evaluate_individual_solution(
                        solution, performance_analysis
                    )
                    comprehensive_evaluation['individual_evaluations'][solution_name] = individual_evaluation
                
                # 对比分析
                comprehensive_evaluation['comparative_analysis'] = self._perform_comparative_analysis(
                    solutions, solution_performances
                )
                
                # 风险评估
                comprehensive_evaluation['risk_assessment'] = self._assess_solution_risks(
                    solutions, solution_performances
                )
                
                # 可行性分析
                comprehensive_evaluation['feasibility_analysis'] = self._analyze_implementation_feasibility(
                    solutions, solution_performances
                )
                
                # 经济效益分析
                comprehensive_evaluation['economic_analysis'] = self._analyze_economic_benefits(
                    solutions, solution_performances
                )
                
                # 生成推荐
                comprehensive_evaluation['recommendations'] = self._generate_comprehensive_recommendations(
                    solutions, comprehensive_evaluation
                )
                
                self.logger.info(f"综合解决方案评估完成，共评估{len(solutions)}个解决方案")
                
                return comprehensive_evaluation
                
            except Exception as e:
                raise SolutionSelectionError(f"综合解决方案评估失败: {str(e)}") from e
    
    def _create_virtual_individual(self, solution: ObjectiveResults, 
                                 renovation_mode: RenovationMode) -> FacadeIndividual:
        """创建虚拟的立面个体（用于性能分析）"""
        try:
            # 基于目标结果创建简化的立面个体
            # 这里使用默认配置，实际应该根据solution的具体参数构建
            individual = FacadeIndividual(
                individual_id=solution.individual_id,
                renovation_mode=renovation_mode,
                window_positions=[(2.0, 1.0), (6.0, 1.0)],  # 默认窗户位置
                window_sizes=[(1.5, 1.8), (1.5, 1.8)],      # 默认窗户尺寸
                window_types=[1, 2],                          # 默认窗户类型
                frame_depths=[0.1, 0.0],                      # 默认窗框深度
                shading_depths=[0.0, 0.5],                    # 默认遮阳深度
                shading_angles=[0.0, 30.0]                    # 默认遮阳角度
            )
            
            return individual
            
        except Exception as e:
            self.logger.error(f"创建虚拟立面个体失败: {str(e)}")
            # 返回最简单的个体
            return FacadeIndividual(
                individual_id=solution.individual_id,
                renovation_mode=renovation_mode,
                window_positions=[(3.0, 1.5)],
                window_sizes=[(1.2, 1.5)],
                window_types=[0],
                frame_depths=[0.0],
                shading_depths=[0.0],
                shading_angles=[0.0]
            )
    
    def _evaluate_individual_solution(self, solution: ObjectiveResults,
                                    performance_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """评估单个解决方案"""
        try:
            # 技术评估
            technical_score = self._calculate_technical_score(solution, performance_analysis)
            
            # 经济评估
            economic_score = self._calculate_economic_score(solution, performance_analysis)
            
            # 环境评估
            environmental_score = self._calculate_environmental_score(solution, performance_analysis)
            
            # 社会评估
            social_score = self._calculate_social_score(solution, performance_analysis)
            
            # 综合评分
            overall_score = (
                technical_score * self.technical_weight +
                economic_score * self.economic_weight +
                environmental_score * self.environmental_weight +
                social_score * self.social_weight
            )
            
            # 解决方案等级
            solution_grade = self._determine_solution_grade(overall_score)
            
            # 优缺点分析
            strengths_weaknesses = self._analyze_solution_strengths_weaknesses(
                solution, performance_analysis
            )
            
            individual_evaluation = {
                'technical_score': technical_score,
                'economic_score': economic_score,
                'environmental_score': environmental_score,
                'social_score': social_score,
                'overall_score': overall_score,
                'solution_grade': solution_grade,
                'strengths': strengths_weaknesses['strengths'],
                'weaknesses': strengths_weaknesses['weaknesses'],
                'key_metrics': {
                    'energy_performance': solution.energy_consumption,
                    'thermal_performance': solution.thermal_performance,
                    'cost_performance': solution.renovation_cost,
                    'feasibility_score': solution.is_feasible
                }
            }
            
            return individual_evaluation
            
        except Exception as e:
            self.logger.error(f"单个解决方案评估失败: {str(e)}")
            return {
                'technical_score': 50,
                'economic_score': 50,
                'environmental_score': 50,
                'social_score': 50,
                'overall_score': 50,
                'solution_grade': '中等',
                'strengths': [],
                'weaknesses': [],
                'key_metrics': {}
            }
    
    def _calculate_technical_score(self, solution: ObjectiveResults,
                                 performance_analysis: Dict[str, Any]) -> float:
        """计算技术评分"""
        try:
            # 能耗性能评分
            energy_score = max(0, min(100, 100 - (solution.energy_consumption - 50) * 2))
            
            # 热工性能评分
            thermal_score = max(0, min(100, 100 - solution.thermal_performance * 100))
            
            # 可行性评分
            feasibility_score = 100 if solution.is_feasible else 50
            
            # 约束违反惩罚
            constraint_penalty = min(50, solution.constraint_violations * 20)
            
            # 综合技术评分
            technical_score = (energy_score * 0.4 + thermal_score * 0.4 + 
                             feasibility_score * 0.2 - constraint_penalty)
            
            return max(0, min(100, technical_score))
            
        except Exception:
            return 60.0
    
    def _calculate_economic_score(self, solution: ObjectiveResults,
                                performance_analysis: Dict[str, Any]) -> float:
        """计算经济评分"""
        try:
            # 初始成本评分（成本越低评分越高）
            cost_score = max(0, min(100, 100 - (solution.renovation_cost - 50000) / 1500))
            
            # 性价比评分
            if solution.energy_consumption > 0:
                cost_effectiveness = solution.renovation_cost / (150 - solution.energy_consumption)
                effectiveness_score = max(0, min(100, 100 - cost_effectiveness / 500))
            else:
                effectiveness_score = 50
            
            # 投资回收期评分（假设数据）
            payback_score = 70  # 简化处理
            
            # 综合经济评分
            economic_score = (cost_score * 0.4 + effectiveness_score * 0.4 + 
                            payback_score * 0.2)
            
            return max(0, min(100, economic_score))
            
        except Exception:
            return 60.0
    
    def _calculate_environmental_score(self, solution: ObjectiveResults,
                                     performance_analysis: Dict[str, Any]) -> float:
        """计算环境评分"""
        try:
            # 节能评分（基于能耗）
            energy_saving_score = max(0, min(100, (150 - solution.energy_consumption) * 2))
            
            # 碳排放评分（假设与能耗成正比）
            carbon_reduction_score = energy_saving_score
            
            # 可持续性评分
            sustainability_score = 75  # 简化处理
            
            # 综合环境评分
            environmental_score = (energy_saving_score * 0.5 + 
                                 carbon_reduction_score * 0.3 + 
                                 sustainability_score * 0.2)
            
            return max(0, min(100, environmental_score))
            
        except Exception:
            return 65.0
    
    def _calculate_social_score(self, solution: ObjectiveResults,
                              performance_analysis: Dict[str, Any]) -> float:
        """计算社会评分"""
        try:
            # 舒适度评分（基于热工性能）
            comfort_score = max(0, min(100, 100 - solution.thermal_performance * 100))
            
            # 美观性评分
            aesthetic_score = 70  # 简化处理
            
            # 用户接受度评分
            user_acceptance_score = 75  # 简化处理
            
            # 综合社会评分
            social_score = (comfort_score * 0.5 + aesthetic_score * 0.3 + 
                          user_acceptance_score * 0.2)
            
            return max(0, min(100, social_score))
            
        except Exception:
            return 65.0
    
    def _determine_solution_grade(self, overall_score: float) -> str:
        """确定解决方案等级"""
        if overall_score >= 90:
            return "优秀"
        elif overall_score >= 80:
            return "良好"
        elif overall_score >= 70:
            return "中等"
        elif overall_score >= 60:
            return "及格"
        else:
            return "需改进"
    
    def _analyze_solution_strengths_weaknesses(self, solution: ObjectiveResults,
                                             performance_analysis: Dict[str, Any]) -> Dict[str, List[str]]:
        """分析解决方案优缺点"""
        strengths = []
        weaknesses = []
        
        # 能耗分析
        if solution.energy_consumption < 70:
            strengths.append("能耗表现优秀，节能效果显著")
        elif solution.energy_consumption > 120:
            weaknesses.append("能耗偏高，需要优化节能措施")
        
        # 热工性能分析
        if solution.thermal_performance < 0.3:
            strengths.append("热工性能良好，室内舒适度高")
        elif solution.thermal_performance > 0.7:
            weaknesses.append("热工性能有待改善")
        
        # 成本分析
        if solution.renovation_cost < 80000:
            strengths.append("改造成本控制良好，经济性佳")
        elif solution.renovation_cost > 150000:
            weaknesses.append("改造成本较高，需要评估投资回报")
        
        # 可行性分析
        if solution.is_feasible:
            strengths.append("技术方案可行，实施风险低")
        else:
            weaknesses.append("存在技术约束，需要进一步优化")
        
        return {'strengths': strengths, 'weaknesses': weaknesses}
    
    def _perform_comparative_analysis(self, solutions: Dict[str, ObjectiveResults],
                                    performances: Dict[str, Any]) -> Dict[str, Any]:
        """执行对比分析"""
        try:
            comparative_analysis = {
                'performance_comparison': {},
                'ranking_analysis': {},
                'trade_off_analysis': {},
                'similarity_analysis': {}
            }
            
            if not solutions:
                return comparative_analysis
            
            solution_list = list(solutions.values())
            
            # 性能对比
            energy_values = [sol.energy_consumption for sol in solution_list]
            thermal_values = [sol.thermal_performance for sol in solution_list]
            cost_values = [sol.renovation_cost for sol in solution_list]
            
            comparative_analysis['performance_comparison'] = {
                'energy_consumption': {
                    'best': min(energy_values),
                    'worst': max(energy_values),
                    'average': np.mean(energy_values),
                    'range': max(energy_values) - min(energy_values)
                },
                'thermal_performance': {
                    'best': min(thermal_values),
                    'worst': max(thermal_values),
                    'average': np.mean(thermal_values),
                    'range': max(thermal_values) - min(thermal_values)
                },
                'renovation_cost': {
                    'lowest': min(cost_values),
                    'highest': max(cost_values),
                    'average': np.mean(cost_values),
                    'range': max(cost_values) - min(cost_values)
                }
            }
            
            # 权衡分析
            comparative_analysis['trade_off_analysis'] = self._analyze_performance_tradeoffs(
                energy_values, thermal_values, cost_values
            )
            
            # 相似性分析
            comparative_analysis['similarity_analysis'] = self._analyze_solution_similarity(solutions)
            
            return comparative_analysis
            
        except Exception as e:
            self.logger.error(f"对比分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _analyze_performance_tradeoffs(self, energy_values: List[float],
                                     thermal_values: List[float],
                                     cost_values: List[float]) -> Dict[str, str]:
        """分析性能权衡"""
        try:
            tradeoffs = {}
            
            # 能耗与成本权衡
            if len(energy_values) > 1 and len(cost_values) > 1:
                energy_cost_corr = np.corrcoef(energy_values, cost_values)[0, 1]
                if energy_cost_corr > 0.5:
                    tradeoffs['energy_cost'] = "低能耗方案通常需要更高投资"
                elif energy_cost_corr < -0.5:
                    tradeoffs['energy_cost'] = "存在低成本高性能的优势方案"
                else:
                    tradeoffs['energy_cost'] = "能耗与成本关系复杂"
            
            # 热工性能与成本权衡
            if len(thermal_values) > 1 and len(cost_values) > 1:
                thermal_cost_corr = np.corrcoef(thermal_values, cost_values)[0, 1]
                if thermal_cost_corr < -0.5:
                    tradeoffs['thermal_cost'] = "提升热工性能需要增加投资"
                else:
                    tradeoffs['thermal_cost'] = "热工性能与成本关系不明显"
            
            return tradeoffs
            
        except Exception:
            return {'analysis': '权衡分析数据不足'}
    
    def _analyze_solution_similarity(self, solutions: Dict[str, ObjectiveResults]) -> Dict[str, Any]:
        """分析解决方案相似性"""
        try:
            similarity_matrix = {}
            solution_names = list(solutions.keys())
            
            for i, name1 in enumerate(solution_names):
                similarity_matrix[name1] = {}
                for j, name2 in enumerate(solution_names):
                    if i != j:
                        similarity = self._calculate_solution_similarity(
                            solutions[name1], solutions[name2]
                        )
                        similarity_matrix[name1][name2] = similarity
            
            # 找到最相似的解决方案对
            max_similarity = 0
            most_similar_pair = None
            
            for name1, similarities in similarity_matrix.items():
                for name2, similarity in similarities.items():
                    if similarity > max_similarity:
                        max_similarity = similarity
                        most_similar_pair = (name1, name2)
            
            return {
                'similarity_matrix': similarity_matrix,
                'most_similar_pair': most_similar_pair,
                'max_similarity': max_similarity
            }
            
        except Exception:
            return {'analysis': '相似性分析失败'}
    
    def _calculate_solution_similarity(self, sol1: ObjectiveResults, 
                                     sol2: ObjectiveResults) -> float:
        """计算两个解决方案的相似性"""
        try:
            # 归一化差异
            energy_diff = abs(sol1.energy_consumption - sol2.energy_consumption) / 200
            thermal_diff = abs(sol1.thermal_performance - sol2.thermal_performance) / 1.0
            cost_diff = abs(sol1.renovation_cost - sol2.renovation_cost) / 200000
            
            # 计算相似性（1 - 欧几里得距离）
            distance = np.sqrt(energy_diff**2 + thermal_diff**2 + cost_diff**2)
            similarity = max(0, 1 - distance)
            
            return similarity
            
        except Exception:
            return 0.5
    
    def _assess_solution_risks(self, solutions: Dict[str, ObjectiveResults],
                             performances: Dict[str, Any]) -> Dict[str, Any]:
        """评估解决方案风险"""
        try:
            risk_assessment = {
                'technical_risks': {},
                'economic_risks': {},
                'implementation_risks': {},
                'overall_risk_levels': {}
            }
            
            for solution_name, solution in solutions.items():
                # 技术风险
                technical_risk = self._assess_technical_risk(solution)
                
                # 经济风险
                economic_risk = self._assess_economic_risk(solution)
                
                # 实施风险
                implementation_risk = self._assess_implementation_risk(solution)
                
                # 总体风险等级
                overall_risk = (technical_risk + economic_risk + implementation_risk) / 3
                risk_level = self._determine_risk_level(overall_risk)
                
                risk_assessment['technical_risks'][solution_name] = technical_risk
                risk_assessment['economic_risks'][solution_name] = economic_risk
                risk_assessment['implementation_risks'][solution_name] = implementation_risk
                risk_assessment['overall_risk_levels'][solution_name] = {
                    'risk_score': overall_risk,
                    'risk_level': risk_level
                }
            
            return risk_assessment
            
        except Exception as e:
            self.logger.error(f"风险评估失败: {str(e)}")
            return {'error': str(e)}
    
    def _assess_technical_risk(self, solution: ObjectiveResults) -> float:
        """评估技术风险"""
        risk_score = 0.0
        
        # 可行性风险
        if not solution.is_feasible:
            risk_score += 30
        
        # 约束违反风险
        risk_score += min(40, solution.constraint_violations * 20)
        
        # 性能偏离风险
        if solution.energy_consumption > 150:
            risk_score += 20
        
        if solution.thermal_performance > 0.8:
            risk_score += 15
        
        return min(100, risk_score)
    
    def _assess_economic_risk(self, solution: ObjectiveResults) -> float:
        """评估经济风险"""
        risk_score = 0.0
        
        # 高成本风险
        if solution.renovation_cost > 200000:
            risk_score += 40
        elif solution.renovation_cost > 150000:
            risk_score += 25
        
        # 低性价比风险
        if solution.energy_consumption > 0:
            cost_per_energy = solution.renovation_cost / (150 - solution.energy_consumption)
            if cost_per_energy > 2000:
                risk_score += 30
        
        return min(100, risk_score)
    
    def _assess_implementation_risk(self, solution: ObjectiveResults) -> float:
        """评估实施风险"""
        # 简化的实施风险评估
        risk_score = 25  # 基础风险
        
        # 复杂度风险（基于成本估算）
        if solution.renovation_cost > 180000:
            risk_score += 20
        
        # 技术难度风险
        if solution.constraint_violations > 0.5:
            risk_score += 15
        
        return min(100, risk_score)
    
    def _determine_risk_level(self, risk_score: float) -> str:
        """确定风险等级"""
        if risk_score < 20:
            return "低风险"
        elif risk_score < 40:
            return "中低风险"
        elif risk_score < 60:
            return "中等风险"
        elif risk_score < 80:
            return "中高风险"
        else:
            return "高风险"
    
    def _analyze_implementation_feasibility(self, solutions: Dict[str, ObjectiveResults],
                                          performances: Dict[str, Any]) -> Dict[str, Any]:
        """分析实施可行性"""
        try:
            feasibility_analysis = {
                'technical_feasibility': {},
                'economic_feasibility': {},
                'timeline_feasibility': {},
                'resource_requirements': {}
            }
            
            for solution_name, solution in solutions.items():
                # 技术可行性
                feasibility_analysis['technical_feasibility'][solution_name] = {
                    'is_feasible': solution.is_feasible,
                    'constraint_violations': solution.constraint_violations,
                    'technical_readiness': 'high' if solution.is_feasible else 'medium'
                }
                
                # 经济可行性
                feasibility_analysis['economic_feasibility'][solution_name] = {
                    'cost_level': self._categorize_cost_level(solution.renovation_cost),
                    'cost_effectiveness': solution.renovation_cost / max(1, 150 - solution.energy_consumption),
                    'financing_difficulty': 'low' if solution.renovation_cost < 100000 else 'medium'
                }
                
                # 时间可行性
                feasibility_analysis['timeline_feasibility'][solution_name] = {
                    'estimated_duration': self._estimate_implementation_duration(solution),
                    'complexity_level': self._assess_implementation_complexity(solution)
                }
            
            return feasibility_analysis
            
        except Exception as e:
            self.logger.error(f"可行性分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _categorize_cost_level(self, cost: float) -> str:
        """成本等级分类"""
        if cost < 80000:
            return "低成本"
        elif cost < 150000:
            return "中等成本"
        else:
            return "高成本"
    
    def _estimate_implementation_duration(self, solution: ObjectiveResults) -> str:
        """估算实施周期"""
        if solution.renovation_cost < 100000:
            return "1-2个月"
        elif solution.renovation_cost < 200000:
            return "2-4个月"
        else:
            return "4-6个月"
    
    def _assess_implementation_complexity(self, solution: ObjectiveResults) -> str:
        """评估实施复杂度"""
        if not solution.is_feasible:
            return "高复杂度"
        elif solution.constraint_violations > 0.3:
            return "中等复杂度"
        else:
            return "低复杂度"
    
    def _analyze_economic_benefits(self, solutions: Dict[str, ObjectiveResults],
                                 performances: Dict[str, Any]) -> Dict[str, Any]:
        """分析经济效益"""
        try:
            economic_analysis = {
                'cost_benefit_analysis': {},
                'investment_returns': {},
                'lifecycle_economics': {}
            }
            
            for solution_name, solution in solutions.items():
                # 成本效益分析
                annual_energy_savings = max(0, 150 - solution.energy_consumption)
                annual_savings_value = annual_energy_savings * 0.6  # 假设电价0.6元/kWh
                
                economic_analysis['cost_benefit_analysis'][solution_name] = {
                    'initial_investment': solution.renovation_cost,
                    'annual_energy_savings': annual_energy_savings,
                    'annual_savings_value': annual_savings_value,
                    'benefit_cost_ratio': annual_savings_value * 10 / solution.renovation_cost if solution.renovation_cost > 0 else 0
                }
                
                # 投资回报分析
                if annual_savings_value > 0:
                    payback_period = solution.renovation_cost / annual_savings_value
                    roi_10_years = (annual_savings_value * 10 - solution.renovation_cost) / solution.renovation_cost * 100
                else:
                    payback_period = float('inf')
                    roi_10_years = -100
                
                economic_analysis['investment_returns'][solution_name] = {
                    'payback_period': min(50, payback_period),
                    'roi_10_years': roi_10_years,
                    'npv_10_years': self._calculate_npv(solution.renovation_cost, annual_savings_value, 10, 0.05)
                }
            
            return economic_analysis
            
        except Exception as e:
            self.logger.error(f"经济效益分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_npv(self, initial_cost: float, annual_savings: float, 
                      years: int, discount_rate: float) -> float:
        """计算净现值"""
        try:
            if discount_rate > 0:
                pv_factor = (1 - (1 + discount_rate) ** (-years)) / discount_rate
            else:
                pv_factor = years
            
            npv = annual_savings * pv_factor - initial_cost
            return npv
            
        except Exception:
            return 0.0
    
    def _generate_comprehensive_recommendations(self, solutions: Dict[str, ObjectiveResults],
                                             evaluation_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成综合推荐"""
        try:
            recommendations = {
                'priority_ranking': [],
                'scenario_recommendations': {},
                'implementation_strategy': {},
                'risk_mitigation': {}
            }
            
            # 根据综合评分排序
            solution_scores = []
            for solution_name in solutions.keys():
                if solution_name in evaluation_results['individual_evaluations']:
                    score = evaluation_results['individual_evaluations'][solution_name]['overall_score']
                    solution_scores.append((solution_name, score))
            
            solution_scores.sort(key=lambda x: x[1], reverse=True)
            recommendations['priority_ranking'] = solution_scores
            
            # 情境推荐
            recommendations['scenario_recommendations'] = {
                'budget_constrained': self._recommend_for_budget_constraint(solutions),
                'performance_focused': self._recommend_for_performance_focus(solutions),
                'balanced_approach': self._recommend_for_balanced_approach(solutions)
            }
            
            # 实施策略
            if solution_scores:
                best_solution_name = solution_scores[0][0]
                recommendations['implementation_strategy'] = {
                    'recommended_solution': best_solution_name,
                    'implementation_phases': self._suggest_implementation_phases(solutions[best_solution_name]),
                    'key_success_factors': self._identify_success_factors(solutions[best_solution_name])
                }
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成综合推荐失败: {str(e)}")
            return {'error': str(e)}
    
    def _recommend_for_budget_constraint(self, solutions: Dict[str, ObjectiveResults]) -> str:
        """预算约束情境推荐"""
        min_cost_solution = min(solutions.items(), key=lambda x: x[1].renovation_cost)
        return f"推荐{min_cost_solution[0]}方案，成本最低为{min_cost_solution[1].renovation_cost:.0f}元"
    
    def _recommend_for_performance_focus(self, solutions: Dict[str, ObjectiveResults]) -> str:
        """性能导向情境推荐"""
        best_energy_solution = min(solutions.items(), key=lambda x: x[1].energy_consumption)
        return f"推荐{best_energy_solution[0]}方案，能耗最低为{best_energy_solution[1].energy_consumption:.1f}kWh/m²"
    
    def _recommend_for_balanced_approach(self, solutions: Dict[str, ObjectiveResults]) -> str:
        """平衡方案推荐"""
        # 简化的平衡评分
        balanced_scores = {}
        for name, solution in solutions.items():
            energy_norm = 100 - solution.energy_consumption
            thermal_norm = 100 - solution.thermal_performance * 100
            cost_norm = 200000 - solution.renovation_cost
            balanced_score = energy_norm * 0.4 + thermal_norm * 0.3 + cost_norm * 0.3
            balanced_scores[name] = balanced_score
        
        best_balanced = max(balanced_scores.items(), key=lambda x: x[1])
        return f"推荐{best_balanced[0]}方案，综合平衡性最佳"
    
    def _suggest_implementation_phases(self, solution: ObjectiveResults) -> List[str]:
        """建议实施阶段"""
        phases = [
            "第一阶段：详细设计和方案确认",
            "第二阶段：材料采购和施工准备",
            "第三阶段：现场施工和安装",
            "第四阶段：调试和验收"
        ]
        
        if solution.renovation_cost > 150000:
            phases.insert(1, "第一阶段补充：资金筹措和审批")
        
        return phases
    
    def _identify_success_factors(self, solution: ObjectiveResults) -> List[str]:
        """识别关键成功因素"""
        factors = [
            "确保施工质量和工艺标准",
            "加强项目进度和成本控制",
            "做好与用户的沟通协调"
        ]
        
        if not solution.is_feasible:
            factors.append("重点关注技术方案的可行性验证")
        
        if solution.renovation_cost > 150000:
            factors.append("制定详细的资金使用和监管计划")
        
        return factors
    
    def generate_evaluation_report(self, evaluation_results: Dict[str, Any]) -> str:
        """生成评估报告"""
        try:
            report_sections = []
            
            # 报告标题
            report_sections.append("# 建筑立面改造解决方案综合评估报告")
            report_sections.append(f"生成时间：{evaluation_results.get('evaluation_timestamp', 'N/A')}")
            report_sections.append("")
            
            # 执行摘要
            report_sections.append("## 执行摘要")
            solutions_count = evaluation_results.get('solutions_count', 0)
            report_sections.append(f"本次评估共分析了{solutions_count}个解决方案。")
            
            # 推荐结果
            if 'recommendations' in evaluation_results and 'priority_ranking' in evaluation_results['recommendations']:
                ranking = evaluation_results['recommendations']['priority_ranking']
                if ranking:
                    best_solution = ranking[0]
                    report_sections.append(f"综合评估结果显示，{best_solution[0]}方案表现最优，综合评分为{best_solution[1]:.1f}分。")
            
            report_sections.append("")
            
            # 详细分析
            report_sections.append("## 详细分析结果")
            
            # 个体解决方案评估
            if 'individual_evaluations' in evaluation_results:
                report_sections.append("### 各解决方案评估结果")
                for solution_name, evaluation in evaluation_results['individual_evaluations'].items():
                    report_sections.append(f"**{solution_name}方案：**")
                    report_sections.append(f"- 综合评分：{evaluation.get('overall_score', 0):.1f}分")
                    report_sections.append(f"- 方案等级：{evaluation.get('solution_grade', 'N/A')}")
                    
                    strengths = evaluation.get('strengths', [])
                    if strengths:
                        report_sections.append("- 主要优势：" + "；".join(strengths))
                    
                    weaknesses = evaluation.get('weaknesses', [])
                    if weaknesses:
                        report_sections.append("- 改进建议：" + "；".join(weaknesses))
                    
                    report_sections.append("")
            
            # 风险评估
            if 'risk_assessment' in evaluation_results:
                report_sections.append("### 风险评估")
                risk_data = evaluation_results['risk_assessment']
                if 'overall_risk_levels' in risk_data:
                    for solution_name, risk_info in risk_data['overall_risk_levels'].items():
                        risk_level = risk_info.get('risk_level', 'N/A')
                        report_sections.append(f"- {solution_name}方案：{risk_level}")
                report_sections.append("")
            
            # 实施建议
            if 'recommendations' in evaluation_results:
                report_sections.append("### 实施建议")
                recommendations = evaluation_results['recommendations']
                
                if 'implementation_strategy' in recommendations:
                    strategy = recommendations['implementation_strategy']
                    recommended = strategy.get('recommended_solution', 'N/A')
                    report_sections.append(f"推荐优先实施：{recommended}方案")
                    
                    phases = strategy.get('implementation_phases', [])
                    if phases:
                        report_sections.append("建议实施阶段：")
                        for phase in phases:
                            report_sections.append(f"- {phase}")
            
            report_sections.append("")
            report_sections.append("---")
            report_sections.append("报告结束")
            
            return "\n".join(report_sections)
            
        except Exception as e:
            self.logger.error(f"生成评估报告失败: {str(e)}")
            return f"评估报告生成失败：{str(e)}"


def create_solution_evaluator() -> SolutionEvaluator:
    """
    创建解决方案评估器实例
    
    Returns:
        配置好的解决方案评估器
    """
    return SolutionEvaluator()