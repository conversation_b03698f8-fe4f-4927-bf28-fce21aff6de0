"""
报告生成器
生成专业的建筑立面优化分析报告
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import VisualizationError, handle_exception
from ..core.data_structures import VisualizationData, ObjectiveResults


class ReportGenerator:
    """
    报告生成器
    
    功能：
    1. 生成HTML格式分析报告
    2. 生成PDF格式报告
    3. 生成Markdown格式报告
    4. 图表集成和布局
    5. 多语言报告支持
    """
    
    def __init__(self):
        """初始化报告生成器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取报告配置
        viz_config = self.config.get_section('visualization')
        self.report_config = viz_config.get('report_generator', {})
        
        # 报告格式配置
        format_config = self.report_config.get('output_formats', {})
        self.enable_html = format_config.get('enable_html', True)
        self.enable_pdf = format_config.get('enable_pdf', False)
        self.enable_markdown = format_config.get('enable_markdown', True)
        
        # 报告样式配置
        style_config = self.report_config.get('report_styles', {})
        self.report_template = style_config.get('template', 'professional')
        self.include_charts = style_config.get('include_charts', True)
        self.include_data_tables = style_config.get('include_data_tables', True)
        
        # 输出配置 - 修复路径
        base_output_dir = self.config.get_output_directory()
        self.output_dir = os.path.join(base_output_dir, 'reports')
        
        # 创建输出目录
        Path(self.output_dir).mkdir(parents=True, exist_ok=True)
        
        self.logger.info("Report generator initialized successfully")
    
    def set_session_output_directory(self, session_dir: str) -> None:
        """设置会话输出目录"""
        try:
            self.session_output_dir = os.path.join(session_dir, 'reports')
            Path(self.session_output_dir).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Report generator session directory set: {self.session_output_dir}")
        except Exception as e:
            self.logger.error(f"Failed to set report generator session directory: {str(e)}")
            self.session_output_dir = self.output_dir
    
    @handle_exception
    def generate_comprehensive_report(self, 
                                    visualization_data: VisualizationData,
                                    selected_solutions: Dict[str, ObjectiveResults],
                                    chart_files: Dict[str, str],
                                    analysis_results: Dict[str, Any] = None) -> Dict[str, str]:
        """
        生成综合分析报告
        
        Args:
            visualization_data: 可视化数据
            selected_solutions: 选定的解决方案
            chart_files: 图表文件路径字典
            analysis_results: 分析结果
            
        Returns:
            生成的报告文件路径字典
            
        Raises:
            VisualizationError: 报告生成失败时抛出
        """
        with LogContext("Comprehensive analysis report generation", self.logger):
            try:
                generated_reports = {}
                
                # 准备报告数据
                report_data = self._prepare_report_data(
                    visualization_data, selected_solutions, analysis_results
                )
                
                # 生成时间戳
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                # 生成HTML报告
                if self.enable_html:
                    html_file = self._generate_html_report(
                        report_data, chart_files, timestamp
                    )
                    generated_reports['html'] = html_file
                
                # 生成Markdown报告
                if self.enable_markdown:
                    md_file = self._generate_markdown_report(
                        report_data, chart_files, timestamp
                    )
                    generated_reports['markdown'] = md_file
                
                # 生成PDF报告（如果启用）
                if self.enable_pdf:
                    try:
                        pdf_file = self._generate_pdf_report(
                            report_data, chart_files, timestamp
                        )
                        generated_reports['pdf'] = pdf_file
                    except Exception as e:
                        self.logger.warning(f"PDF report generation failed: {str(e)}")
                
                self.logger.info(f"Comprehensive analysis report generation completed, {len(generated_reports)} files generated")
                
                return generated_reports
                
            except Exception as e:
                raise VisualizationError(f"Comprehensive analysis report generation failed: {str(e)}") from e
    
    def _prepare_report_data(self, visualization_data: VisualizationData,
                           selected_solutions: Dict[str, ObjectiveResults],
                           analysis_results: Dict[str, Any] = None) -> Dict[str, Any]:
        """准备报告数据"""
        try:
            report_data = {
                'report_info': {
                    'title': 'Building Facade Optimization Analysis Report',
                    'title_en': 'Building Facade Optimization Analysis Report',
                    'generated_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'generated_time_en': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'version': '1.0'
                },
                'project_info': {
                    'name': 'Facade Optimization Project',
                    'location': 'TBD',
                    'area': getattr(visualization_data.facade_elements, 'total_area', 1000),
                    'orientation': getattr(visualization_data.climate_data, 'orientation', 'South').value if hasattr(visualization_data.climate_data, 'orientation') else 'South'
                },
                'optimization_summary': self._extract_optimization_summary(visualization_data),
                'solution_analysis': self._extract_solution_analysis(selected_solutions),
                'performance_metrics': self._extract_performance_metrics(visualization_data),
                'recommendations': self._generate_recommendations(selected_solutions, analysis_results)
            }
            
            return report_data
            
        except Exception as e:
            self.logger.error(f"Failed to prepare report data: {str(e)}")
            return {}
    
    def _extract_optimization_summary(self, visualization_data: VisualizationData) -> Dict[str, Any]:
        """提取优化概要信息"""
        try:
            optimization_results = visualization_data.optimization_results
            
            summary = {
                'total_solutions': len(optimization_results.pareto_solutions) if optimization_results.pareto_solutions else 0,
                'feasible_solutions': sum(1 for sol in optimization_results.pareto_solutions if sol.is_feasible) if optimization_results.pareto_solutions else 0,
                'optimization_objectives': ['Energy Minimization', 'Thermal Performance Optimization', 'Renovation Cost Control'],
                'constraint_types': ['Geometric Constraints', 'Building Codes', 'Structural Constraints', 'Functional Constraints', 'Aesthetic Constraints']
            }
            
            # 计算性能范围
            if optimization_results.pareto_solutions:
                energy_values = [sol.energy_consumption for sol in optimization_results.pareto_solutions]
                thermal_values = [sol.thermal_performance for sol in optimization_results.pareto_solutions]
                cost_values = [sol.renovation_cost for sol in optimization_results.pareto_solutions]
                
                summary.update({
                    'energy_range': {
                        'min': min(energy_values),
                        'max': max(energy_values),
                        'avg': sum(energy_values) / len(energy_values)
                    },
                    'thermal_range': {
                        'min': min(thermal_values),
                        'max': max(thermal_values),
                        'avg': sum(thermal_values) / len(thermal_values)
                    },
                    'cost_range': {
                        'min': min(cost_values),
                        'max': max(cost_values),
                        'avg': sum(cost_values) / len(cost_values)
                    }
                })
            
            return summary
            
        except Exception as e:
            self.logger.error(f"Failed to extract optimization summary: {str(e)}")
            return {}
    
    def _extract_solution_analysis(self, selected_solutions: Dict[str, ObjectiveResults]) -> Dict[str, Any]:
        """提取解决方案分析信息"""
        try:
            analysis = {
                'solution_count': len(selected_solutions),
                'solution_details': {},
                'performance_comparison': {}
            }
            
            # 解决方案详情
            for solution_name, solution in selected_solutions.items():
                analysis['solution_details'][solution_name] = {
                    'energy_consumption': solution.energy_consumption,
                    'thermal_performance': solution.thermal_performance,
                    'renovation_cost': solution.renovation_cost,
                    'is_feasible': solution.is_feasible,
                    'constraint_violations': solution.constraint_violations,
                    'comprehensive_score': self._calculate_comprehensive_score(solution)
                }
            
            # 性能对比
            if selected_solutions:
                energy_values = [sol.energy_consumption for sol in selected_solutions.values()]
                thermal_values = [sol.thermal_performance for sol in selected_solutions.values()]
                cost_values = [sol.renovation_cost for sol in selected_solutions.values()]
                
                analysis['performance_comparison'] = {
                    'best_energy': min(energy_values),
                    'worst_energy': max(energy_values),
                    'best_thermal': min(thermal_values),
                    'worst_thermal': max(thermal_values),
                    'lowest_cost': min(cost_values),
                    'highest_cost': max(cost_values)
                }
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Failed to extract solution analysis: {str(e)}")
            return {}
    
    def _extract_performance_metrics(self, visualization_data: VisualizationData) -> Dict[str, Any]:
        """提取性能指标信息"""
        try:
            metrics = {}
            
            # 能耗性能指标
            if visualization_data.energy_breakdown:
                energy = visualization_data.energy_breakdown
                metrics['energy_metrics'] = {
                    'total_energy': energy.total_energy,
                    'heating_energy': energy.heating_energy,
                    'cooling_energy': energy.cooling_energy,
                    'lighting_energy': energy.lighting_energy,
                    'ventilation_energy': energy.ventilation_energy,
                    'energy_intensity': energy.total_energy,  # kWh/m²/year
                    'energy_grade': self._classify_energy_performance(energy.total_energy)
                }
            
            # 热工性能指标
            if visualization_data.thermal_metrics:
                thermal = visualization_data.thermal_metrics
                metrics['thermal_metrics'] = {
                    'thermal_transmittance': thermal.thermal_transmittance,
                    'thermal_bridge_effect': thermal.thermal_bridge_effect,
                    'thermal_inertia': thermal.thermal_inertia,
                    'comfort_hours': thermal.comfort_hours,
                    'comfort_percentage': thermal.comfort_hours / 8760 * 100,
                    'thermal_grade': self._classify_thermal_performance(thermal.comfort_hours)
                }
            
            # 成本指标
            if visualization_data.cost_breakdown:
                cost = visualization_data.cost_breakdown
                metrics['cost_metrics'] = {
                    'total_initial_cost': cost.total_initial_cost,
                    'material_cost': cost.material_cost,
                    'labor_cost': cost.labor_cost,
                    'equipment_cost': cost.equipment_cost,
                    'annual_maintenance': cost.maintenance_cost,
                    'lifecycle_cost': cost.lifecycle_cost,
                    'cost_per_area': cost.cost_per_area,
                    'cost_grade': self._classify_cost_performance(cost.cost_per_area)
                }
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to extract performance metrics: {str(e)}")
            return {}
    
    def _generate_recommendations(self, selected_solutions: Dict[str, ObjectiveResults],
                                analysis_results: Dict[str, Any] = None) -> List[str]:
        """生成建议"""
        try:
            recommendations = []
            
            if not selected_solutions:
                return ["No available solution data, unable to generate recommendations."]
            
            # 找出最佳解决方案
            best_solution_name = None
            best_score = -1
            
            for name, solution in selected_solutions.items():
                score = self._calculate_comprehensive_score(solution)
                if score > best_score:
                    best_score = score
                    best_solution_name = name
            
            if best_solution_name:
                recommendations.append(f"Recommend adopting '{best_solution_name}' solution, which performs best in comprehensive performance evaluation.")
            
            # 基于性能特点的建议
            energy_solutions = [(name, sol.energy_consumption) for name, sol in selected_solutions.items()]
            energy_solutions.sort(key=lambda x: x[1])
            
            if energy_solutions:
                best_energy_solution = energy_solutions[0]
                if best_energy_solution[1] < 80:
                    recommendations.append(f"'{best_energy_solution[0]}' solution performs excellently in energy saving, with annual energy consumption of only {best_energy_solution[1]:.1f} kWh/m².")
                elif best_energy_solution[1] > 120:
                    recommendations.append("It is recommended to further optimize the window-to-wall ratio and window performance to improve building energy consumption performance.")
            
            # 成本效益建议
            cost_solutions = [(name, sol.renovation_cost) for name, sol in selected_solutions.items()]
            cost_solutions.sort(key=lambda x: x[1])
            
            if cost_solutions:
                lowest_cost = cost_solutions[0]
                highest_cost = cost_solutions[-1]
                
                if highest_cost[1] / lowest_cost[1] > 2:
                    recommendations.append("The cost differences between solutions are significant, it is recommended to make trade-off choices based on project budget and performance requirements.")
                
                if lowest_cost[1] < 100000:
                    recommendations.append(f"'{lowest_cost[0]}' solution has lower cost, suitable for projects with limited budget.")
            
            # Implementation recommendations
            recommendations.extend([
                "It is recommended to conduct detailed technical feasibility analysis and cost accounting before implementation.",
                "It is recommended to implement renovation solutions in phases to reduce construction risks and financial pressure.",
                "It is recommended to conduct performance monitoring after renovation completion to verify actual effects."
            ])
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Failed to generate recommendations: {str(e)}")
            return ["Recommendation generation failed, please check data integrity."]
    
    def _calculate_comprehensive_score(self, solution: ObjectiveResults) -> float:
        """计算综合评分"""
        try:
            # 简化的综合评分算法
            energy_score = max(0, min(100, 100 - solution.energy_consumption))
            thermal_score = max(0, min(100, (1 - solution.thermal_performance) * 100))
            cost_score = max(0, min(100, 100 - solution.renovation_cost / 2000))
            
            # 可行性惩罚
            feasibility_penalty = 0 if solution.is_feasible else 20
            constraint_penalty = solution.constraint_violations * 10
            
            comprehensive_score = (energy_score + thermal_score + cost_score) / 3 - feasibility_penalty - constraint_penalty
            
            return max(0, comprehensive_score)
            
        except Exception:
            return 50.0
    
    def _classify_energy_performance(self, energy_consumption: float) -> str:
        """分类能耗性能等级"""
        if energy_consumption < 50:
            return "Excellent (A+)"
        elif energy_consumption < 80:
            return "Good (A)"
        elif energy_consumption < 120:
            return "Average (B)"
        elif energy_consumption < 160:
            return "Acceptable (C)"
        else:
            return "Needs Improvement (D)"
    
    def _classify_thermal_performance(self, comfort_hours: float) -> str:
        """分类热工性能等级"""
        comfort_percentage = comfort_hours / 8760 * 100
        
        if comfort_percentage >= 90:
            return "Excellent (A+)"
        elif comfort_percentage >= 80:
            return "Good (A)"
        elif comfort_percentage >= 70:
            return "Average (B)"
        elif comfort_percentage >= 60:
            return "Acceptable (C)"
        else:
            return "Needs Improvement (D)"
    
    def _classify_cost_performance(self, cost_per_area: float) -> str:
        """分类成本性能等级"""
        if cost_per_area < 800:
            return "Economic"
        elif cost_per_area < 1200:
            return "Standard"
        elif cost_per_area < 1800:
            return "Premium"
        else:
            return "Luxury"
    
    def _generate_html_report(self, report_data: Dict[str, Any], 
                            chart_files: Dict[str, str], timestamp: str) -> str:
        """生成HTML报告"""
        try:
            filename = f"facade_optimization_report_{timestamp}.html"
            filepath = os.path.join(self.output_dir, filename)
            
            # HTML模板
            html_content = self._build_html_template(report_data, chart_files)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            self.logger.info(f"HTML report generated: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to generate HTML report: {str(e)}")
            return ""
    
    def _build_html_template(self, report_data: Dict[str, Any], 
                           chart_files: Dict[str, str]) -> str:
        """构建HTML模板"""
        try:
            html = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{report_data['report_info']['title']}</title>
    <style>
        {self._get_html_styles()}
    </style>
</head>
<body>
    <div class="container">
        <header class="report-header">
            <h1>{report_data['report_info']['title']}</h1>
            <h2>{report_data['report_info']['title_en']}</h2>
            <p class="generated-time">Generated Time: {report_data['report_info']['generated_time']}</p>
        </header>
        
        <section class="executive-summary">
            <h2>Executive Summary</h2>
            {self._build_executive_summary_html(report_data)}
        </section>
        
        <section class="project-info">
            <h2>Project Information</h2>
            {self._build_project_info_html(report_data['project_info'])}
        </section>
        
        <section class="optimization-results">
            <h2>Optimization Results</h2>
            {self._build_optimization_results_html(report_data['optimization_summary'])}
        </section>
        
        <section class="solution-analysis">
            <h2>Solution Analysis</h2>
            {self._build_solution_analysis_html(report_data['solution_analysis'])}
        </section>
        
        <section class="performance-metrics">
            <h2>Performance Metrics</h2>
            {self._build_performance_metrics_html(report_data['performance_metrics'])}
        </section>
        
        {self._build_charts_section_html(chart_files) if self.include_charts else ''}
        
        <section class="recommendations">
            <h2>Recommendations & Conclusions</h2>
            {self._build_recommendations_html(report_data['recommendations'])}
        </section>
        
        <footer class="report-footer">
            <p>This report is automatically generated by Building Facade Optimization System</p>
            <p>Generated by Building Facade Optimization System</p>
        </footer>
    </div>
</body>
</html>
"""
            return html
            
        except Exception as e:
            self.logger.error(f"Failed to build HTML template: {str(e)}")
            return "<html><body><h1>Report Generation Failed</h1></body></html>"
    
    def _get_html_styles(self) -> str:
        """获取HTML样式"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, 'DejaVu Sans', 'Liberation Sans', Helvetica, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .report-header {
            text-align: center;
            border-bottom: 3px solid #2E86AB;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .report-header h1 {
            color: #2E86AB;
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .report-header h2 {
            color: #666;
            font-size: 1.5em;
            font-weight: normal;
            margin-bottom: 10px;
        }
        
        .generated-time {
            color: #888;
            font-size: 0.9em;
        }
        
        section {
            margin-bottom: 40px;
        }
        
        section h2 {
            color: #2E86AB;
            font-size: 1.8em;
            border-left: 4px solid #2E86AB;
            padding-left: 15px;
            margin-bottom: 20px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .info-card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #2E86AB;
        }
        
        .info-card h3 {
            color: #2E86AB;
            margin-bottom: 10px;
        }
        
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #2E86AB;
        }
        
        .chart-container {
            text-align: center;
            margin: 20px 0;
        }
        
        .chart-container img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .recommendations ul {
            list-style-type: none;
        }
        
        .recommendations li {
            background-color: #e8f4f8;
            margin: 10px 0;
            padding: 15px;
            border-left: 4px solid #2E86AB;
            border-radius: 4px;
        }
        
        .recommendations li:before {
            content: "💡 ";
            margin-right: 10px;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .data-table th,
        .data-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .data-table th {
            background-color: #2E86AB;
            color: white;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        
        .report-footer {
            text-align: center;
            padding-top: 20px;
            border-top: 2px solid #2E86AB;
            color: #666;
            font-size: 0.9em;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .report-header h1 {
                font-size: 2em;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
        """
    
    def _build_executive_summary_html(self, report_data: Dict[str, Any]) -> str:
        """构建执行摘要HTML"""
        optimization_summary = report_data.get('optimization_summary', {})
        solution_analysis = report_data.get('solution_analysis', {})
        
        total_solutions = optimization_summary.get('total_solutions', 0)
        feasible_solutions = optimization_summary.get('feasible_solutions', 0)
        solution_count = solution_analysis.get('solution_count', 0)
        
        return f"""
        <div class="info-grid">
            <div class="info-card">
                <h3>Optimization Overview</h3>
                <p>This optimization analysis generated a total of <span class="metric-value">{total_solutions}</span> solutions,
                of which <span class="metric-value">{feasible_solutions}</span> meet the constraint conditions.
                Finally, <span class="metric-value">{solution_count}</span> excellent solutions were selected for detailed analysis.</p>
            </div>
            <div class="info-card">
                <h3>Optimization Objectives</h3>
                <ul>
                    <li>✓ Building Energy Minimization</li>
                    <li>✓ Thermal Performance Optimization</li>
                    <li>✓ Renovation Cost Control</li>
                </ul>
            </div>
        </div>
        """
    
    def _build_project_info_html(self, project_info: Dict[str, Any]) -> str:
        """构建项目信息HTML"""
        return f"""
        <div class="info-grid">
            <div class="info-card">
                <h3>Project Name</h3>
                <p>{project_info.get('name', 'Not Specified')}</p>
            </div>
            <div class="info-card">
                <h3>Building Area</h3>
                <p><span class="metric-value">{project_info.get('area', 0):.1f}</span> m²</p>
            </div>
            <div class="info-card">
                <h3>Building Orientation</h3>
                <p>{project_info.get('orientation', 'Not Specified')}</p>
            </div>
            <div class="info-card">
                <h3>Project Location</h3>
                <p>{project_info.get('location', 'TBD')}</p>
            </div>
        </div>
        """
    
    def _build_optimization_results_html(self, optimization_summary: Dict[str, Any]) -> str:
        """构建优化结果HTML"""
        if not optimization_summary:
            return "<p>No optimization results data available</p>"
        
        energy_range = optimization_summary.get('energy_range', {})
        thermal_range = optimization_summary.get('thermal_range', {})
        cost_range = optimization_summary.get('cost_range', {})
        
        return f"""
        <div class="info-grid">
            <div class="info-card">
                <h3>Energy Performance Range</h3>
                <p>Minimum: <span class="metric-value">{energy_range.get('min', 0):.1f}</span> kWh/m²/year</p>
                <p>Maximum: <span class="metric-value">{energy_range.get('max', 0):.1f}</span> kWh/m²/year</p>
                <p>Average: <span class="metric-value">{energy_range.get('avg', 0):.1f}</span> kWh/m²/year</p>
            </div>
            <div class="info-card">
                <h3>Thermal Performance Range</h3>
                <p>Best: <span class="metric-value">{thermal_range.get('min', 0):.3f}</span></p>
                <p>Worst: <span class="metric-value">{thermal_range.get('max', 0):.3f}</span></p>
                <p>Average: <span class="metric-value">{thermal_range.get('avg', 0):.3f}</span></p>
            </div>
            <div class="info-card">
                <h3>Renovation Cost Range</h3>
                <p>Minimum: <span class="metric-value">{cost_range.get('min', 0):,.0f}</span> CNY</p>
                <p>Maximum: <span class="metric-value">{cost_range.get('max', 0):,.0f}</span> CNY</p>
                <p>Average: <span class="metric-value">{cost_range.get('avg', 0):,.0f}</span> CNY</p>
            </div>
        </div>
        """
    
    def _build_solution_analysis_html(self, solution_analysis: Dict[str, Any]) -> str:
        """构建解决方案分析HTML"""
        if not solution_analysis or 'solution_details' not in solution_analysis:
            return "<p>No solution analysis data available</p>"
        
        solution_details = solution_analysis['solution_details']
        
        # 构建解决方案对比表
        table_html = """
        <table class="data-table">
            <thead>
                <tr>
                    <th>Solution Name</th>
                    <th>Energy (kWh/m²/year)</th>
                    <th>Thermal Performance</th>
                    <th>Renovation Cost (CNY)</th>
                    <th>Comprehensive Score</th>
                    <th>Feasibility</th>
                </tr>
            </thead>
            <tbody>
        """
        
        for solution_name, details in solution_details.items():
            feasible_text = "✓ Feasible" if details.get('is_feasible', False) else "✗ Infeasible"
            table_html += f"""
                <tr>
                    <td>{solution_name}</td>
                    <td>{details.get('energy_consumption', 0):.1f}</td>
                    <td>{details.get('thermal_performance', 0):.3f}</td>
                    <td>{details.get('renovation_cost', 0):,.0f}</td>
                    <td>{details.get('comprehensive_score', 0):.1f}</td>
                    <td>{feasible_text}</td>
                </tr>
            """
        
        table_html += "</tbody></table>"
        
        return table_html
    
    def _build_performance_metrics_html(self, performance_metrics: Dict[str, Any]) -> str:
        """构建性能指标HTML"""
        if not performance_metrics:
            return "<p>No performance metrics data available</p>"
        
        html_sections = []
        
        # 能耗指标
        if 'energy_metrics' in performance_metrics:
            energy = performance_metrics['energy_metrics']
            html_sections.append(f"""
            <div class="info-card">
                <h3>Energy Performance Metrics</h3>
                <p>Total Energy: <span class="metric-value">{energy.get('total_energy', 0):.1f}</span> kWh/m²/year</p>
                <p>Heating Energy: {energy.get('heating_energy', 0):.1f} kWh/m²/year</p>
                <p>Cooling Energy: {energy.get('cooling_energy', 0):.1f} kWh/m²/year</p>
                <p>Lighting Energy: {energy.get('lighting_energy', 0):.1f} kWh/m²/year</p>
                <p>Performance Grade: <span class="metric-value">{energy.get('energy_grade', 'Not Rated')}</span></p>
            </div>
            """)
        
        # 热工指标
        if 'thermal_metrics' in performance_metrics:
            thermal = performance_metrics['thermal_metrics']
            html_sections.append(f"""
            <div class="info-card">
                <h3>Thermal Performance Metrics</h3>
                <p>Thermal Transmittance: <span class="metric-value">{thermal.get('thermal_transmittance', 0):.3f}</span> W/m²K</p>
                <p>Comfort Hours: <span class="metric-value">{thermal.get('comfort_hours', 0):.0f}</span> hours</p>
                <p>Comfort Rate: <span class="metric-value">{thermal.get('comfort_percentage', 0):.1f}%</span></p>
                <p>Performance Grade: <span class="metric-value">{thermal.get('thermal_grade', 'Not Rated')}</span></p>
            </div>
            """)
        
        # 成本指标
        if 'cost_metrics' in performance_metrics:
            cost = performance_metrics['cost_metrics']
            html_sections.append(f"""
            <div class="info-card">
                <h3>Cost Metrics</h3>
                <p>Total Investment: <span class="metric-value">{cost.get('total_initial_cost', 0):,.0f}</span> CNY</p>
                <p>Unit Cost: <span class="metric-value">{cost.get('cost_per_area', 0):.0f}</span> CNY/m²</p>
                <p>Annual Maintenance: {cost.get('annual_maintenance', 0):,.0f} CNY</p>
                <p>Lifecycle Cost: {cost.get('lifecycle_cost', 0):,.0f} CNY</p>
                <p>Cost Grade: <span class="metric-value">{cost.get('cost_grade', 'Not Rated')}</span></p>
            </div>
            """)
        
        return f'<div class="info-grid">{"".join(html_sections)}</div>'
    
    def _build_charts_section_html(self, chart_files: Dict[str, str]) -> str:
        """构建图表部分HTML"""
        if not chart_files:
            return ""
        
        charts_html = '<section class="charts"><h2>Analysis Charts</h2>'
        
        chart_titles = {
            'pareto_frontier': 'Pareto Frontier Analysis',
            'optimization_convergence': 'Optimization Convergence Process',
            'energy_performance': 'Energy Performance Analysis',
            'thermal_performance': 'Thermal Performance Analysis',
            'cost_analysis': 'Cost Analysis',
            'multi_solution_comparison': 'Multi-Solution Comparison',
            'comprehensive_radar': 'Comprehensive Performance Radar Chart'
        }
        
        for chart_key, chart_path in chart_files.items():
            if chart_path and os.path.exists(chart_path):
                title = chart_titles.get(chart_key, chart_key)
                charts_html += f"""
                <div class="chart-container">
                    <h3>{title}</h3>
                    <img src="{chart_path}" alt="{title}">
                </div>
                """
        
        charts_html += '</section>'
        return charts_html
    
    def _build_recommendations_html(self, recommendations: List[str]) -> str:
        """构建建议HTML"""
        if not recommendations:
            return "<p>No recommendations available</p>"
        
        recommendations_html = "<ul>"
        for recommendation in recommendations:
            recommendations_html += f"<li>{recommendation}</li>"
        recommendations_html += "</ul>"
        
        return recommendations_html
    
    def _generate_markdown_report(self, report_data: Dict[str, Any],
                                chart_files: Dict[str, str], timestamp: str) -> str:
        """生成Markdown报告"""
        try:
            filename = f"facade_optimization_report_{timestamp}.md"
            filepath = os.path.join(self.output_dir, filename)
            
            # Markdown内容
            md_content = self._build_markdown_content(report_data, chart_files)
            
            # 写入文件
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(md_content)
            
            self.logger.info(f"Markdown report generated: {filepath}")
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to generate Markdown report: {str(e)}")
            return ""
    
    def _build_markdown_content(self, report_data: Dict[str, Any],
                              chart_files: Dict[str, str]) -> str:
        """构建Markdown内容"""
        try:
            md_content = f"""# {report_data['report_info']['title']}

**Generated Time**: {report_data['report_info']['generated_time']}
**Version**: {report_data['report_info']['version']}

---

## Executive Summary

This building facade optimization analysis conducted comprehensive performance evaluation and solution optimization for {report_data['project_info']['name']}.

### Key Findings

- Generated **{report_data['optimization_summary'].get('total_solutions', 0)}** optimization solutions
- Selected **{report_data['solution_analysis'].get('solution_count', 0)}** recommended solutions
- Optimization objectives: Energy minimization, thermal performance improvement, cost control

---

## Project Information

| Project Attribute | Value |
|----------|----| 
| Project Name | {report_data['project_info'].get('name', 'Not Specified')} |
| Building Area | {report_data['project_info'].get('area', 0):.1f} m² |
| Building Orientation | {report_data['project_info'].get('orientation', 'Not Specified')} |
| Project Location | {report_data['project_info'].get('location', 'TBD')} |

---

## Optimization Results Analysis

### Performance Range Statistics

"""

            # 添加性能范围信息
            if 'optimization_summary' in report_data:
                summary = report_data['optimization_summary']
                if 'energy_range' in summary:
                    energy = summary['energy_range']
                    md_content += f"""
#### Energy Performance
- Best: {energy.get('min', 0):.1f} kWh/m²/year
- Worst: {energy.get('max', 0):.1f} kWh/m²/year  
- Average: {energy.get('avg', 0):.1f} kWh/m²/year
"""

                if 'cost_range' in summary:
                    cost = summary['cost_range']
                    md_content += f"""
#### Renovation Cost
- Minimum: {cost.get('min', 0):,.0f} CNY
- Maximum: {cost.get('max', 0):,.0f} CNY
- Average: {cost.get('avg', 0):,.0f} CNY
"""

            # 添加解决方案对比
            if 'solution_analysis' in report_data and 'solution_details' in report_data['solution_analysis']:
                md_content += """
---

## Recommended Solution Comparison

| Solution Name | Energy (kWh/m²/year) | Thermal Performance | Renovation Cost (CNY) | Comprehensive Score | Feasibility |
|----------|------------------|----------|---------------|----------|--------|
"""
                
                solution_details = report_data['solution_analysis']['solution_details']
                for solution_name, details in solution_details.items():
                    feasible = "✓" if details.get('is_feasible', False) else "✗"
                    md_content += f"| {solution_name} | {details.get('energy_consumption', 0):.1f} | {details.get('thermal_performance', 0):.3f} | {details.get('renovation_cost', 0):,.0f} | {details.get('comprehensive_score', 0):.1f} | {feasible} |\n"

            # 添加图表
            if self.include_charts and chart_files:
                md_content += "\n---\n\n## Analysis Charts\n\n"
                
                chart_titles = {
                    'pareto_frontier': 'Pareto Frontier Analysis',
                    'optimization_convergence': 'Optimization Convergence Process',
                    'energy_performance': 'Energy Performance Analysis',
                    'thermal_performance': 'Thermal Performance Analysis',
                    'cost_analysis': 'Cost Analysis',
                    'multi_solution_comparison': 'Multi-Solution Comparison'
                }
                
                for chart_key, chart_path in chart_files.items():
                    if chart_path and os.path.exists(chart_path):
                        title = chart_titles.get(chart_key, chart_key)
                        md_content += f"### {title}\n\n![{title}]({chart_path})\n\n"

            # 添加建议
            md_content += "\n---\n\n## Recommendations & Conclusions\n\n"
            
            if 'recommendations' in report_data:
                for i, recommendation in enumerate(report_data['recommendations'], 1):
                    md_content += f"{i}. {recommendation}\n\n"

            md_content += "\n---\n\n*This report is automatically generated by Building Facade Optimization System*"

            return md_content
            
        except Exception as e:
            self.logger.error(f"Failed to build Markdown content: {str(e)}")
            return "# Report Generation Failed\n\nPlease check data integrity."
    
    def _generate_pdf_report(self, report_data: Dict[str, Any],
                           chart_files: Dict[str, str], timestamp: str) -> str:
        """生成PDF报告"""
        try:
            # PDF生成需要额外的库支持，这里提供基本框架
            filename = f"facade_optimization_report_{timestamp}.pdf"
            filepath = os.path.join(self.output_dir, filename)
            
            # 简化实现：先生成HTML，再转换为PDF
            html_content = self._build_html_template(report_data, chart_files)
            
            # 注意: 实际PDF生成需要安装如 weasyprint 或 reportlab 等库
            # 这里仅作为占位符实现
            self.logger.warning("PDF generation requires additional library support, currently only generating HTML version")
            
            return filepath
            
        except Exception as e:
            self.logger.error(f"Failed to generate PDF report: {str(e)}")
            return ""


def create_report_generator() -> ReportGenerator:
    """
    创建报告生成器实例
    
    Returns:
        配置好的报告生成器
    """
    return ReportGenerator()