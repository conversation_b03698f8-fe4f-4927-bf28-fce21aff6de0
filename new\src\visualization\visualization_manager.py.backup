"""
可视化管理器
统一管理和协调所有可视化功能
"""

import os
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import VisualizationError, handle_exception
from ..core.data_structures import VisualizationData, ObjectiveResults

from .chart_config import ChartConfig, create_chart_config
from .optimization_charts import OptimizationCharts, create_optimization_charts
from .performance_charts import PerformanceCharts, create_performance_charts
from .comparison_charts import ComparisonCharts, create_comparison_charts
from .report_generator import ReportGenerator, create_report_generator



def setup_chinese_fonts():
    """设置中文字体"""
    import matplotlib.pyplot as plt
    import matplotlib
    fonts = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei', 'WenQuanYi Micro Hei']
    matplotlib.rcParams['font.sans-serif'] = fonts
    matplotlib.rcParams['axes.unicode_minus'] = False
    plt.rcParams['font.sans-serif'] = fonts
    plt.rcParams['axes.unicode_minus'] = False

def create_chinese_legend(ax, labels, **kwargs):
    """创建中文图例"""
    setup_chinese_fonts()
    return ax.legend(labels, **kwargs)

def set_chinese_labels(ax, xlabel=None, ylabel=None, title=None):
    """设置中文标签"""
    setup_chinese_fonts()
    if xlabel:
        ax.set_xlabel(xlabel, fontproperties='SimHei')
    if ylabel:
        ax.set_ylabel(ylabel, fontproperties='SimHei')
    if title:
        ax.set_title(title, fontproperties='SimHei')


class VisualizationManager:
    """
    可视化管理器
    
    功能：
    1. 统一管理所有可视化组件
    2. 协调图表生成流程
    3. 管理可视化配置
    4. 生成完整的可视化报告
    5. 提供可视化API接口
    """
    
    def __init__(self):
        """初始化可视化管理器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取可视化管理配置
        viz_config = self.config.get_section('visualization')
        self.manager_config = viz_config.get('visualization_manager', {})
        
        # 初始化所有可视化组件
        self.chart_config = create_chart_config()
        self.optimization_charts = create_optimization_charts()
        self.performance_charts = create_performance_charts()
        self.comparison_charts = create_comparison_charts()
        self.report_generator = create_report_generator()
        
        # 可视化任务配置
        task_config = self.manager_config.get('task_configuration', {})
        self.auto_generate_all_charts = task_config.get('auto_generate_all_charts', True)
        self.parallel_chart_generation = task_config.get('parallel_chart_generation', False)
        self.cache_generated_charts = task_config.get('cache_generated_charts', True)
        
        # 输出管理配置
        output_config = self.manager_config.get('output_management', {})
        self.organize_outputs = output_config.get('organize_by_timestamp', True)
        self.cleanup_old_outputs = output_config.get('cleanup_old_outputs', False)
        self.max_output_age_days = output_config.get('max_output_age_days', 7)
        
        # 生成的图表缓存
        self.generated_charts = {}
        self.current_session_id = None
        
        self.logger.info("Visualization manager initialized successfully")
    
    def set_session_output_directory(self, session_dir: str) -> None:
        """设置会话输出目录 - 新的简化结构"""
        try:
            # 创建主要目录结构
            charts_dir = os.path.join(session_dir, 'charts')
            pdf_dir = os.path.join(session_dir, 'pdf')
            svg_dir = os.path.join(session_dir, 'svg')
            reports_dir = os.path.join(session_dir, 'reports')
            
            # 确保目录存在
            for directory in [charts_dir, pdf_dir, svg_dir, reports_dir]:
                os.makedirs(directory, exist_ok=True)
            
            # 为所有组件设置会话目录
            if hasattr(self.optimization_charts, 'set_session_output_directory'):
                self.optimization_charts.set_session_output_directory(session_dir, 'charts')
            if hasattr(self.performance_charts, 'set_session_output_directory'):
                self.performance_charts.set_session_output_directory(session_dir, 'charts')
            if hasattr(self.comparison_charts, 'set_session_output_directory'):
                self.comparison_charts.set_session_output_directory(session_dir, 'charts')
            if hasattr(self.report_generator, 'set_session_output_directory'):
                self.report_generator.set_session_output_directory(session_dir)
            
            # 保存目录信息供新增图表使用
            self.session_directories = {
                'session_root': session_dir,
                'charts': charts_dir,
                'pdf': pdf_dir,
                'svg': svg_dir,
                'reports': reports_dir
            }
            
            self.logger.info(f"Session directory structure created: {session_dir}")
        except Exception as e:
            self.logger.error(f"Failed to set session directory: {str(e)}")
    
    @handle_exception
    def generate_complete_visualization_suite(self, 
                                            visualization_data: VisualizationData,
                                            selected_solutions: Dict[str, ObjectiveResults] = None,
                                            custom_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        生成完整的可视化套件
        
        Args:
            visualization_data: 可视化数据
            selected_solutions: 选定的解决方案
            custom_options: 自定义选项
            
        Returns:
            可视化结果字典，包含所有图表路径和报告路径
            
        Raises:
            VisualizationError: 可视化生成失败时抛出
        """
        with LogContext("Complete visualization suite generation", self.logger):
            try:
                # 创建会话ID
                self.current_session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
                
                # 获取会话目录 - 优先使用custom_options中的session_dir
                session_dir = None
                if custom_options and 'session_dir' in custom_options:
                    session_dir = custom_options['session_dir']
                
                if session_dir:
                    self.set_session_output_directory(session_dir)
                
                self.logger.info(f"Starting complete visualization suite generation, session ID: {self.current_session_id}")
                
                # 初始化结果字典
                visualization_results = {
                    'session_id': self.current_session_id,
                    'generated_time': datetime.now().isoformat(),
                    'charts': {},
                    'reports': {},
                    'metadata': {
                        'total_charts': 0,
                        'successful_charts': 0,
                        'failed_charts': 0,
                        'generation_time': 0
                    }
                }
                
                start_time = datetime.now()
                
                # 验证输入数据
                self._validate_visualization_data(visualization_data)
                
                # 处理自定义选项
                options = self._process_custom_options(custom_options)
                
                # 生成新增的可视化图表
                try:
                    # 所有解的排列展示
                    solutions_grid = self._create_solutions_grid_visualization(
                        visualization_data.optimization_results,
                        visualization_data.facade_elements,
                        selected_solutions
                    )
                    visualization_results['charts']['solutions_grid'] = solutions_grid
                    
                    # 原始与最佳方案对比
                    comparison_chart = self._create_facade_comparison_visualization(
                        visualization_data.facade_elements,
                        visualization_data.optimization_results,
                        selected_solutions
                    )
                    visualization_results['charts']['facade_comparison'] = comparison_chart
                    
                    # 3D轴测图
                    isometric_3d = self._create_3d_isometric_visualization(
                        visualization_data.facade_elements,
                        selected_solutions or {}
                    )
                    visualization_results['charts']['isometric_3d'] = isometric_3d
                    
                    visualization_results['metadata']['successful_charts'] += 3
                    self.logger.info("Additional visualization charts generation completed")
                    
                except Exception as e:
                    self.logger.error(f"Additional visualization charts generation failed: {str(e)}")
                    visualization_results['metadata']['failed_charts'] += 3
                
                # 1. 生成优化结果图表（3类）
                if options.get('generate_optimization_charts', True):
                    optimization_charts = self._generate_optimization_charts(
                        visualization_data, selected_solutions
                    )
                    visualization_results['charts'].update(optimization_charts)
                
                # 2. 生成性能分析图表（4类）
                if options.get('generate_performance_charts', True):
                    performance_charts = self._generate_performance_charts(
                        visualization_data
                    )
                    visualization_results['charts'].update(performance_charts)
                
                # 3. 生成对比分析图表（4类）
                if options.get('generate_comparison_charts', True) and selected_solutions:
                    comparison_charts = self._generate_comparison_charts(
                        selected_solutions, visualization_data
                    )
                    visualization_results['charts'].update(comparison_charts)
                
                # 4. 生成综合报告
                if options.get('generate_reports', True):
                    reports = self._generate_comprehensive_reports(
                        visualization_data, selected_solutions, 
                        visualization_results['charts']
                    )
                    visualization_results['reports'].update(reports)
                
                # 计算生成统计
                end_time = datetime.now()
                generation_time = (end_time - start_time).total_seconds()
                
                total_charts = len(visualization_results['charts'])
                successful_charts = sum(1 for path in visualization_results['charts'].values() if path and os.path.exists(path))
                failed_charts = total_charts - successful_charts
                
                visualization_results['metadata'].update({
                    'total_charts': total_charts,
                    'successful_charts': successful_charts,
                    'failed_charts': failed_charts,
                    'generation_time': generation_time
                })
                
                # 输出组织和清理
                if self.organize_outputs:
                    self._organize_output_files(visualization_results)
                
                if self.cleanup_old_outputs:
                    self._cleanup_old_outputs()
                
                self.logger.info(f"Complete visualization suite generation completed: {successful_charts}/{total_charts} charts successful, time elapsed {generation_time:.2f}s")
                
                return visualization_results
                
            except Exception as e:
                raise VisualizationError(f"Complete visualization suite generation failed: {str(e)}") from e
    
    def _validate_visualization_data(self, visualization_data: VisualizationData):
        """验证可视化数据"""
        try:
            if not visualization_data:
                raise VisualizationError("Visualization data is empty")
            
            # 检查必需的数据组件
            required_components = ['facade_elements', 'climate_data']
            missing_components = []
            
            for component in required_components:
                if not hasattr(visualization_data, component) or getattr(visualization_data, component) is None:
                    missing_components.append(component)
            
            if missing_components:
                self.logger.warning(f"Missing visualization data components: {missing_components}")
            
            self.logger.info("Visualization data validation passed")
            
        except Exception as e:
            raise VisualizationError(f"Visualization data validation failed: {str(e)}")
    
    def _process_custom_options(self, custom_options: Dict[str, Any] = None) -> Dict[str, Any]:
        """处理自定义选项"""
        try:
            # 默认选项
            default_options = {
                'generate_optimization_charts': True,
                'generate_performance_charts': True,
                'generate_comparison_charts': True,
                'generate_reports': True,
                'chart_formats': ['png'],
                'report_formats': ['html', 'markdown'],
                'high_quality_mode': True,
                'include_statistical_analysis': True
            }
            
            # 合并自定义选项
            if custom_options:
                default_options.update(custom_options)
            
            self.logger.info(f"Visualization options configuration completed: {list(default_options.keys())}")
            
            return default_options
            
        except Exception as e:
            self.logger.error(f"Failed to process custom options: {str(e)}")
            return {}
    
    def _generate_optimization_charts(self, visualization_data: VisualizationData,
                                    selected_solutions: Dict[str, ObjectiveResults] = None) -> Dict[str, str]:
        """生成优化结果图表"""
        try:
            optimization_charts = {}
            
            self.logger.info("Starting optimization result charts generation...")
            
            # 1. 帕累托前沿图（3D和2D版本）
            try:
                pareto_3d_path = self.optimization_charts.create_pareto_frontier_chart(
                    visualization_data, selected_solutions, chart_type='3d'
                )
                if pareto_3d_path:
                    optimization_charts['pareto_frontier_3d'] = pareto_3d_path
                
                pareto_2d_path = self.optimization_charts.create_pareto_frontier_chart(
                    visualization_data, selected_solutions, chart_type='2d'
                )
                if pareto_2d_path:
                    optimization_charts['pareto_frontier_2d'] = pareto_2d_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate Pareto frontier chart: {str(e)}")
            
            # 2. 优化收敛过程图
            try:
                convergence_path = self.optimization_charts.create_convergence_chart(
                    visualization_data
                )
                if convergence_path:
                    optimization_charts['optimization_convergence'] = convergence_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate optimization convergence chart: {str(e)}")
            
            # 3. 目标函数分布图
            try:
                distribution_path = self.optimization_charts.create_objective_distribution_chart(
                    visualization_data
                )
                if distribution_path:
                    optimization_charts['objective_distribution'] = distribution_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate objective distribution chart: {str(e)}")
            
            self.logger.info(f"优化 result charts generation completed, {len(optimization_charts)} charts generated")
            
            return optimization_charts
            
        except Exception as e:
            self.logger.error(f"Failed to generate optimization result charts: {str(e)}")
            return {}
    
    def _generate_performance_charts(self, visualization_data: VisualizationData) -> Dict[str, str]:
        """生成性能分析图表"""
        try:
            performance_charts = {}
            
            self.logger.info("Starting performance analysis charts generation...")
            
            # 1. 能耗性能图表
            try:
                energy_path = self.performance_charts.create_energy_performance_chart(
                    visualization_data
                )
                if energy_path:
                    performance_charts['energy_performance'] = energy_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate energy performance chart: {str(e)}")
            
            # 2. 热工性能图表  
            try:
                thermal_path = self.performance_charts.create_thermal_performance_chart(
                    visualization_data
                )
                if thermal_path:
                    performance_charts['thermal_performance'] = thermal_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate thermal performance chart: {str(e)}")
            
            # 3. 成本分析图表
            try:
                cost_path = self.performance_charts.create_cost_analysis_chart(
                    visualization_data
                )
                if cost_path:
                    performance_charts['cost_analysis'] = cost_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate cost analysis chart: {str(e)}")
            
            # 4. 综合性能雷达图
            try:
                radar_path = self.performance_charts.create_comprehensive_radar_chart(
                    visualization_data
                )
                if radar_path:
                    performance_charts['comprehensive_radar'] = radar_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate comprehensive radar chart: {str(e)}")
            
            self.logger.info(f"性能 analysis charts generation completed, {len(performance_charts)} charts generated")
            
            return performance_charts
            
        except Exception as e:
            self.logger.error(f"Failed to generate performance analysis charts: {str(e)}")
            return {}
    
    def _generate_comparison_charts(self, selected_solutions: Dict[str, ObjectiveResults],
                                  visualization_data: VisualizationData = None) -> Dict[str, str]:
        """生成对比分析图表"""
        try:
            comparison_charts = {}
            
            self.logger.info("Starting comparison analysis charts generation...")
            
            # 1. 多方案性能对比图
            try:
                multi_comparison_path = self.comparison_charts.create_multi_solution_comparison_chart(
                    selected_solutions
                )
                if multi_comparison_path:
                    comparison_charts['multi_solution_comparison'] = multi_comparison_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate multi-solution comparison chart: {str(e)}")
            
            # 2. 基准对比分析图
            try:
                benchmark_path = self.comparison_charts.create_benchmark_comparison_chart(
                    selected_solutions
                )
                if benchmark_path:
                    comparison_charts['benchmark_comparison'] = benchmark_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate benchmark comparison chart: {str(e)}")
            
            # 3. 敏感性分析图
            try:
                if selected_solutions:
                    base_solution = list(selected_solutions.values())[0]
                    sensitivity_path = self.comparison_charts.create_sensitivity_analysis_chart(
                        base_solution
                    )
                    if sensitivity_path:
                        comparison_charts['sensitivity_analysis'] = sensitivity_path
                        
            except Exception as e:
                self.logger.error(f"Failed to generate sensitivity analysis chart: {str(e)}")
            
            # 4. 权衡分析图
            try:
                tradeoff_path = self.comparison_charts.create_tradeoff_analysis_chart(
                    selected_solutions
                )
                if tradeoff_path:
                    comparison_charts['tradeoff_analysis'] = tradeoff_path
                    
            except Exception as e:
                self.logger.error(f"Failed to generate tradeoff analysis chart: {str(e)}")
            
            self.logger.info(f"对比 analysis charts generation completed, {len(comparison_charts)} charts generated")
            
            return comparison_charts
            
        except Exception as e:
            self.logger.error(f"Failed to generate comparison analysis charts: {str(e)}")
            return {}
    
    def _generate_comprehensive_reports(self, visualization_data: VisualizationData,
                                      selected_solutions: Dict[str, ObjectiveResults],
                                      chart_files: Dict[str, str]) -> Dict[str, str]:
        """生成综合报告"""
        try:
            self.logger.info("Starting comprehensive report generation...")
            
            # 准备分析结果数据
            analysis_results = {
                'chart_generation_summary': {
                    'total_charts': len(chart_files),
                    'successful_charts': sum(1 for path in chart_files.values() if path and os.path.exists(path))
                },
                'solution_recommendations': self._generate_solution_recommendations(selected_solutions)
            }
            
            # 生成报告
            reports = self.report_generator.generate_comprehensive_report(
                visualization_data=visualization_data,
                selected_solutions=selected_solutions,
                chart_files=chart_files,
                analysis_results=analysis_results
            )
            
            self.logger.info(f"综合优化 report generation completed, {len(reports)} reports generated")
            
            return reports
            
        except Exception as e:
            self.logger.error(f"Failed to generate comprehensive report: {str(e)}")
            return {}
    
    def _generate_solution_recommendations(self, selected_solutions: Dict[str, ObjectiveResults]) -> List[str]:
        """生成解决方案推荐"""
        try:
            recommendations = []
            
            if not selected_solutions:
                return ["No available solution data"]
            
            # 找出各方面最优的解决方案
            energy_best = min(selected_solutions.items(), key=lambda x: x[1].energy_consumption)
            cost_best = min(selected_solutions.items(), key=lambda x: x[1].renovation_cost)
            
            recommendations.append(f"Energy optimal solution: {energy_best[0]}, energy consumption: {energy_best[1].energy_consumption:.1f} kWh/m²/year")
            recommendations.append(f"成本 optimal solution: {cost_best[0]}, renovation cost: {cost_best[1].renovation_cost:,.0f} yuan")
            
            # 综合推荐
            comprehensive_scores = {}
            for name, solution in selected_solutions.items():
                score = (200 - solution.energy_consumption) + (1 - solution.thermal_performance) * 100 + (200000 - solution.renovation_cost) / 1000
                comprehensive_scores[name] = score
            
            best_comprehensive = max(comprehensive_scores.items(), key=lambda x: x[1])
            recommendations.append(f"综合优化 optimal solution: {best_comprehensive[0]}, recommended for priority implementation")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Failed to generate solution recommendations: {str(e)}")
            return ["Recommendation generation failed"]
    
    def _organize_output_files(self, visualization_results: Dict[str, Any]):
        """组织输出文件"""
        try:
            if not self.organize_outputs:
                return
            
            session_id = visualization_results['session_id']
            
            # 创建会话目录
            session_dir = os.path.join("outputs", f"session_{session_id}")
            os.makedirs(session_dir, exist_ok=True)
            
            # 创建子目录
            charts_dir = os.path.join(session_dir, "charts")
            reports_dir = os.path.join(session_dir, "reports")
            os.makedirs(charts_dir, exist_ok=True)
            os.makedirs(reports_dir, exist_ok=True)
            
            # 移动图表文件
            for chart_name, chart_path in visualization_results['charts'].items():
                if chart_path and os.path.exists(chart_path):
                    filename = os.path.basename(chart_path)
                    new_path = os.path.join(charts_dir, filename)
                    try:
                        os.rename(chart_path, new_path)
                        visualization_results['charts'][chart_name] = new_path
                    except OSError:
                        pass  # 文件可能已经在目标位置
            
            # 移动报告文件
            for report_name, report_path in visualization_results['reports'].items():
                if report_path and os.path.exists(report_path):
                    filename = os.path.basename(report_path)
                    new_path = os.path.join(reports_dir, filename)
                    try:
                        os.rename(report_path, new_path)
                        visualization_results['reports'][report_name] = new_path
                    except OSError:
                        pass  # 文件可能已经在目标位置
            
            # 生成会话信息文件
            session_info_path = os.path.join(session_dir, "session_info.json")
            with open(session_info_path, 'w', encoding='utf-8') as f:
                import json
                json.dump(visualization_results, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Output files organized to directory: {session_dir}")
            
        except Exception as e:
            self.logger.error(f"Failed to organize output files: {str(e)}")
    
    def _cleanup_old_outputs(self):
        """清理旧的输出文件"""
        try:
            if not self.cleanup_old_outputs:
                return
            
            import time
            from pathlib import Path
            
            outputs_dir = Path("outputs")
            if not outputs_dir.exists():
                return
            
            current_time = time.time()
            cutoff_time = current_time - (self.max_output_age_days * 24 * 3600)
            
            cleaned_count = 0
            for item in outputs_dir.iterdir():
                if item.is_dir() and item.name.startswith("session_"):
                    if item.stat().st_mtime < cutoff_time:
                        import shutil
                        shutil.rmtree(item)
                        cleaned_count += 1
            
            if cleaned_count > 0:
                self.logger.info(f"Cleaned {cleaned_count} expired output directories")
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old output files: {str(e)}")
    
    @handle_exception
    def generate_single_chart(self, chart_type: str, visualization_data: VisualizationData,
                            selected_solutions: Dict[str, ObjectiveResults] = None,
                            **kwargs) -> str:
        """
        生成单个图表
        
        Args:
            chart_type: 图表类型
            visualization_data: 可视化数据
            selected_solutions: 选定的解决方案
            **kwargs: 额外参数
            
        Returns:
            生成的图表文件路径
        """
        with LogContext(f"Single chart generation - {chart_type}", self.logger):
            try:
                chart_path = ""
                
                # 优化结果图表
                if chart_type == 'pareto_frontier_3d':
                    chart_path = self.optimization_charts.create_pareto_frontier_chart(
                        visualization_data, selected_solutions, chart_type='3d'
                    )
                elif chart_type == 'pareto_frontier_2d':
                    chart_path = self.optimization_charts.create_pareto_frontier_chart(
                        visualization_data, selected_solutions, chart_type='2d'
                    )
                elif chart_type == 'optimization_convergence':
                    chart_path = self.optimization_charts.create_convergence_chart(
                        visualization_data
                    )
                elif chart_type == 'objective_distribution':
                    chart_path = self.optimization_charts.create_objective_distribution_chart(
                        visualization_data
                    )
                
                # 性能分析图表
                elif chart_type == 'energy_performance':
                    chart_path = self.performance_charts.create_energy_performance_chart(
                        visualization_data
                    )
                elif chart_type == 'thermal_performance':
                    chart_path = self.performance_charts.create_thermal_performance_chart(
                        visualization_data
                    )
                elif chart_type == 'cost_analysis':
                    chart_path = self.performance_charts.create_cost_analysis_chart(
                        visualization_data
                    )
                elif chart_type == 'comprehensive_radar':
                    chart_path = self.performance_charts.create_comprehensive_radar_chart(
                        visualization_data
                    )
                
                # 对比分析图表
                elif chart_type == 'multi_solution_comparison' and selected_solutions:
                    chart_path = self.comparison_charts.create_multi_solution_comparison_chart(
                        selected_solutions
                    )
                elif chart_type == 'benchmark_comparison' and selected_solutions:
                    chart_path = self.comparison_charts.create_benchmark_comparison_chart(
                        selected_solutions
                    )
                elif chart_type == 'sensitivity_analysis' and selected_solutions:
                    base_solution = list(selected_solutions.values())[0]
                    chart_path = self.comparison_charts.create_sensitivity_analysis_chart(
                        base_solution
                    )
                elif chart_type == 'tradeoff_analysis' and selected_solutions:
                    chart_path = self.comparison_charts.create_tradeoff_analysis_chart(
                        selected_solutions
                    )
                
                else:
                    raise VisualizationError(f"Unsupported chart type: {chart_type}")
                
                if chart_path:
                    self.logger.info(f"Single chart generation successful: {chart_type} -> {chart_path}")
                else:
                    self.logger.warning(f"Single chart generation failed: {chart_type}")
                
                return chart_path
                
            except Exception as e:
                raise VisualizationError(f"Single chart generation failed ({chart_type}): {str(e)}") from e
    
    def get_available_chart_types(self) -> Dict[str, List[str]]:
        """获取可用的图表类型"""
        return {
            'optimization_charts': [
                'pareto_frontier_3d',
                'pareto_frontier_2d', 
                'optimization_convergence',
                'objective_distribution'
            ],
            'performance_charts': [
                'energy_performance',
                'thermal_performance',
                'cost_analysis',
                'comprehensive_radar'
            ],
            'comparison_charts': [
                'multi_solution_comparison',
                'benchmark_comparison',
                'sensitivity_analysis',
                'tradeoff_analysis'
            ]
        }
    
    def get_visualization_status(self) -> Dict[str, Any]:
        """获取可视化状态"""
        return {
            'current_session_id': self.current_session_id,
            'generated_charts_count': len(self.generated_charts),
            'chart_config': {
                'style': self.chart_config.chart_style.value,
                'dpi': self.chart_config.dpi,
                'bilingual_mode': self.chart_config.bilingual_mode
            },
            'manager_config': {
                'auto_generate_all': self.auto_generate_all_charts,
                'organize_outputs': self.organize_outputs,
                'cleanup_old_outputs': self.cleanup_old_outputs
            }
        }
    
    def update_chart_config(self, config_updates: Dict[str, Any]):
        """更新图表配置"""
        try:
            # 更新图表配置
            if 'dpi' in config_updates:
                self.chart_config.dpi = config_updates['dpi']
            
            if 'bilingual_mode' in config_updates:
                self.chart_config.bilingual_mode = config_updates['bilingual_mode']
            
            # 重新应用全局样式
            self.chart_config._apply_global_style()
            
            self.logger.info(f"Chart configuration updated: {list(config_updates.keys())}")
            
        except Exception as e:
            self.logger.error(f"Failed to update chart configuration: {str(e)}")
    
    def _create_solutions_grid_visualization(self, optimization_results, facade_elements, selected_solutions=None):
        """创建所有解的排列展示图 - 修复数据输入"""
        try:
            import matplotlib.pyplot as plt

# 设置matplotlib中文字体支持
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

            import matplotlib.patches as patches
            from matplotlib.gridspec import GridSpec
            
            # 修复数据获取 - 支持多种数据结构
            pareto_solutions = []
            if isinstance(optimization_results, dict):
                if 'pareto_optimal_solutions' in optimization_results:
                    pareto_solutions = optimization_results['pareto_optimal_solutions']
                elif 'pareto_solutions' in optimization_results:
                    pareto_solutions = optimization_results['pareto_solutions']
                elif 'solutions' in optimization_results:
                    pareto_solutions = optimization_results['solutions']
            elif hasattr(optimization_results, 'pareto_solutions'):
                pareto_solutions = optimization_results.pareto_solutions
            elif hasattr(optimization_results, 'pareto_optimal_solutions'):
                pareto_solutions = optimization_results.pareto_optimal_solutions
            
            # 如果没有解，使用选定的解作为替代
            if not pareto_solutions and selected_solutions:
                if isinstance(selected_solutions, dict):
                    pareto_solutions = list(selected_solutions.values())
                elif isinstance(selected_solutions, list):
                    pareto_solutions = selected_solutions
            
            if not pareto_solutions:
                self.logger.warning("No solutions found for grid visualization")
                return None
            
            # 计算网格尺寸
            n_solutions = min(len(pareto_solutions), 16)  # 最多显示16个解
            grid_rows = int(n_solutions**0.5) + (1 if n_solutions % int(n_solutions**0.5) else 0)
            grid_cols = int(n_solutions / grid_rows) + (1 if n_solutions % grid_rows else 0)
            
            # 创建图形
            fig = plt.figure(figsize=(15, 12))
            gs = GridSpec(grid_rows + 1, grid_cols, figure=fig, height_ratios=[1]*grid_rows + [0.2])
            
            # Set chart title in English
            fig.suptitle('Optimization Solutions Grid Display', 
                        fontsize=16, fontweight='bold', y=0.95)
            
            # 绘制每个解
            for i, solution in enumerate(pareto_solutions[:n_solutions]):
                row = i // grid_cols
                col = i % grid_cols
                
                ax = fig.add_subplot(gs[row, col])
                
                # 绘制简化的建筑立面
                self._draw_facade_solution(ax, facade_elements, solution)
                
                # Add solution information in English
                info_text = f"Solution {i+1}\n"
                if hasattr(solution, 'energy_consumption'):
                    info_text += f"Energy: {solution.energy_consumption:.1f} kWh/m²\n"
                if hasattr(solution, 'thermal_performance'):
                    info_text += f"Thermal: {solution.thermal_performance:.2f}\n"
                if hasattr(solution, 'renovation_cost'):
                    info_text += f"成本: {solution.renovation_cost/1000:.0f}k"
                
                ax.text(0.02, 0.98, info_text, transform=ax.transAxes, 
                       fontsize=8, verticalalignment='top',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))
                
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.set_aspect('equal')
                ax.axis('off')
            
            # Add legend
            legend_ax = fig.add_subplot(gs[-1, :])
            self._add_solutions_legend(legend_ax)
            legend_ax.axis('off')
            
            plt.tight_layout()
            
            # 保存图表到不同格式目录
            if hasattr(self, 'session_directories'):
                # 保存PNG到charts目录
                png_path = os.path.join(self.session_directories['charts'], 'solutions_grid.png')
                plt.savefig(png_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
                
                # 保存PDF到pdf目录
                pdf_path = os.path.join(self.session_directories['pdf'], 'solutions_grid.pdf')
                plt.savefig(pdf_path, format='pdf', dpi=300, bbox_inches='tight', facecolor='white')
                
                # 保存SVG到svg目录
                svg_path = os.path.join(self.session_directories['svg'], 'solutions_grid.svg')
                plt.savefig(svg_path, format='svg', dpi=300, bbox_inches='tight', facecolor='white')
                
                output_path = png_path
            else:
                # 默认保存
                output_dir = getattr(self.optimization_charts, 'output_dir', 'outputs/charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'solutions_grid.png')
                plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            
            plt.close()
            
            self.logger.info(f"Solutions grid visualization generated: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Failed to generate solutions grid: {str(e)}")
            return None
    
    def _create_facade_comparison_visualization(self, facade_elements, optimization_results, selected_solutions):
        """创建原始与最佳方案对比图 - 修复数据输入"""
        try:
            import matplotlib.pyplot as plt
            from matplotlib.gridspec import GridSpec
            
            # 创建图形
            fig = plt.figure(figsize=(20, 8))
            gs = GridSpec(2, 5, figure=fig, height_ratios=[3, 1])
            
            # Chart title in English
            fig.suptitle('Facade Optimization Solutions Comparison', 
                        fontsize=18, fontweight='bold', y=0.95)
            
            # Solution names in English
            solution_names = ['Original\nDesign', 'Energy\nOptimized', 
                            'Thermal\nOptimized', 'Cost\nOptimized', 
                            'Comprehensive\nOptimized']
            
            # 修复选定解数据结构
            solutions_list = []
            if selected_solutions:
                if isinstance(selected_solutions, dict):
                    # 按照顺序获取解
                    keys = ['energy_best', 'thermal_best', 'cost_best', 'comprehensive_best']
                    for key in keys:
                        if key in selected_solutions:
                            solutions_list.append(selected_solutions[key])
                        else:
                            # 如果没有特定类型，使用第一个可用的解
                            available_solutions = list(selected_solutions.values())
                            if available_solutions:
                                solutions_list.append(available_solutions[0])
                            else:
                                solutions_list.append(None)
                elif isinstance(selected_solutions, list):
                    solutions_list = selected_solutions[:4]
            
            # 填充缺失的解
            while len(solutions_list) < 4:
                solutions_list.append(None)
            
            # 绘制每个方案
            for i, name in enumerate(solution_names):
                ax = fig.add_subplot(gs[0, i])
                
                if i == 0:
                    # 原始方案
                    self._draw_original_facade(ax, facade_elements)
                else:
                    # 优化方案
                    solution = solutions_list[i-1] if i-1 < len(solutions_list) else None
                    self._draw_optimized_facade(ax, facade_elements, solution)
                
                ax.set_title(name, fontsize=12, fontweight='bold', pad=10)
                ax.set_xlim(0, 1)
                ax.set_ylim(0, 1)
                ax.set_aspect('equal')
                ax.axis('off')
            
            # 添加性能指标对比表
            metrics_ax = fig.add_subplot(gs[1, :])
            # 将解列表转换为字典格式以兼容表格渲染
            solutions_dict = {}
            if solutions_list:
                keys = ['energy_best', 'thermal_best', 'cost_best', 'comprehensive_best']
                for i, solution in enumerate(solutions_list):
                    if solution and i < len(keys):
                        solutions_dict[keys[i]] = solution
            self._add_performance_comparison_table(metrics_ax, solutions_dict)
            metrics_ax.axis('off')
            
            plt.tight_layout()
            
            # 保存图表到不同格式目录
            if hasattr(self, 'session_directories'):
                # 保存PNG到charts目录
                png_path = os.path.join(self.session_directories['charts'], 'facade_comparison.png')
                plt.savefig(png_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
                
                # 保存PDF到pdf目录
                pdf_path = os.path.join(self.session_directories['pdf'], 'facade_comparison.pdf')
                plt.savefig(pdf_path, format='pdf', dpi=300, bbox_inches='tight', facecolor='white')
                
                # 保存SVG到svg目录
                svg_path = os.path.join(self.session_directories['svg'], 'facade_comparison.svg')
                plt.savefig(svg_path, format='svg', dpi=300, bbox_inches='tight', facecolor='white')
                
                output_path = png_path
            else:
                # 默认保存
                output_dir = getattr(self.comparison_charts, 'output_dir', 'outputs/charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'facade_comparison.png')
                plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            
            plt.close()
            
            self.logger.info(f"立面 comparison chart generated: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Failed to generate facade comparison: {str(e)}")
            return None
    
    def _create_3d_isometric_visualization(self, facade_elements, selected_solutions):
        """创建清晰易读的3D立面可视化"""
        try:
            # 确定输出路径
            if hasattr(self, 'session_directories'):
                output_path = os.path.join(self.session_directories['charts'], 'clear_3d_facade.png')
            else:
                output_dir = getattr(self.comparison_charts, 'output_dir', 'outputs/charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'clear_3d_facade.png')
            
            # 使用新的清晰3D可视化方法
            result_path = self._create_clear_3d_facade(
                facade_elements=facade_elements,
                selected_solutions=selected_solutions,
                output_path=output_path
            )
            
            if result_path:
                self.logger.info(f"清晰3D立面图已生成: {result_path}")
                return result_path
            else:
                self.logger.warning("清晰3D立面图生成失败，使用备用方案")
                return self._create_simple_3d_visualization(facade_elements, selected_solutions)
                
        except Exception as e:
            self.logger.error(f"清晰3D立面图生成失败: {str(e)}")
            return self._create_simple_3d_visualization(facade_elements, selected_solutions)
    
    def _create_clear_3d_facade(self, facade_elements, selected_solutions, output_path):
        """创建清晰易读的3D立面可视化"""
        try:
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            import numpy as np
            
            # 建筑参数
            wall_width = 12.0
            wall_height = 8.0
            wall_thickness = 0.24
            
            fig = plt.figure(figsize=(20, 15))
            gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.2)
            
            fig.suptitle('清晰3D立面可视化 - 窗户、窗框、遮阳板对比\nClear 3D Facade Visualization - Windows, Frames & Shading Comparison', 
                        fontsize=16, fontweight='bold', y=0.96)
            
            # 解决方案配置 - 根据实际 selected_solutions 生成
            solution_configs = self._generate_solution_configs(selected_solutions)
            
            positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
            
            for i, ((title, window_types), pos) in enumerate(zip(solution_configs, positions)):
                ax = fig.add_subplot(gs[pos[0], pos[1]], projection='3d')
                
                # 绘制清晰的3D立面
                self._draw_clear_3d_facade_subplot(ax, wall_width, wall_height, wall_thickness, window_types)
                
                # 设置标题
                ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
                
                # 设置最佳观察视角
                ax.view_init(elev=20, azim=-45)
                
                # 设置坐标轴
                ax.set_xlim(-0.5, wall_width + 0.5)
                ax.set_ylim(-1.2, wall_thickness + 0.3)
                ax.set_zlim(-0.2, wall_height + 0.5)
                
                # 简化坐标轴标签
                ax.set_xlabel('宽度 (m)', fontsize=9)
                ax.set_ylabel('深度 (m)', fontsize=9)
                ax.set_zlabel('高度 (m)', fontsize=9)
                
                # 设置网格和背景
                ax.grid(True, alpha=0.3)
                ax.xaxis.pane.fill = True
                ax.yaxis.pane.fill = True
                ax.zaxis.pane.fill = True
                ax.xaxis.pane.set_facecolor('#F8F8F8')
                ax.yaxis.pane.set_facecolor('#F8F8F8')
                ax.zaxis.pane.set_facecolor('#F8F8F8')
                ax.xaxis.pane.set_alpha(0.3)
                ax.yaxis.pane.set_alpha(0.3)
                ax.zaxis.pane.set_alpha(0.3)
            
            plt.tight_layout()
            
            # 保存图表
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            plt.savefig(output_path, dpi=300, bbox_inches='tight', 
                       facecolor='white', edgecolor='none')
            
            # 保存PDF版本
            pdf_path = output_path.replace('.png', '.pdf')
            plt.savefig(pdf_path, format='pdf', dpi=300, bbox_inches='tight', 
                       facecolor='white')
            
            plt.close()
            
            return output_path
            
        except Exception as e:
            self.logger.error(f"创建清晰3D立面失败: {str(e)}")
            return None
    
    def _generate_solution_configs(self, selected_solutions):
        """根据实际选定的解决方案生成配置"""
        try:
            if selected_solutions and len(selected_solutions) > 0:
                # 使用实际解决方案数据
                configs = []
                solution_names = list(selected_solutions.keys())[:4]  # 最多显示4个方案
                
                for i, solution_name in enumerate(solution_names):
                    solution = selected_solutions[solution_name]
                    # 提取窗户类型信息
                    window_types = self._extract_window_types_from_solution(solution)
                    configs.append((solution_name, window_types))
                
                # 如果方案数量不足4个，用默认配置补充
                while len(configs) < 4:
                    default_configs = [
                        ('原始设计\nOriginal', [0, 0, 0, 0, 0, 0]),
                        ('能耗优化\nEnergy Opt', [1, 1, 1, 0, 0, 0]),
                        ('热工优化\nThermal Opt', [2, 2, 1, 1, 0, 0]),
                        ('综合优化\nComprehensive', [2, 2, 2, 1, 1, 1])
                    ]
                    configs.append(default_configs[len(configs)])
                
                return configs
            else:
                # 使用默认配置
                return [
                    ('原始设计\nOriginal', [0, 0, 0, 0, 0, 0]),
                    ('能耗优化\nEnergy Opt', [1, 1, 1, 0, 0, 0]),
                    ('热工优化\nThermal Opt', [2, 2, 1, 1, 0, 0]),
                    ('综合优化\nComprehensive', [2, 2, 2, 1, 1, 1])
                ]
        except Exception as e:
            self.logger.error(f"生成解决方案配置失败: {str(e)}")
            # 返回默认配置
            return [
                ('原始设计\nOriginal', [0, 0, 0, 0, 0, 0]),
                ('能耗优化\nEnergy Opt', [1, 1, 1, 0, 0, 0]),
                ('热工优化\nThermal Opt', [2, 2, 1, 1, 0, 0]),
                ('综合优化\nComprehensive', [2, 2, 2, 1, 1, 1])
            ]
    
    def _extract_window_types_from_solution(self, solution):
        """从解决方案中提取窗户类型信息"""
        try:
            # 默认6个窗户，类型为0（普通窗户）
            window_types = [0] * 6
            
            # 尝试多种方式提取窗户类型信息
            if hasattr(solution, 'window_types'):
                window_types = solution.window_types[:6]
            elif hasattr(solution, 'variables') and 'window_types' in solution.variables:
                window_types = solution.variables['window_types'][:6]
            elif hasattr(solution, 'decision_variables'):
                # 从决策变量中提取
                for var_name, var_value in solution.decision_variables.items():
                    if 'window' in var_name.lower() and 'type' in var_name.lower():
                        if isinstance(var_value, list):
                            window_types = var_value[:6]
                        break
            elif hasattr(solution, 'optimization_result'):
                # 从优化结果中提取
                result = solution.optimization_result
                if hasattr(result, 'window_types'):
                    window_types = result.window_types[:6]
            
            # 确保窗户类型在有效范围内 (0: 普通, 1: 有窗框, 2: 有遮阳)
            window_types = [max(0, min(2, int(wt))) for wt in window_types]
            
            # 补齐到6个窗户
            while len(window_types) < 6:
                window_types.append(0)
            
            return window_types[:6]
            
        except Exception as e:
            self.logger.error(f"提取窗户类型失败: {str(e)}")
            return [0, 0, 0, 0, 0, 0]  # 返回默认值
    
    def _validate_and_process_window_data_local(self, facade_elements, solution):
        """本地版本的窗户数据验证和处理函数"""
        try:
            window_data = []
            
            # 检查是否有真实窗户数据
            if facade_elements and hasattr(facade_elements, 'windows') and facade_elements.windows:
                windows = facade_elements.windows
                self.logger.info(f"使用真实窗户数据，共 {len(windows)} 个窗户")
            else:
                # 使用默认窗户布局（2x3网格）
                windows = self._create_default_windows_local()
                self.logger.info("使用默认窗户布局")
            
            # 处理每个窗户的数据
            for i, window in enumerate(windows):
                # 获取窗户3D坐标
                x_3d, z_3d, w_3d, h_3d = self._get_window_3d_coords_local(window, i)
                
                # 获取窗户类型和参数
                window_type = 0  # 默认普通窗户
                frame_depth = 0.08  # 默认窗框深度
                shading_depth = 1.0  # 默认遮阳板深度
                shading_angle = 30.0  # 默认遮阳角度
                
                if solution:
                    # 从解决方案中提取窗户类型
                    window_types = self._extract_window_types_from_solution(solution)
                    if i < len(window_types):
                        window_type = window_types[i]
                    
                    # 根据窗户类型设置参数
                    if window_type >= 1:  # 有窗框
                        frame_depth = getattr(solution, 'frame_depth', 0.08)
                    
                    if window_type >= 2:  # 有遮阳板
                        shading_depth = getattr(solution, 'shading_projection', 1.0)
                        shading_angle = getattr(solution, 'shading_angle', 30.0)
                else:
                    # 演示模式：创建多样化的窗户类型
                    if i < 2:  # 前2个窗户有遮阳板
                        window_type = 2
                        shading_depth = 0.8 + i * 0.2
                        shading_angle = 25 + i * 10
                    elif i < 4:  # 第3-4个窗户有窗框
                        window_type = 1
                        frame_depth = 0.08 + i * 0.02
                
                window_data.append((x_3d, z_3d, w_3d, h_3d, window_type, frame_depth, shading_depth, shading_angle))
                
                # 记录窗户信息用于调试
                self.logger.debug(f"窗户 {i}: 类型={window_type}, 窗框深度={frame_depth:.2f}, 遮阳深度={shading_depth:.2f}")
            
            return window_data
            
        except Exception as e:
            self.logger.error(f"窗户数据验证处理失败: {str(e)}")
            # 返回默认窗户数据
            return self._create_fallback_window_data_local()
    
    def _create_default_windows_local(self):
        """创建默认窗户布局（2x3网格）"""
        try:
            windows = []
            # 2行3列的窗户布局
            positions = [
                (1.5, 1.0), (4.5, 1.0), (7.5, 1.0),  # 下排
                (1.5, 4.0), (4.5, 4.0), (7.5, 4.0),  # 上排
            ]
            
            for i, (x, z) in enumerate(positions):
                window = {
                    'id': i,
                    'x': x,
                    'z': z,
                    'width': 2.0,
                    'height': 1.5,
                    'bbox': [x, z, x + 2.0, z + 1.5]
                }
                windows.append(window)
            
            return windows
            
        except Exception as e:
            self.logger.error(f"创建默认窗户布局失败: {str(e)}")
            return []
    
    def _get_window_3d_coords_local(self, window, index):
        """获取窗户的3D坐标"""
        try:
            if isinstance(window, dict):
                # 字典格式的窗户数据
                if 'bbox' in window:
                    bbox = window['bbox']
                    x_3d = bbox[0]
                    z_3d = bbox[1]
                    w_3d = bbox[2] - bbox[0]
                    h_3d = bbox[3] - bbox[1]
                else:
                    x_3d = window.get('x', 1.0 + index * 2.0)
                    z_3d = window.get('z', 1.0)
                    w_3d = window.get('width', 2.0)
                    h_3d = window.get('height', 1.5)
            else:
                # 对象格式的窗户数据
                if hasattr(window, 'bbox'):
                    bbox = window.bbox
                    x_3d = bbox[0]
                    z_3d = bbox[1]
                    w_3d = bbox[2] - bbox[0]
                    h_3d = bbox[3] - bbox[1]
                else:
                    x_3d = getattr(window, 'x', 1.0 + index * 2.0)
                    z_3d = getattr(window, 'z', 1.0)
                    w_3d = getattr(window, 'width', 2.0)
                    h_3d = getattr(window, 'height', 1.5)
            
            return x_3d, z_3d, w_3d, h_3d
            
        except Exception as e:
            self.logger.error(f"获取窗户3D坐标失败: {str(e)}")
            # 使用默认位置
            x_3d = 1.0 + index * 2.0
            z_3d = 1.0 if index < 3 else 4.0
            w_3d = 2.0
            h_3d = 1.5
            return x_3d, z_3d, w_3d, h_3d
    
    def _create_fallback_window_data_local(self):
        """创建备用窗户数据"""
        try:
            fallback_data = []
            positions = [
                (1.5, 1.0), (4.5, 1.0), (7.5, 1.0),  # 下排
                (1.5, 4.0), (4.5, 4.0), (7.5, 4.0),  # 上排
            ]
            
            for i, (x, z) in enumerate(positions):
                # 创建多样化的窗户类型用于演示
                window_type = 0 if i >= 4 else (2 if i < 2 else 1)
                frame_depth = 0.08
                shading_depth = 0.8 + i * 0.1
                shading_angle = 25 + i * 5
                
                fallback_data.append((x, z, 2.0, 1.5, window_type, frame_depth, shading_depth, shading_angle))
            
            return fallback_data
            
        except Exception as e:
            self.logger.error(f"创建备用窗户数据失败: {str(e)}")
            return [(1.5, 1.0, 2.0, 1.5, 0, 0.08, 0.8, 30.0)]
    
    def _draw_clear_3d_facade_subplot(self, ax, wall_width, wall_height, wall_thickness, window_types):
        """绘制清晰的3D立面子图"""
        try:
            # 1. 绘制简洁的墙体
            wall_vertices = [
                [[0, 0, 0], [wall_width, 0, 0], 
                 [wall_width, 0, wall_height], [0, 0, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(wall_vertices, alpha=0.8, 
                                               facecolor='#F0F0F0', edgecolor='#888888', linewidth=2))
            
            # 墙体厚度
            thickness_vertices = [
                [[0, 0, wall_height], [wall_width, 0, wall_height], 
                 [wall_width, wall_thickness, wall_height], [0, wall_thickness, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(thickness_vertices, alpha=0.9, 
                                               facecolor='#D0D0D0', edgecolor='#666666', linewidth=1))
            
            # 2. 绘制窗户（2x3布局）
            window_positions = [
                (1.5, 1.0, 2.0, 1.5),   # 左下
                (4.5, 1.0, 2.0, 1.5),   # 中下  
                (7.5, 1.0, 2.0, 1.5),   # 右下
                (1.5, 4.0, 2.0, 1.5),   # 左上
                (4.5, 4.0, 2.0, 1.5),   # 中上
                (7.5, 4.0, 2.0, 1.5),   # 右上
            ]
            
            for i, (x, z, w, h) in enumerate(window_positions):
                window_type = window_types[i] if i < len(window_types) else 0
                
                # 绘制基础窗户
                self._draw_clear_window_element(ax, x, z, w, h, window_type)
                
                # 绘制窗框（如果需要）
                if window_type >= 1:
                    self._draw_clear_frame_element(ax, x, z, w, h)
                
                # 绘制遮阳板（如果需要）
                if window_type >= 2:
                    self._draw_clear_shading_element(ax, x, z, w, h)
                
                # 添加窗户编号
                ax.text(x + w/2, -0.15, z + h/2, f'{i+1}', 
                       fontsize=12, ha='center', va='center', weight='bold',
                       bbox=dict(boxstyle='circle,pad=0.3', facecolor='white', alpha=0.9))
            
            # 3. 添加类型统计
            type_counts = {0: 0, 1: 0, 2: 0}
            for wtype in window_types:
                type_counts[wtype] += 1
            
            stats_text = f"普通:{type_counts[0]} 窗框:{type_counts[1]} 遮阳:{type_counts[2]}"
            ax.text2D(0.02, 0.02, stats_text, transform=ax.transAxes, 
                     fontsize=10, verticalalignment='bottom',
                     bbox=dict(boxstyle='round,pad=0.3', facecolor='lightyellow', alpha=0.9))
            
        except Exception as e:
            self.logger.error(f"绘制清晰3D立面子图失败: {str(e)}")
    
    def _draw_clear_window_element(self, ax, x, z, w, h, window_type):
        """绘制清晰的窗户元素"""
        try:
            # 窗户颜色根据类型区分
            colors = {0: '#87CEEB', 1: '#4169E1', 2: '#228B22'}
            color = colors.get(window_type, '#87CEEB')
            
            # 窗户玻璃
            glass = [
                [[x+0.1, -0.02, z+0.1], [x+w-0.1, -0.02, z+0.1], 
                 [x+w-0.1, -0.02, z+h-0.1], [x+0.1, -0.02, z+h-0.1]]
            ]
            ax.add_collection3d(Poly3DCollection(glass, alpha=0.7, 
                                               facecolor=color, edgecolor='#000080', linewidth=2))
            
            # 窗户分隔条
            ax.plot([x, x+w], [-0.01, -0.01], [z+h/2, z+h/2], 'white', linewidth=4)
            ax.plot([x+w/2, x+w/2], [-0.01, -0.01], [z, z+h], 'white', linewidth=4)
            
        except Exception as e:
            self.logger.error(f"绘制窗户元素失败: {str(e)}")
    
    def _draw_clear_frame_element(self, ax, x, z, w, h):
        """绘制清晰的窗框元素"""
        try:
            frame_width = 0.15
            frame_depth = 0.08
            frame_color = '#8B4513'
            
            # 简化的窗框 - 只绘制主要部分
            frame_parts = [
                # 上框
                [[x-frame_width, -frame_depth, z+h], [x+w+frame_width, -frame_depth, z+h], 
                 [x+w+frame_width, -frame_depth, z+h+frame_width], [x-frame_width, -frame_depth, z+h+frame_width]],
                # 下框
                [[x-frame_width, -frame_depth, z-frame_width], [x+w+frame_width, -frame_depth, z-frame_width], 
                 [x+w+frame_width, -frame_depth, z], [x-frame_width, -frame_depth, z]],
                # 左框
                [[x-frame_width, -frame_depth, z], [x, -frame_depth, z], 
                 [x, -frame_depth, z+h], [x-frame_width, -frame_depth, z+h]],
                # 右框
                [[x+w, -frame_depth, z], [x+w+frame_width, -frame_depth, z], 
                 [x+w+frame_width, -frame_depth, z+h], [x+w, -frame_depth, z+h]]
            ]
            
            for frame_part in frame_parts:
                ax.add_collection3d(Poly3DCollection([frame_part], alpha=1.0, 
                                                   facecolor=frame_color, edgecolor='#654321', linewidth=2))
            
        except Exception as e:
            self.logger.error(f"绘制窗框元素失败: {str(e)}")
    
    def _draw_clear_shading_element(self, ax, x, z, w, h):
        """绘制清晰的遮阳板元素"""
        try:
            shading_projection = 0.8
            shading_thickness = 0.08
            shading_color = '#2E8B57'
            shading_z = z + h + 0.2
            
            # 遮阳板主体
            shading_main = [
                [[x-0.2, -shading_projection, shading_z], 
                 [x+w+0.2, -shading_projection, shading_z], 
                 [x+w+0.2, 0.3, shading_z+shading_thickness], 
                 [x-0.2, 0.3, shading_z+shading_thickness]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_main, alpha=0.9, 
                                               facecolor=shading_color, edgecolor='#1F5F1F', linewidth=2))
            
            # 遮阳板前端
            shading_front = [
                [[x-0.2, -shading_projection, shading_z], 
                 [x+w+0.2, -shading_projection, shading_z], 
                 [x+w+0.2, -shading_projection, shading_z+shading_thickness], 
                 [x-0.2, -shading_projection, shading_z+shading_thickness]]
            ]
            ax.add_collection3d(Poly3DCollection(shading_front, alpha=1.0, 
                                               facecolor=shading_color, edgecolor='#1F5F1F', linewidth=2))
            
            # 简化的支撑
            for support_x in [x, x+w]:
                ax.plot([support_x, support_x], [-0.1, 0.24], [shading_z, shading_z+shading_thickness], 
                       color='#8B4513', linewidth=6)
            
        except Exception as e:
            self.logger.error(f"绘制遮阳板元素失败: {str(e)}")
    
    def _create_simple_3d_visualization(self, facade_elements, selected_solutions):
        """备用的3D可视化方案"""
        try:
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D
            
            fig = plt.figure(figsize=(16, 12))
            gs = fig.add_gridspec(2, 2, hspace=0.3, wspace=0.2)
            
            fig.suptitle('3D Facade Visualization - Fallback Mode', 
                        fontsize=16, fontweight='bold', y=0.95)
            
            solution_names = ['Original Design\n原始设计', 
                            'Energy Optimized\n能耗优化', 
                            'Thermal Optimized\n热工优化', 
                            'Comprehensive Optimized\n综合优化']
            
            # 处理选定解数据
            solutions_list = []
            if selected_solutions:
                if isinstance(selected_solutions, dict):
                    keys = ['energy_best', 'thermal_best', 'comprehensive_best']
                    for key in keys:
                        solutions_list.append(selected_solutions.get(key, None))
                elif isinstance(selected_solutions, list):
                    solutions_list = selected_solutions[:3]
            
            while len(solutions_list) < 3:
                solutions_list.append(None)
            
            positions = [(0, 0), (0, 1), (1, 0), (1, 1)]
            
            for i, (pos, name) in enumerate(zip(positions, solution_names)):
                ax = fig.add_subplot(gs[pos[0], pos[1]], projection='3d')
                
                # 绘制简化的3D立面
                self._draw_simple_3d_facade(ax, facade_elements, 
                                          solutions_list[i-1] if i > 0 and i-1 < len(solutions_list) else None)
                
                ax.set_title(name, fontsize=12, fontweight='bold', pad=15)
                ax.view_init(elev=25, azim=45)
                ax.set_xlim(0, 10)
                ax.set_ylim(-1, 1)
                ax.set_zlim(0, 6)
                ax.set_xlabel('')
                ax.set_ylabel('')
                ax.set_zlabel('')
                ax.set_xticks([])
                ax.set_yticks([])
                ax.set_zticks([])
            
            plt.tight_layout()
            
            # 保存图表
            if hasattr(self, 'session_directories'):
                output_path = os.path.join(self.session_directories['charts'], 'fallback_3d_facade.png')
            else:
                output_dir = getattr(self.comparison_charts, 'output_dir', 'outputs/charts')
                os.makedirs(output_dir, exist_ok=True)
                output_path = os.path.join(output_dir, 'fallback_3d_facade.png')
            
            plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white', edgecolor='none')
            plt.close()
            
            self.logger.info(f"备用3D可视化已生成: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"备用3D可视化生成失败: {str(e)}")
            return None
    
    def _draw_simple_3d_facade(self, ax, facade_elements, solution):
        """绘制简化的3D立面"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # 简化的墙体
            wall_vertices = [
                [[0, 0, 0], [10, 0, 0], [10, 0, 6], [0, 0, 6]]
            ]
            ax.add_collection3d(Poly3DCollection(wall_vertices, alpha=0.7, 
                                               facecolor='lightgray', edgecolor='black'))
            
            # 简化的窗户
            window_positions = [(2, 2), (5, 2), (8, 2), (2, 4), (5, 4), (8, 4)]
            for i, (x, z) in enumerate(window_positions):
                window_verts = [
                    [[x-0.5, -0.1, z-0.5], [x+0.5, -0.1, z-0.5], 
                     [x+0.5, -0.1, z+0.5], [x-0.5, -0.1, z+0.5]]
                ]
                ax.add_collection3d(Poly3DCollection(window_verts, alpha=0.8, 
                                                   facecolor='lightblue', edgecolor='blue'))
                
                # 根据解决方案添加窗框和遮阳
                if solution and hasattr(solution, 'window_types'):
                    window_type = solution.window_types[i] if i < len(solution.window_types) else 0
                    
                    if window_type >= 1:  # 窗框
                        frame_verts = [
                            [[x-0.6, -0.15, z-0.6], [x+0.6, -0.15, z-0.6], 
                             [x+0.6, -0.15, z+0.6], [x-0.6, -0.15, z+0.6]]
                        ]
                        ax.add_collection3d(Poly3DCollection(frame_verts, alpha=0.9, 
                                                           facecolor='brown', edgecolor='darkbrown'))
                    
                    if window_type >= 2:  # 遮阳板
                        shading_verts = [
                            [[x-0.7, -0.5, z+0.5], [x+0.7, -0.5, z+0.5], 
                             [x+0.7, 0.1, z+0.5], [x-0.7, 0.1, z+0.5]]
                        ]
                        ax.add_collection3d(Poly3DCollection(shading_verts, alpha=0.8, 
                                                           facecolor='green', edgecolor='darkgreen'))
            
        except Exception as e:
            self.logger.error(f"绘制简化3D立面失败: {str(e)}")
    
    def _draw_facade_solution(self, ax, facade_elements, solution):
        """在给定轴上绘制立面方案 - 使用真实的facade_elements数据"""
        try:
            import matplotlib.patches as patches
            
            # 绘制墙体 - 使用真实数据
            if facade_elements and hasattr(facade_elements, 'walls') and facade_elements.walls:
                for wall in facade_elements.walls:
                    # 规范化墙体坐标到[0,1]范围
                    if hasattr(wall, 'bbox'):
                        x = wall.bbox[0] / 640  # 假设原图宽度640
                        y = (480 - wall.bbox[3]) / 480  # 假设原图高度480，Y轴翻转
                        w = (wall.bbox[2] - wall.bbox[0]) / 640
                        h = (wall.bbox[3] - wall.bbox[1]) / 480
                    else:
                        # 备用默认值
                        x, y, w, h = 0.1, 0.1, 0.8, 0.8
                    
                    wall_rect = patches.Rectangle((x, y), w, h, 
                                                linewidth=2, edgecolor='black', 
                                                facecolor='lightgray', alpha=0.8)
                    ax.add_patch(wall_rect)
            else:
                # 如果没有墙体数据，使用默认
                wall_rect = patches.Rectangle((0.1, 0.1), 0.8, 0.8, 
                                            linewidth=2, edgecolor='black', 
                                            facecolor='lightgray', alpha=0.8)
                ax.add_patch(wall_rect)
            
            # 绘制窗户 - 使用真实数据
            if facade_elements and hasattr(facade_elements, 'windows') and facade_elements.windows:
                for i, window in enumerate(facade_elements.windows):
                    # 规范化窗户坐标
                    if hasattr(window, 'bbox'):
                        x = window.bbox[0] / 640
                        y = (480 - window.bbox[3]) / 480  # Y轴翻转
                        w = (window.bbox[2] - window.bbox[0]) / 640
                        h = (window.bbox[3] - window.bbox[1]) / 480
                    else:
                        # 备用位置
                        positions = [(0.2, 0.6), (0.5, 0.6), (0.7, 0.6), (0.2, 0.3), (0.5, 0.3), (0.7, 0.3)]
                        if i < len(positions):
                            x, y = positions[i]
                            w, h = 0.15, 0.2
                        else:
                            continue
                    
                    # 绘制窗户
                    window_patch = patches.Rectangle((x, y), w, h, 
                                                   linewidth=1, edgecolor='blue', 
                                                   facecolor='lightblue', alpha=0.7)
                    ax.add_patch(window_patch)
                    
                    # 根据解决方案添加窗框和遮阳
                    if solution and hasattr(solution, 'window_types'):
                        window_type = solution.window_types[i] if i < len(solution.window_types) else 0
                        
                        if window_type >= 1:  # 有窗框
                            frame = patches.Rectangle((x-0.01, y-0.01), w+0.02, h+0.02, 
                                                    linewidth=2, edgecolor='brown', 
                                                    facecolor='none')
                            ax.add_patch(frame)
                        
                        if window_type >= 2:  # 有遮阳
                            shading = patches.Rectangle((x-0.02, y+h), w+0.04, 0.05, 
                                                       linewidth=1, edgecolor='green', 
                                                       facecolor='lightgreen', alpha=0.6)
                            ax.add_patch(shading)
            
            # 绘制门 - 使用真实数据
            if facade_elements and hasattr(facade_elements, 'doors') and facade_elements.doors:
                for door in facade_elements.doors:
                    if hasattr(door, 'bbox'):
                        x = door.bbox[0] / 640
                        y = (480 - door.bbox[3]) / 480
                        w = (door.bbox[2] - door.bbox[0]) / 640
                        h = (door.bbox[3] - door.bbox[1]) / 480
                        
                        door_patch = patches.Rectangle((x, y), w, h, 
                                                     linewidth=2, edgecolor='red', 
                                                     facecolor='lightyellow', alpha=0.7)
                        ax.add_patch(door_patch)
            
            # 绘制遮阳设施（如果有独立的遮阳数据）
            if facade_elements and hasattr(facade_elements, 'shading') and facade_elements.shading:
                for shading in facade_elements.shading:
                    if hasattr(shading, 'bbox'):
                        x = shading.bbox[0] / 640
                        y = (480 - shading.bbox[3]) / 480
                        w = (shading.bbox[2] - shading.bbox[0]) / 640
                        h = (shading.bbox[3] - shading.bbox[1]) / 480
                        
                        shading_patch = patches.Rectangle((x, y), w, h, 
                                                        linewidth=1, edgecolor='green', 
                                                        facecolor='lightgreen', alpha=0.6)
                        ax.add_patch(shading_patch)
            
            # 绘制框架（如果有独立的框架数据）
            if facade_elements and hasattr(facade_elements, 'frames') and facade_elements.frames:
                for frame in facade_elements.frames:
                    if hasattr(frame, 'bbox'):
                        x = frame.bbox[0] / 640
                        y = (480 - frame.bbox[3]) / 480
                        w = (frame.bbox[2] - frame.bbox[0]) / 640
                        h = (frame.bbox[3] - frame.bbox[1]) / 480
                        
                        frame_patch = patches.Rectangle((x, y), w, h, 
                                                      linewidth=2, edgecolor='brown', 
                                                      facecolor='none')
                        ax.add_patch(frame_patch)
            
            # 添加调试信息到右上角
            if facade_elements:
                info_text = f"Elements: W{len(getattr(facade_elements, 'walls', []))}"
                info_text += f" Win{len(getattr(facade_elements, 'windows', []))}"
                info_text += f" D{len(getattr(facade_elements, 'doors', []))}"
                info_text += f" S{len(getattr(facade_elements, 'shading', []))}"
                info_text += f" F{len(getattr(facade_elements, 'frames', []))}"
                
                ax.text(0.98, 0.98, info_text, transform=ax.transAxes, 
                       fontsize=6, verticalalignment='top', horizontalalignment='right',
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='yellow', alpha=0.7))
            
        except Exception as e:
            self.logger.error(f"Failed to draw facade solution: {str(e)}")
            # 绘制错误指示
            ax.text(0.5, 0.5, 'Drawing Error', transform=ax.transAxes, 
                   fontsize=10, ha='center', va='center',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7))
    
    def _draw_original_facade(self, ax, facade_elements):
        """绘制原始立面"""
        self._draw_facade_solution(ax, facade_elements, None)
    
    def _draw_optimized_facade(self, ax, facade_elements, solution):
        """绘制优化后立面"""
        self._draw_facade_solution(ax, facade_elements, solution)
    
    def _draw_3d_facade(self, ax, facade_elements, solution):
        """
        绘制立面的高质量3D视图 - 基于Enhanced3DVisualizer的专业实现
        
        功能：
        1. 真实砖墙结构（外墙、内墙、侧面、砖缝纹理）
        2. 完整窗户系统（洞口、双层玻璃、窗台、分隔条）
        3. 立体窗框系统（外框、内框、连接件、厚度展示）
        4. 真实遮阳板（主体、支撑结构、投影效果）
        5. 专业材质渲染和光影效果
        """
        try:
            import numpy as np
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            import matplotlib.colors as mcolors
            
            # === 真实建筑构件尺寸参数（按中国建筑标准）===
            self.wall_thickness = 0.24      # 标准砖墙厚度24cm
            self.window_depth = 0.18        # 窗户洞口深度18cm
            self.frame_width = 0.10         # 窗框宽度10cm（更明显）
            self.frame_depth = 0.08         # 窗框厚度8cm（更立体）
            self.shading_thickness = 0.08   # 遮阳板厚度8cm
            self.shading_projection = 1.0   # 遮阳板伸出长度1米（更明显）
            self.window_sill_depth = 0.15   # 窗台深度15cm
            self.window_sill_height = 0.08  # 窗台高度8cm
            
            # 墙体和显示尺寸
            self.wall_width = 12.0    # 墙体宽度12米（更宽展示）
            self.wall_height = 8.0    # 墙体高度8米（更高展示）
            
            # 材质颜色定义（更真实的建筑材质）
            self.colors = {
                'wall_outer': '#E6E6E6',      # 外墙面 - 浅灰色
                'wall_inner': '#F8F8F8',      # 内墙面 - 近白色
                'wall_side': '#D0D0D0',       # 墙体侧面 - 中灰色
                'brick_line': '#B0B0B0',      # 砖缝线 - 深灰色
                'window_glass': '#87CEEB',    # 窗户玻璃 - 天蓝色
                'window_frame': '#8B4513',    # 窗框 - 棕色
                'window_sill': '#C0C0C0',     # 窗台 - 银灰色
                'shading_main': '#2E8B57',    # 遮阳板主体 - 海绿色
                'shading_support': '#8B4513', # 遮阳板支撑 - 棕色
                'door': '#8B4513'             # 门 - 棕色
            }
            
            # === 1. 绘制专业级墙体结构 ===
            self._draw_professional_wall_system(ax)
            
            # === 2. 处理和验证窗户数据 ===
            window_data = self._validate_and_process_window_data_local(facade_elements, solution)
            
            # === 3. 绘制完整窗户系统 ===
            for i, window_info in enumerate(window_data):
                x_3d, z_3d, w_3d, h_3d, window_type, frame_depth, shading_depth, shading_angle = window_info
                
                # 绘制窗户洞口
                self._draw_window_opening(ax, x_3d, z_3d, w_3d, h_3d)
                
                # 绘制窗户玻璃系统
                self._draw_professional_window_glass(ax, x_3d, z_3d, w_3d, h_3d)
                
                # 绘制窗台
                self._draw_enhanced_window_sill(ax, x_3d, z_3d, w_3d)
                
                # 根据窗户类型绘制窗框和遮阳板
                if window_type >= 1:  # 有窗框
                    self._draw_professional_window_frame(ax, x_3d, z_3d, w_3d, h_3d, frame_depth)
                
                if window_type >= 2:  # 有遮阳板
                    self._draw_professional_shading_system(ax, x_3d, z_3d, w_3d, h_3d, shading_depth, shading_angle)
            
            # === 4. 绘制门系统（如果有）===
            if facade_elements and hasattr(facade_elements, 'doors') and facade_elements.doors:
                for door in facade_elements.doors:
                    self._draw_professional_door(ax, door)
            
            # === 5. 添加专业光影和材质效果 ===
            self._add_professional_lighting_effects(ax)
            
            # === 6. 设置真实的3D视角 ===
            self._setup_realistic_3d_view(ax)
            
        except Exception as e:
            self.logger.error(f"Failed to draw 3D facade: {str(e)}")
            # 绘制专业错误提示
            self._draw_error_indicator(ax, str(e))
    
    def _draw_professional_wall_system(self, ax):
        """绘制专业级墙体系统（非立方体，真实建筑构造）"""
        try:
            from mpl_toolkits.mplot3d.art3d import Poly3DCollection
            
            # === 外墙面（主立面）- 专业砖墙纹理 ===
            outer_wall = [
                [[0, 0, 0], [self.wall_width, 0, 0], 
                 [self.wall_width, 0, self.wall_height], [0, 0, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(outer_wall, alpha=0.95, 
                                               facecolor=self.colors['wall_outer'], 
                                               edgecolor=self.colors['brick_line'], linewidth=1.2))
            
            # === 内墙面 - 室内装饰面 ===
            inner_wall = [
                [[0, self.wall_thickness, 0], [self.wall_width, self.wall_thickness, 0], 
                 [self.wall_width, self.wall_thickness, self.wall_height], [0, self.wall_thickness, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(inner_wall, alpha=0.8, 
                                               facecolor=self.colors['wall_inner'], 
                                               edgecolor='#E0E0E0', linewidth=0.8))
            
            # === 墙体顶面 - 显示墙体厚度和构造 ===
            top_wall = [
                [[0, 0, self.wall_height], [self.wall_width, 0, self.wall_height], 
                 [self.wall_width, self.wall_thickness, self.wall_height], [0, self.wall_thickness, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(top_wall, alpha=0.9, 
                                               facecolor=self.colors['wall_side'], 
                                               edgecolor='#A0A0A0', linewidth=0.8))
            
            # === 墙体左侧面 - 展示墙体厚度 ===
            left_wall = [
                [[0, 0, 0], [0, self.wall_thickness, 0], 
                 [0, self.wall_thickness, self.wall_height], [0, 0, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(left_wall, alpha=0.9, 
                                               facecolor=self.colors['wall_side'], 
                                               edgecolor='#A0A0A0', linewidth=0.8))
            
            # === 墙体右侧面 - 展示墙体厚度 ===
            right_wall = [
                [[self.wall_width, 0, 0], [self.wall_width, self.wall_thickness, 0], 
                 [self.wall_width, self.wall_thickness, self.wall_height], [self.wall_width, 0, self.wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(right_wall, alpha=0.9, 
                                               facecolor=self.colors['wall_side'], 
                                               edgecolor='#A0A0A0', linewidth=0.8))
            
            # === 添加专业砖墙纹理 ===
            self._add_professional_brick_texture(ax)
            
        except Exception as e:
            self.logger.error(f"Failed to draw professional wall system: {str(e)}")
            
            # === 墙体顶面 ===
            top_wall = [
                [[0, 0, wall_height], [wall_width, 0, wall_height], 
                 [wall_width, wall_thickness, wall_height], [0, wall_thickness, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(top_wall, alpha=0.7, facecolor='#CCCCCC', edgecolor='#888888', linewidth=0.5))
            
            # === 墙体左侧面 ===
            left_wall = [
                [[0, 0, 0], [0, wall_thickness, 0], [0, wall_thickness, wall_height], [0, 0, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(left_wall, alpha=0.7, facecolor='#BBBBBB', edgecolor='#888888', linewidth=0.5))
            
            
            right_wall = [
                [[wall_width, 0, 0], [wall_width, wall_thickness, 0], 
                 [wall_width, wall_thickness, wall_height], [wall_width, 0, wall_height]]
            ]
            ax.add_collection3d(Poly3DCollection(right_wall, alpha=0.7, facecolor='#BBBBBB', edgecolor='#888888', linewidth=0.5))
            
        except Exception as e:
            self.logger.error(f"Failed to draw professional wall system: {str(e)}")
    
    def _get_window_3d_coords(self, windex):
        """获取窗户的3D坐标"""
        try:
            # 使用默认窗户位置
            default_windows = [
                (1.5, 1.0, 1.5, 1.8),  # 左下窗户
                (4.25, 1.0, 1.5, 1.8), # 中下窗户
                (7.0, 1.0, 1.5, 1.8),  # 右下窗户
                (1.5, 3.5, 1.5, 1.8),  # 左上窗户
                (4.25, 3.5, 1.5, 1.8), # 中上窗户
                (7.0, 3.5, 1.5, 1.8)   # 右上窗户
            ]
            
            if index < len(default_windows):
                return default_windows[index]
            else:
                return (2.0, 2.0, 1.5, 1.8)  # 默认值
                
            （米）
                x_3d = x_2d * 
                z_3d = y_2d * 6.0   # Z轴高度，缩放到0-6米
                w_3d
                h_3d = h_2d * 6.0   # 窗户高度
            else:
    ）
                positions_3d = [
                    (1.5, 1.0, 1.5, 1.8),  #户
             中下窗户
                    (7.0, 1.0, 1.5, 1.8),  # 右下窗户
            
                    (4.25, 3.5, 1.5, 1.8窗户
                    (7.0, 3.5, 窗户
                ]
                if index < len(positions_3d):
                    x_3d, z_3d, w_3d, h_3d 
                else:
             5, 1.8
            
            return x_3d, z_3d, w_3d, h_3d
            
        except Exception as e:
            self.logger.error(f"获取窗户3D坐标失败: {str(e)}")
            return 2.0, 2.0, 1.5, 值
    
    def _dra
        """绘制窗户洞口（在墙体中的开口）"""
        try:
            n
            
            # 洞口内侧面（上）
    ng = [
                [[x, 0, z+h], [x+w, 0, z+h], [x+w, wall_thickness, z+h],
            ]
            
                                      h=0.5))
            
            # 洞口内侧面（下）
            bottom_ope= [
            
            ]
            ax.add_collection3d(Poly3DCollection(bottom_opening, alpha=0.8, 
                     h=0.5))
            
            # 洞口内侧面（左）
            left_opening = [
                [[x, 0, z z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(left_opening, alpha=0.8, 
                                               facecolor='#AAAAAA', e
            
            # 洞口内侧面（右）
             [
                [[, z+h]]
            ]
            ax.add_collection3d(Poly3DCollection(right_ope
                                               facecolor='#AAA
            
        except Exception as e:
            self.logger.error(f"绘")
    
    def _drah):
        """绘制窗户玻璃（带透"
        try:
            from mpl_toolkits.mplion
            
            # 外层玻璃
            outer_glass = [
                [[x, -window_depth*0.3, z], [x+w,
                 [x+w, -window_depth*0.3, z
            ]
            .3, 
                             idth=1))
            
            层玻璃
            inner_glass = [
                [[x, window_depth*0.7, z], [x+w, window_depth*0.7, z], 
z+h]]
       ]
            ax.add_collection3d(Poly3DCollection(inner_glas
       ))
            
    条（十字形）
            条
            h_divider = [
        
                 [x+w, window_deptr(e)}")视角失败: {s置3D(f"设ger.error    self.log      :
  on as eceptiExexcept             
       2, 6])
  [10,x_aspect(x.set_bo a         设置纵横比
        #   
               45)
 ev=25, azim=el.view_init(ax      果）
      轴测投影效 # 设置视角（                 
 ([])
     zticks     ax.set_   [])
    icks(   ax.set_yt   ([])
      set_xticks   ax.      ')
   bel('zla     ax.set_      
 l('')labe    ax.set_y
        ('')et_xlabel     ax.s
       隐藏）设置轴标签（          # 
              (0, 6)
x.set_zlim      a     
 ylim(-1, 1)     ax.set_10)
       , (0set_xlim   ax.         比例
 设置轴       #     
     try:
   例"""D视角和比置真实的3"设""
        elf, ax):ic_3d_view(srealist _setup_ef
    d   tr(e)}")
  {s效果失败:"添加光影er.error(f   self.logg       on as e:
  xcepti except E 
            .3)
      lpha=0rue, ad(T.gri        ax
    tgray')olor('lighect_edgaxis.pane.seax.z           ay')
 gr'lightgecolor(t_ed.pane.se ax.yaxis        
   lightgray')('ecoloret_edgis.pane.sax  ax.x          
 设置网格颜色  #      
               e
 .fill = Falsis.paneax        ax.z  e
  ll = False.fixis.panx.ya   a       lse
   = Faillpane.f ax.xaxis.
            设置背景颜色     #             try:
"
  效果"""添加光影""     ax):
    elf,fects(s_efngti _add_ligh
    def
    e)}"){str("绘制门失败: ror(ff.logger.er  sel
          on as e:xceptipt E exce  
            5))
     idth=0.e', linewangolor='oredgecd', color='gol       face                                    =1.0, 
    , alphaon(handlectiolleDC3d(Poly3tiond_collecax.ad               ]
   05]]
      dle_z+0. han0.08,[handle_x, -e_z+0.05], dl, -0.08, han+0.05handle_x         [ 
        dle_z-0.05],08, han, -0.le_x+0.05.05], [handle_z-0nd8, hax, -0.0e_     [[handl      [
      handle =       5
      h_3d * 0.d +le_z = z_3   hand        8
  0.d *w_3_3d + le_x = x  hand       # 门把手
                     
 =1))
     idthew lin321',lor='#654edgeco#8B4513', cecolor='    fa                                           .9, 
a=0el, alphanion(door_pectDCollPoly3ollection3d(ax.add_c            ]
        _3d]]
    .05, z_3d+h, -03d, [x_, z_3d+h_3d]w_3d, -0.05d+  [x_3      
         ],  z_3d3d, -0.05,[x_3d+w_, z_3d], x_3d, -0.05          [[    el = [
  pan      door_
      # 门板               
  
       ckness)wall_thi_3d,  h, w_3d,d, z_3d(ax, x_3ow_openingw_wind self._dra          
   # 门洞口        
            , 2.2
  0, 1.0, 8.5, h_3d = z_3d, w_3d  x_3d,         位置
        # 默认门       
              else: 6.0
      h_2d *3d =       h_          10.0
_3d = w_2d *  w            # 门从地面开始
  z_3d = 0           .0
        10d = x_2d *        x_3         
               0
box[1]) / 48 - door.br.bbox[3] = (doo     h_2d           ]) / 640
bbox[0] - door.(door.bbox[2 w_2d =               80
 ]) / 4x[3door.bbod = (480 - z_2            / 640
    ] bbox[0or. x_2d = do     
          r, 'bbox'):attr(doo    if has
         # 获取门的坐标             
     ion
     3DCollectolyd import P3d.art3lkits.mplotpl_too     from m            try:
"""
   真实的门"绘制"     "
   ckness):or, wall_thi, ax, doic_door(selfealist _draw_r    def")
    
{str(e)}户失败: "绘制默认窗error(fger.f.log    sel
        s e:ption aept Exce     exc  
            ss)
 _thickneion, wallrojectng_pkness, shadishading_thic                                                   w, h,
x, x, z, (ahadinglistic_seaf._draw_r     sel                 2个窗户有遮阳板
   < 2:  # 前f i  i           s)
       l_thicknesdepth, walrame__width, fme         fra                                         
        w, h,(ax, x, z,mec_window_fratiraw_realis   self._d                     # 前3个窗户有窗框
 i < 3:       if          板
     窗框，前2个有遮阳前3个窗户有用：      # 演示   
                else:   
        _thickness)allction, wading_projeickness, shding_th    sha                                            w, h,
   z, , x, axding(_shaisticreal_draw_       self.        
         :  # 有遮阳板_type >= 2indow  if w             
                        ess)
 ll_thickn, wapthdeth, frame__wid     frame                                               h, 
    z, w, , ame(ax, xfric_window_realist self._draw_                    
   窗框# 有e >= 1:  ndow_typ       if wi                  
         
      e 0 elsw_types)windoion.olut i < len(sypes[i] ifw_twindo= solution.ow_type    wind              pes'):
    'window_tyn,utio(soltrsatnd ha solution a if               和遮阳板
决方案绘制窗框# 根据解             
             
      ess)hickn.12, wall_t z, w, 0_sill(ax, x,draw_windowf._         sel   
        # 绘制窗台    
                   )
     dow_depth, win, x, z, w, h_glass(ax_draw_window     self.    
       户玻璃   # 绘制窗              
           kness)
    wall_thic h, , x, z, w,pening(axraw_window_o    self._d      洞口
            # 绘制窗户          ions):
ault_positefumerate(d) in en, z, w, h, (x for i           
           ]
             # 右上
  1.8) 5,  3.5, 1.    (7.0,          8), # 中上
  .5, 1.5, 1..25, 3          (4    # 左上
    .8), 3.5, 1.5, 11.5,   (  
           .8),  # 右下.5, 1(7.0, 1.0, 1            
    , # 中下1.5, 1.8)(4.25, 1.0,            左下
      5, 1.8),  #, 1.  (1.5, 1.0             ns = [
 itiolt_pos    defau）
        位置（3x2网格认窗户     # 默     ry:
         t）"""
 数据时绘制默认窗户（当没有真实      """ction):
  ding_projeness, shaing_thickepth, shad, frame_dwidthframe_                                pth, 
window_des, hicknestion, wall_t, soluaxself, 3d(ows_efault_windef _draw_d    
    dr(e)}")
: {stf"绘制遮阳板失败r.error( self.logge            as e:
ptionexcept Exce    
             .5))
   =0inewidth4321', lr='#65, edgecolor='#8B4513'colo       face                                     
   alpha=0.9, port_right, tion(suply3DColleclection3d(Poax.add_col           ]
       
      ss]]kneng_thics+shadiing_z_pond-0.1, shadg_y_eadin+0.05, sh [x+w            ess], 
    ing_thickn_pos+shadding_z1, shad-0.ding_y_enha[x+w+0.1, s              s], 
   ing_z_pot+0.1, shad_y_star.1, shading+0x+w    [      
       pos], _z_, shadingrt+0.1tahading_y_sx+w+0.05, s[[          [
      t_right = suppor      右支撑
             #      
            idth=0.5))
21', linew6543dgecolor='#'#8B4513', eacecolor=   f                                       9, 
     lpha=0.rt_left, aon(suppoollecti(Poly3DCn3dlectio ax.add_col
              ]]
         g_thickness]s+shadinhading_z_po_end-0.1, sng_yx-0.1, shadi   [          ess], 
    ading_thicknng_z_pos+shshadi1, g_y_end-0., shadin0.05       [x-       s], 
   g_z_po.1, shadinng_y_start+0shadi [x-0.05,                s], 
 z_poing_ shadtart+0.1,hading_y_s[[x-0.1, s           = [
      port_left   sup       
  支撑      # 左===
      == 遮阳板支撑结构  =     #    
          )
     idth=0.5), linewdge_colorr=shading_e', edgecoloF7A1Fcolor='#1    face                                       .8, 
    pha=0alt, ng_righction(shadile3DCollyd(Pocollection3   ax.add_           ]
          ckness]]
g_thi+shadin_posshading_zt, y_star shading_ [x+w+0.2,              
   ckness],_thi+shading_z_posinghad, sendy_ng_0.2, shadi    [x+w+        , 
     ing_z_pos]had s_end,ding_ysha2,    [x+w+0.          , 
    z_pos]rt, shading_ding_y_stax+w+0.2, sha[[          
      ht = [ding_rig      sha端面
       # 遮阳板右         
     
         ))h=0.5linewidtge_color, ng_edshadiecolor= edg7A1F','#1F  facecolor=                                      
       , =0.8alphaft, _leingion(shadllect(Poly3DCo3dlectiond_colax.ad                   ]
 s]]
    cknesg_thios+shadining_z_pt, shadstarng_y_dix-0.2, sha           [
      ess], ng_thicknos+shadiz_pshading_d, _enng_y2, shadix-0.   [              _pos], 
ng_z, shadiading_y_endsh-0.2,          [x       _pos], 
  shading_ztart, shading_y_s [[x-0.2,      [
         t = _lefshading        板左端面
     # 遮阳              
   1))
      h=r, linewidtlocohading_edge_ecolor=s, edg#228B22' facecolor='                                          9, 
    lpha=0.nt, ashading_frotion(lec(Poly3DColion3ddd_collect   ax.a
         ]            hickness]]
s+shading_t_z_podingrt, shang_y_sta-0.2, shadi         [x       
 ickness], _thingshads+ding_z_po sha_start, shading_y   [x+w+0.2,              os], 
_p, shading_zrty_staading_.2, sh+0        [x+w         ], 
posz_rt, shading_ding_y_sta.2, sha-0[[x              = [
   frontg_   shadin        遮阳板前端面
       #    
               =0.5))
, linewidtholordge_c_eingadlor=shD32', edgeco2Cor='#3    facecol                                 
          ,  alpha=0.7m,ading_bottotion(shecllPoly3DCod(ion3d_collectad     ax. ]
          
        ing_z_pos]]shadg_y_end, 2, shadin   [x-0.          
    _z_pos], hadingg_y_end, s shadin+w+0.2,[x              s], 
   hading_z_pog_y_start, s, shadin+w+0.2 [x           s], 
     ding_z_po shang_y_start,hadi.2, s       [[x-0        om = [
 ading_bott       sh  遮阳板底面
         # 
                1))
  idth=newe_color, liing_edglor=shadgeco, edor=shading_colloreco fac                                            0.8, 
  top, alpha=shading_DCollection(y3d(Pollection3add_col  ax.             ]
     ness]]
    ding_thick+shading_z_posy_end, sha shading_     [x-0.2,        ess], 
    knding_thic+shahading_z_posy_end, s shading_.2,w+0   [x+         s], 
     ng_thicknes_pos+shadiing_zrt, shadg_y_sta shadin   [x+w+0.2,              
hickness], ading_tg_z_pos+shart, shadinading_y_stsh, .2[[x-0        
        g_top = [     shadin        # 遮阳板顶面
          
 体 === 遮阳板主   # ===                
     窗户上方10cm
 h + 0.1  # = z + pos shading_z_   
        ess + 0.05icknd = wall_thshading_y_en       
     ng_projectiot = -shading_y_starhadin    s方）
        板位置（在窗户上   # 遮阳 
                   00'
 #0064 = '_edge_color    shading        # 森林绿
 '#228B22'  ng_color =     shadi      
 颜色 遮阳板  #           
    on
       ly3DCollectiPomport 3d.art3d ikits.mplotm mpl_tool       frotry:
     "
        板""真实的立体遮阳"""绘制     :
   ckness)wall_thiojection, hading_prckness, sg_thi shadinw, h,ax, x, z, ng(self, listic_shadi _draw_rea
    def   
 (e)}")tr失败: {s(f"绘制窗框gger.errorself.lo         
   ion as e:xcept except E        
         =0.5))
  thnewidr, licologe_rame_edolor=f2D', edgec052='#A   facecolor                                          a=0.8, 
  op, alph_tideion(frame_sect(Poly3DCollcollection3ddd_x.a         a    ]
     
      ame_width]]+frckness, z+hth, wall_thix-frame_widh], [me_widtz+h+fra, thicknessll_waidth, me_w[x+w+fra                
 e_width], fram, z+h+rame_depth, -fame_width], [x+w+frdth_wime z+h+fra_depth,-frameh, ame_widt    [[x-fr           
 op = [ide_tame_s        fr    架侧面（显示厚度）
         # 框    
           )
5)ewidth=0.in l_edge_color,lor=frameco, edgeme_coloror=fraecol  fac                                              
8,alpha=0._inner, metop_fraon(CollectiPoly3Dollection3d(  ax.add_c                ]
     ]]
 thwid z+h+frame_ess,knl_thicwalrame_width, -fe_width], [x, z+h+framss_thicknewallame_width,      [x+w+fr           z+h], 
 ess, wall_thickn, rame_width+h], [x+w+f, zicknessdth, wall_th[[x-frame_wi            = [
     innere_ramtop_f        上框内侧
          #    ）===
   接到墙体（连= 内框架   # ==        
           
  h=1))inewidtlor, l_co_edgelor=framecolor, edgeme_cor=frafacecolo                                         
       alpha=0.9, _outer,amefrtion(right_oly3DCollection3d(Plecadd_col    ax.
                  ]
  pth, z+h]]-frame_deh], [x+w, me_depth, z+dth, -frame_wi [x+w+fra               pth, z], 
 de -frame_dth,e_wiframx+w+ z], [ame_depth,-frx+w,        [[
          = [rame_outer_fht   rig框
         # 右   
                   dth=1))
  olor, linewiframe_edge_cr=olor, edgecme_coloor=fraecolac f                                    
          lpha=0.9, ter, aft_frame_ouion(leectd(Poly3DCollction3x.add_colle    a      ]
     
         h]]e_depth, z+h, -framdt-frame_with, z+h], [x_depme -fra       [x,       z], 
    rame_depth,z], [x, -fh, rame_depth, -fame_widt      [[x-fr          r = [
_frame_oute        left框
         # 左
                
   dth=1))wi_color, lineedgeame_=fr, edgecolorolorrame_clor=fceco     fa                                         .9, 
  alpha=0uter,ttom_frame_oction(boolleon3d(Poly3DCollecti   ax.add_c     
      ]
          pth, z]]h, -frame_deme_widt [x-fra z],_depth,idth, -frameframe_ww+    [x+   
          _width], frameth, z-ep-frame_dame_width, +fridth], [x+wframe_wpth, z-, -frame_dedth[[x-frame_wi           
      [uter =ttom_frame_o bo        
    下框 #  
           
          1))linewidth=lor, come_edge_gecolor=fra, edcolorme_cecolor=fra        fa                                  
     pha=0.9, e_outer, alframction(top_(Poly3DColleion3dllect   ax.add_co        ]
            dth]]
 _wiramez+h+fame_depth, dth, -frx-frame_width], [+h+frame_wi_depth, zidth, -frame_wframe[x+w+                 
 h],z+_depth, th, -frame_widamex+w+fr, [th, z+h]_dep, -framewidthrame_[[x-f           r = [
     _frame_oute   top
          # 上框    ===
       外框架 =      # == 
                  4321'
lor = '#65come_edge_        fra色
    3'  # 棕'#8B451lor = corame_          f  # 窗框颜色
                
  
      DCollectionPoly33d import lot3d.artoolkits.mp  from mpl_t              try:

    立体窗框""""""绘制真实的     kness):
   ll_thicdepth, warame_ame_width, fz, w, h, fr ax, x, elf,dow_frame(s_winrealistic  def _draw_
  ")
    {str(e)}(f"绘制窗台失败: errorer.elf.logg       s
     s e:Exception a except       
            
 5))width=0.A0', line'#A0A0dgecolor=', eC0C0C0facecolor='#                                               =0.9, 
ont, alpha_frtion(sillly3DColleclection3d(Pocoldd_        ax.a  ]
       
       ]]h, zll_dept0.05, -six-z], [th, , -sill_dep0.05     [x+w+        
     ight],, z-sill_heill_depth5, -s [x+w+0.0_height],epth, z-sill_d5, -sill[[x-0.0            
    l_front = [     sil    窗台前面
           #   
       
       ewidth=1))lin'#A9A9A9', , edgecolor=3'olor='#D3D3D      facec                                   9, 
      pha=0.sill_top, alction((Poly3DCollellection3dx.add_co     a
              ]     ]
 z]ss+0.02,l_thickne[x-0.05, wal], 2, zess+0.0cknwall_thi [x+w+0.05,             
     th, z],sill_dep+0.05, -th, z], [x+w_dep.05, -sill    [[x-0           p = [
 _to  sill         
     # 窗台顶面 
               cm
    05  # 窗台厚度5ht = 0.heigill_    s
               on
     3DCollectiport Polyd imt3d.art3ts.mplom mpl_toolki         frotry:
   
        窗台"""绘制"        ""ess):
 wall_thicknh,ll_deptx, z, w, siself, ax, ndow_sill(def _draw_wi    
    r(e)}")
败: {st绘制窗户玻璃失.error(f"erogglf.l se        :
    as eiont Except   excep 
         5))
       inewidth=0.'gray', llor=co, edgehite''wlor=      faceco                                  9, 
       0.er, alpha=ion(v_dividDCollectly3tion3d(Podd_collec       ax.a        ]
 ]
        0.7, z+h]depth*ow_ind wx+w/2-0.02,+h], [0.7, zdepth*ow_0.02, windw/2+     [x+             
*0.3, z],ndow_depth, -wi0.02/2+ [x+wh*0.3, z],dow_dept02, -winw/2-0.      [[x+
          vider = [   v_di     隔条
      # 垂直分      
          ))
      newidth=0.5, liy'lor='gra edgecor='white',    facecolo                                       , 
    =0.9der, alphavi_diollection(h3DClyction3d(Po.add_colle      ax  ]
            2]]
    +h/2+0.0epth*0.7, z[x, window_d.02],  z+h/2+0th*0.7,],+h/2-0.02.3, zdepth*0window_2], [x+w, -.0z+h/2-0h*0.3, ptw_dewindo[x, -         [# 水平分隔璃分隔  # 玻      0.5dth=e', linewilor='darkbludgecotcyan', eolor='lighacec       f                                 .2, a=0s, alph     h*0.7, ptdendow_ [x, wi7, z+h],w_depth*0.ndo wi [x+w,                # 内, linew='blue'ecoloredglue', tbecolor='ligh       fac           ass, alpha=0r_gllection(outeolDCd(Poly3_collection3ax.add, z+h]]_depth*0.3window+h], [x, -, 3, z]epth*0.window_d -ly3DCollectort Pod imprt3.a3dot明效果）""ndow_depth, wi z, w, lf, ax, x,w_glass(se_window)}: {str(e失败户洞口制窗5))width=0.8888', line'#88or=colA', edgeAA.8, , alpha=0ning, 0+h], [x+wss, zckne wall_thi, [x+w,ess, z]cknwall_thiz], [x+w, w, 0, x+ning =ight_operth=0.5))id888', linewor='#888dgecol+h], [x, 0,s, zl_thicknes], [x, walickness, zl_th, [x, wal]ewidt8888', lin'#88 edgecolor=A',AAAAAcecolor='#      fa                    ickness, z]][x, wall_thness, z], ickthall_ z], [x+w, w 0,x+w,x, 0, z], [  [[  g innewidt8888', lin'#88, edgecolor=AAAA'lor='#AA faceco        ha=0.8, ing, alpion(top_openollect3DClyion3d(Poollectd_cax.ads, z+h]]icknesth, wall_[x  top_openi       lectioy3DCol Pol importd.art3d.mplot3pl_toolkitsom mfress): wall_thickn z, w, h,ax, x,elf, ing(sndow_openw_wi# 默认1.8  2.0, 1.= 2.0,  h_3d _3d,_3d, w   x_3d, z    3d[index]ions_itos p=1.8)   # 右上1.5, # 中上), 上窗户, 1.8),  # 左.5 3.5, 11.5,     (   .5, 1.8), #0, 1  (4.25, 1.       左下窗网格布局3x2窗户位置（ 使用默认    #        窗户宽度  # 0.0 = w_2d * 1缩放到0-10米10.0  #  转换为3D坐标   # 48.bbox[1]) / ] - windoww.bbox[3indo# 规范化到0-1 640   'bbox'indow,dow, in(e)} {str"绘制真实墙体失败:error(flinewidth=0BBBBB', '#B, edgecolor=#DDDDDD'7,lpha=0. a_wall,htig 0, wall_hll_width,ght], [waei面 ====== 墙体右侧# widtB', liner='#BBBBBdgecolo', er='#DDDDDD    facecolo                  .7, _h[0, 0, wallght], l_heiss, walwall_thickne, , [00]kness, =0.inewidth', lor='#BBBBBB, edgecol='#DDDDDD'colorace     f   ,  alpha=0.7p_wall,ton(iollectt] wall_heighs,nesck0.5))ewidth=', lin#DDDDDD edgecolor='F5','#F5F5r=height]kness, wall_hic [0, wall_t_height],, wallckness wall_thih,_widt     [wall=1))ewidthCC', lincolor='#CCCCE8', edgelor='#E8E8   faceco      pha=0.l, alon(outer_wallecti(Poly3DColction3dht]igl_he 0, walight], [0,_hedth, 0, wallall_wi [w 0, 0],
                        
                        x_3d = x_2d * 10
                        z_3d = z_2d * 6
                        w_3d = w_2d * 10
                        h_3d = h_2d * 6
                        
                        door_verts = [
                            [[x_3d, -0.1, 0], [x_3d+w_3d, -0.1, 0], 
                             [x_3d+w_3d, -0.1, h_3d], [x_3d, -0.1, h_3d]]
                        ]
                        ax.add_collection3d(Poly3DCollection(door_verts, alpha=0.7, facecolor='lightyellow', edgecolor='red'))
            
            # 绘制遮阳设施（如果有独立数据）
            if facade_elements and hasattr(facade_elements, 'shading') and facade_elements.shading:
                for shading in facade_elements.shading:
                    if hasattr(shading, 'bbox'):
                        x_2d = shading.bbox[0] / 640
                        z_2d = (480 - shading.bbox[3]) / 480
                        w_2d = (shading.bbox[2] - shading.bbox[0]) / 640
                        h_2d = (shading.bbox[3] - shading.bbox[1]) / 480
                        
                        x_3d = x_2d * 10
                        z_3d = z_2d * 6
                        w_3d = w_2d * 10
                        h_3d = h_2d * 6
                        
                        shading_verts = [
                            [[x_3d, -0.3, z_3d], [x_3d+w_3d, -0.3, z_3d], 
                             [x_3d+w_3d, 0, z_3d], [x_3d, 0, z_3d]]
                        ]
                        ax.add_collection3d(Poly3DCollection(shading_verts, alpha=0.6, facecolor='lightgreen', edgecolor='green'))
            
            # 绘制框架（如果有独立数据）
            if facade_elements and hasattr(facade_elements, 'frames') and facade_elements.frames:
                for frame in facade_elements.frames:
                    if hasattr(frame, 'bbox'):
                        x_2d = frame.bbox[0] / 640
                        z_2d = (480 - frame.bbox[3]) / 480
                        w_2d = (frame.bbox[2] - frame.bbox[0]) / 640
                        h_2d = (frame.bbox[3] - frame.bbox[1]) / 480
                        
                        x_3d = x_2d * 10
                        z_3d = z_2d * 6
                        w_3d = w_2d * 10
                        h_3d = h_2d * 6
                        
                        # 框架绘制为线框
                        frame_lines = [
                            [[x_3d, -0.05, z_3d], [x_3d+w_3d, -0.05, z_3d], 
                             [x_3d+w_3d, -0.05, z_3d+h_3d], [x_3d, -0.05, z_3d+h_3d]]
                        ]
                        ax.add_collection3d(Poly3DCollection(frame_lines, alpha=0.8, facecolor='none', edgecolor='brown', linewidth=2))
            
        except Exception as e:
            self.logger.error(f"Failed to draw 3D facade view: {str(e)}")
            # 添加错误指示
            ax.text(5, 4, 3, '3D Drawing Error', fontsize=10, ha='center', va='center',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='red', alpha=0.7))
    
    def _add_solutions_legend(self, ax):
        """Add solution legend in English"""
        try:
            import matplotlib.patches as patches
            
            # Legend elements in English
            legend_elements = [
                ('Wall', 'lightgray', 'black'),
                ('窗户', 'lightblue', 'blue'),
                ('窗框', 'none', 'brown'),
                ('遮阳', 'lightgreen', 'green')
            ]
            
            for i, (label, facecolor, edgecolor) in enumerate(legend_elements):
                x = i * 0.25 + 0.1
                rect = patches.Rectangle((x, 0.3), 0.05, 0.4, 
                                       facecolor=facecolor, edgecolor=edgecolor, linewidth=2)
                ax.add_patch(rect)
                ax.text(x + 0.07, 0.5, label, fontsize=10, verticalalignment='center')
            
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            
        except Exception as e:
            self.logger.error(f"Failed to add legend: {str(e)}")
    
    def _add_performance_comparison_table(self, ax, selected_solutions):
        """Add performance comparison table in English"""
        try:
            if not selected_solutions:
                ax.text(0.5, 0.5, 'No Performance Data Available', 
                       ha='center', va='center', fontsize=12)
                return
            
            # Create table data in English
            headers = ['Solution', 'Energy\n(kWh/m²)', 'Thermal\nPerformance', 'Cost\n(CNY)']
            data = []
            
            for name, solution in selected_solutions.items():
                row = [
                    name,
                    f"{getattr(solution, 'energy_consumption', 0):.1f}",
                    f"{getattr(solution, 'thermal_performance', 0):.2f}",
                    f"{getattr(solution, 'renovation_cost', 0):,.0f}"
                ]
                data.append(row)
            
            # 绘制表格
            table = ax.table(cellText=data, colLabels=headers, 
                           cellLoc='center', loc='center',
                           colWidths=[0.25, 0.25, 0.25, 0.25])
            
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1, 2)
            
            # 设置表格样式
            for (i, j), cell in table.get_celld().items():
                if i == 0:  # 标题行
                    cell.set_text_props(weight='bold')
                    cell.set_facecolor('#E6E6FA')
                else:
                    cell.set_facecolor('#F8F8FF')
                cell.set_edgecolor('black')
                cell.set_linewidth(1)
            
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            
        except Exception as e:
            self.logger.error(f"Failed to add performance table: {str(e)}")


def create_visualization_manager() -> VisualizationManager:
    """
    创建可视化管理器实例
    
    Returns:
        配置好的可视化管理器
    """
    return VisualizationManager()