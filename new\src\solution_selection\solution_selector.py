"""
解决方案选择器
从帕累托最优解集中选择四个维度的最佳解决方案
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
from datetime import datetime

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import SolutionSelectionError, handle_exception
from ..core.data_structures import (
    ObjectiveResults, OptimizationResults, FacadeIndividual
)
from ..core.utils import MathUtils


class SolutionSelector:
    """
    解决方案选择器
    
    功能：
    1. 从帕累托前沿选择四维度最佳解
    2. 综合最佳解选择
    3. 单目标最佳解选择
    4. 平衡性解决方案选择
    5. 解决方案多样性分析
    6. 决策支持和推荐
    """
    
    def __init__(self):
        """初始化解决方案选择器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取解决方案选择配置
        solution_config = self.config.get_section('solution_selection')
        self.selection_config = solution_config.get('selection_parameters', {})
        
        # 权重配置
        self.weights = self.selection_config.get('objective_weights', {})
        self.energy_weight = self.weights.get('energy_weight', 0.4)
        self.thermal_weight = self.weights.get('thermal_weight', 0.3)
        self.cost_weight = self.weights.get('cost_weight', 0.3)
        
        # 选择策略配置
        self.strategies = self.selection_config.get('selection_strategies', {})
        self.use_clustering = self.strategies.get('use_clustering', True)
        self.cluster_count = self.strategies.get('cluster_count', 5)
        self.diversity_threshold = self.strategies.get('diversity_threshold', 0.05)  # 放宽相似性阈值
        
        # 评价准则配置
        self.criteria = self.selection_config.get('evaluation_criteria', {})
        self.min_energy_threshold = self.criteria.get('min_energy_threshold', 30.0)
        self.min_comfort_hours = self.criteria.get('min_comfort_hours', 5000)
        self.max_cost_per_area = self.criteria.get('max_cost_per_area', 2000.0)
        
        self.logger.info("解决方案选择器初始化完成")
    
    @handle_exception
    def select_four_dimension_solutions(self, optimization_results: OptimizationResults) -> Dict[str, ObjectiveResults]:
        """
        选择四维度解决方案
        
        Args:
            optimization_results: 优化结果
            
        Returns:
            四个维度的最佳解决方案
            
        Raises:
            SolutionSelectionError: 选择失败时抛出
        """
        with LogContext("四维度解决方案选择", self.logger):
            try:
                pareto_solutions = optimization_results.pareto_solutions
                
                if not pareto_solutions:
                    raise SolutionSelectionError("没有可用的帕累托解")
                
                # 检查pareto_solutions的类型并处理
                objective_solutions = []
                for sol in pareto_solutions:
                    if isinstance(sol, ObjectiveResults):
                        # 如果已经是ObjectiveResults类型，直接使用
                        objective_solutions.append(sol)
                    else:
                        # 如果是FacadeIndividual类型，进行转换
                        objective_result = self._convert_to_objective_results(sol)
                        objective_solutions.append(objective_result)
                
                # 筛选可行解
                feasible_solutions = [sol for sol in objective_solutions if sol.is_feasible]
                
                if not feasible_solutions:
                    self.logger.warning("没有可行解，使用全部帕累托解")
                    feasible_solutions = objective_solutions
                
                # 1. 综合最佳解
                comprehensive_best = self._select_comprehensive_best(feasible_solutions)
                
                # 2. 能耗最佳解
                energy_best = self._select_energy_best(feasible_solutions)
                
                # 3. 热工性能最佳解
                thermal_best = self._select_thermal_best(feasible_solutions)
                
                # 4. 成本最佳解
                cost_best = self._select_cost_best(feasible_solutions)
                
                # 验证解的多样性
                selected_solutions = {
                    'comprehensive_best': comprehensive_best,
                    'energy_best': energy_best,
                    'thermal_best': thermal_best,
                    'cost_best': cost_best
                }
                
                # 确保解的多样性
                diversified_solutions = self._ensure_solution_diversity(selected_solutions, feasible_solutions)
                
                self.logger.info(f"四维度解决方案选择完成: 综合最佳={comprehensive_best.individual_id}, "
                               f"能耗最佳={energy_best.individual_id}, "
                               f"热工最佳={thermal_best.individual_id}, "
                               f"成本最佳={cost_best.individual_id}")
                
                return diversified_solutions
                
            except Exception as e:
                raise SolutionSelectionError(f"四维度解决方案选择失败: {str(e)}") from e
    
    def _convert_to_objective_results(self, individual: FacadeIndividual) -> ObjectiveResults:
        """将FacadeIndividual转换为ObjectiveResults"""
        try:
            from datetime import datetime
            
            # 处理 constraint_violations 字段，可能是字典或浮点数
            if isinstance(individual.constraint_violations, dict):
                constraint_violations = sum(individual.constraint_violations.values())
            elif isinstance(individual.constraint_violations, (int, float)):
                constraint_violations = float(individual.constraint_violations)
            else:
                constraint_violations = 0.0
            
            objective_result = ObjectiveResults(
                individual_id=individual.individual_id,
                energy_consumption=individual.energy_consumption,
                thermal_performance=individual.thermal_performance,
                renovation_cost=individual.renovation_cost,
                constraint_violations=constraint_violations,
                is_feasible=individual.rank == 0,  # Pareto前沿解认为是可行的
                evaluation_timestamp=datetime.now().isoformat(),
                # 复制窗户设计参数用于可视化
                window_positions=individual.window_positions.copy() if individual.window_positions else [],
                window_sizes=individual.window_sizes.copy() if individual.window_sizes else [],
                window_types=individual.window_types.copy() if individual.window_types else [],
                frame_depths=individual.frame_depths.copy() if individual.frame_depths else [],
                shading_depths=individual.shading_depths.copy() if individual.shading_depths else [],
                shading_angles=individual.shading_angles.copy() if individual.shading_angles else []
            )
            
            return objective_result
        except Exception as e:
            self.logger.error(f"转换FacadeIndividual到ObjectiveResults失败: {str(e)}")
            # 返回一个基本的ObjectiveResults对象作为备选
            return ObjectiveResults(
                individual_id=individual.individual_id,
                energy_consumption=individual.energy_consumption,
                thermal_performance=individual.thermal_performance,
                renovation_cost=individual.renovation_cost
            )
    
    def _select_comprehensive_best(self, solutions: List[ObjectiveResults]) -> ObjectiveResults:
        """选择综合最佳解"""
        try:
            best_solution = None
            best_score = float('-inf')
            
            for solution in solutions:
                # 计算综合评分
                score = self._calculate_comprehensive_score(solution)
                
                if score > best_score:
                    best_score = score
                    best_solution = solution
            
            return best_solution or solutions[0]
            
        except Exception as e:
            self.logger.error(f"选择综合最佳解失败: {str(e)}")
            return solutions[0]
    
    def _select_energy_best(self, solutions: List[ObjectiveResults]) -> ObjectiveResults:
        """选择能耗最佳解"""
        try:
            # 筛选满足基本要求的解
            qualified_solutions = [
                sol for sol in solutions 
                if sol.thermal_performance < 0.8 and sol.renovation_cost < 200000
            ]
            
            if not qualified_solutions:
                qualified_solutions = solutions
            
            # 选择能耗最低的解
            energy_best = min(qualified_solutions, key=lambda x: x.energy_consumption)
            
            return energy_best
            
        except Exception as e:
            self.logger.error(f"选择能耗最佳解失败: {str(e)}")
            return min(solutions, key=lambda x: x.energy_consumption)
    
    def _select_thermal_best(self, solutions: List[ObjectiveResults]) -> ObjectiveResults:
        """选择热工性能最佳解"""
        try:
            # 筛选满足基本要求的解
            qualified_solutions = [
                sol for sol in solutions 
                if sol.energy_consumption < 150 and sol.renovation_cost < 200000
            ]
            
            if not qualified_solutions:
                qualified_solutions = solutions
            
            # 选择热工性能最佳的解（thermal_performance越小越好）
            thermal_best = min(qualified_solutions, key=lambda x: x.thermal_performance)
            
            return thermal_best
            
        except Exception as e:
            self.logger.error(f"选择热工性能最佳解失败: {str(e)}")
            return min(solutions, key=lambda x: x.thermal_performance)
    
    def _select_cost_best(self, solutions: List[ObjectiveResults]) -> ObjectiveResults:
        """选择成本最佳解"""
        try:
            # 筛选满足基本性能要求的解
            qualified_solutions = [
                sol for sol in solutions 
                if sol.energy_consumption < 120 and sol.thermal_performance < 0.6
            ]
            
            if not qualified_solutions:
                qualified_solutions = solutions
            
            # 选择成本最低的解
            cost_best = min(qualified_solutions, key=lambda x: x.renovation_cost)
            
            return cost_best
            
        except Exception as e:
            self.logger.error(f"选择成本最佳解失败: {str(e)}")
            return min(solutions, key=lambda x: x.renovation_cost)
    
    def _calculate_comprehensive_score(self, solution: ObjectiveResults) -> float:
        """计算综合评分"""
        try:
            # 归一化处理（假设的最大值和最小值）
            energy_norm = max(0, 1 - solution.energy_consumption / 200)  # 能耗越低越好
            thermal_norm = max(0, 1 - solution.thermal_performance)       # 热工性能越小越好
            cost_norm = max(0, 1 - solution.renovation_cost / 300000)     # 成本越低越好
            
            # 加权综合评分
            comprehensive_score = (
                energy_norm * self.energy_weight +
                thermal_norm * self.thermal_weight +
                cost_norm * self.cost_weight
            )
            
            # 可行性惩罚
            if not solution.is_feasible:
                comprehensive_score *= 0.5
            
            # 严重约束违反惩罚
            if solution.constraint_violations > 1.0:
                comprehensive_score *= 0.3
            
            return comprehensive_score
            
        except Exception:
            return 0.0
    
    def _ensure_solution_diversity(self, selected_solutions: Dict[str, ObjectiveResults],
                                 all_solutions: List[ObjectiveResults]) -> Dict[str, ObjectiveResults]:
        """确保解的多样性"""
        try:
            solutions_list = list(selected_solutions.values())
            solution_names = list(selected_solutions.keys())
            
            # 计算解之间的距离
            for i in range(len(solutions_list)):
                for j in range(i + 1, len(solutions_list)):
                    distance = self._calculate_solution_distance(solutions_list[i], solutions_list[j])
                    
                    # 如果两个解太相似，替换相似度较低的那个
                    if distance < self.diversity_threshold:
                        self.logger.info(f"检测到相似解: {solution_names[i]} 和 {solution_names[j]}")
                        
                        # 寻找替代解
                        replacement = self._find_diverse_replacement(
                            solutions_list[j], solutions_list, all_solutions
                        )
                        
                        if replacement:
                            selected_solutions[solution_names[j]] = replacement
                            solutions_list[j] = replacement
                            self.logger.info(f"替换解 {solution_names[j]} 为 {replacement.individual_id}")
            
            return selected_solutions
            
        except Exception as e:
            self.logger.error(f"确保解多样性失败: {str(e)}")
            return selected_solutions
    
    def _calculate_solution_distance(self, sol1: ObjectiveResults, sol2: ObjectiveResults) -> float:
        """计算两个解之间的距离"""
        try:
            # 更合理的归一化目标函数值 - 基于实际数据范围
            energy_diff = abs(sol1.energy_consumption - sol2.energy_consumption) / 100  # 能耗范围约0-100
            thermal_diff = abs(sol1.thermal_performance - sol2.thermal_performance) / 0.5  # 热工性能范围约0-0.5
            cost_diff = abs(sol1.renovation_cost - sol2.renovation_cost) / 50000  # 成本范围约0-50000

            # 欧几里得距离
            distance = np.sqrt(energy_diff**2 + thermal_diff**2 + cost_diff**2)

            return distance

        except Exception:
            return 1.0
    
    def _find_diverse_replacement(self, current_solution: ObjectiveResults,
                                existing_solutions: List[ObjectiveResults],
                                all_solutions: List[ObjectiveResults]) -> Optional[ObjectiveResults]:
        """寻找多样化的替代解"""
        try:
            best_replacement = None
            max_min_distance = 0.0
            
            for candidate in all_solutions:
                if candidate.individual_id == current_solution.individual_id:
                    continue
                
                # 检查候选解是否已被选择
                already_selected = any(
                    candidate.individual_id == existing.individual_id 
                    for existing in existing_solutions
                )
                
                if already_selected:
                    continue
                
                # 计算候选解到所有现有解的最小距离
                min_distance = min(
                    self._calculate_solution_distance(candidate, existing)
                    for existing in existing_solutions
                    if existing.individual_id != current_solution.individual_id
                )
                
                # 选择最小距离最大的候选解
                if min_distance > max_min_distance:
                    max_min_distance = min_distance
                    best_replacement = candidate
            
            return best_replacement
            
        except Exception:
            return None
    
    def analyze_solution_characteristics(self, solutions: Dict[str, ObjectiveResults]) -> Dict[str, Any]:
        """分析解决方案特征"""
        try:
            analysis = {
                'solution_comparison': {},
                'performance_ranges': {},
                'trade_offs': {},
                'recommendations': []
            }
            
            # 解决方案对比
            for name, solution in solutions.items():
                analysis['solution_comparison'][name] = {
                    'energy_consumption': solution.energy_consumption,
                    'thermal_performance': solution.thermal_performance,
                    'renovation_cost': solution.renovation_cost,
                    'comprehensive_score': self._calculate_comprehensive_score(solution),
                    'is_feasible': solution.is_feasible,
                    'constraint_violations': solution.constraint_violations
                }
            
            # 性能范围分析
            if solutions:
                energy_values = [sol.energy_consumption for sol in solutions.values()]
                thermal_values = [sol.thermal_performance for sol in solutions.values()]
                cost_values = [sol.renovation_cost for sol in solutions.values()]
                
                analysis['performance_ranges'] = {
                    'energy_range': {'min': min(energy_values), 'max': max(energy_values)},
                    'thermal_range': {'min': min(thermal_values), 'max': max(thermal_values)},
                    'cost_range': {'min': min(cost_values), 'max': max(cost_values)}
                }
            
            # 权衡分析
            analysis['trade_offs'] = self._analyze_trade_offs(solutions)
            
            # 生成推荐
            analysis['recommendations'] = self._generate_solution_recommendations(solutions)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"分析解决方案特征失败: {str(e)}")
            return {}
    
    def _analyze_trade_offs(self, solutions: Dict[str, ObjectiveResults]) -> Dict[str, str]:
        """分析权衡关系"""
        try:
            trade_offs = {}
            
            if len(solutions) < 2:
                return trade_offs
            
            solution_list = list(solutions.values())
            
            # 能耗与成本权衡
            energy_values = [sol.energy_consumption for sol in solution_list]
            cost_values = [sol.renovation_cost for sol in solution_list]
            
            if len(set(energy_values)) > 1 and len(set(cost_values)) > 1:
                correlation = np.corrcoef(energy_values, cost_values)[0, 1]
                if correlation > 0.5:
                    trade_offs['energy_cost'] = "能耗和成本呈正相关，低能耗通常需要更高投资"
                elif correlation < -0.5:
                    trade_offs['energy_cost'] = "能耗和成本呈负相关，可能存在性价比优势"
                else:
                    trade_offs['energy_cost'] = "能耗和成本关系复杂，需要具体分析"
            
            # 热工性能与成本权衡
            thermal_values = [sol.thermal_performance for sol in solution_list]
            
            if len(set(thermal_values)) > 1 and len(set(cost_values)) > 1:
                correlation = np.corrcoef(thermal_values, cost_values)[0, 1]
                if correlation < -0.5:
                    trade_offs['thermal_cost'] = "热工性能改善通常需要更高投资"
                else:
                    trade_offs['thermal_cost'] = "热工性能与成本关系不明显"
            
            return trade_offs
            
        except Exception:
            return {}
    
    def _generate_solution_recommendations(self, solutions: Dict[str, ObjectiveResults]) -> List[str]:
        """生成解决方案推荐"""
        try:
            recommendations = []
            
            if not solutions:
                return recommendations
            
            # 找出最佳综合解
            best_comprehensive = None
            best_score = float('-inf')
            
            for name, solution in solutions.items():
                score = self._calculate_comprehensive_score(solution)
                if score > best_score:
                    best_score = score
                    best_comprehensive = name
            
            if best_comprehensive:
                recommendations.append(f"推荐优先考虑{best_comprehensive}解决方案，综合性能最佳")
            
            # 成本效益分析
            cost_effective = None
            best_cost_effectiveness = float('-inf')
            
            for name, solution in solutions.items():
                if solution.renovation_cost > 0:
                    # 简化的成本效益指标
                    effectiveness = (200 - solution.energy_consumption) / (solution.renovation_cost / 10000)
                    if effectiveness > best_cost_effectiveness:
                        best_cost_effectiveness = effectiveness
                        cost_effective = name
            
            if cost_effective and cost_effective != best_comprehensive:
                recommendations.append(f"{cost_effective}解决方案具有较好的成本效益")
            
            # 特定场景推荐
            energy_solution = min(solutions.items(), key=lambda x: x[1].energy_consumption)
            if energy_solution[1].energy_consumption < 60:
                recommendations.append(f"对于极致节能需求，推荐{energy_solution[0]}解决方案")
            
            cost_solution = min(solutions.items(), key=lambda x: x[1].renovation_cost)
            if cost_solution[1].renovation_cost < 100000:
                recommendations.append(f"预算有限情况下，{cost_solution[0]}解决方案是可行选择")
            
            return recommendations
            
        except Exception:
            return ["无法生成推荐建议"]
    
    def rank_solutions_by_preference(self, solutions: Dict[str, ObjectiveResults],
                                   user_preferences: Dict[str, float]) -> List[Tuple[str, float]]:
        """根据用户偏好对解决方案排序"""
        try:
            # 用户偏好权重
            energy_pref = user_preferences.get('energy_weight', 0.33)
            thermal_pref = user_preferences.get('thermal_weight', 0.33)
            cost_pref = user_preferences.get('cost_weight', 0.34)
            
            # 归一化权重
            total_weight = energy_pref + thermal_pref + cost_pref
            if total_weight > 0:
                energy_pref /= total_weight
                thermal_pref /= total_weight
                cost_pref /= total_weight
            
            # 计算偏好评分
            solution_scores = []
            
            for name, solution in solutions.items():
                # 归一化性能指标
                energy_norm = max(0, 1 - solution.energy_consumption / 200)
                thermal_norm = max(0, 1 - solution.thermal_performance)
                cost_norm = max(0, 1 - solution.renovation_cost / 300000)
                
                # 基于用户偏好的加权评分
                preference_score = (
                    energy_norm * energy_pref +
                    thermal_norm * thermal_pref +
                    cost_norm * cost_pref
                )
                
                solution_scores.append((name, preference_score))
            
            # 按评分排序
            solution_scores.sort(key=lambda x: x[1], reverse=True)
            
            return solution_scores
            
        except Exception as e:
            self.logger.error(f"根据偏好排序失败: {str(e)}")
            return [(name, 0.5) for name in solutions.keys()]


def create_solution_selector() -> SolutionSelector:
    """
    创建解决方案选择器实例
    
    Returns:
        配置好的解决方案选择器
    """
    return SolutionSelector()