现在我对这个项目很失望 需要完全重构这个项目  我再次说明我的目标   这个项目是基于yolo识别后的建筑立面色块图进行的 系统首先提取原始建筑立面的数据得到墙体 窗户 遮阳 门 窗框等数据 包括面积长高宽比例大小等，具体的方法参考D:\桌面\123\立面优化项目\src\image_processing里面的方法   第二个数据是提取epw的数据方便后续的优化   然后将提取的数据气候数据以及我自主选择朝向进行基于气候数据和朝向遗传算法的优化   优化的目标函数是建筑的能耗以及建筑的性能以及改造的成本   可以改动的变化有两个 第一种是当墙体没有任何构建时默认为新建项目，这个时候可以改造的就是随机确定窗户的位置大小  以及窗户是否是窗框还是遮阳，注意窗框和遮阳的变化只有宽度也就是伸出墙壁的宽度  注意窗框和遮阳必须和窗户贴合        第二 当建筑立面有原本窗户遮阳窗框这些构建时，这个时候可以改造的是窗户基于窗户原来位置的横向变化长度变化，也就是可以改造窗框，窗户禁止重叠和超过立面范围 以及窗户是否是窗框还是遮阳，注意窗框和遮阳的变化只有宽度也就是伸出墙壁的宽度  注意窗框和遮阳必须和窗户贴合     优化过程我想要得到两种优化方法，第一个是固定优化100代  然后筛选得到的解中 综合得分 能耗优先得分以及成本和性能这四个维度的最好解     并且优化过程要产生我后续分析图的相关数据并且正确传递   下面是可视化模块想要的分析图   1. **算法收敛分析图** (01_algorithm_convergence.png)

   - NSGA-III收敛曲线

   - 超体积指标变化

   - 分布性指标分析

   

2. **帕累托前沿分析图** (02_pareto_frontier_analysis.png)

   -帕累托前沿散点图

   - 多维度2D投影

   - 帕累托层级分布

   - 拥挤距离分析

3. **热工性能综合分析图** (03_thermal_performance_analysis.png)

   - 传热系数分布热力图

   - 热桥效应分析

   - 热惰性时间常数

   - 季节性热工性能对比

4. **能耗分解分析图** (04_energy_breakdown_analysis.png)

   

   - 月度能耗变化曲线

   - 日负荷曲线分析

### 🎨 第三类：方案对比展示图纸

5. **最佳方案对比图** (05_best_solutions_comparison.png)

   - 原始方案与综合 能耗 性能三个维度的最佳方案的可视化     雷达图性能对比

   - 关键指标柱状图  详细性能数据表格  

   

6. **方案网格展示图** (06_solutions_grid_layout.png)

   -所有生成的优化解方案网格布局

   - 帕累托最优解标记

   - 性能指标颜色编码

   - 精致建筑立面展示    注意05和06都要用同一个立面可视化，要有精致墙体窗户窗框展示

   - 遮阳板细节呈现

   - 

7 **方案3D展示图** (08_3d_solutions.png)

   - 轴测图3D效果

   - 窗户大小变化展示

   - 窗框遮阳厚度对比

   - 好看为主

8 **综合性能雷达图** (09_comprehensive_performance_radar.png)

   - 多维性能雷达图

   - 基准对比分析

   - 改进效果展示

   - 综合评分排序

9. **解聚类分析图** (10_solution_clustering_analysis.png)

    - K-means聚类结果

    - 聚类中心特征

    - 聚类质量评估

10. **参数相关性分析图** (11_parameter_correlation_analysis.png)

    - 参数相关性热力图

    - 主成分分析

    - 参数重要性排序

    - 交互效应分析

注意每一个都是创建文件夹然后单独输出子图    子图要保证中英双版本 并且保证图例文字信息等不重叠  注意单独生成这些可视化的代码文件 但是他们要有统一的风格颜色字体样式图表设置等的修改器方便统一修改   请注意虽然我列出来的很简陋但是你要保证每一个图表效果达到最高  尽可能丰富图表的表达效果并且设计三套配色 默认为红蓝低饱和   特别是立面可视化和3d立面要精致美观    最后要主函数 要求以下功能  第一交互式输入立面 epw地址 选择朝向 权重后开始生成50个解 注意要用项目里面的模块来分析  选择其中遗传算法优化如果生成解不够则自主加大代数和变异或者其他参数  最后输出所有可视化   第二个功能是生成输入数量的帕累托解   第三个功能是单独测试遗传算法   第四个功能是单独测试可视化     然后在D:\桌面\123\new这个文件夹重构新的项目