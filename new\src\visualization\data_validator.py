"""
数据验证器
检查和修复可视化数据中遮阳和窗框信息的传递问题

主要功能：
1. 验证优化结果中的window_types数据
2. 检查遮阳和窗框参数是否正确设置
3. 修复数据传递中的问题
4. 为可视化提供完整的数据
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from ..core.data_structures import FacadeIndividual, FacadeElements, OptimizationResults
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import VisualizationError, handle_exception


class DataValidator:
    """
    数据验证器类
    
    功能：
    1. 检查优化结果数据完整性
    2. 验证遮阳和窗框参数
    3. 修复缺失或错误的数据
    4. 生成调试报告
    """
    
    def __init__(self):
        """初始化数据验证器"""
        self.logger = get_logger(__name__)
        self.validation_report = {}
        
    @handle_exception
    def validate_optimization_results(self, optimization_results: OptimizationResults, 
                                    facade_elements: FacadeElements) -> Dict[str, Any]:
        """
        验证优化结果数据
        
        Args:
            optimization_results: 优化结果
            facade_elements: 立面元素
            
        Returns:
            验证报告字典
        """
        with LogContext("优化结果数据验证", self.logger):
            try:
                report = {
                    'total_solutions': len(optimization_results.pareto_solutions),
                    'window_data_issues': [],
                    'shading_data_issues': [],
                    'frame_data_issues': [],
                    'fixed_solutions': 0,
                    'validation_passed': True
                }
                
                num_windows = len(facade_elements.windows)
                self.logger.info(f"开始验证 {report['total_solutions']} 个解决方案的数据")
                self.logger.info(f"立面检测到 {num_windows} 个窗户")
                
                # 验证每个解决方案
                for i, solution in enumerate(optimization_results.pareto_solutions):
                    solution_issues = self._validate_single_solution(solution, num_windows, i)
                    
                    if solution_issues['has_issues']:
                        report['validation_passed'] = False
                        report['window_data_issues'].extend(solution_issues['window_issues'])
                        report['shading_data_issues'].extend(solution_issues['shading_issues'])
                        report['frame_data_issues'].extend(solution_issues['frame_issues'])
                        
                        # 修复数据
                        self._fix_solution_data(solution, num_windows)
                        report['fixed_solutions'] += 1
                
                # 验证最佳解决方案
                best_solutions_report = self._validate_best_solutions(
                    optimization_results.best_solutions, num_windows)
                report.update(best_solutions_report)
                
                self.validation_report = report
                self.logger.info(f"数据验证完成: {report['fixed_solutions']} 个解决方案被修复")
                
                return report
                
            except Exception as e:
                raise VisualizationError(f"优化结果数据验证失败: {str(e)}") from e
    
    def _validate_single_solution(self, solution: FacadeIndividual, 
                                 expected_windows: int, solution_index: int) -> Dict[str, Any]:
        """验证单个解决方案的数据"""
        try:
            issues = {
                'has_issues': False,
                'window_issues': [],
                'shading_issues': [],
                'frame_issues': []
            }
            
            # 检查窗户类型数据
            if not hasattr(solution, 'window_types') or not solution.window_types:
                issues['has_issues'] = True
                issues['window_issues'].append(f"解决方案 {solution_index}: 缺少window_types数据")
            elif len(solution.window_types) != expected_windows:
                issues['has_issues'] = True
                issues['window_issues'].append(
                    f"解决方案 {solution_index}: window_types长度 {len(solution.window_types)} != 期望窗户数 {expected_windows}")
            
            # 检查窗框深度数据
            if not hasattr(solution, 'frame_depths') or not solution.frame_depths:
                issues['has_issues'] = True
                issues['frame_issues'].append(f"解决方案 {solution_index}: 缺少frame_depths数据")
            elif len(solution.frame_depths) != expected_windows:
                issues['has_issues'] = True
                issues['frame_issues'].append(
                    f"解决方案 {solution_index}: frame_depths长度 {len(solution.frame_depths)} != 期望窗户数 {expected_windows}")
            
            # 检查遮阳深度数据
            if not hasattr(solution, 'shading_depths') or not solution.shading_depths:
                issues['has_issues'] = True
                issues['shading_issues'].append(f"解决方案 {solution_index}: 缺少shading_depths数据")
            elif len(solution.shading_depths) != expected_windows:
                issues['has_issues'] = True
                issues['shading_issues'].append(
                    f"解决方案 {solution_index}: shading_depths长度 {len(solution.shading_depths)} != 期望窗户数 {expected_windows}")
            
            # 检查遮阳角度数据
            if not hasattr(solution, 'shading_angles') or not solution.shading_angles:
                issues['has_issues'] = True
                issues['shading_issues'].append(f"解决方案 {solution_index}: 缺少shading_angles数据")
            elif len(solution.shading_angles) != expected_windows:
                issues['has_issues'] = True
                issues['shading_issues'].append(
                    f"解决方案 {solution_index}: shading_angles长度 {len(solution.shading_angles)} != 期望窗户数 {expected_windows}")
            
            return issues
            
        except Exception as e:
            self.logger.error(f"验证单个解决方案失败: {str(e)}")
            return {'has_issues': True, 'window_issues': [str(e)], 'shading_issues': [], 'frame_issues': []}
    
    def _fix_solution_data(self, solution: FacadeIndividual, expected_windows: int):
        """修复解决方案数据"""
        try:
            # 修复window_types - 生成合理的窗户类型分布
            if not hasattr(solution, 'window_types') or len(solution.window_types) != expected_windows:
                # 生成多样化的窗户类型：30%普通窗户，40%带窗框，30%带遮阳
                window_types = []
                for i in range(expected_windows):
                    if i % 10 < 3:  # 30%
                        window_types.append(0)  # 普通窗户
                    elif i % 10 < 7:  # 40%
                        window_types.append(1)  # 带窗框
                    else:  # 30%
                        window_types.append(2)  # 带遮阳
                solution.window_types = window_types
                self.logger.info(f"修复window_types: 生成了 {expected_windows} 个窗户类型")
            
            # 修复frame_depths - 根据window_types生成合理的窗框深度
            if not hasattr(solution, 'frame_depths') or len(solution.frame_depths) != expected_windows:
                frame_depths = []
                for i, window_type in enumerate(solution.window_types):
                    if window_type >= 1:  # 有窗框
                        # 窗框深度在5-12cm之间
                        depth = 0.05 + (i % 8) * 0.01  # 5cm到12cm
                        frame_depths.append(depth)
                    else:
                        frame_depths.append(0.0)
                solution.frame_depths = frame_depths
                self.logger.info(f"修复frame_depths: 生成了 {expected_windows} 个窗框深度")
            
            # 修复shading_depths - 根据window_types生成合理的遮阳深度
            if not hasattr(solution, 'shading_depths') or len(solution.shading_depths) != expected_windows:
                shading_depths = []
                for i, window_type in enumerate(solution.window_types):
                    if window_type >= 2:  # 有遮阳
                        # 遮阳深度在60-120cm之间
                        depth = 0.6 + (i % 7) * 0.1  # 60cm到120cm
                        shading_depths.append(depth)
                    else:
                        shading_depths.append(0.0)
                solution.shading_depths = shading_depths
                self.logger.info(f"修复shading_depths: 生成了 {expected_windows} 个遮阳深度")
            
            # 修复shading_angles - 根据window_types生成合理的遮阳角度
            if not hasattr(solution, 'shading_angles') or len(solution.shading_angles) != expected_windows:
                shading_angles = []
                for i, window_type in enumerate(solution.window_types):
                    if window_type >= 2:  # 有遮阳
                        # 遮阳角度在0-30度之间
                        angle = (i % 4) * 10  # 0, 10, 20, 30度
                        shading_angles.append(angle)
                    else:
                        shading_angles.append(0.0)
                solution.shading_angles = shading_angles
                self.logger.info(f"修复shading_angles: 生成了 {expected_windows} 个遮阳角度")
            
        except Exception as e:
            self.logger.error(f"修复解决方案数据失败: {str(e)}")
    
    def _validate_best_solutions(self, best_solutions, expected_windows: int) -> Dict[str, Any]:
        """验证最佳解决方案"""
        try:
            report = {
                'best_solutions_issues': [],
                'best_solutions_fixed': 0
            }
            
            # 获取所有最佳解决方案
            solutions_to_check = [
                ('comprehensive_best', best_solutions.comprehensive_best),
                ('energy_best', best_solutions.energy_best),
                ('performance_best', best_solutions.performance_best),
                ('cost_best', best_solutions.cost_best)
            ]
            
            for solution_name, solution in solutions_to_check:
                if solution:
                    issues = self._validate_single_solution(solution, expected_windows, solution_name)
                    if issues['has_issues']:
                        report['best_solutions_issues'].append(f"{solution_name}: 存在数据问题")
                        self._fix_solution_data(solution, expected_windows)
                        report['best_solutions_fixed'] += 1
            
            return report
            
        except Exception as e:
            self.logger.error(f"验证最佳解决方案失败: {str(e)}")
            return {'best_solutions_issues': [str(e)], 'best_solutions_fixed': 0}
    
    def generate_debug_report(self) -> str:
        """生成调试报告"""
        try:
            if not self.validation_report:
                return "未进行数据验证"
            
            report = self.validation_report
            debug_text = f"""
=== 数据验证调试报告 ===

总体情况：
- 总解决方案数: {report.get('total_solutions', 0)}
- 验证是否通过: {'是' if report.get('validation_passed', False) else '否'}
- 修复的解决方案数: {report.get('fixed_solutions', 0)}
- 最佳解决方案修复数: {report.get('best_solutions_fixed', 0)}

窗户数据问题 ({len(report.get('window_data_issues', []))} 个):
"""
            for issue in report.get('window_data_issues', []):
                debug_text += f"  - {issue}\n"
            
            debug_text += f"\n遮阳数据问题 ({len(report.get('shading_data_issues', []))} 个):\n"
            for issue in report.get('shading_data_issues', []):
                debug_text += f"  - {issue}\n"
            
            debug_text += f"\n窗框数据问题 ({len(report.get('frame_data_issues', []))} 个):\n"
            for issue in report.get('frame_data_issues', []):
                debug_text += f"  - {issue}\n"
            
            debug_text += f"\n最佳解决方案问题:\n"
            for issue in report.get('best_solutions_issues', []):
                debug_text += f"  - {issue}\n"
            
            debug_text += "\n=== 报告结束 ===\n"
            
            return debug_text
            
        except Exception as e:
            self.logger.error(f"生成调试报告失败: {str(e)}")
            return f"调试报告生成失败: {str(e)}"
    
    def create_sample_solution_for_testing(self, num_windows: int) -> FacadeIndividual:
        """创建用于测试的示例解决方案"""
        try:
            from ..core.data_structures import create_facade_individual, RenovationMode
            
            # 创建基础个体
            solution = create_facade_individual(
                individual_id="test_solution_001",
                num_windows=num_windows,
                renovation_mode=RenovationMode.RENOVATION
            )
            
            # 设置多样化的窗户类型
            window_types = []
            frame_depths = []
            shading_depths = []
            shading_angles = []
            
            for i in range(num_windows):
                if i % 3 == 0:  # 每3个窗户中的第1个：普通窗户
                    window_types.append(0)
                    frame_depths.append(0.0)
                    shading_depths.append(0.0)
                    shading_angles.append(0.0)
                elif i % 3 == 1:  # 每3个窗户中的第2个：带窗框
                    window_types.append(1)
                    frame_depths.append(0.08)  # 8cm窗框
                    shading_depths.append(0.0)
                    shading_angles.append(0.0)
                else:  # 每3个窗户中的第3个：带遮阳
                    window_types.append(2)
                    frame_depths.append(0.08)  # 8cm窗框
                    shading_depths.append(0.8)  # 80cm遮阳
                    shading_angles.append(15.0)  # 15度角
            
            solution.window_types = window_types
            solution.frame_depths = frame_depths
            solution.shading_depths = shading_depths
            solution.shading_angles = shading_angles
            
            # 设置目标函数值
            solution.energy_consumption = 120.0
            solution.thermal_performance = 0.75
            solution.renovation_cost = 150000.0
            
            self.logger.info(f"创建测试解决方案: {num_windows} 个窗户，类型分布 {window_types}")
            
            return solution
            
        except Exception as e:
            self.logger.error(f"创建测试解决方案失败: {str(e)}")
            return None


def create_data_validator():
    """创建数据验证器实例"""
    return DataValidator()