"""
立面数据提取器 - 主控制器
整合色块图像处理和几何计算，提供统一的立面数据提取接口
"""

from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import numpy as np

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext, get_performance_logger
from ..core.exceptions import FacadeExtractionError, handle_exception
from ..core.data_structures import FacadeElements, VisualizationData
from ..core.utils import FileUtils, DataUtils, TimeUtils

from .yolo_segmentation_processor import ColorBlockImageProcessor
from .geometry_calculator import GeometryCalculator
from .element_validator import ElementValidator
from ..core.data_validation import DataValidator


class FacadeDataExtractor:
    """
    立面数据提取器主控制器
    
    功能：
    1. 统一的立面数据提取接口
    2. 处理流程管理和监控
    3. 结果验证和质量控制
    4. 数据存储和缓存管理
    5. 详细的处理报告生成
    """
    
    def __init__(self):
        """初始化立面数据提取器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        
        # 初始化组件
        self.color_block_processor = ColorBlockImageProcessor()
        self.geometry_calculator = GeometryCalculator()
        self.element_validator = ElementValidator()
        self.data_validator = DataValidator()
        
        # 配置参数
        self.enable_caching = self.config.get('system.cache_enabled', True)
        self.save_intermediate_results = self.config.get('output.save_intermediate_results', True)
        self.create_debug_output = True
        
        self.logger.info("立面数据提取器初始化完成")
    
    @handle_exception
    def extract_facade_data(self, image_path: str, 
                          output_dir: Optional[str] = None,
                          enable_validation: bool = True,
                          save_debug: bool = True) -> Dict[str, Any]:
        """
        提取立面数据的主要接口
        
        Args:
            image_path: 立面识别后的色块图像路径
            output_dir: 输出目录，如果为None则使用配置的默认目录
            enable_validation: 是否启用验证
            save_debug: 是否保存调试信息
            
        Returns:
            包含立面元素、几何参数、验证结果等的完整数据字典
            
        Raises:
            FacadeExtractionError: 提取失败时抛出
        """
        with LogContext("立面数据提取", self.logger):
            # 设置输出目录
            if output_dir is None:
                output_dir = self.config.get_output_directory()
            
            # 创建处理会话
            session_id = TimeUtils.get_timestamp()
            session_dir = Path(output_dir) / f"facade_extraction_{session_id}"
            FileUtils.ensure_directory(session_dir)
            
            try:
                # 步骤0: 数据验证 - 确保不使用模拟数据
                with self.performance_logger.time_operation("数据验证"):
                    image_validation = self.data_validator.validate_image_data(image_path)
                    if not image_validation['is_valid']:
                        error_msg = f"图像数据验证失败: {'; '.join(image_validation['validation_errors'])}"
                        raise FacadeExtractionError(error_msg)
                    
                    if image_validation['validation_warnings']:
                        self.logger.warning(f"图像数据验证警告: {'; '.join(image_validation['validation_warnings'])}")
                    
                    self.logger.info("图像数据验证通过 - 确认为真实数据")
                
                # 步骤1: 色块图像处理  
                with self.performance_logger.time_operation("色块图像处理"):
                    facade_elements = self.color_block_processor.process_color_block_image(image_path)
                    self.logger.info(f"色块图像处理完成: 检测到 {len(facade_elements.get_all_elements())} 个元素")
                    
                    # 验证提取的立面元素
                    elements_validation = self.data_validator.validate_facade_elements(facade_elements)
                    if not elements_validation['is_valid']:
                        error_msg = f"立面元素验证失败: {'; '.join(elements_validation['validation_errors'])}"
                        raise FacadeExtractionError(error_msg)
                
                # 步骤2: 几何参数计算
                with self.performance_logger.time_operation("几何参数计算"):
                    geometry_params = self.geometry_calculator.calculate_facade_parameters(facade_elements)
                    self.logger.info(f"几何参数计算完成: 窗墙比 {geometry_params.get('window_wall_ratio', 0):.3f}")
                
                # 步骤3: 数据验证
                validation_result = {}
                if enable_validation:
                    with self.performance_logger.time_operation("数据验证"):
                        validation_result = self.element_validator.validate_facade_data(
                            facade_elements, geometry_params
                        )
                        self.logger.info(f"数据验证完成: {'通过' if validation_result.get('is_valid', False) else '存在问题'}")
                
                # 步骤4: 生成处理统计
                processing_stats = self._generate_processing_statistics(
                    facade_elements, geometry_params, validation_result
                )
                
                # 步骤5: 保存结果
                extraction_result = {
                    'session_id': session_id,
                    'input_image_path': str(image_path),
                    'facade_elements': facade_elements,
                    'geometry_parameters': geometry_params,
                    'validation_result': validation_result,
                    'processing_statistics': processing_stats,
                    'extraction_metadata': {
                        'timestamp': TimeUtils.get_timestamp('%Y-%m-%d %H:%M:%S'),
                        'processing_time_seconds': sum(
                            self.performance_logger._start_times.values()
                        ) if hasattr(self.performance_logger, '_start_times') else 0,
                        'output_directory': str(session_dir),
                        'configuration_snapshot': self._get_config_snapshot()
                    }
                }
                
                # 保存结果
                if self.save_intermediate_results:
                    self._save_extraction_results(extraction_result, session_dir)
                
                # 生成调试输出
                if save_debug:
                    self._generate_debug_output(extraction_result, session_dir)
                
                # 生成处理报告
                report_path = self._generate_processing_report(extraction_result, session_dir)
                extraction_result['report_path'] = str(report_path)
                
                self.logger.info(f"立面数据提取完成: {len(facade_elements.get_all_elements())} 个元素")
                return extraction_result
                
            except Exception as e:
                self.logger.error(f"立面数据提取失败: {str(e)}")
                raise FacadeExtractionError(f"立面数据提取失败: {str(e)}") from e
    
    def batch_extract_facade_data(self, image_paths: List[str],
                                 output_dir: Optional[str] = None,
                                 continue_on_error: bool = True) -> Dict[str, Any]:
        """
        批量提取立面数据
        
        Args:
            image_paths: 图像路径列表
            output_dir: 输出目录
            continue_on_error: 是否在错误时继续处理其他图像
            
        Returns:
            批量处理结果
        """
        with LogContext("批量立面数据提取", self.logger):
            batch_id = TimeUtils.get_timestamp()
            
            if output_dir is None:
                output_dir = self.config.get_output_directory()
            
            batch_dir = Path(output_dir) / f"batch_extraction_{batch_id}"
            FileUtils.ensure_directory(batch_dir)
            
            results = {
                'batch_id': batch_id,
                'total_images': len(image_paths),
                'successful_extractions': [],
                'failed_extractions': [],
                'batch_statistics': {},
                'batch_directory': str(batch_dir)
            }
            
            for i, image_path in enumerate(image_paths):
                self.logger.info(f"处理图像 {i+1}/{len(image_paths)}: {image_path}")
                
                try:
                    # 为每个图像创建子目录
                    image_name = Path(image_path).stem
                    image_output_dir = batch_dir / f"{i:03d}_{image_name}"
                    
                    extraction_result = self.extract_facade_data(
                        image_path,
                        output_dir=str(image_output_dir),
                        save_debug=True
                    )
                    
                    results['successful_extractions'].append({
                        'image_path': image_path,
                        'result': extraction_result
                    })
                    
                except Exception as e:
                    error_info = {
                        'image_path': image_path,
                        'error': str(e),
                        'error_type': type(e).__name__
                    }
                    
                    results['failed_extractions'].append(error_info)
                    self.logger.error(f"图像处理失败 {image_path}: {str(e)}")
                    
                    if not continue_on_error:
                        raise
            
            # 生成批量统计
            if results['successful_extractions']:
                results['batch_statistics'] = self._generate_batch_statistics(
                    results['successful_extractions']
                )
            
            # 保存批量结果
            batch_summary_path = batch_dir / "batch_summary.json"
            DataUtils.save_json(results, batch_summary_path)
            
            self.logger.info(f"批量处理完成: {len(results['successful_extractions'])} 成功, "
                           f"{len(results['failed_extractions'])} 失败")
            
            return results
    
    def extract_facade_elements_only(self, image_path: str) -> FacadeElements:
        """
        仅提取立面元素，不进行几何计算和验证
        
        Args:
            image_path: 图像路径
            
        Returns:
            FacadeElements对象
        """
        return self.color_block_processor.process_color_block_image(image_path)
    
    def calculate_geometry_only(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """
        仅计算几何参数
        
        Args:
            facade_elements: 立面元素
            
        Returns:
            几何参数字典
        """
        return self.geometry_calculator.calculate_facade_parameters(facade_elements)
    
    def validate_facade_only(self, facade_elements: FacadeElements, 
                           geometry_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        仅验证立面数据
        
        Args:
            facade_elements: 立面元素
            geometry_params: 几何参数（可选）
            
        Returns:
            验证结果
        """
        if geometry_params is None:
            geometry_params = self.geometry_calculator.calculate_facade_parameters(facade_elements)
        
        return self.element_validator.validate_facade_data(facade_elements, geometry_params)
    
    def _generate_processing_statistics(self, facade_elements: FacadeElements,
                                      geometry_params: Dict[str, Any],
                                      validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成处理统计信息"""
        stats = {
            'element_summary': {
                'total_elements': len(facade_elements.get_all_elements()),
                'walls': len(facade_elements.walls),
                'windows': len(facade_elements.windows),
                'doors': len(facade_elements.doors),
                'shading': len(facade_elements.shading),
                'frames': len(facade_elements.frames)
            },
            'key_metrics': {
                'window_wall_ratio': geometry_params.get('window_wall_ratio', 0),
                'total_opening_ratio': geometry_params.get('total_opening_ratio', 0),
                'window_density': geometry_params.get('window_density', 0),
                'facade_area_estimate': geometry_params.get('estimated_facade_area', 0)
            },
            'quality_indicators': {
                'validation_passed': validation_result.get('is_valid', False),
                'constraint_violations': len(validation_result.get('violations', [])),
                'warnings_count': len(validation_result.get('warnings', []))
            },
            'image_properties': {
                'image_shape': facade_elements.image_shape,
                'pixel_to_meter_ratio': facade_elements.pixel_to_meter_ratio,
                'estimated_facade_dimensions': self._estimate_facade_dimensions(facade_elements)
            }
        }
        
        return stats
    
    def _estimate_facade_dimensions(self, facade_elements: FacadeElements) -> Dict[str, float]:
        """估算立面实际尺寸"""
        if facade_elements.image_shape[0] == 0:
            return {'width': 0, 'height': 0}
        
        height_pixels, width_pixels = facade_elements.image_shape[:2]
        
        return {
            'width_meters': width_pixels * facade_elements.pixel_to_meter_ratio,
            'height_meters': height_pixels * facade_elements.pixel_to_meter_ratio,
            'aspect_ratio': width_pixels / height_pixels
        }
    
    def _get_config_snapshot(self) -> Dict[str, Any]:
        """获取配置快照"""
        return {
            'image_processing': self.config.get_section('image_processing'),
            'optimization_constraints': self.config.get_section('optimization_constraints'),
            'system': self.config.get_section('system')
        }
    
    def _save_extraction_results(self, extraction_result: Dict[str, Any], 
                               output_dir: Path) -> None:
        """保存提取结果"""
        try:
            # 保存完整结果（序列化版本）
            serializable_result = self._make_serializable(extraction_result)
            DataUtils.save_json(serializable_result, output_dir / "extraction_result.json")
            
            # 保存立面元素数据
            facade_elements = extraction_result['facade_elements']
            DataUtils.save_json(facade_elements.to_dict(), output_dir / "facade_elements.json")
            
            # 保存几何参数
            DataUtils.save_json(
                extraction_result['geometry_parameters'], 
                output_dir / "geometry_parameters.json"
            )
            
            # 保存验证结果
            if extraction_result['validation_result']:
                DataUtils.save_json(
                    extraction_result['validation_result'],
                    output_dir / "validation_result.json"
                )
            
            self.logger.debug(f"提取结果已保存到: {output_dir}")
            
        except Exception as e:
            self.logger.error(f"保存提取结果失败: {str(e)}")
    
    def _make_serializable(self, data: Any) -> Any:
        """将数据转换为可序列化格式"""
        if hasattr(data, 'to_dict'):
            return data.to_dict()
        elif isinstance(data, dict):
            return {k: self._make_serializable(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        elif isinstance(data, (int, float, str, bool)) or data is None:
            return data
        else:
            return str(data)
    
    def _generate_debug_output(self, extraction_result: Dict[str, Any],
                             output_dir: Path) -> None:
        """生成调试输出"""
        try:
            debug_dir = output_dir / "debug"
            FileUtils.ensure_directory(debug_dir)
            
            # 保存调试可视化
            facade_elements = extraction_result['facade_elements']
            image_path = extraction_result['input_image_path']
            
            self.color_block_processor.save_debug_visualization(
                image_path, facade_elements, str(debug_dir)
            )
            
            # 保存处理统计的可视化版本
            stats = extraction_result['processing_statistics']
            self._save_statistics_visualization(stats, debug_dir)
            
            self.logger.debug(f"调试输出已保存到: {debug_dir}")
            
        except Exception as e:
            self.logger.error(f"生成调试输出失败: {str(e)}")
    
    def _save_statistics_visualization(self, stats: Dict[str, Any], 
                                     output_dir: Path) -> None:
        """保存统计信息可视化"""
        try:
            import matplotlib.pyplot as plt
            import seaborn as sns
            
            # 设置字体 - 避免中文字体警告
            plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
            
            # 创建元素分布饼图
            element_counts = stats['element_summary']
            if sum(element_counts.values()) > 0:
                plt.figure(figsize=(10, 6))
                
                # 过滤掉数量为0的元素
                non_zero_counts = {k: v for k, v in element_counts.items() 
                                 if v > 0 and k != 'total_elements'}
                
                if non_zero_counts:
                    plt.subplot(1, 2, 1)
                    plt.pie(non_zero_counts.values(), labels=non_zero_counts.keys(), 
                           autopct='%1.1f%%', startangle=90)
                    plt.title('建筑元素分布')
                
                # 关键指标柱状图
                plt.subplot(1, 2, 2)
                metrics = stats['key_metrics']
                metric_names = list(metrics.keys())
                metric_values = list(metrics.values())
                
                plt.bar(range(len(metric_names)), metric_values)
                plt.xticks(range(len(metric_names)), metric_names, rotation=45)
                plt.title('关键几何指标')
                plt.ylabel('数值')
                
                plt.tight_layout()
                plt.savefig(output_dir / "processing_statistics.png", dpi=300, bbox_inches='tight')
                plt.close()
            
        except ImportError:
            self.logger.warning("matplotlib未安装，跳过统计可视化")
        except Exception as e:
            self.logger.error(f"保存统计可视化失败: {str(e)}")
    
    def _generate_processing_report(self, extraction_result: Dict[str, Any],
                                  output_dir: Path) -> Path:
        """生成处理报告"""
        try:
            report_path = output_dir / "processing_report.md"
            
            facade_elements = extraction_result['facade_elements']
            geometry_params = extraction_result['geometry_parameters']
            validation_result = extraction_result['validation_result']
            stats = extraction_result['processing_statistics']
            metadata = extraction_result['extraction_metadata']
            
            report_content = f"""# 建筑立面数据提取报告

## 基本信息
- **会话ID**: {extraction_result['session_id']}
- **输入图像**: {extraction_result['input_image_path']}
- **处理时间**: {metadata['timestamp']}
- **处理耗时**: {metadata.get('processing_time_seconds', 0):.2f} 秒

## 元素检测结果
| 元素类型 | 数量 | 占比 |
|---------|------|------|
| 墙体 | {stats['element_summary']['walls']} | {stats['element_summary']['walls']/max(stats['element_summary']['total_elements'], 1)*100:.1f}% |
| 窗户 | {stats['element_summary']['windows']} | {stats['element_summary']['windows']/max(stats['element_summary']['total_elements'], 1)*100:.1f}% |
| 门 | {stats['element_summary']['doors']} | {stats['element_summary']['doors']/max(stats['element_summary']['total_elements'], 1)*100:.1f}% |
| 遮阳 | {stats['element_summary']['shading']} | {stats['element_summary']['shading']/max(stats['element_summary']['total_elements'], 1)*100:.1f}% |
| 窗框 | {stats['element_summary']['frames']} | {stats['element_summary']['frames']/max(stats['element_summary']['total_elements'], 1)*100:.1f}% |

**总计**: {stats['element_summary']['total_elements']} 个元素

## 几何参数
- **窗墙比**: {stats['key_metrics']['window_wall_ratio']:.3f}
- **总开口率**: {stats['key_metrics']['total_opening_ratio']:.3f}
- **窗户密度**: {stats['key_metrics']['window_density']:.3f} 个/m²
- **立面面积估算**: {stats['key_metrics']['facade_area_estimate']:.2f} m²

## 立面尺寸估算
- **宽度**: {stats['image_properties']['estimated_facade_dimensions']['width_meters']:.2f} m
- **高度**: {stats['image_properties']['estimated_facade_dimensions']['height_meters']:.2f} m
- **宽高比**: {stats['image_properties']['estimated_facade_dimensions']['aspect_ratio']:.2f}

## 质量评估
- **验证状态**: {'✅ 通过' if stats['quality_indicators']['validation_passed'] else '❌ 未通过'}
- **约束违反**: {stats['quality_indicators']['constraint_violations']} 个
- **警告数量**: {stats['quality_indicators']['warnings_count']} 个
"""
            
            # 添加验证详情
            if validation_result and validation_result.get('violations'):
                report_content += "\n## 约束违反详情\n"
                for violation in validation_result['violations']:
                    report_content += f"- {violation}\n"
            
            if validation_result and validation_result.get('warnings'):
                report_content += "\n## 警告信息\n"
                for warning in validation_result['warnings']:
                    report_content += f"- {warning}\n"
            
            # 添加配置信息
            report_content += f"""
## 处理配置
- **像素-米转换比例**: {facade_elements.pixel_to_meter_ratio}
- **图像尺寸**: {facade_elements.image_shape[1]} × {facade_elements.image_shape[0]} 像素
- **处理模式**: 自动检测
- **输出目录**: {output_dir}

## 文件输出
- `extraction_result.json`: 完整提取结果
- `facade_elements.json`: 立面元素数据
- `geometry_parameters.json`: 几何参数
- `validation_result.json`: 验证结果
- `debug/`: 调试可视化文件

---
*报告生成时间: {TimeUtils.get_timestamp('%Y-%m-%d %H:%M:%S')}*
"""
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"处理报告已生成: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"生成处理报告失败: {str(e)}")
            return output_dir / "report_generation_failed.txt"
    
    def _generate_batch_statistics(self, successful_extractions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成批量处理统计"""
        try:
            batch_stats = {
                'summary': {
                    'total_processed': len(successful_extractions),
                    'average_elements_per_image': 0,
                    'average_window_wall_ratio': 0,
                    'processing_success_rate': 0
                },
                'distribution_analysis': {},
                'quality_metrics': {}
            }
            
            if not successful_extractions:
                return batch_stats
            
            # 收集所有结果的统计数据
            all_element_counts = []
            all_wwr = []
            all_validation_passed = []
            
            for extraction in successful_extractions:
                result = extraction['result']
                stats = result['processing_statistics']
                
                all_element_counts.append(stats['element_summary']['total_elements'])
                all_wwr.append(stats['key_metrics']['window_wall_ratio'])
                all_validation_passed.append(stats['quality_indicators']['validation_passed'])
            
            # 计算汇总统计
            batch_stats['summary']['average_elements_per_image'] = np.mean(all_element_counts)
            batch_stats['summary']['average_window_wall_ratio'] = np.mean(all_wwr)
            batch_stats['summary']['processing_success_rate'] = sum(all_validation_passed) / len(all_validation_passed)
            
            # 分布分析
            batch_stats['distribution_analysis'] = {
                'element_count_distribution': {
                    'min': min(all_element_counts),
                    'max': max(all_element_counts),
                    'std': np.std(all_element_counts)
                },
                'wwr_distribution': {
                    'min': min(all_wwr),
                    'max': max(all_wwr), 
                    'std': np.std(all_wwr)
                }
            }
            
            return batch_stats
            
        except Exception as e:
            self.logger.error(f"生成批量统计失败: {str(e)}")
            return {'error': str(e)}


def create_facade_data_extractor() -> FacadeDataExtractor:
    """
    创建立面数据提取器实例
    
    Returns:
        配置好的立面数据提取器
    """
    return FacadeDataExtractor()