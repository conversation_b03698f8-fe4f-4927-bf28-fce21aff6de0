{"session_id": "20250731_174222", "termination_reason": "max_generations", "total_generations": 120, "total_evaluations": 30000, "computation_time": 185.952283, "pareto_solutions_count": 250, "representative_solutions_count": 3, "optimization_statistics": {"final_population_size": 250, "final_feasible_count": 250, "feasibility_ratio": 1.0, "objective_statistics": {"energy_consumption": {"min": 20.0, "max": 20.0, "mean": 20.0, "std": 0.0}, "thermal_performance": {"min": 0.06359538065543865, "max": 0.07054137780486246, "mean": 0.06393187864194419, "std": 0.0014849263913816945}, "renovation_cost": {"min": 500.0, "max": 520.3258417263164, "mean": 519.3455792101702, "std": 4.344552772453771}}, "convergence_statistics": {"final_hypervolume": 144.65342056182538, "hypervolume_improvement": 142.03170037531268, "final_convergence_metric": 0.00315921343671955, "generations_to_convergence": 121}, "performance_statistics": {}}, "optimization_metadata": {"population_size": 250, "final_feasible_count": 250, "pareto_front_size": 250, "optimization_parameters": {"algorithm": "NSGA-III", "population_size": 250, "crossover_rate": 0.6, "mutation_rate": 0.55, "mutation_strength": 0.5, "tournament_size": 3, "max_generations": 120, "max_time_seconds": 2400, "convergence_threshold": 0.0001, "stagnation_generations": 50}}}