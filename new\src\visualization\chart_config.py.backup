"""
图表配置管理
定义专业图表的样式、颜色、字体等配置
"""

import matplotlib.pyplot as plt
# 设置英文字体 - 避免中文字体问题
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
import matplotlib.font_manager as fm
import seaborn as sns
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from dataclasses import dataclass
from enum import Enum

from ..core.config import get_config
from ..core.logging_config import get_logger

# 强制设置英文字体 - 确保兼容性
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False



class ChartStyle(Enum):
    """图表样式枚举"""
    PROFESSIONAL = "professional"
    MODERN = "modern"
    ACADEMIC = "academic"
    TECHNICAL = "technical"


@dataclass
class ColorPalette:
    """颜色调色板"""
    primary: str
    secondary: str
    accent: str
    background: str
    text: str
    grid: str
    success: str
    warning: str
    error: str
    neutral: str


@dataclass
class FontConfig:
    """字体配置"""
    family: str
    title_size: int
    label_size: int
    tick_size: int
    legend_size: int
    annotation_size: int


class ChartConfig:
    """
    图表配置管理器
    
    功能：
    1. 专业样式配置
    2. 中英文双语支持
    3. 颜色主题管理
    4. 字体和布局设置
    5. 图表尺寸配置
    """
    
    def __init__(self):
        """初始化图表配置"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取可视化配置
        viz_config = self.config.get_section('visualization')
        self.chart_config = viz_config.get('chart_configuration', {})
        
        # 样式设置
        style_config = self.chart_config.get('style_settings', {})
        self.chart_style = ChartStyle(style_config.get('chart_style', 'professional'))
        self.dpi = style_config.get('dpi', 300)
        self.figure_format = style_config.get('figure_format', 'png')
        
        # 语言设置
        language_config = self.chart_config.get('language_settings', {})
        self.primary_language = language_config.get('primary_language', 'zh')
        self.bilingual_mode = language_config.get('bilingual_mode', True)
        
        # 颜色主题
        self.color_palettes = self._initialize_color_palettes()
        self.current_palette = self.color_palettes[self.chart_style.value]
        
        # 字体配置
        self.font_configs = self._initialize_font_configs()
        self.current_font = self.font_configs[self.chart_style.value]
        
        # 图表尺寸配置
        size_config = self.chart_config.get('size_settings', {})
        self.default_figure_size = tuple(size_config.get('default_figure_size', [12, 8]))
        self.large_figure_size = tuple(size_config.get('large_figure_size', [16, 10]))
        self.small_figure_size = tuple(size_config.get('small_figure_size', [10, 6]))
        
        # 应用全局样式设置
        self._apply_global_style()
        
        self.logger.info(f"图表配置初始化完成: 样式={self.chart_style.value}, DPI={self.dpi}")
    
    def _initialize_color_palettes(self) -> Dict[str, ColorPalette]:
        """初始化颜色调色板"""
        palettes = {
            'professional': ColorPalette(
                primary='#2E86AB',      # 专业蓝
                secondary='#A23B72',    # 深玫红
                accent='#F18F01',       # 活力橙
                background='#FFFFFF',   # 纯白
                text='#2C3E50',         # 深灰蓝
                grid='#E8E8E8',         # 浅灰
                success='#27AE60',      # 成功绿
                warning='#F39C12',      # 警告橙
                error='#E74C3C',        # 错误红
                neutral='#95A5A6'       # 中性灰
            ),
            'modern': ColorPalette(
                primary='#667EEA',      # 现代紫
                secondary='#764BA2',    # 深紫
                accent='#F093FB',       # 粉紫
                background='#FAFAFA',   # 浅灰白
                text='#4A5568',         # 现代灰
                grid='#E2E8F0',         # 浅蓝灰
                success='#48BB78',      # 现代绿
                warning='#ED8936',      # 现代橙
                error='#F56565',        # 现代红
                neutral='#A0AEC0'       # 现代中灰
            ),
            'academic': ColorPalette(
                primary='#1F4E79',      # 学术蓝
                secondary='#8B4513',    # 学术棕
                accent='#228B22',       # 学术绿
                background='#FFFEF7',   # 象牙白
                text='#2F2F2F',         # 深炭灰
                grid='#D3D3D3',         # 浅灰
                success='#006400',      # 深绿
                warning='#FF8C00',      # 深橙
                error='#8B0000',        # 深红
                neutral='#708090'       # 石板灰
            ),
            'technical': ColorPalette(
                primary='#003366',      # 技术深蓝
                secondary='#336699',    # 技术蓝
                accent='#FF6600',       # 技术橙
                background='#F8F9FA',   # 技术白
                text='#212529',         # 技术黑
                grid='#DEE2E6',         # 技术灰
                success='#28A745',      # 技术绿
                warning='#FFC107',      # 技术黄
                error='#DC3545',        # 技术红
                neutral='#6C757D'       # 技术中灰
            )
        }
        
        return palettes
    
    def _initialize_font_configs(self) -> Dict[str, FontConfig]:
        """初始化字体配置"""
        # 设置英文字体 - 确保兼容性
        english_fonts = [
            'Arial',           # Arial字体
            'DejaVu Sans',     # DejaVu Sans字体  
            'Liberation Sans', # Liberation Sans字体
            'Helvetica',       # Helvetica字体
            'sans-serif'       # 系统默认无衬线字体
        ]
        
        available_font = None
        for font_name in english_fonts:
            try:
                # 检查字体是否可用
                font_files = [f for f in fm.fontManager.ttflist if font_name.lower() in f.name.lower()]
                if font_files:
                    available_font = font_name
                    self.logger.info(f"Found available English font: {font_name}")
                    break
            except Exception as e:
                self.logger.debug(f"Font check failed for {font_name}: {e}")
                continue
        
        if not available_font:
            # 使用Arial字体作为默认（Windows系统通常可用）
            available_font = 'Arial'
            self.logger.warning(f"No English font found, using default: {available_font}")
        
        # 确保使用英文字体
        if available_font not in english_fonts:
            available_font = 'Arial'  # 强制使用Arial
        
        font_configs = {
            'professional': FontConfig(
                family=available_font,
                title_size=16,
                label_size=12,
                tick_size=10,
                legend_size=11,
                annotation_size=9
            ),
            'modern': FontConfig(
                family=available_font,
                title_size=18,
                label_size=13,
                tick_size=11,
                legend_size=12,
                annotation_size=10
            ),
            'academic': FontConfig(
                family=available_font,
                title_size=14,
                label_size=11,
                tick_size=9,
                legend_size=10,
                annotation_size=8
            ),
            'technical': FontConfig(
                family=available_font,
                title_size=15,
                label_size=12,
                tick_size=10,
                legend_size=11,
                annotation_size=9
            )
        }
        
        return font_configs
    
    def _apply_global_style(self):
        """应用全局样式设置"""
        try:
            # 设置matplotlib参数，确保英文字体正确显示
            font_list = [self.current_font.family, 'Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
            
            plt.rcParams.update({
                'font.sans-serif': font_list,
                'font.family': ['sans-serif'],
                'axes.unicode_minus': False,  # 解决负号显示问题
                'font.size': self.current_font.label_size,
                'axes.titlesize': self.current_font.title_size,
                'axes.labelsize': self.current_font.label_size,
                'xtick.labelsize': self.current_font.tick_size,
                'ytick.labelsize': self.current_font.tick_size,
                'legend.fontsize': self.current_font.legend_size,
                'figure.titlesize': self.current_font.title_size,
                'figure.dpi': self.dpi,
                'savefig.dpi': self.dpi,
                'savefig.format': self.figure_format,
                'axes.facecolor': self.current_palette.background,
                'figure.facecolor': self.current_palette.background,
                'text.color': self.current_palette.text,
                'axes.labelcolor': self.current_palette.text,
                'xtick.color': self.current_palette.text,
                'ytick.color': self.current_palette.text,
                'axes.edgecolor': self.current_palette.grid,
                'grid.color': self.current_palette.grid,
                'grid.alpha': 0.3,
                'axes.grid': True,
                'axes.axisbelow': True,
                'lines.linewidth': 2,
                'lines.markersize': 6,
                'patch.linewidth': 0.5,
                'patch.facecolor': self.current_palette.primary,
                'boxplot.boxprops.color': self.current_palette.primary,
                'boxplot.whiskerprops.color': self.current_palette.primary,
                'boxplot.capprops.color': self.current_palette.primary,
                'boxplot.medianprops.color': self.current_palette.accent
            })
            
            self.logger.info(f"English font list applied: {font_list}")
            
            # 设置seaborn样式
            sns.set_style("whitegrid", {
                'axes.facecolor': self.current_palette.background,
                'figure.facecolor': self.current_palette.background,
                'grid.color': self.current_palette.grid
            })
            
            # 设置seaborn调色板
            colors = [
                self.current_palette.primary,
                self.current_palette.secondary,
                self.current_palette.accent,
                self.current_palette.success,
                self.current_palette.warning,
                self.current_palette.error,
                self.current_palette.neutral
            ]
            sns.set_palette(colors)
            
        except Exception as e:
            self.logger.error(f"应用全局样式设置失败: {str(e)}")
    
    def get_color_sequence(self, n_colors: int) -> List[str]:
        """获取颜色序列"""
        try:
            base_colors = [
                self.current_palette.primary,
                self.current_palette.secondary,
                self.current_palette.accent,
                self.current_palette.success,
                self.current_palette.warning,
                self.current_palette.error,
                self.current_palette.neutral
            ]
            
            if n_colors <= len(base_colors):
                return base_colors[:n_colors]
            else:
                # 生成更多颜色
                extended_colors = base_colors.copy()
                while len(extended_colors) < n_colors:
                    # 使用HSV颜色空间生成变化色
                    for base_color in base_colors:
                        if len(extended_colors) >= n_colors:
                            break
                        # 简化的颜色变化
                        extended_colors.append(self._vary_color(base_color))
                
                return extended_colors[:n_colors]
                
        except Exception as e:
            self.logger.error(f"获取颜色序列失败: {str(e)}")
            return ['#1f77b4'] * n_colors
    
    def _vary_color(self, color: str) -> str:
        """生成颜色变化"""
        try:
            # 简化的颜色变化：调整亮度
            import colorsys
            
            # 转换为RGB
            if color.startswith('#'):
                hex_color = color[1:]
                rgb = tuple(int(hex_color[i:i+2], 16) / 255.0 for i in (0, 2, 4))
            else:
                return color
            
            # 转换为HSV
            hsv = colorsys.rgb_to_hsv(*rgb)
            
            # 调整饱和度和亮度
            new_hsv = (hsv[0], min(1.0, hsv[1] * 0.8), min(1.0, hsv[2] * 1.2))
            
            # 转换回RGB
            new_rgb = colorsys.hsv_to_rgb(*new_hsv)
            
            # 转换为十六进制
            hex_color = '#{:02x}{:02x}{:02x}'.format(
                int(new_rgb[0] * 255),
                int(new_rgb[1] * 255),
                int(new_rgb[2] * 255)
            )
            
            return hex_color
            
        except Exception:
            return color
    
    def get_bilingual_text(self, chinese_text: str, english_text: str) -> str:
        """获取双语文本"""
        if not self.bilingual_mode:
            return chinese_text if self.primary_language == 'zh' else english_text
        
        if self.primary_language == 'zh':
            return f"{chinese_text}\n{english_text}"
        else:
            return f"{english_text}\n{chinese_text}"
    
    def get_figure_size(self, size_type: str = 'default') -> Tuple[int, int]:
        """获取图形尺寸"""
        size_map = {
            'default': self.default_figure_size,
            'large': self.large_figure_size,
            'small': self.small_figure_size
        }
        
        return size_map.get(size_type, self.default_figure_size)
    
    def create_custom_colormap(self, colors: List[str], name: str = 'custom') -> Any:
        """创建自定义颜色映射"""
        try:
            from matplotlib.colors import LinearSegmentedColormap
            
            # 创建颜色映射
            cmap = LinearSegmentedColormap.from_list(name, colors, N=256)
            
            return cmap
            
        except Exception as e:
            self.logger.error(f"创建自定义颜色映射失败: {str(e)}")
            return plt.cm.viridis
    
    def apply_chart_style(self, ax, title: str = "", xlabel: str = "", ylabel: str = ""):
        """应用图表样式"""
        try:
            # 设置标题
            if title:
                ax.set_title(title, fontsize=self.current_font.title_size, 
                           fontweight='bold', color=self.current_palette.text, pad=20)
            
            # 设置轴标签
            if xlabel:
                ax.set_xlabel(xlabel, fontsize=self.current_font.label_size, 
                            color=self.current_palette.text)
            
            if ylabel:
                ax.set_ylabel(ylabel, fontsize=self.current_font.label_size, 
                            color=self.current_palette.text)
            
            # 设置刻度标签
            ax.tick_params(axis='both', which='major', labelsize=self.current_font.tick_size,
                         colors=self.current_palette.text)
            
            # 设置网格
            ax.grid(True, alpha=0.3, color=self.current_palette.grid, linewidth=0.5)
            
            # 设置脊柱样式
            for spine in ax.spines.values():
                spine.set_color(self.current_palette.grid)
                spine.set_linewidth(0.8)
            
            # 设置背景色
            ax.set_facecolor(self.current_palette.background)
            
        except Exception as e:
            self.logger.error(f"应用图表样式失败: {str(e)}")
    
    def add_professional_annotations(self, ax, annotations: List[Dict[str, Any]]):
        """添加专业注释"""
        try:
            for annotation in annotations:
                x = annotation.get('x', 0)
                y = annotation.get('y', 0)
                text = annotation.get('text', '')
                style = annotation.get('style', 'default')
                
                if style == 'arrow':
                    ax.annotate(text, xy=(x, y), xytext=(x+0.1, y+0.1),
                               arrowprops=dict(arrowstyle='->', 
                                             color=self.current_palette.accent,
                                             lw=1.5),
                               fontsize=self.current_font.annotation_size,
                               color=self.current_palette.text,
                               bbox=dict(boxstyle="round,pad=0.3", 
                                       facecolor=self.current_palette.background,
                                       edgecolor=self.current_palette.accent,
                                       alpha=0.8))
                else:
                    ax.text(x, y, text, 
                           fontsize=self.current_font.annotation_size,
                           color=self.current_palette.text,
                           ha='center', va='center',
                           bbox=dict(boxstyle="round,pad=0.3",
                                   facecolor=self.current_palette.background,
                                   alpha=0.7))
                           
        except Exception as e:
            self.logger.error(f"添加专业注释失败: {str(e)}")
    
    def save_high_quality_figure(self, fig, filepath: str, **kwargs):
        """保存高质量图形"""
        try:
            # 默认保存参数
            save_params = {
                'dpi': self.dpi,
                'format': self.figure_format,
                'bbox_inches': 'tight',
                'facecolor': self.current_palette.background,
                'edgecolor': 'none',
                'transparent': False,
                'pad_inches': 0.2
            }
            
            # 更新用户参数
            save_params.update(kwargs)
            
            # 保存图形
            fig.savefig(filepath, **save_params)
            
            self.logger.info(f"高质量图形已保存: {filepath}")
            
        except Exception as e:
            self.logger.error(f"保存高质量图形失败: {str(e)}")
    
    def create_professional_legend(self, ax, labels: List[str], colors: List[str] = None,
                                 location: str = 'best'):
        """创建专业图例"""
        try:
            if colors is None:
                colors = self.get_color_sequence(len(labels))
            
            # 创建图例元素
            legend_elements = []
            for label, color in zip(labels, colors):
                from matplotlib.patches import Patch
                legend_elements.append(Patch(facecolor=color, label=label))
            
            # 添加图例
            legend = ax.legend(handles=legend_elements, 
                             loc=location,
                             fontsize=self.current_font.legend_size,
                             frameon=True,
                             fancybox=True,
                             shadow=True,
                             framealpha=0.9,
                             facecolor=self.current_palette.background,
                             edgecolor=self.current_palette.grid)
            
            # 设置图例文本颜色
            for text in legend.get_texts():
                text.set_color(self.current_palette.text)
                
        except Exception as e:
            self.logger.error(f"创建专业图例失败: {str(e)}")


def create_chart_config() -> ChartConfig:
    """
    创建图表配置实例
    
    Returns:
        配置好的图表配置管理器
    """
    return ChartConfig()