# 立面优化系统统一配置文件
# 整合所有配置项，简化配置管理

# 系统基础配置
system:
  debug_mode: false
  log_level: "INFO"
  cache_enabled: true
  max_workers: 4  # 并行处理线程数

# 优化算法配置
optimization:
  # NSGA-III参数
  algorithm: "NSGA-III"
  population_size: 140  # 减少种群大小以提高速度
  max_generations: 10   # 减少最大代数
  max_time_seconds: 600 # 10分钟超时
  
  # 遗传算子参数
  crossover_rate: 0.8
  mutation_rate: 0.3
  mutation_strength: 0.35
  
  # 收敛控制
  convergence_threshold: 1e-4
  stagnation_generations: 5  # 减少停滞检测代数
  min_improvement_ratio: 0.001
  
  # 性能优化设置
  use_sampling: true      # 启用采样优化
  sample_ratio: 0.3       # 采样比例
  use_caching: true       # 启用缓存
  parallel_evaluation: false  # 暂时禁用并行评估

# 目标函数配置
objectives:
  # 能耗目标
  energy:
    weight: 1.0
    target_range: [50, 80]  # kWh/m²·year
    baseline: 120
    
  # 热工性能目标
  thermal:
    weight: 1.0
    target_range: [0.8, 1.0]
    comfort_temp_range: [20, 26]  # °C
    
  # 成本目标
  cost:
    weight: 1.0
    target_range: [500, 15000]  # 元
    baseline: 2000

# 约束条件
constraints:
  window_wall_ratio:
    min: 0.1
    max: 0.8
  
  window_size:
    min_width: 0.5
    max_width: 3.0
    min_height: 0.5
    max_height: 2.5
  
  wall_margin: 0.2

# 图像处理配置
image_processing:
  pixel_to_meter_ratio: 0.01
  
  color_mapping:
    walls:
      min: [180, 180, 180]
      max: [255, 255, 255]
    windows:
      min: [0, 100, 200]
      max: [50, 150, 255]
    doors:
      min: [240, 240, 0]
      max: [255, 255, 20]
    shading:
      min: [0, 200, 0]
      max: [20, 255, 20]
    frames:
      min: [0, 0, 0]
      max: [30, 30, 30]
  
  preprocessing:
    noise_filter_kernel: 3
    min_contour_area: 100
    max_contour_area: 50000

# 性能评估配置
performance:
  # 热工性能
  thermal:
    heating_setpoint: 20.0
    cooling_setpoint: 26.0
    adaptive_comfort: true
    sample_hours: 2000  # 采样小时数
    
  # 能耗分析
  energy:
    lighting_power_density: 8.0  # W/m²
    hvac_cop_heating: 3.0
    hvac_cop_cooling: 3.5
    use_vectorized_calculation: true
    
  # 成本分析
  cost:
    discount_rate: 0.05
    design_life: 20
    energy_price: 0.6  # 元/kWh

# 可视化配置
visualization:
  style: "modern"
  figure_size: [12, 8]
  dpi: 300
  save_format: "png"
  output_formats: ["png"]
  
  # 图表配置
  charts:
    show_convergence: true
    show_pareto_front: true
    show_3d_visualization: true
    max_solutions_display: 50

# 输出配置
output:
  base_directory: "outputs"
  save_intermediate: false  # 禁用中间结果保存以提高速度
  create_debug_visualizations: false
  
  # 报告配置
  reports:
    generate_detailed_report: true
    include_charts: true
    format: "markdown"

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  # 日志文件配置
  files:
    main: "logs/main.log"
    optimization: "logs/optimization.log"
    performance: "logs/performance.log"
    error: "logs/error.log"
  
  # 日志轮转
  rotation:
    max_size: "10MB"
    backup_count: 3
    
  # 性能日志
  performance_logging:
    enabled: true
    detailed: false  # 禁用详细性能日志以减少开销

# 缓存配置
cache:
  enabled: true
  directory: "temp/cache"
  max_size: "100MB"
  ttl: 3600  # 1小时过期
  
  # 缓存策略
  strategies:
    objective_evaluation: true
    climate_data: true
    thermal_analysis: true

# 开发和调试配置
development:
  enable_profiling: false
  save_debug_data: false
  verbose_logging: false
  
  # 测试配置
  testing:
    quick_test_generations: 3
    quick_test_population: 50
    timeout_seconds: 300
