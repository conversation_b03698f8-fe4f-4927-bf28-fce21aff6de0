"""
气候数据处理器 - 主控制器
整合EPW解析和朝向调整，提供统一的气候数据处理接口
"""

from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path
import numpy as np

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext, get_performance_logger
from ..core.exceptions import ClimateDataError, handle_exception
from ..core.data_structures import HourlyClimateData, OrientedClimateData, Orientation, parse_orientation
from ..core.utils import FileUtils, DataUtils, TimeUtils, ValidationUtils

from .epw_file_parser import EPWFileParser
from .orientation_adjuster import OrientationAdjuster


class ClimateDataProcessor:
    """
    气候数据处理器主控制器
    
    功能：
    1. 统一的气候数据处理接口
    2. 支持批量处理多个EPW文件
    3. 多朝向并行处理
    4. 气候数据缓存管理
    5. 处理结果验证和统计
    """
    
    def __init__(self):
        """初始化气候数据处理器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger()
        
        # 初始化组件
        self.epw_parser = EPWFileParser()
        self.orientation_adjuster = OrientationAdjuster()
        
        # 配置参数
        self.enable_caching = self.config.get('system.cache_enabled', True)
        self.cache_directory = Path(self.config.get('system.temp_directory', 'temp')) / 'climate_cache'
        
        # 创建缓存目录
        if self.enable_caching:
            FileUtils.ensure_directory(self.cache_directory)
        
        self.logger.info("气候数据处理器初始化完成")
    
    @handle_exception
    def process_climate_data(self, epw_file_path: str, 
                           orientation: str,
                           output_dir: Optional[str] = None,
                           enable_caching: bool = True) -> Dict[str, Any]:
        """
        处理气候数据的主要接口
        
        Args:
            epw_file_path: EPW文件路径
            orientation: 建筑朝向（字符串）
            output_dir: 输出目录
            enable_caching: 是否启用缓存
            
        Returns:
            包含朝向调整后气候数据的完整字典
            
        Raises:
            ClimateDataError: 处理失败时抛出
        """
        with LogContext("气候数据处理", self.logger):
            # 解析朝向
            orientation_enum = parse_orientation(orientation)
            
            # 设置输出目录
            if output_dir is None:
                output_dir = self.config.get_output_directory()
            
            # 创建处理会话
            session_id = TimeUtils.get_timestamp()
            session_dir = Path(output_dir) / f"climate_processing_{session_id}"
            FileUtils.ensure_directory(session_dir)
            
            try:
                # 检查缓存
                cache_key = None
                if enable_caching and self.enable_caching:
                    cache_key = self._generate_cache_key(epw_file_path, orientation)
                    cached_result = self._load_from_cache(cache_key)
                    if cached_result:
                        self.logger.info(f"从缓存加载气候数据: {cache_key}")
                        # 验证缓存数据是否完整
                        if ('oriented_climate_data' in cached_result and 
                            hasattr(cached_result['oriented_climate_data'], 'hourly_data')):
                            cached_result['session_dir'] = str(session_dir)
                            self.logger.info(f"缓存数据验证通过: {len(cached_result['oriented_climate_data'].hourly_data)} 小时数据")
                            return cached_result
                        else:
                            self.logger.warning("缓存数据格式异常，重新处理")
                            # 删除无效缓存
                            try:
                                cache_file = self.cache_directory / f"{cache_key}.json"
                                if cache_file.exists():
                                    cache_file.unlink()
                                    self.logger.info("已删除无效缓存文件")
                            except Exception:
                                pass
                
                # 步骤1: 解析EPW文件
                with self.performance_logger.time_operation("EPW文件解析"):
                    epw_result = self.epw_parser.parse_epw_file(epw_file_path)
                    self.logger.info(f"EPW解析完成: {len(epw_result['hourly_data'])} 小时数据")
                
                # 步骤2: 朝向调整
                with self.performance_logger.time_operation("朝向调整"):
                    oriented_climate_data = self.orientation_adjuster.adjust_climate_for_orientation(
                        epw_result['hourly_data'],
                        orientation_enum,
                        epw_result['header_info'].get('location', {})
                    )
                    self.logger.info(f"朝向调整完成: {orientation}")
                
                # 步骤3: 数据验证
                with self.performance_logger.time_operation("数据验证"):
                    validation_result = self._validate_climate_data(oriented_climate_data)
                    self.logger.info(f"数据验证完成: {'通过' if validation_result.get('is_valid', False) else '存在问题'}")
                
                # 步骤4: 生成处理统计
                processing_stats = self._generate_processing_statistics(
                    epw_result, oriented_climate_data, validation_result
                )
                
                # 步骤5: 整合处理结果
                processing_result = {
                    'session_id': session_id,
                    'epw_file_path': str(epw_file_path),
                    'orientation': orientation,
                    'epw_parsing_result': epw_result,
                    'oriented_climate_data': oriented_climate_data,
                    'validation_result': validation_result,
                    'processing_statistics': processing_stats,
                    'processing_metadata': {
                        'timestamp': TimeUtils.get_timestamp('%Y-%m-%d %H:%M:%S'),
                        'session_dir': str(session_dir),
                        'cache_key': cache_key,
                        'configuration_snapshot': self._get_config_snapshot()
                    }
                }
                
                # 保存结果
                self._save_processing_results(processing_result, session_dir)
                
                # 缓存结果
                if enable_caching and self.enable_caching and cache_key:
                    self._save_to_cache(cache_key, processing_result)
                
                # 生成处理报告
                report_path = self._generate_processing_report(processing_result, session_dir)
                processing_result['report_path'] = str(report_path)
                
                self.logger.info(f"气候数据处理完成: {orientation}朝向, {len(oriented_climate_data.hourly_data)} 小时数据")
                return processing_result
                
            except Exception as e:
                self.logger.error(f"气候数据处理失败: {str(e)}")
                raise ClimateDataError(f"气候数据处理失败: {str(e)}") from e
    
    def process_multiple_orientations(self, epw_file_path: str,
                                    orientations: List[str],
                                    output_dir: Optional[str] = None) -> Dict[str, Any]:
        """
        处理多个朝向的气候数据
        
        Args:
            epw_file_path: EPW文件路径
            orientations: 朝向列表
            output_dir: 输出目录
            
        Returns:
            多朝向处理结果
        """
        with LogContext("多朝向气候数据处理", self.logger):
            batch_id = TimeUtils.get_timestamp()
            
            if output_dir is None:
                output_dir = self.config.get_output_directory()
            
            batch_dir = Path(output_dir) / f"multi_orientation_{batch_id}"
            FileUtils.ensure_directory(batch_dir)
            
            results = {
                'batch_id': batch_id,
                'epw_file_path': str(epw_file_path),
                'orientations': orientations,
                'successful_processing': {},
                'failed_processing': {},
                'comparative_analysis': {},
                'batch_directory': str(batch_dir)
            }
            
            # 首先解析EPW文件（只需解析一次）
            try:
                with self.performance_logger.time_operation("EPW文件解析"):
                    epw_result = self.epw_parser.parse_epw_file(epw_file_path)
                    self.logger.info(f"EPW解析完成，开始处理 {len(orientations)} 个朝向")
            except Exception as e:
                results['failed_processing']['epw_parsing'] = str(e)
                return results
            
            # 处理每个朝向
            for orientation in orientations:
                self.logger.info(f"处理朝向: {orientation}")
                
                try:
                    # 为每个朝向创建子目录
                    orient_dir = batch_dir / f"orientation_{orientation}"
                    FileUtils.ensure_directory(orient_dir)
                    
                    # 朝向调整
                    orientation_enum = parse_orientation(orientation)
                    oriented_climate_data = self.orientation_adjuster.adjust_climate_for_orientation(
                        epw_result['hourly_data'],
                        orientation_enum,
                        epw_result['header_info'].get('location', {})
                    )
                    
                    # 数据验证
                    validation_result = self._validate_climate_data(oriented_climate_data)
                    
                    # 处理统计
                    processing_stats = self._generate_processing_statistics(
                        epw_result, oriented_climate_data, validation_result
                    )
                    
                    # 保存朝向处理结果
                    orient_result = {
                        'orientation': orientation,
                        'oriented_climate_data': oriented_climate_data,
                        'validation_result': validation_result,
                        'processing_statistics': processing_stats,
                        'output_directory': str(orient_dir)
                    }
                    
                    results['successful_processing'][orientation] = orient_result
                    
                    # 保存单个朝向结果
                    self._save_orientation_results(orient_result, orient_dir)
                    
                except Exception as e:
                    error_info = {
                        'orientation': orientation,
                        'error': str(e),
                        'error_type': type(e).__name__
                    }
                    results['failed_processing'][orientation] = error_info
                    self.logger.error(f"朝向处理失败 {orientation}: {str(e)}")
            
            # 生成对比分析
            if len(results['successful_processing']) > 1:
                results['comparative_analysis'] = self._generate_comparative_analysis(
                    results['successful_processing']
                )
            
            # 保存批处理结果
            batch_summary_path = batch_dir / "batch_summary.json"
            DataUtils.save_json(self._make_serializable(results), batch_summary_path)
            
            # 生成朝向对比报告
            self._generate_orientation_comparison_report(results, batch_dir)
            
            self.logger.info(f"多朝向处理完成: {len(results['successful_processing'])} 成功, "
                           f"{len(results['failed_processing'])} 失败")
            
            return results
    
    def get_orientation_recommendations(self, epw_file_path: str) -> Dict[str, Any]:
        """
        获取朝向优化建议
        
        Args:
            epw_file_path: EPW文件路径
            
        Returns:
            朝向建议字典
        """
        try:
            # 解析EPW文件
            epw_result = self.epw_parser.parse_epw_file(epw_file_path)
            
            # 获取朝向建议
            recommendations = self.orientation_adjuster.get_orientation_recommendations(
                epw_result['hourly_data'],
                epw_result['header_info'].get('location', {})
            )
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"获取朝向建议失败: {str(e)}")
            return {'error': str(e)}
    
    def _generate_cache_key(self, epw_file_path: str, orientation: str) -> str:
        """生成缓存键"""
        from ..core.utils import HashUtils
        
        # 基于文件路径、修改时间和朝向生成缓存键
        file_mtime = Path(epw_file_path).stat().st_mtime
        cache_content = f"{epw_file_path}_{file_mtime}_{orientation}"
        
        return HashUtils.calculate_string_hash(cache_content, 'md5')
    
    def _load_from_cache(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """从缓存加载数据并重构对象"""
        try:
            cache_file = self.cache_directory / f"{cache_key}.json"
            if cache_file.exists():
                cached_data = DataUtils.load_json(cache_file)
                # 重构OrientedClimateData对象
                if 'oriented_climate_data' in cached_data:
                    cached_data['oriented_climate_data'] = self._reconstruct_oriented_climate_data(
                        cached_data['oriented_climate_data']
                    )
                return cached_data
            return None
        except Exception as e:
            self.logger.warning(f"加载缓存失败: {str(e)}")
            return None
    
    def _reconstruct_oriented_climate_data(self, data_dict: Dict[str, Any]) -> OrientedClimateData:
        """从字典重构OrientedClimateData对象"""
        try:
            from ..core.data_structures import OrientedClimateData, OrientedSolarData, OrientedWindData, HourlyClimateData, Orientation
            
            # 重构HourlyClimateData列表
            hourly_data = []
            for hour_dict in data_dict.get('hourly_data', []):
                hourly_climate = HourlyClimateData(
                    hour=hour_dict.get('hour', 0),
                    month=hour_dict.get('month', 1),
                    day=hour_dict.get('day', 1),
                    time=hour_dict.get('time', 0),
                    dry_bulb_temperature=hour_dict.get('dry_bulb_temperature', 20.0),
                    dew_point_temperature=hour_dict.get('dew_point_temperature', 15.0),
                    relative_humidity=hour_dict.get('relative_humidity', 50.0),
                    atmospheric_pressure=hour_dict.get('atmospheric_pressure', 101325.0),
                    wind_speed=hour_dict.get('wind_speed', 0.0),
                    wind_direction=hour_dict.get('wind_direction', 0.0),
                    direct_normal_irradiance=hour_dict.get('direct_normal_irradiance', 0.0),
                    diffuse_horizontal_irradiance=hour_dict.get('diffuse_horizontal_irradiance', 0.0),
                    global_horizontal_irradiance=hour_dict.get('global_horizontal_irradiance', 0.0),
                    sky_cover=hour_dict.get('sky_cover', 0.0)
                )
                hourly_data.append(hourly_climate)
            
            # 重构OrientedSolarData
            solar_dict = data_dict.get('solar_data', {})
            solar_data = OrientedSolarData(
                orientation=Orientation(solar_dict.get('orientation', 'south')),
                hourly_irradiance=solar_dict.get('hourly_irradiance', []),
                peak_irradiance=solar_dict.get('peak_irradiance', 0.0),
                daily_totals=solar_dict.get('daily_totals', []),
                seasonal_averages=solar_dict.get('seasonal_averages', {})
            )
            
            # 重构OrientedWindData
            wind_dict = data_dict.get('wind_data', {})
            wind_data = OrientedWindData(
                orientation=Orientation(wind_dict.get('orientation', 'south')),
                hourly_wind_effects=wind_dict.get('hourly_wind_effects', []),
                prevailing_direction_factor=wind_dict.get('prevailing_direction_factor', 1.0),
                seasonal_patterns=wind_dict.get('seasonal_patterns', {})
            )
            
            # 重构OrientedClimateData
            oriented_climate_data = OrientedClimateData(
                orientation=Orientation(data_dict.get('orientation', 'south')),
                hourly_data=hourly_data,
                solar_data=solar_data,
                wind_data=wind_data,
                location_info=data_dict.get('location_info', {})
            )
            
            return oriented_climate_data
            
        except Exception as e:
            self.logger.error(f"重构OrientedClimateData失败: {str(e)}")
            # 如果重构失败，返回一个最小的占位对象
            from ..core.data_structures import OrientedClimateData, OrientedSolarData, OrientedWindData, Orientation
            return OrientedClimateData(
                orientation=Orientation.SOUTH,
                hourly_data=[],
                solar_data=OrientedSolarData(Orientation.SOUTH, [], 0.0, [], {}),
                wind_data=OrientedWindData(Orientation.SOUTH, [], 1.0, {}),
                location_info={}
            )
    
    def _save_to_cache(self, cache_key: str, data: Dict[str, Any]) -> None:
        """保存数据到缓存 - 修复版本，保存完整hourly_data"""
        try:
            cache_file = self.cache_directory / f"{cache_key}.json"
            # 创建包含完整hourly_data的可序列化副本
            serializable_data = self._make_serializable_with_full_data(data)
            DataUtils.save_json(serializable_data, cache_file)
            self.logger.debug(f"数据已缓存: {cache_key}")
        except Exception as e:
            self.logger.warning(f"保存缓存失败: {str(e)}")
    
    def _make_serializable_with_full_data(self, data: Any) -> Any:
        """将数据转换为可序列化格式，保留完整的hourly_data"""
        if isinstance(data, dict):
            result = {}
            for k, v in data.items():
                if k == 'oriented_climate_data' and hasattr(v, 'hourly_data'):
                    # 为OrientedClimateData特殊处理，保存完整数据 
                    result[k] = {
                        'orientation': v.orientation.value,
                        'location_info': v.location_info,
                        'hourly_data': [self._serialize_hourly_data(h) for h in v.hourly_data],
                        'solar_data': {
                            'orientation': v.solar_data.orientation.value,
                            'hourly_irradiance': v.solar_data.hourly_irradiance,
                            'peak_irradiance': v.solar_data.peak_irradiance,
                            'daily_totals': v.solar_data.daily_totals,
                            'seasonal_averages': v.solar_data.seasonal_averages
                        },
                        'wind_data': {
                            'orientation': v.wind_data.orientation.value,
                            'hourly_wind_effects': v.wind_data.hourly_wind_effects,
                            'prevailing_direction_factor': v.wind_data.prevailing_direction_factor,
                            'seasonal_patterns': v.wind_data.seasonal_patterns
                        }
                    }
                else:
                    result[k] = self._make_serializable_with_full_data(v)
            return result
        elif isinstance(data, list):
            return [self._make_serializable_with_full_data(item) for item in data]
        elif hasattr(data, 'to_dict'):
            return data.to_dict()
        elif isinstance(data, (int, float, str, bool)) or data is None:
            return data
        else:
            return str(data)
    
    def _serialize_hourly_data(self, hourly_data) -> Dict[str, Any]:
        """序列化单个小时气候数据"""
        return {
            'hour': hourly_data.hour,
            'month': hourly_data.month,
            'day': hourly_data.day,
            'time': hourly_data.time,
            'dry_bulb_temperature': hourly_data.dry_bulb_temperature,
            'dew_point_temperature': hourly_data.dew_point_temperature,
            'relative_humidity': hourly_data.relative_humidity,
            'atmospheric_pressure': hourly_data.atmospheric_pressure,
            'wind_speed': hourly_data.wind_speed,
            'wind_direction': hourly_data.wind_direction,
            'direct_normal_irradiance': hourly_data.direct_normal_irradiance,
            'diffuse_horizontal_irradiance': hourly_data.diffuse_horizontal_irradiance,
            'global_horizontal_irradiance': hourly_data.global_horizontal_irradiance,
            'sky_cover': hourly_data.sky_cover
        }
    
    def _validate_climate_data(self, oriented_climate_data: OrientedClimateData) -> Dict[str, Any]:
        """验证气候数据"""
        validation_result = {
            'is_valid': True,
            'violations': [],
            'warnings': [],
            'quality_metrics': {}
        }
        
        try:
            hourly_data = oriented_climate_data.hourly_data
            
            # 数据完整性检查
            if len(hourly_data) != 8760:
                validation_result['warnings'].append(
                    f"数据不完整: {len(hourly_data)} / 8760 小时"
                )
            
            # 数据范围检查
            temperatures = [data.dry_bulb_temperature for data in hourly_data]
            if min(temperatures) < -50 or max(temperatures) > 60:
                validation_result['warnings'].append("温度数据存在异常值")
            
            radiation_values = [data.global_horizontal_irradiance for data in hourly_data]
            if max(radiation_values) > 1500:
                validation_result['warnings'].append("辐射数据存在异常值")
            
            # 朝向调整质量检查
            solar_data = oriented_climate_data.solar_data
            if solar_data.peak_irradiance == 0:
                validation_result['violations'].append("朝向调整后太阳辐射为零")
                validation_result['is_valid'] = False
            
            # 计算质量指标
            validation_result['quality_metrics'] = {
                'data_completeness': len(hourly_data) / 8760,
                'temperature_range': max(temperatures) - min(temperatures),
                'radiation_utilization': solar_data.peak_irradiance / max(radiation_values) if max(radiation_values) > 0 else 0,
                'seasonal_variation': self._calculate_seasonal_variation(hourly_data)
            }
            
            return validation_result
            
        except Exception as e:
            self.logger.error(f"气候数据验证失败: {str(e)}")
            return {
                'is_valid': False,
                'violations': [f"验证过程失败: {str(e)}"],
                'warnings': [],
                'quality_metrics': {}
            }
    
    def _calculate_seasonal_variation(self, hourly_data: List[HourlyClimateData]) -> float:
        """计算季节变化度"""
        try:
            seasons = {
                'winter': [12, 1, 2],
                'spring': [3, 4, 5],
                'summer': [6, 7, 8],
                'autumn': [9, 10, 11]
            }
            
            seasonal_temps = {}
            for season, months in seasons.items():
                season_data = [data.dry_bulb_temperature for data in hourly_data if data.month in months]
                if season_data:
                    seasonal_temps[season] = np.mean(season_data)
            
            if len(seasonal_temps) >= 2:
                temp_values = list(seasonal_temps.values())
                return max(temp_values) - min(temp_values)
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def _generate_processing_statistics(self, epw_result: Dict[str, Any],
                                      oriented_climate_data: OrientedClimateData,
                                      validation_result: Dict[str, Any]) -> Dict[str, Any]:
        """生成处理统计"""
        try:
            stats = {
                'epw_file_info': {
                    'location': epw_result['header_info'].get('location', {}),
                    'data_quality': epw_result['quality_report'],
                    'climate_statistics': epw_result['statistics']
                },
                'orientation_adjustment': {
                    'orientation': oriented_climate_data.orientation.value,
                    'solar_peak_irradiance': oriented_climate_data.solar_data.peak_irradiance,
                    'seasonal_solar_averages': oriented_climate_data.solar_data.seasonal_averages,
                    'wind_patterns': oriented_climate_data.wind_data.seasonal_patterns
                },
                'data_validation': {
                    'is_valid': validation_result['is_valid'],
                    'quality_score': self._calculate_overall_quality_score(validation_result),
                    'violation_count': len(validation_result.get('violations', [])),
                    'warning_count': len(validation_result.get('warnings', []))
                },
                'processing_performance': {
                    'data_count': len(oriented_climate_data.hourly_data),
                    'processing_timestamp': TimeUtils.get_timestamp('%Y-%m-%d %H:%M:%S')
                }
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"生成处理统计失败: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_overall_quality_score(self, validation_result: Dict[str, Any]) -> float:
        """计算总体质量评分"""
        try:
            quality_metrics = validation_result.get('quality_metrics', {})
            
            # 基础分数
            base_score = 1.0
            
            # 数据完整性扣分
            completeness = quality_metrics.get('data_completeness', 1.0)
            if completeness < 1.0:
                base_score -= (1.0 - completeness) * 0.3
            
            # 违规扣分
            violations = len(validation_result.get('violations', []))
            base_score -= violations * 0.2
            
            # 警告扣分
            warnings = len(validation_result.get('warnings', []))
            base_score -= warnings * 0.05
            
            return max(0.0, min(1.0, base_score))
            
        except Exception:
            return 0.0
    
    def _get_config_snapshot(self) -> Dict[str, Any]:
        """获取配置快照"""
        return {
            'climate_processing': self.config.get_section('climate_processing'),
            'system': self.config.get_section('system')
        }
    
    def _save_processing_results(self, processing_result: Dict[str, Any], 
                               session_dir: Path) -> None:
        """保存处理结果"""
        try:
            # 保存完整结果（序列化版本）
            serializable_result = self._make_serializable(processing_result)
            DataUtils.save_json(serializable_result, session_dir / "climate_processing_result.json")
            
            # 保存朝向气候数据
            oriented_data = processing_result['oriented_climate_data']
            DataUtils.save_json(oriented_data.to_dict(), session_dir / "oriented_climate_data.json")
            
            # 保存处理统计
            DataUtils.save_json(
                processing_result['processing_statistics'],
                session_dir / "processing_statistics.json"
            )
            
            self.logger.debug(f"处理结果已保存到: {session_dir}")
            
        except Exception as e:
            self.logger.error(f"保存处理结果失败: {str(e)}")
    
    def _save_orientation_results(self, orient_result: Dict[str, Any],
                                output_dir: Path) -> None:
        """保存单个朝向结果"""
        try:
            serializable_result = self._make_serializable(orient_result)
            DataUtils.save_json(serializable_result, output_dir / "orientation_result.json")
            
            # 保存朝向气候数据
            oriented_data = orient_result['oriented_climate_data']
            DataUtils.save_json(oriented_data.to_dict(), output_dir / "climate_data.json")
            
        except Exception as e:
            self.logger.error(f"保存朝向结果失败: {str(e)}")
    
    def _make_serializable(self, data: Any) -> Any:
        """将数据转换为可序列化格式"""
        if hasattr(data, 'to_dict'):
            return data.to_dict()
        elif isinstance(data, dict):
            return {k: self._make_serializable(v) for k, v in data.items()}
        elif isinstance(data, list):
            return [self._make_serializable(item) for item in data]
        elif isinstance(data, (int, float, str, bool)) or data is None:
            return data
        else:
            return str(data)
    
    def _generate_comparative_analysis(self, successful_processing: Dict[str, Any]) -> Dict[str, Any]:
        """生成朝向对比分析"""
        try:
            analysis = {
                'orientation_ranking': {},
                'performance_comparison': {},
                'seasonal_analysis': {},
                'recommendations': []
            }
            
            # 收集各朝向的性能数据
            orientation_data = {}
            
            for orientation, result in successful_processing.items():
                solar_data = result['oriented_climate_data'].solar_data
                wind_data = result['oriented_climate_data'].wind_data
                
                orientation_data[orientation] = {
                    'peak_solar': solar_data.peak_irradiance,
                    'annual_solar': sum(solar_data.daily_totals),
                    'seasonal_solar': solar_data.seasonal_averages,
                    'wind_factor': wind_data.prevailing_direction_factor
                }
            
            # 朝向排名
            solar_ranking = sorted(orientation_data.items(), 
                                 key=lambda x: x[1]['annual_solar'], reverse=True)
            analysis['orientation_ranking']['solar_performance'] = [o[0] for o in solar_ranking]
            
            # 性能对比
            for metric in ['peak_solar', 'annual_solar', 'wind_factor']:
                analysis['performance_comparison'][metric] = {
                    orient: data[metric] for orient, data in orientation_data.items()
                }
            
            # 季节性分析
            for season in ['spring', 'summer', 'autumn', 'winter']:
                season_ranking = sorted(
                    orientation_data.items(),
                    key=lambda x: x[1]['seasonal_solar'].get(season, 0),
                    reverse=True
                )
                analysis['seasonal_analysis'][season] = [o[0] for o in season_ranking]
            
            # 生成建议
            best_overall = solar_ranking[0][0]
            analysis['recommendations'].append(f"综合性能最佳朝向: {best_overall}")
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"生成对比分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _generate_processing_report(self, processing_result: Dict[str, Any],
                                  session_dir: Path) -> Path:
        """生成处理报告"""
        try:
            report_path = session_dir / "climate_processing_report.md"
            
            epw_result = processing_result['epw_parsing_result']
            oriented_data = processing_result['oriented_climate_data']
            stats = processing_result['processing_statistics']
            validation = processing_result['validation_result']
            
            location = epw_result['header_info'].get('location', {})
            
            report_content = f"""# 气候数据处理报告

## 基本信息
- **会话ID**: {processing_result['session_id']}
- **EPW文件**: {processing_result['epw_file_path']}
- **建筑朝向**: {processing_result['orientation']}
- **处理时间**: {processing_result['processing_metadata']['timestamp']}

## 位置信息
- **城市**: {location.get('city', 'Unknown')}
- **国家**: {location.get('country', 'Unknown')}
- **纬度**: {location.get('latitude', 0):.2f}°
- **经度**: {location.get('longitude', 0):.2f}°
- **海拔**: {location.get('elevation', 0):.0f} m

## EPW数据质量
- **数据完整性**: {epw_result['quality_report'].get('data_completeness', 0):.1%}
- **缺失数据**: {epw_result['quality_report'].get('missing_data_count', 0)} 小时
- **异常值**: {epw_result['quality_report'].get('outlier_count', 0)} 个

## 气候特征
- **年平均温度**: {epw_result['statistics'].get('temperature', {}).get('mean', 0):.1f}°C
- **温度范围**: {epw_result['statistics'].get('temperature', {}).get('min', 0):.1f} ~ {epw_result['statistics'].get('temperature', {}).get('max', 0):.1f}°C
- **年总辐射**: {epw_result['statistics'].get('solar_radiation', {}).get('annual_total', 0):.0f} Wh/m²
- **平均风速**: {epw_result['statistics'].get('wind_speed', {}).get('mean', 0):.1f} m/s

## 朝向调整结果
- **朝向**: {oriented_data.orientation.value}
- **峰值辐射**: {oriented_data.solar_data.peak_irradiance:.0f} W/m²
- **季节性太阳辐射**:
  - 春季: {oriented_data.solar_data.seasonal_averages.get('spring', 0):.0f} W/m²
  - 夏季: {oriented_data.solar_data.seasonal_averages.get('summer', 0):.0f} W/m²
  - 秋季: {oriented_data.solar_data.seasonal_averages.get('autumn', 0):.0f} W/m²
  - 冬季: {oriented_data.solar_data.seasonal_averages.get('winter', 0):.0f} W/m²

## 数据验证
- **验证状态**: {'✅ 通过' if validation['is_valid'] else '❌ 未通过'}
- **质量评分**: {stats['data_validation']['quality_score']:.2f}
- **违规数量**: {stats['data_validation']['violation_count']} 个
- **警告数量**: {stats['data_validation']['warning_count']} 个
"""
            
            # 添加违规和警告详情
            if validation.get('violations'):
                report_content += "\n## 验证违规\n"
                for violation in validation['violations']:
                    report_content += f"- {violation}\n"
            
            if validation.get('warnings'):
                report_content += "\n## 警告信息\n"
                for warning in validation['warnings']:
                    report_content += f"- {warning}\n"
            
            report_content += f"""
## 处理配置
- **缓存启用**: {'是' if self.enable_caching else '否'}
- **输出目录**: {session_dir}

## 文件输出
- `climate_processing_result.json`: 完整处理结果
- `oriented_climate_data.json`: 朝向调整后气候数据
- `processing_statistics.json`: 处理统计信息

---
*报告生成时间: {TimeUtils.get_timestamp('%Y-%m-%d %H:%M:%S')}*
"""
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"处理报告已生成: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"生成处理报告失败: {str(e)}")
            return session_dir / "report_generation_failed.txt"
    
    def _generate_orientation_comparison_report(self, results: Dict[str, Any],
                                              batch_dir: Path) -> None:
        """生成朝向对比报告"""
        try:
            report_path = batch_dir / "orientation_comparison_report.md"
            
            successful = results['successful_processing']
            comparative = results.get('comparative_analysis', {})
            
            report_content = f"""# 朝向对比分析报告

## 批处理信息
- **批处理ID**: {results['batch_id']}
- **EPW文件**: {results['epw_file_path']}
- **处理朝向**: {', '.join(results['orientations'])}
- **成功处理**: {len(successful)} 个朝向
- **失败处理**: {len(results['failed_processing'])} 个朝向

## 朝向性能排名

### 太阳能性能排名
"""
            
            if 'solar_performance' in comparative.get('orientation_ranking', {}):
                for i, orientation in enumerate(comparative['orientation_ranking']['solar_performance'], 1):
                    report_content += f"{i}. {orientation}\n"
            
            report_content += "\n### 季节性性能分析\n"
            
            for season in ['spring', 'summer', 'autumn', 'winter']:
                if season in comparative.get('seasonal_analysis', {}):
                    season_ranking = comparative['seasonal_analysis'][season]
                    report_content += f"\n**{season.title()}季节排名**: {', '.join(season_ranking[:3])}\n"
            
            # 添加性能数据表格
            if 'performance_comparison' in comparative:
                report_content += "\n## 性能对比数据\n\n| 朝向 | 峰值辐射(W/m²) | 年总辐射 | 风向因子 |\n|-----|-------------|---------|--------|\n"
                
                perf_comp = comparative['performance_comparison']
                for orientation in successful.keys():
                    peak_solar = perf_comp.get('peak_solar', {}).get(orientation, 0)
                    annual_solar = perf_comp.get('annual_solar', {}).get(orientation, 0)
                    wind_factor = perf_comp.get('wind_factor', {}).get(orientation, 0)
                    
                    report_content += f"| {orientation} | {peak_solar:.0f} | {annual_solar:.0f} | {wind_factor:.2f} |\n"
            
            # 添加建议
            if 'recommendations' in comparative:
                report_content += "\n## 优化建议\n"
                for rec in comparative['recommendations']:
                    report_content += f"- {rec}\n"
            
            report_content += f"""
---
*报告生成时间: {TimeUtils.get_timestamp('%Y-%m-%d %H:%M:%S')}*
"""
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            self.logger.info(f"朝向对比报告已生成: {report_path}")
            
        except Exception as e:
            self.logger.error(f"生成朝向对比报告失败: {str(e)}")


def create_climate_data_processor() -> ClimateDataProcessor:
    """
    创建气候数据处理器实例
    
    Returns:
        配置好的气候数据处理器
    """
    return ClimateDataProcessor()