# 图像处理配置 - 严格禁止模拟数据
# 所有参数必须基于真实测量数据

image_processing:
  # 像素到米的转换比例 - 必须根据实际图像标定
  # 示例: 如果图像中1米对应100像素，则比例为0.01
  # 警告: 此参数必须通过实际测量确定，禁止使用估算值
  pixel_to_meter_ratio: null  # 必须在使用前明确设置
  
  # 颜色映射配置 - 基于真实立面识别结果
  color_mapping:
    walls:
      min: [125, 125, 125]  # 灰色下限 - 基于实际分析BGR[128 128 128]
      max: [135, 135, 135]  # 灰色上限
    windows:
      min: [250, 0, 250]    # 紫红色下限 - 基于实际分析BGR[254 0 255]
      max: [255, 5, 255]    # 紫红色上限
    doors:
      min: [0, 150, 0]      # 绿色下限
      max: [100, 255, 100]  # 绿色上限
    shading:
      min: [200, 0, 0]      # 红色下限
      max: [255, 100, 100]  # 红色上限
    frames:
      min: [200, 200, 0]    # 黄色下限
      max: [255, 255, 100]  # 黄色上限
  
  # 颜色匹配策略
  color_matching:
    strategy: "range"       # 使用范围匹配
    tolerance: 15           # 颜色容差
    use_hsv: false         # 使用BGR颜色空间
  
  # 预处理参数
  preprocessing:
    noise_filter_kernel: 3    # 噪声过滤核大小
    min_contour_area: 100     # 最小轮廓面积（像素）
    max_contour_area: 50000   # 最大轮廓面积（像素）

# 数据验证配置
data_validation:
  # 严格验证模式
  strict_validation: true
  allow_simulated_data: false
  
  # 合理性检查参数
  pixel_to_meter_ratio_range:
    min: 0.001  # 最小比例 (1像素=1毫米)
    max: 0.1    # 最大比例 (1像素=10厘米)
  
  facade_area_range:
    min: 10.0     # 最小立面面积（平方米）
    max: 10000.0  # 最大立面面积（平方米）
  
  # 图像质量要求
  image_quality:
    min_resolution: [200, 200]  # 最小分辨率
    max_aspect_ratio: 5.0       # 最大宽高比
    min_aspect_ratio: 0.2       # 最小宽高比
    min_brightness: 20          # 最小亮度
    max_brightness: 235         # 最大亮度
    min_contrast: 10            # 最小对比度

# 几何计算约束
geometry_constraints:
  window_size:
    min_width: 0.3    # 最小窗户宽度（米）
    max_width: 10.0   # 最大窗户宽度（米）
    min_height: 0.3   # 最小窗户高度（米）
    max_height: 5.0   # 最大窗户高度（米）
  
  window_wall_ratio:
    min: 0.05   # 最小窗墙比
    max: 0.9    # 最大窗墙比
  
  wall_margin: 0.2  # 墙体边距（米）

# 禁用的功能 - 防止意外使用模拟数据
disabled_features:
  - "mock_data_generation"
  - "synthetic_image_creation"
  - "random_facade_generation"
  - "test_data_simulation"