"""
朝向调整器
根据建筑朝向调整气候数据，特别是太阳辐射和风向数据
"""

import numpy as np
import math
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import OrientationAdjustmentError, handle_exception
from ..core.data_structures import (
    HourlyClimateData, OrientedClimateData, OrientedSolarData, 
    OrientedWindData, Orientation
)
from ..core.utils import MathUtils


class OrientationAdjuster:
    """
    建筑朝向调整器
    
    功能：
    1. 根据建筑朝向调整太阳辐射数据
    2. 调整风向对不同朝向立面的影响
    3. 计算朝向相关的热负荷系数
    4. 提供季节性朝向修正
    5. 生成朝向优化建议
    """
    
    def __init__(self):
        """初始化朝向调整器"""
        self.config = get_config() 
        self.logger = get_logger(__name__)
        
        # 获取朝向调整配置
        climate_config = self.config.get_section('climate_processing')
        orientation_config = climate_config.get('orientation_adjustment', {})
        
        # 太阳辐射修正系数
        self.solar_factors = orientation_config.get('solar_factor', {
            'south': 1.0,
            'southeast': 0.9,
            'southwest': 0.9,  
            'east': 0.7,
            'west': 0.7,
            'north': 0.3,
            'northeast': 0.5,
            'northwest': 0.5
        })
        
        # 风向影响系数
        self.wind_factors = orientation_config.get('wind_factor', {
            'south': 0.8,
            'southeast': 0.9,
            'southwest': 0.9,
            'east': 1.0,
            'west': 1.0,
            'north': 0.6,
            'northeast': 0.8,
            'northwest': 0.8
        })
        
        # 朝向角度映射
        self.orientation_angles = {
            Orientation.SOUTH: 180,
            Orientation.SOUTHEAST: 135,
            Orientation.SOUTHWEST: 225,
            Orientation.EAST: 90,
            Orientation.WEST: 270,
            Orientation.NORTH: 0,
            Orientation.NORTHEAST: 45,
            Orientation.NORTHWEST: 315
        }
        
        self.logger.info("朝向调整器初始化完成")
    
    @handle_exception
    def adjust_climate_for_orientation(self, hourly_data: List[HourlyClimateData],
                                     orientation: Orientation,
                                     location_info: Optional[Dict[str, Any]] = None) -> OrientedClimateData:
        """
        根据建筑朝向调整气候数据
        
        Args:
            hourly_data: 原始小时气候数据
            orientation: 建筑朝向
            location_info: 位置信息（包含纬度、经度等）
            
        Returns:
            调整后的朝向气候数据
            
        Raises:
            OrientationAdjustmentError: 调整失败时抛出
        """
        with LogContext(f"朝向调整 - {orientation.value}", self.logger):
            try:
                # 计算太阳辐射调整
                solar_data = self._calculate_oriented_solar_data(
                    hourly_data, orientation, location_info
                )
                
                # 计算风向调整
                wind_data = self._calculate_oriented_wind_data(
                    hourly_data, orientation
                )
                
                # 调整每小时数据
                adjusted_hourly_data = self._adjust_hourly_climate_data(
                    hourly_data, orientation, solar_data, wind_data
                )
                
                # 创建朝向气候数据对象
                oriented_climate_data = OrientedClimateData(
                    orientation=orientation,
                    hourly_data=adjusted_hourly_data,
                    solar_data=solar_data,
                    wind_data=wind_data,
                    location_info=location_info or {}
                )
                
                self.logger.info(f"朝向调整完成: {orientation.value}, "
                               f"调整了 {len(adjusted_hourly_data)} 小时数据")
                
                return oriented_climate_data
                
            except Exception as e:
                raise OrientationAdjustmentError(f"朝向调整失败: {str(e)}") from e
    
    def _calculate_oriented_solar_data(self, hourly_data: List[HourlyClimateData],
                                     orientation: Orientation,
                                     location_info: Optional[Dict[str, Any]]) -> OrientedSolarData:
        """计算朝向相关的太阳辐射数据"""
        try:
            # 获取基础太阳辐射修正系数
            base_solar_factor = self.solar_factors.get(orientation.value, 1.0)
            
            # 计算每小时朝向辐射
            hourly_irradiance = []
            daily_totals = []
            current_day_total = 0.0
            current_day = hourly_data[0].day if hourly_data else 1
            
            for i, data in enumerate(hourly_data):
                # 计算太阳位置和朝向辐射
                oriented_irradiance = self._calculate_hourly_oriented_irradiance(
                    data, orientation, location_info, i
                )
                
                hourly_irradiance.append(oriented_irradiance)
                current_day_total += oriented_irradiance
                
                # 检查是否到新的一天
                if data.day != current_day or i == len(hourly_data) - 1:
                    daily_totals.append(current_day_total)
                    current_day_total = 0.0
                    current_day = data.day
            
            # 计算季节性平均值
            seasonal_averages = self._calculate_seasonal_solar_averages(
                hourly_data, hourly_irradiance
            )
            
            # 找到峰值辐射
            peak_irradiance = max(hourly_irradiance) if hourly_irradiance else 0
            
            solar_data = OrientedSolarData(
                orientation=orientation,
                hourly_irradiance=hourly_irradiance,
                peak_irradiance=peak_irradiance,
                daily_totals=daily_totals,
                seasonal_averages=seasonal_averages
            )
            
            return solar_data
            
        except Exception as e:
            self.logger.error(f"计算朝向太阳辐射数据失败: {str(e)}")
            # 返回默认值
            return OrientedSolarData(
                orientation=orientation,
                hourly_irradiance=[0.0] * len(hourly_data),
                peak_irradiance=0.0,
                daily_totals=[0.0] * 365,
                seasonal_averages={'spring': 0, 'summer': 0, 'autumn': 0, 'winter': 0}
            )
    
    def _calculate_hourly_oriented_irradiance(self, data: HourlyClimateData,
                                            orientation: Orientation,
                                            location_info: Optional[Dict[str, Any]],
                                            hour_index: int) -> float:
        """计算单小时朝向辐射"""
        try:
            # 基础辐射值
            base_irradiance = data.global_horizontal_irradiance
            
            # 获取朝向修正系数
            base_factor = self.solar_factors.get(orientation.value, 1.0)
            
            # 时间修正（考虑太阳角度）
            time_factor = self._calculate_time_correction_factor(
                data.time, data.month, orientation
            )
            
            # 季节修正
            seasonal_factor = self._calculate_seasonal_correction_factor(
                data.month, orientation
            )
            
            # 天空条件修正
            sky_factor = self._calculate_sky_condition_factor(data.sky_cover)
            
            # 地理位置修正
            location_factor = 1.0
            if location_info and 'latitude' in location_info:
                location_factor = self._calculate_location_correction_factor(
                    location_info['latitude'], data.month, orientation
                )
            
            # 综合修正
            total_factor = base_factor * time_factor * seasonal_factor * sky_factor * location_factor
            
            # 计算最终朝向辐射
            oriented_irradiance = base_irradiance * total_factor
            
            # 确保非负值
            return max(0.0, oriented_irradiance)
            
        except Exception as e:
            self.logger.warning(f"计算第{hour_index}小时朝向辐射失败: {str(e)}")
            return 0.0
    
    def _calculate_time_correction_factor(self, hour: int, month: int,
                                        orientation: Orientation) -> float:
        """计算时间修正系数"""
        try:
            # 获取朝向角度
            orientation_angle = self.orientation_angles.get(orientation, 180)
            
            # 太阳时角（简化计算）
            solar_hour = hour - 12  # 以正午为0点
            solar_angle = solar_hour * 15  # 每小时15度
            
            # 计算朝向与太阳的角度差
            angle_diff = abs(orientation_angle - (180 + solar_angle))
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            # 角度修正系数（余弦函数）
            angle_factor = max(0, math.cos(math.radians(angle_diff)))
            
            # 时段加权（早晚辐射较弱）
            if 6 <= hour <= 18:
                time_weight = 1.0
            elif 5 <= hour < 6 or 18 < hour <= 19:
                time_weight = 0.3
            else:
                time_weight = 0.0
            
            return angle_factor * time_weight
            
        except Exception:
            return 1.0
    
    def _calculate_seasonal_correction_factor(self, month: int,
                                            orientation: Orientation) -> float:
        """计算季节修正系数"""
        try:
            # 季节基础系数
            if month in [12, 1, 2]:  # 冬季
                season = 'winter'
            elif month in [3, 4, 5]:  # 春季
                season = 'spring'
            elif month in [6, 7, 8]:  # 夏季
                season = 'summer'
            else:  # 秋季
                season = 'autumn'
            
            # 不同朝向的季节修正
            seasonal_adjustments = {
                'south': {'winter': 1.2, 'spring': 1.0, 'summer': 0.8, 'autumn': 1.0},
                'north': {'winter': 0.3, 'spring': 0.4, 'summer': 0.5, 'autumn': 0.4},
                'east': {'winter': 0.8, 'spring': 1.0, 'summer': 1.0, 'autumn': 0.8},
                'west': {'winter': 0.8, 'spring': 1.0, 'summer': 1.0, 'autumn': 0.8},
                'southeast': {'winter': 1.1, 'spring': 1.0, 'summer': 0.9, 'autumn': 1.0},
                'southwest': {'winter': 1.1, 'spring': 1.0, 'summer': 0.9, 'autumn': 1.0},
                'northeast': {'winter': 0.6, 'spring': 0.8, 'summer': 0.8, 'autumn': 0.6},
                'northwest': {'winter': 0.6, 'spring': 0.8, 'summer': 0.8, 'autumn': 0.6}
            }
            
            orientation_key = orientation.value
            if orientation_key in seasonal_adjustments:
                return seasonal_adjustments[orientation_key].get(season, 1.0)
            else:
                return 1.0
                
        except Exception:
            return 1.0
    
    def _calculate_sky_condition_factor(self, sky_cover: float) -> float:
        """计算天空条件修正系数"""
        try:
            # 云量对太阳辐射的影响（0-10等级）
            if sky_cover <= 2:
                return 1.0  # 晴天
            elif sky_cover <= 5:
                return 0.8  # 少云
            elif sky_cover <= 8:
                return 0.5  # 多云
            else:
                return 0.2  # 阴天
                
        except Exception:
            return 1.0
    
    def _calculate_location_correction_factor(self, latitude: float, month: int,
                                            orientation: Orientation) -> float:
        """计算地理位置修正系数"""
        try:
            # 纬度对不同朝向的影响
            lat_rad = math.radians(abs(latitude))
            
            # 太阳赤纬角简化计算
            day_of_year = month * 30  # 简化
            declination = 23.45 * math.sin(math.radians(360 * (284 + day_of_year) / 365))
            decl_rad = math.radians(declination)
            
            # 不同朝向的纬度修正
            if orientation in [Orientation.SOUTH]:
                # 南向在高纬度地区冬季优势明显
                if month in [12, 1, 2]:
                    return 1.0 + 0.3 * (lat_rad / math.pi * 2)
                else:
                    return 1.0
            elif orientation in [Orientation.NORTH]:
                # 北向在高纬度地区劣势明显
                return max(0.1, 1.0 - 0.5 * (lat_rad / math.pi * 2))
            else:
                # 东西向受纬度影响较小
                return 1.0
                
        except Exception:
            return 1.0
    
    def _calculate_seasonal_solar_averages(self, hourly_data: List[HourlyClimateData],
                                         hourly_irradiance: List[float]) -> Dict[str, float]:
        """计算季节性太阳辐射平均值"""
        try:
            seasons = {
                'spring': [3, 4, 5],
                'summer': [6, 7, 8], 
                'autumn': [9, 10, 11],
                'winter': [12, 1, 2]
            }
            
            seasonal_averages = {}
            
            for season_name, months in seasons.items():
                season_irradiance = [
                    hourly_irradiance[i] for i, data in enumerate(hourly_data)
                    if data.month in months
                ]
                
                if season_irradiance:
                    seasonal_averages[season_name] = np.mean(season_irradiance)
                else:
                    seasonal_averages[season_name] = 0.0
            
            return seasonal_averages
            
        except Exception as e:
            self.logger.warning(f"计算季节性太阳辐射平均值失败: {str(e)}")
            return {'spring': 0, 'summer': 0, 'autumn': 0, 'winter': 0}
    
    def _calculate_oriented_wind_data(self, hourly_data: List[HourlyClimateData],
                                    orientation: Orientation) -> OrientedWindData:
        """计算朝向相关的风数据"""
        try:
            # 获取基础风向影响系数
            base_wind_factor = self.wind_factors.get(orientation.value, 1.0)
            
            # 计算每小时风影响系数
            hourly_wind_effects = []
            
            for data in hourly_data:
                # 计算风向与建筑朝向的关系
                wind_effect = self._calculate_hourly_wind_effect(
                    data.wind_direction, data.wind_speed, orientation
                )
                hourly_wind_effects.append(wind_effect)
            
            # 分析主导风向影响
            prevailing_direction_factor = self._calculate_prevailing_wind_factor(
                hourly_data, orientation
            )
            
            # 计算季节性风向模式
            seasonal_patterns = self._calculate_seasonal_wind_patterns(
                hourly_data, orientation
            )
            
            wind_data = OrientedWindData(
                orientation=orientation,
                hourly_wind_effects=hourly_wind_effects,
                prevailing_direction_factor=prevailing_direction_factor,
                seasonal_patterns=seasonal_patterns
            )
            
            return wind_data
            
        except Exception as e:
            self.logger.error(f"计算朝向风数据失败: {str(e)}")
            # 返回默认值
            return OrientedWindData(
                orientation=orientation,
                hourly_wind_effects=[1.0] * len(hourly_data),
                prevailing_direction_factor=1.0,
                seasonal_patterns={'spring': 1.0, 'summer': 1.0, 'autumn': 1.0, 'winter': 1.0}
            )
    
    def _calculate_hourly_wind_effect(self, wind_direction: float, wind_speed: float,
                                    orientation: Orientation) -> float:
        """计算单小时风影响系数"""
        try:
            # 获取建筑朝向角度
            building_angle = self.orientation_angles.get(orientation, 180)
            
            # 计算风向与建筑朝向的角度差
            angle_diff = abs(wind_direction - building_angle)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            # 风向影响系数（正面风影响最大）
            direction_factor = 1.0 - (angle_diff / 180) * 0.7
            
            # 风速影响系数
            speed_factor = min(1.0, wind_speed / 10.0)  # 10m/s为参考风速
            
            # 综合风影响系数
            total_effect = direction_factor * speed_factor
            
            return max(0.1, total_effect)  # 最小保持10%的影响
            
        except Exception:
            return 1.0
    
    def _calculate_prevailing_wind_factor(self, hourly_data: List[HourlyClimateData],
                                        orientation: Orientation) -> float:
        """计算主导风向影响因子"""
        try:
            # 统计风向分布
            wind_directions = [data.wind_direction for data in hourly_data]
            wind_speeds = [data.wind_speed for data in hourly_data]
            
            # 按16个方向分组统计
            direction_bins = 16
            bin_size = 360 / direction_bins
            
            direction_weights = {}
            for i in range(direction_bins):
                direction_weights[i] = 0.0
            
            # 计算每个方向的加权影响
            for direction, speed in zip(wind_directions, wind_speeds):
                bin_index = int(direction / bin_size) % direction_bins
                direction_weights[bin_index] += speed
            
            # 找到主导风向
            dominant_bin = max(direction_weights.keys(), key=lambda k: direction_weights[k])
            dominant_direction = dominant_bin * bin_size + bin_size / 2
            
            # 计算主导风向对当前朝向的影响
            building_angle = self.orientation_angles.get(orientation, 180)
            angle_diff = abs(dominant_direction - building_angle)
            if angle_diff > 180:
                angle_diff = 360 - angle_diff
            
            # 主导风向影响因子
            prevailing_factor = 1.0 - (angle_diff / 180) * 0.5
            
            return max(0.3, prevailing_factor)
            
        except Exception:
            return 1.0
    
    def _calculate_seasonal_wind_patterns(self, hourly_data: List[HourlyClimateData],
                                        orientation: Orientation) -> Dict[str, float]:
        """计算季节性风向模式"""
        try:
            seasons = {
                'spring': [3, 4, 5],
                'summer': [6, 7, 8],
                'autumn': [9, 10, 11],
                'winter': [12, 1, 2]
            }
            
            seasonal_patterns = {}
            
            for season_name, months in seasons.items():
                season_data = [data for data in hourly_data if data.month in months]
                
                if season_data:
                    # 计算季节平均风影响
                    wind_effects = [
                        self._calculate_hourly_wind_effect(
                            data.wind_direction, data.wind_speed, orientation
                        ) for data in season_data
                    ]
                    seasonal_patterns[season_name] = np.mean(wind_effects)
                else:
                    seasonal_patterns[season_name] = 1.0
            
            return seasonal_patterns
            
        except Exception:
            return {'spring': 1.0, 'summer': 1.0, 'autumn': 1.0, 'winter': 1.0}
    
    def _adjust_hourly_climate_data(self, hourly_data: List[HourlyClimateData],
                                  orientation: Orientation,
                                  solar_data: OrientedSolarData,
                                  wind_data: OrientedWindData) -> List[HourlyClimateData]:
        """调整每小时气候数据"""
        try:
            adjusted_data = []
            
            for i, data in enumerate(hourly_data):
                # 创建调整后的数据副本
                adjusted = HourlyClimateData(
                    hour=data.hour,
                    month=data.month,
                    day=data.day,
                    time=data.time,
                    dry_bulb_temperature=data.dry_bulb_temperature,
                    dew_point_temperature=data.dew_point_temperature,
                    relative_humidity=data.relative_humidity,
                    atmospheric_pressure=data.atmospheric_pressure,
                    wind_speed=data.wind_speed,
                    wind_direction=data.wind_direction,
                    direct_normal_irradiance=data.direct_normal_irradiance,
                    diffuse_horizontal_irradiance=data.diffuse_horizontal_irradiance,
                    global_horizontal_irradiance=solar_data.hourly_irradiance[i] if i < len(solar_data.hourly_irradiance) else data.global_horizontal_irradiance,
                    sky_cover=data.sky_cover
                )
                
                adjusted_data.append(adjusted)
            
            return adjusted_data
            
        except Exception as e:
            self.logger.error(f"调整小时气候数据失败: {str(e)}")
            return hourly_data
    
    def get_orientation_recommendations(self, hourly_data: List[HourlyClimateData],
                                     location_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取朝向优化建议
        
        Args:
            hourly_data: 气候数据
            location_info: 位置信息
            
        Returns:
            朝向建议字典
        """
        try:
            recommendations = {
                'optimal_orientations': [],
                'orientation_analysis': {},
                'seasonal_recommendations': {},
                'summary': ""
            }
            
            # 分析所有朝向的性能
            orientation_scores = {}
            
            for orientation in Orientation:
                # 计算朝向得分
                solar_score = self._calculate_orientation_solar_score(
                    hourly_data, orientation, location_info
                )
                wind_score = self._calculate_orientation_wind_score(
                    hourly_data, orientation
                )
                
                # 综合评分
                total_score = solar_score * 0.7 + wind_score * 0.3
                
                orientation_scores[orientation.value] = {
                    'total_score': total_score,
                    'solar_score': solar_score,
                    'wind_score': wind_score
                }
            
            # 排序找出最优朝向
            sorted_orientations = sorted(
                orientation_scores.items(),
                key=lambda x: x[1]['total_score'],
                reverse=True
            )
            
            recommendations['optimal_orientations'] = [
                orient[0] for orient in sorted_orientations[:3]
            ]
            recommendations['orientation_analysis'] = orientation_scores
            
            # 生成季节性建议
            recommendations['seasonal_recommendations'] = self._generate_seasonal_recommendations(
                hourly_data, location_info
            )
            
            # 生成总结
            best_orientation = sorted_orientations[0][0]
            best_score = sorted_orientations[0][1]['total_score']
            
            recommendations['summary'] = (
                f"推荐朝向: {best_orientation} (评分: {best_score:.2f})\n"
                f"该朝向在太阳辐射利用和风环境方面具有最佳综合性能。"
            )
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"生成朝向建议失败: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_orientation_solar_score(self, hourly_data: List[HourlyClimateData],
                                         orientation: Orientation,
                                         location_info: Optional[Dict[str, Any]]) -> float:
        """计算朝向太阳能得分"""
        try:
            # 计算年总太阳辐射
            total_radiation = 0.0
            
            for i, data in enumerate(hourly_data):
                oriented_irradiance = self._calculate_hourly_oriented_irradiance(
                    data, orientation, location_info, i
                )
                total_radiation += oriented_irradiance
            
            # 归一化评分（以南向为基准）
            south_total = sum(
                data.global_horizontal_irradiance * self.solar_factors.get('south', 1.0)
                for data in hourly_data
            )
            
            if south_total > 0:
                return min(1.0, total_radiation / south_total)
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def _calculate_orientation_wind_score(self, hourly_data: List[HourlyClimateData],
                                        orientation: Orientation) -> float:
        """计算朝向风环境得分"""
        try:
            # 计算平均风影响系数
            wind_effects = []
            
            for data in hourly_data:
                effect = self._calculate_hourly_wind_effect(
                    data.wind_direction, data.wind_speed, orientation
                )
                wind_effects.append(effect)
            
            # 风环境得分（中等风影响为最佳）
            avg_effect = np.mean(wind_effects)
            
            # 理想风影响系数为0.6-0.8（既不太强也不太弱）
            if 0.6 <= avg_effect <= 0.8:
                return 1.0
            elif avg_effect < 0.6:
                return avg_effect / 0.6
            else:
                return max(0.2, 1.0 - (avg_effect - 0.8) / 0.2)
                
        except Exception:
            return 0.5
    
    def _generate_seasonal_recommendations(self, hourly_data: List[HourlyClimateData],
                                         location_info: Optional[Dict[str, Any]]) -> Dict[str, str]:
        """生成季节性建议"""
        try:
            recommendations = {}
            
            # 分析各季节特点
            seasons_data = {
                'spring': [data for data in hourly_data if data.month in [3, 4, 5]],
                'summer': [data for data in hourly_data if data.month in [6, 7, 8]],
                'autumn': [data for data in hourly_data if data.month in [9, 10, 11]],
                'winter': [data for data in hourly_data if data.month in [12, 1, 2]]
            }
            
            for season, season_data in seasons_data.items():
                if not season_data:
                    continue
                
                avg_temp = np.mean([d.dry_bulb_temperature for d in season_data])
                avg_radiation = np.mean([d.global_horizontal_irradiance for d in season_data])
                
                if season == 'winter':
                    if avg_temp < 5:
                        recommendations[season] = "冬季建议选择南向或东南向，最大化太阳得热"
                    else:
                        recommendations[season] = "冬季温和，可选择南向或西南向"
                elif season == 'summer':
                    if avg_temp > 30:
                        recommendations[season] = "夏季炎热，建议选择北向或东向，减少过热"
                    else:
                        recommendations[season] = "夏季适中，可选择东向或西向"
                else:
                    recommendations[season] = f"{season}季节气候适中，多种朝向皆可"
            
            return recommendations
            
        except Exception:
            return {}


def create_orientation_adjuster() -> OrientationAdjuster:
    """
    创建朝向调整器实例
    
    Returns:
        配置好的朝向调整器
    """
    return OrientationAdjuster()