"""
立面元素验证器
对提取的建筑元素进行质量检查和约束验证
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import DataValidationError, handle_exception
from ..core.data_structures import FacadeElements, BuildingElement, ElementType
from ..core.utils import ValidationUtils, MathUtils


class ElementValidator:
    """
    立面元素验证器
    
    功能：
    1. 几何约束验证
    2. 物理合理性检查
    3. 数据质量评估
    4. 异常值检测
    5. 建筑规范符合性检查
    """
    
    def __init__(self):
        """初始化元素验证器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取约束配置
        self.constraints = self.config.get_section('optimization_constraints')
        self.performance_config = self.config.get_section('performance_evaluation')
        
        # 验证阈值
        self.min_element_area = 0.01  # 最小元素面积（平方米）
        self.max_aspect_ratio = 10.0  # 最大宽高比
        self.min_aspect_ratio = 0.1   # 最小宽高比
        
        self.logger.info("立面元素验证器初始化完成")
    
    @handle_exception
    def validate_facade_data(self, facade_elements: FacadeElements,
                           geometry_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        验证立面数据的完整性和合理性
        
        Args:
            facade_elements: 立面元素数据
            geometry_params: 几何参数（可选）
            
        Returns:
            验证结果字典
        """
        with LogContext("立面数据验证", self.logger):
            validation_result = {
                'is_valid': True,
                'violations': [],
                'warnings': [],
                'quality_score': 0.0,
                'validation_details': {},
                'recommendations': []
            }
            
            try:
                # 基础数据验证
                basic_validation = self._validate_basic_data(facade_elements)
                validation_result['validation_details']['basic_data'] = basic_validation
                
                # 几何约束验证
                geometry_validation = self._validate_geometry_constraints(facade_elements)
                validation_result['validation_details']['geometry_constraints'] = geometry_validation
                
                # 物理合理性验证
                physics_validation = self._validate_physical_reasonableness(facade_elements)
                validation_result['validation_details']['physical_reasonableness'] = physics_validation
                
                # 建筑规范验证
                building_code_validation = self._validate_building_codes(facade_elements, geometry_params)
                validation_result['validation_details']['building_codes'] = building_code_validation
                
                # 数据质量评估
                quality_assessment = self._assess_data_quality(facade_elements)
                validation_result['validation_details']['data_quality'] = quality_assessment
                
                # 汇总验证结果
                self._consolidate_validation_results(validation_result)
                
                # 生成改进建议
                validation_result['recommendations'] = self._generate_recommendations(
                    facade_elements, validation_result
                )
                
                self.logger.info(f"立面数据验证完成: {'通过' if validation_result['is_valid'] else '未通过'}, "
                               f"质量评分: {validation_result['quality_score']:.2f}")
                
                return validation_result
                
            except Exception as e:
                self.logger.error(f"立面数据验证失败: {str(e)}")
                validation_result.update({
                    'is_valid': False,
                    'violations': [f"验证过程失败: {str(e)}"],
                    'quality_score': 0.0
                })
                return validation_result
    
    def _validate_basic_data(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """基础数据验证"""
        basic_validation = {
            'passed': True,
            'violations': [],
            'warnings': [],
            'checks': {}
        }
        
        # 检查1: 数据完整性
        total_elements = len(facade_elements.get_all_elements())
        if total_elements == 0:
            basic_validation['violations'].append("未检测到任何建筑元素")
            basic_validation['passed'] = False
        
        basic_validation['checks']['total_elements'] = {
            'value': total_elements,
            'passed': total_elements > 0
        }
        
        # 检查2: 基本元素存在性
        has_walls = len(facade_elements.walls) > 0
        has_windows = len(facade_elements.windows) > 0
        
        if not (has_walls or has_windows):
            basic_validation['warnings'].append("既未检测到墙体也未检测到窗户")
        
        basic_validation['checks']['has_primary_elements'] = {
            'has_walls': has_walls,
            'has_windows': has_windows,
            'passed': has_walls or has_windows
        }
        
        # 检查3: 元素属性完整性
        incomplete_elements = []
        for element in facade_elements.get_all_elements():
            if not self._validate_element_attributes(element):
                incomplete_elements.append(element.element_id)
        
        if incomplete_elements:
            basic_validation['warnings'].append(
                f"元素属性不完整: {', '.join(incomplete_elements[:5])}"
                + (f" 等{len(incomplete_elements)}个" if len(incomplete_elements) > 5 else "")
            )
        
        basic_validation['checks']['attribute_completeness'] = {
            'incomplete_count': len(incomplete_elements),
            'passed': len(incomplete_elements) == 0
        }
        
        return basic_validation
    
    def _validate_element_attributes(self, element: BuildingElement) -> bool:
        """验证单个元素的属性完整性"""
        required_attrs = ['element_id', 'element_type', 'bbox', 'area', 'width', 'height']
        
        for attr in required_attrs:
            if not hasattr(element, attr) or getattr(element, attr) is None:
                return False
            
            # 检查数值属性的合理性
            if attr in ['area', 'width', 'height']:
                value = getattr(element, attr)
                if not isinstance(value, (int, float)) or value <= 0:
                    return False
        
        return True
    
    def _validate_geometry_constraints(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """几何约束验证"""
        geometry_validation = {
            'passed': True,
            'violations': [],
            'warnings': [],
            'checks': {}
        }
        
        # 检查1: 窗墙比约束
        wwr_check = self._check_window_wall_ratio_constraint(facade_elements)
        geometry_validation['checks']['window_wall_ratio'] = wwr_check
        if not wwr_check['passed']:
            geometry_validation['violations'].extend(wwr_check['violations'])
            geometry_validation['passed'] = False
        
        # 检查2: 窗户尺寸约束
        window_size_check = self._check_window_size_constraints(facade_elements)
        geometry_validation['checks']['window_sizes'] = window_size_check
        if not window_size_check['passed']:
            geometry_validation['violations'].extend(window_size_check['violations'])
            geometry_validation['passed'] = False
        
        # 检查3: 元素重叠检查
        overlap_check = self._check_element_overlaps(facade_elements)
        geometry_validation['checks']['element_overlaps'] = overlap_check
        if not overlap_check['passed']:
            geometry_validation['violations'].extend(overlap_check['violations'])
            geometry_validation['passed'] = False
        
        # 检查4: 边界约束
        boundary_check = self._check_boundary_constraints(facade_elements)
        geometry_validation['checks']['boundary_constraints'] = boundary_check
        if not boundary_check['passed']:
            geometry_validation['violations'].extend(boundary_check['violations'])
            geometry_validation['passed'] = False
        
        # 检查5: 最小距离约束
        distance_check = self._check_minimum_distances(facade_elements)
        geometry_validation['checks']['minimum_distances'] = distance_check
        if not distance_check['passed']:
            geometry_validation['warnings'].extend(distance_check['warnings'])
        
        return geometry_validation
    
    def _check_window_wall_ratio_constraint(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查窗墙比约束"""
        wwr_constraint = self.constraints.get('window_wall_ratio', {})
        min_wwr = wwr_constraint.get('min', 0.0)
        max_wwr = wwr_constraint.get('max', 1.0)
        
        # 计算当前窗墙比
        window_area = sum(w.area for w in facade_elements.windows)
        wall_area = sum(w.area for w in facade_elements.walls)
        
        if wall_area > 0:
            current_wwr = window_area / wall_area
        else:
            # 如果没有检测到墙体，使用估算
            if facade_elements.windows and facade_elements.image_shape[0] > 0:
                # 基于图像尺寸估算立面面积
                total_area = (facade_elements.image_shape[0] * facade_elements.image_shape[1] * 
                            (facade_elements.pixel_to_meter_ratio ** 2) * 0.8)
                estimated_wall_area = total_area - window_area
                current_wwr = window_area / estimated_wall_area if estimated_wall_area > 0 else 0
            else:
                current_wwr = 0
        
        violations = []
        if current_wwr < min_wwr:
            violations.append(f"窗墙比 {current_wwr:.3f} 低于最小值 {min_wwr}")
        elif current_wwr > max_wwr:
            violations.append(f"窗墙比 {current_wwr:.3f} 超过最大值 {max_wwr}")
        
        return {
            'passed': len(violations) == 0,
            'violations': violations,
            'current_value': current_wwr,
            'min_allowed': min_wwr,
            'max_allowed': max_wwr
        }
    
    def _check_window_size_constraints(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查窗户尺寸约束"""
        size_constraint = self.constraints.get('window_size', {})
        min_width = size_constraint.get('min_width', 0.5)
        max_width = size_constraint.get('max_width', 3.0)
        min_height = size_constraint.get('min_height', 0.8)
        max_height = size_constraint.get('max_height', 2.5)
        
        violations = []
        valid_windows = 0
        
        for window in facade_elements.windows:
            valid = True
            
            if window.width < min_width:
                violations.append(f"窗户 {window.element_id} 宽度 {window.width:.2f}m 小于最小值 {min_width}m")
                valid = False
            elif window.width > max_width:
                violations.append(f"窗户 {window.element_id} 宽度 {window.width:.2f}m 超过最大值 {max_width}m")
                valid = False
            
            if window.height < min_height:
                violations.append(f"窗户 {window.element_id} 高度 {window.height:.2f}m 小于最小值 {min_height}m")
                valid = False
            elif window.height > max_height:
                violations.append(f"窗户 {window.element_id} 高度 {window.height:.2f}m 超过最大值 {max_height}m")
                valid = False
            
            if valid:
                valid_windows += 1
        
        return {
            'passed': len(violations) == 0,
            'violations': violations,
            'valid_windows': valid_windows,
            'total_windows': len(facade_elements.windows),
            'compliance_rate': valid_windows / max(len(facade_elements.windows), 1)
        }
    
    def _check_element_overlaps(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查元素重叠"""
        violations = []
        overlapping_pairs = []
        
        all_elements = facade_elements.get_all_elements()
        
        for i, elem1 in enumerate(all_elements):
            for j, elem2 in enumerate(all_elements):
                if i >= j:
                    continue
                
                if self._rectangles_overlap(elem1.bbox, elem2.bbox):
                    overlap_info = {
                        'element1': elem1.element_id,
                        'element2': elem2.element_id,
                        'overlap_area': self._calculate_overlap_area(elem1.bbox, elem2.bbox)
                    }
                    overlapping_pairs.append(overlap_info)
                    violations.append(f"元素重叠: {elem1.element_id} 与 {elem2.element_id}")
        
        return {
            'passed': len(violations) == 0,
            'violations': violations,
            'overlapping_pairs': overlapping_pairs,
            'overlap_count': len(overlapping_pairs)
        }
    
    def _rectangles_overlap(self, rect1: Tuple[float, float, float, float],
                          rect2: Tuple[float, float, float, float]) -> bool:
        """检查两个矩形是否重叠"""
        x1_min, y1_min, x1_max, y1_max = rect1
        x2_min, y2_min, x2_max, y2_max = rect2
        
        return not (x1_max <= x2_min or x2_max <= x1_min or 
                   y1_max <= y2_min or y2_max <= y1_min)
    
    def _calculate_overlap_area(self, rect1: Tuple[float, float, float, float],
                              rect2: Tuple[float, float, float, float]) -> float:
        """计算两个矩形的重叠面积"""
        x1_min, y1_min, x1_max, y1_max = rect1
        x2_min, y2_min, x2_max, y2_max = rect2
        
        # 计算重叠区域
        overlap_x_min = max(x1_min, x2_min)
        overlap_y_min = max(y1_min, y2_min)
        overlap_x_max = min(x1_max, x2_max)
        overlap_y_max = min(y1_max, y2_max)
        
        if overlap_x_max > overlap_x_min and overlap_y_max > overlap_y_min:
            return (overlap_x_max - overlap_x_min) * (overlap_y_max - overlap_y_min)
        else:
            return 0.0
    
    def _check_boundary_constraints(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查边界约束"""
        violations = []
        
        if facade_elements.image_shape[0] == 0:
            return {'passed': True, 'violations': [], 'note': 'No image dimensions available'}
        
        image_height, image_width = facade_elements.image_shape[:2]
        wall_margin = self.constraints.get('wall_margin', 0.2)  # 米
        wall_margin_pixels = wall_margin / facade_elements.pixel_to_meter_ratio
        
        for element in facade_elements.get_all_elements():
            x_min, y_min, x_max, y_max = element.bbox
            
            if (x_min < wall_margin_pixels or 
                y_min < wall_margin_pixels or 
                x_max > image_width - wall_margin_pixels or 
                y_max > image_height - wall_margin_pixels):
                
                violations.append(f"元素 {element.element_id} 超出边界约束（边距: {wall_margin}m）")
        
        return {
            'passed': len(violations) == 0,
            'violations': violations,
            'boundary_margin': wall_margin
        }
    
    def _check_minimum_distances(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查最小距离约束"""
        min_distance = self.constraints.get('minimum_distance', 0.3)  # 米
        warnings = []
        
        windows = facade_elements.windows
        close_pairs = []
        
        for i, w1 in enumerate(windows):
            for j, w2 in enumerate(windows):
                if i >= j:
                    continue
                
                distance = MathUtils.calculate_distance(w1.center, w2.center)
                distance_meters = distance * facade_elements.pixel_to_meter_ratio
                
                if distance_meters < min_distance:
                    close_pairs.append({
                        'window1': w1.element_id,
                        'window2': w2.element_id,
                        'distance': distance_meters
                    })
                    warnings.append(f"窗户 {w1.element_id} 和 {w2.element_id} 距离过近: {distance_meters:.2f}m")
        
        return {
            'passed': len(warnings) == 0,
            'warnings': warnings,
            'close_pairs': close_pairs,
            'minimum_distance_threshold': min_distance
        }
    
    def _validate_physical_reasonableness(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """物理合理性验证"""
        physics_validation = {
            'passed': True,
            'violations': [],
            'warnings': [],
            'checks': {}
        }
        
        # 检查1: 元素尺寸合理性
        size_check = self._check_element_size_reasonableness(facade_elements)
        physics_validation['checks']['element_sizes'] = size_check
        if not size_check['passed']:
            physics_validation['warnings'].extend(size_check['warnings'])
        
        # 检查2: 宽高比合理性
        aspect_ratio_check = self._check_aspect_ratio_reasonableness(facade_elements)
        physics_validation['checks']['aspect_ratios'] = aspect_ratio_check
        if not aspect_ratio_check['passed']:
            physics_validation['warnings'].extend(aspect_ratio_check['warnings'])
        
        # 检查3: 立面密度合理性
        density_check = self._check_facade_density_reasonableness(facade_elements)
        physics_validation['checks']['facade_density'] = density_check
        if not density_check['passed']:
            physics_validation['warnings'].extend(density_check['warnings'])
        
        return physics_validation
    
    def _check_element_size_reasonableness(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查元素尺寸合理性"""
        warnings = []
        size_issues = []
        
        for element in facade_elements.get_all_elements():
            # 检查最小面积
            if element.area < self.min_element_area:
                size_issues.append({
                    'element_id': element.element_id,
                    'issue': 'too_small',
                    'area': element.area
                })
                warnings.append(f"元素 {element.element_id} 面积过小: {element.area:.3f}m²")
            
            # 检查异常大的元素
            max_reasonable_area = 50.0  # 50平方米
            if element.area > max_reasonable_area:
                size_issues.append({
                    'element_id': element.element_id,
                    'issue': 'too_large',
                    'area': element.area
                })
                warnings.append(f"元素 {element.element_id} 面积异常大: {element.area:.2f}m²")
        
        return {
            'passed': len(warnings) == 0,
            'warnings': warnings,
            'size_issues': size_issues
        }
    
    def _check_aspect_ratio_reasonableness(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查宽高比合理性"""
        warnings = []
        ratio_issues = []
        
        for element in facade_elements.get_all_elements():
            if element.height > 0:
                aspect_ratio = element.width / element.height
                
                if aspect_ratio > self.max_aspect_ratio:
                    ratio_issues.append({
                        'element_id': element.element_id,
                        'issue': 'too_wide',
                        'aspect_ratio': aspect_ratio
                    })
                    warnings.append(f"元素 {element.element_id} 过于狭长: 宽高比 {aspect_ratio:.2f}")
                
                elif aspect_ratio < self.min_aspect_ratio:
                    ratio_issues.append({
                        'element_id': element.element_id,
                        'issue': 'too_narrow',
                        'aspect_ratio': aspect_ratio
                    })
                    warnings.append(f"元素 {element.element_id} 过于狭窄: 宽高比 {aspect_ratio:.2f}")
        
        return {
            'passed': len(warnings) == 0,
            'warnings': warnings,
            'ratio_issues': ratio_issues
        }
    
    def _check_facade_density_reasonableness(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查立面密度合理性"""
        warnings = []
        
        # 估算立面面积
        if facade_elements.image_shape[0] > 0:
            total_area = (facade_elements.image_shape[0] * facade_elements.image_shape[1] * 
                         (facade_elements.pixel_to_meter_ratio ** 2))
        else:
            return {'passed': True, 'warnings': [], 'note': 'No image dimensions available'}
        
        # 计算窗户密度
        window_count = len(facade_elements.windows)
        window_density = window_count / total_area if total_area > 0 else 0
        
        # 合理的窗户密度范围（个/m²）
        min_reasonable_density = 0.01  # 每100平方米至少1个窗户
        max_reasonable_density = 2.0   # 每平方米最多2个窗户
        
        if window_density < min_reasonable_density:
            warnings.append(f"窗户密度过低: {window_density:.3f} 个/m²")
        elif window_density > max_reasonable_density:
            warnings.append(f"窗户密度过高: {window_density:.3f} 个/m²")
        
        return {
            'passed': len(warnings) == 0,
            'warnings': warnings,
            'window_density': window_density,
            'reasonable_range': [min_reasonable_density, max_reasonable_density]
        }
    
    def _validate_building_codes(self, facade_elements: FacadeElements,
                                geometry_params: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """建筑规范验证"""
        code_validation = {
            'passed': True,
            'violations': [],
            'warnings': [],
            'checks': {}
        }
        
        # 检查1: 采光要求
        lighting_check = self._check_lighting_requirements(facade_elements)
        code_validation['checks']['lighting_requirements'] = lighting_check
        if not lighting_check['passed']:
            code_validation['warnings'].extend(lighting_check['warnings'])
        
        # 检查2: 通风要求
        ventilation_check = self._check_ventilation_requirements(facade_elements)
        code_validation['checks']['ventilation_requirements'] = ventilation_check
        if not ventilation_check['passed']:
            code_validation['warnings'].extend(ventilation_check['warnings'])
        
        # 检查3: 安全疏散
        safety_check = self._check_safety_requirements(facade_elements)
        code_validation['checks']['safety_requirements'] = safety_check
        if not safety_check['passed']:
            code_validation['violations'].extend(safety_check['violations'])
            code_validation['passed'] = False
        
        return code_validation
    
    def _check_lighting_requirements(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查采光要求"""
        warnings = []
        
        # 计算窗地比（简化为窗墙比的估算）
        window_area = sum(w.area for w in facade_elements.windows)
        
        # 估算房间面积（基于立面尺寸）
        if facade_elements.image_shape[0] > 0:
            facade_width = facade_elements.image_shape[1] * facade_elements.pixel_to_meter_ratio
            assumed_depth = 6.0  # 假设房间进深6米
            estimated_floor_area = facade_width * assumed_depth
            
            window_floor_ratio = window_area / estimated_floor_area if estimated_floor_area > 0 else 0
            
            # 最小窗地比要求（根据建筑规范）
            min_window_floor_ratio = 0.1  # 10%
            
            if window_floor_ratio < min_window_floor_ratio:
                warnings.append(f"窗地比可能不足: {window_floor_ratio:.3f} < {min_window_floor_ratio}")
        
        return {
            'passed': len(warnings) == 0,
            'warnings': warnings
        }
    
    def _check_ventilation_requirements(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查通风要求"""
        warnings = []
        
        # 检查可开启窗户面积（这里简化处理）
        total_window_area = sum(w.area for w in facade_elements.windows)
        
        # 假设50%的窗户可开启
        openable_area = total_window_area * 0.5
        
        # 估算房间面积进行通风面积检查
        if facade_elements.image_shape[0] > 0:
            facade_width = facade_elements.image_shape[1] * facade_elements.pixel_to_meter_ratio
            assumed_depth = 6.0
            estimated_floor_area = facade_width * assumed_depth
            
            ventilation_ratio = openable_area / estimated_floor_area if estimated_floor_area > 0 else 0
            
            # 最小通风面积比例
            min_ventilation_ratio = 0.05  # 5%
            
            if ventilation_ratio < min_ventilation_ratio:
                warnings.append(f"通风面积可能不足: {ventilation_ratio:.3f} < {min_ventilation_ratio}")
        
        return {
            'passed': len(warnings) == 0,
            'warnings': warnings
        }
    
    def _check_safety_requirements(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """检查安全要求"""
        violations = []
        
        # 检查门的存在（安全疏散）
        door_count = len(facade_elements.doors)
        
        if door_count == 0:
            violations.append("未检测到门，可能影响安全疏散")
        
        # 检查窗户高度（防坠落）
        for window in facade_elements.windows:
            # 假设窗台高度从bbox的y坐标推算
            window_sill_height = window.bbox[1] * facade_elements.pixel_to_meter_ratio
            
            # 最小窗台高度（从地面算起，这里简化处理）
            min_sill_height = 0.9  # 0.9米
            
            if window_sill_height < min_sill_height:
                violations.append(f"窗户 {window.element_id} 窗台可能过低")
        
        return {
            'passed': len(violations) == 0,
            'violations': violations
        }
    
    def _assess_data_quality(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """评估数据质量"""
        quality_metrics = {
            'completeness_score': 0.0,
            'consistency_score': 0.0,
            'accuracy_score': 0.0,
            'overall_score': 0.0
        }
        
        # 完整性评分
        quality_metrics['completeness_score'] = self._calculate_completeness_score(facade_elements)
        
        # 一致性评分
        quality_metrics['consistency_score'] = self._calculate_consistency_score(facade_elements)
        
        # 准确性评分（基于几何合理性）
        quality_metrics['accuracy_score'] = self._calculate_accuracy_score(facade_elements)
        
        # 综合评分
        quality_metrics['overall_score'] = (
            quality_metrics['completeness_score'] * 0.3 +
            quality_metrics['consistency_score'] * 0.3 +
            quality_metrics['accuracy_score'] * 0.4
        )
        
        return quality_metrics
    
    def _calculate_completeness_score(self, facade_elements: FacadeElements) -> float:
        """计算完整性评分"""
        total_elements = len(facade_elements.get_all_elements())
        
        if total_elements == 0:
            return 0.0
        
        # 基于检测到的元素类型数量
        detected_types = 0
        if facade_elements.walls:
            detected_types += 1
        if facade_elements.windows:
            detected_types += 1
        if facade_elements.doors:
            detected_types += 1
        if facade_elements.shading:
            detected_types += 1
        if facade_elements.frames:
            detected_types += 1
        
        type_score = detected_types / 5.0  # 最多5种类型
        
        # 基于元素数量合理性
        element_count_score = min(total_elements / 10.0, 1.0)  # 10个元素得满分
        
        return (type_score + element_count_score) / 2.0
    
    def _calculate_consistency_score(self, facade_elements: FacadeElements) -> float:
        """计算一致性评分"""
        if not facade_elements.windows:
            return 1.0
        
        # 窗户尺寸一致性
        window_widths = [w.width for w in facade_elements.windows]
        window_heights = [w.height for w in facade_elements.windows]
        
        width_cv = np.std(window_widths) / np.mean(window_widths) if np.mean(window_widths) > 0 else 0
        height_cv = np.std(window_heights) / np.mean(window_heights) if np.mean(window_heights) > 0 else 0
        
        # CV越小，一致性越好
        width_consistency = max(0, 1 - width_cv)
        height_consistency = max(0, 1 - height_cv)
        
        return (width_consistency + height_consistency) / 2.0
    
    def _calculate_accuracy_score(self, facade_elements: FacadeElements) -> float:
        """计算准确性评分"""
        penalty = 0.0
        
        # 基于几何合理性扣分
        for element in facade_elements.get_all_elements():
            # 面积过小扣分
            if element.area < self.min_element_area:
                penalty += 0.1
            
            # 宽高比异常扣分
            if element.height > 0:
                aspect_ratio = element.width / element.height
                if aspect_ratio > self.max_aspect_ratio or aspect_ratio < self.min_aspect_ratio:
                    penalty += 0.05
        
        return max(0.0, 1.0 - penalty)
    
    def _consolidate_validation_results(self, validation_result: Dict[str, Any]) -> None:
        """汇总验证结果"""
        # 收集所有违规和警告
        all_violations = []
        all_warnings = []
        
        for detail in validation_result['validation_details'].values():
            if isinstance(detail, dict):
                all_violations.extend(detail.get('violations', []))
                all_warnings.extend(detail.get('warnings', []))
        
        validation_result['violations'] = all_violations
        validation_result['warnings'] = all_warnings
        
        # 计算总体有效性
        validation_result['is_valid'] = len(all_violations) == 0
        
        # 计算质量评分
        quality_assessment = validation_result['validation_details'].get('data_quality', {})
        validation_result['quality_score'] = quality_assessment.get('overall_score', 0.0)
        
        # 如果有违规，降低质量评分
        if all_violations:
            violation_penalty = min(len(all_violations) * 0.1, 0.5)
            validation_result['quality_score'] = max(0.0, 
                validation_result['quality_score'] - violation_penalty)
    
    def _generate_recommendations(self, facade_elements: FacadeElements,
                                validation_result: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于违规生成建议
        violations = validation_result.get('violations', [])
        
        if any('窗墙比' in v for v in violations):
            recommendations.append("建议调整窗户数量或尺寸以满足窗墙比要求")
        
        if any('重叠' in v for v in violations):
            recommendations.append("建议调整元素位置以避免重叠")
        
        if any('边界' in v for v in violations):
            recommendations.append("建议调整元素位置以满足边界约束")
        
        if any('尺寸' in v for v in violations):
            recommendations.append("建议检查并调整元素尺寸至合理范围")
        
        # 基于质量评分生成建议
        quality_score = validation_result.get('quality_score', 0.0)
        
        if quality_score < 0.6:
            recommendations.append("数据质量较低，建议重新处理图像或调整识别参数")
        
        if len(facade_elements.windows) == 0:
            recommendations.append("未检测到窗户，建议检查图像质量或调整颜色识别参数")
        
        if len(facade_elements.walls) == 0:
            recommendations.append("未检测到墙体，建议启用智能墙体检测或调整识别参数")
        
        return recommendations


def create_element_validator() -> ElementValidator:
    """
    创建元素验证器实例
    
    Returns:
        配置好的元素验证器
    """
    return ElementValidator()