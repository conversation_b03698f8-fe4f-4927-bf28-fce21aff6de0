"""
自定义异常类定义
定义了建筑立面优化系统中使用的所有异常类型
"""


class FacadeOptimizationError(Exception):
    """建筑立面优化系统基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        """
        初始化异常
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详细信息
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        result = self.message
        if self.error_code:
            result = f"[{self.error_code}] {result}"
        if self.details:
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            result += f" ({details_str})"
        return result


class ConfigurationError(FacadeOptimizationError):
    """配置错误"""
    pass


class DataValidationError(FacadeOptimizationError):
    """数据验证错误"""
    pass


# === 立面数据提取相关异常 ===

class FacadeExtractionError(FacadeOptimizationError):
    """立面数据提取异常基类"""
    pass


class YOLOSegmentationError(FacadeExtractionError):
    """YOLO分割处理异常"""
    pass


class ImageProcessingError(FacadeExtractionError):
    """图像处理异常"""
    pass


class GeometryCalculationError(FacadeExtractionError):
    """几何参数计算异常"""
    pass


class ElementExtractionError(FacadeExtractionError):
    """建筑元素提取异常"""
    pass


# === 气候数据处理相关异常 ===

class ClimateDataError(FacadeOptimizationError):
    """气候数据处理异常基类"""
    pass


class EPWParsingError(ClimateDataError):
    """EPW文件解析异常"""
    pass


class OrientationAdjustmentError(ClimateDataError):
    """朝向调整异常"""
    pass


class ClimateDataValidationError(ClimateDataError):
    """气候数据验证异常"""
    pass


# === 遗传算法优化相关异常 ===

class OptimizationError(FacadeOptimizationError):
    """优化异常基类"""
    pass


class GeneticAlgorithmError(OptimizationError):
    """遗传算法异常"""
    pass


class ObjectiveFunctionError(OptimizationError):
    """目标函数计算异常"""
    pass


class ConstraintViolationError(OptimizationError):
    """约束违反异常"""
    pass


class ConvergenceError(OptimizationError):
    """收敛异常"""
    pass


class IndividualEncodingError(OptimizationError):
    """个体编码异常"""
    pass


# === 性能评估相关异常 ===

class PerformanceEvaluationError(FacadeOptimizationError):
    """性能评估异常基类"""
    pass


class EnergyCalculationError(PerformanceEvaluationError):
    """能耗计算异常"""
    pass


class ThermalAnalysisError(PerformanceEvaluationError):
    """热工分析异常"""
    pass


class CostEvaluationError(PerformanceEvaluationError):
    """成本评估异常"""
    pass


class ComfortAnalysisError(PerformanceEvaluationError):
    """舒适度分析异常"""
    pass


# === 解决方案选择相关异常 ===

class SolutionSelectionError(FacadeOptimizationError):
    """解决方案选择异常基类"""
    pass


class ParetoFrontError(SolutionSelectionError):
    """帕累托前沿计算异常"""
    pass


class SolutionRankingError(SolutionSelectionError):
    """解决方案排序异常"""
    pass


class MultiObjectiveError(SolutionSelectionError):
    """多目标处理异常"""
    pass


# === 可视化相关异常 ===

class VisualizationError(FacadeOptimizationError):
    """可视化异常基类"""
    pass


class ChartGenerationError(VisualizationError):
    """图表生成异常"""
    pass


class StyleConfigurationError(VisualizationError):
    """样式配置异常"""
    pass


class RenderingError(VisualizationError):
    """渲染异常"""
    pass


class OutputError(VisualizationError):
    """输出异常"""
    pass


# === 数据管理相关异常 ===

class DataManagementError(FacadeOptimizationError):
    """数据管理异常基类"""
    pass


class DataStorageError(DataManagementError):
    """数据存储异常"""
    pass


class DataTransferError(DataManagementError):
    """数据传输异常"""
    pass


class SerializationError(DataManagementError):
    """序列化异常"""
    pass


# === 系统集成相关异常 ===

class SystemIntegrationError(FacadeOptimizationError):
    """系统集成异常基类"""
    pass


class ModuleIntegrationError(SystemIntegrationError):
    """模块集成异常"""
    pass


class WorkflowError(SystemIntegrationError):
    """工作流异常"""
    pass


class ResourceError(SystemIntegrationError):
    """资源异常"""
    pass


# === 用户界面相关异常 ===

class UserInterfaceError(FacadeOptimizationError):
    """用户界面异常基类"""
    pass


class InputValidationError(UserInterfaceError):
    """输入验证异常"""
    pass


class InteractionError(UserInterfaceError):
    """交互异常"""
    pass


# === 异常处理工具函数 ===

def handle_exception(func):
    """
    异常处理装饰器
    
    用于包装函数并提供统一的异常处理
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except FacadeOptimizationError:
            # 重新抛出自定义异常
            raise
        except Exception as e:
            # 将其他异常包装为系统异常
            raise SystemIntegrationError(
                f"在执行 {func.__name__} 时发生未预期的错误: {str(e)}",
                error_code="UNEXPECTED_ERROR",
                details={"function": func.__name__, "original_error": str(e)}
            ) from e
    
    return wrapper


def validate_not_none(value, name: str):
    """
    验证值不为None
    
    Args:
        value: 要验证的值
        name: 参数名称
        
    Raises:
        DataValidationError: 如果值为None
    """
    if value is None:
        raise DataValidationError(
            f"参数 {name} 不能为空",
            error_code="NULL_VALUE",
            details={"parameter": name}
        )


def validate_positive(value: float, name: str):
    """
    验证值为正数
    
    Args:
        value: 要验证的值
        name: 参数名称
        
    Raises:
        DataValidationError: 如果值不为正数
    """
    if value <= 0:
        raise DataValidationError(
            f"参数 {name} 必须为正数，当前值: {value}",
            error_code="NON_POSITIVE_VALUE",
            details={"parameter": name, "value": value}
        )


def validate_range(value: float, name: str, min_val: float, max_val: float):
    """
    验证值在指定范围内
    
    Args:
        value: 要验证的值
        name: 参数名称
        min_val: 最小值
        max_val: 最大值
        
    Raises:
        DataValidationError: 如果值不在范围内
    """
    if not (min_val <= value <= max_val):
        raise DataValidationError(
            f"参数 {name} 必须在 [{min_val}, {max_val}] 范围内，当前值: {value}",
            error_code="OUT_OF_RANGE",
            details={
                "parameter": name,
                "value": value,
                "min": min_val,
                "max": max_val
            }
        )


def validate_file_exists(file_path: str):
    """
    验证文件存在
    
    Args:
        file_path: 文件路径
        
    Raises:
        DataValidationError: 如果文件不存在
    """
    import os
    if not os.path.exists(file_path):
        raise DataValidationError(
            f"文件不存在: {file_path}",
            error_code="FILE_NOT_FOUND",
            details={"file_path": file_path}
        )


def validate_directory_exists(dir_path: str):
    """
    验证目录存在
    
    Args:
        dir_path: 目录路径
        
    Raises:
        DataValidationError: 如果目录不存在
    """
    import os
    if not os.path.isdir(dir_path):
        raise DataValidationError(
            f"目录不存在: {dir_path}",
            error_code="DIRECTORY_NOT_FOUND",
            details={"directory_path": dir_path}
        )


class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(self, operation: str, logger=None):
        """
        初始化错误上下文
        
        Args:
            operation: 操作名称
            logger: 日志器
        """
        self.operation = operation
        self.logger = logger
    
    def __enter__(self):
        if self.logger:
            self.logger.debug(f"开始操作: {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            error_msg = f"操作失败: {self.operation} - {str(exc_val)}"
            if self.logger:
                self.logger.error(error_msg)
            
            # 如果不是自定义异常，则包装为系统异常
            if not isinstance(exc_val, FacadeOptimizationError):
                raise SystemIntegrationError(
                    error_msg,
                    error_code="OPERATION_FAILED",
                    details={
                        "operation": self.operation,
                        "original_error": str(exc_val)
                    }
                ) from exc_val
        else:
            if self.logger:
                self.logger.debug(f"操作成功: {self.operation}")
        
        return False  # 不抑制异常