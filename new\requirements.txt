# 建筑立面优化系统依赖包
# Building Facade Optimization System Dependencies

# 核心科学计算库 / Core Scientific Computing
numpy>=1.21.0
pandas>=1.3.0
scipy>=1.7.0

# 机器学习和深度学习 / Machine Learning & Deep Learning
scikit-learn>=1.0.0
torch>=1.9.0
torchvision>=0.10.0
ultralytics>=8.0.0  # YOLOv8
# yolov5>=7.0.0     # 可选 YOLO 版本

# 可视化库 / Visualization Libraries
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0
bokeh>=2.4.0

# 图像处理 / Image Processing
opencv-python>=4.5.0
Pillow>=8.3.0
imageio>=2.9.0
scikit-image>=0.18.0

# 数据处理和文件操作 / Data Processing & File Operations
openpyxl>=3.0.7
xlsxwriter>=3.0.0
h5py>=3.1.0
# netCDF4>=1.5.7  # 可选，气候数据处理

# 优化算法库 / Optimization Libraries
deap>=1.3.1
pymoo>=0.6.0
# platypus-opt>=1.0.4  # 可选优化库

# 气候数据处理 / Climate Data Processing
# eppy>=0.5.56   # EPW 文件处理，可选
# pvlib>=0.9.0   # 太阳能计算，可选

# 报告生成 / Report Generation
jinja2>=3.0.0
markdown>=3.3.0
# weasyprint>=54.0  # PDF 生成，可选
# reportlab>=3.6.0  # PDF 报告，可选

# 配置和日志 / Configuration & Logging
pyyaml>=5.4.0
configparser>=5.0.0
colorlog>=6.4.0
python-dotenv>=0.19.0

# 数学和统计 / Mathematics & Statistics
sympy>=1.8.0
statsmodels>=0.12.0

# 并行计算 / Parallel Computing
joblib>=1.0.0
# multiprocessing-logging>=0.3.0

# 性能优化 / Performance Optimization
numba>=0.56.0  # JIT编译加速
memory-profiler>=0.60.0
psutil>=5.8.0

# 进度显示 / Progress Display
tqdm>=4.62.0

# 数据验证 / Data Validation
cerberus>=1.3.4

# 缓存 / Caching
diskcache>=5.4.0

# 时间处理 / Time Processing
arrow>=1.2.0

# 聚类分析 / Clustering Analysis
hdbscan>=0.8.27

# 图形处理 / Graph Processing
networkx>=2.6.0

# HTTP请求 / HTTP Requests
requests>=2.26.0

# 类型提示 / Type Hints
typing-extensions>=4.0.0

# 测试框架 / Testing Framework (开发环境)
pytest>=6.2.0
pytest-cov>=2.12.0

# 代码质量 / Code Quality (开发环境)
black>=21.0.0
flake8>=3.9.0
mypy>=0.910

# 开发工具 / Development Tools (可选)
# jupyter>=1.0.0
# ipython>=7.25.0

# 文档生成 / Documentation (可选)
# sphinx>=4.0.0
# sphinx-rtd-theme>=1.0.0

# 可选依赖 / Optional Dependencies
# 如果需要GPU加速，请安装CUDA版本的PyTorch
# For GPU acceleration, install CUDA version of PyTorch

# GUI支持 / GUI Support (可选)
# tkinter  # Python内置
# PyQt5>=5.15.0

# 建筑相关专业库 / Building-specific Professional Libraries (可选)
# ladybug-tools>=1.0.0  # 高级建筑性能分析
# honeybee-core>=1.0.0  # 建筑能耗模拟