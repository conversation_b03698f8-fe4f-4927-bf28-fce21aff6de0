"""
Data Comparison Visualization Charts
Professional visualization for solution comparison, time series comparison, benchmark comparison, etc. (4 types of charts)
"""

import numpy as np
import matplotlib.pyplot as plt
# Set English font - avoid Chinese font issues
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans', 'Helvetica', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False
import seaborn as sns
import pandas as pd
from typing import Dict, List, Tuple, Any, Optional
import matplotlib.patches as patches
from matplotlib.sankey import Sankey
from matplotlib.patches import Rectangle
import matplotlib.gridspec as gridspec

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import VisualizationError, handle_exception
from ..core.data_structures import (
    VisualizationData, ObjectiveResults, OptimizationResults,
    EnergyBreakdown, ThermalPerformanceMetrics, CostBreakdown
)
from .base_visualizer import BaseVisualizer, create_base_visualizer


class ComparisonCharts:
    """
    Data Comparison Visualization Charts class
    
    Functions:
    1. Multi-solution performance comparison chart
    2. Benchmark comparison analysis chart
    3. Sensitivity analysis chart
    4. Trade-off analysis chart
    """
    
    def __init__(self):
        """Initialize Data Comparison Visualization Charts"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        self.base_visualizer = create_base_visualizer()
        
        # Get comparison visualization configuration
        viz_config = self.config.get_section('visualization')
        self.comparison_config = viz_config.get('comparison_charts', {})
        
        # Comparison style configuration
        style_config = self.comparison_config.get('comparison_styles', {})
        self.highlight_best = style_config.get('highlight_best_solution', True)
        self.show_statistical_info = style_config.get('show_statistical_info', True)
        self.use_parallel_coordinates = style_config.get('use_parallel_coordinates', True)
        
        self.logger.info("Data Comparison Visualization Charts initialized successfully")
    
    def set_session_output_directory(self, session_dir: str) -> None:
        """Set session output directory"""
        if hasattr(self.base_visualizer, 'set_session_output_directory'):
            self.base_visualizer.set_session_output_directory(session_dir)
            self.output_dir = self.base_visualizer.output_dir
    
    @handle_exception
    def create_multi_solution_comparison_chart(self, solutions: Dict[str, ObjectiveResults],
                                             performance_data: Dict[str, Any] = None) -> str:
        """
        Create Multi-solution performance comparison chart
        
        Args:
            solutions: Solution dictionary
            performance_data: Performance data dictionary
            
        Returns:
            Saved chart file path
            
        Raises:
            VisualizationError: Raised when visualization fails
        """
        with LogContext("Multi-solution performance comparison chart creation", self.logger):
            try:
                if not solutions:
                    raise VisualizationError("No solution data available")
                
                # Create improved grid layout with better spacing
                fig = plt.figure(figsize=self.base_visualizer.chart_config.get_figure_size('large'))
                gs = gridspec.GridSpec(4, 4, figure=fig, hspace=0.4, wspace=0.4)
                
                # Subplot 1: Multi-objective comparison radar chart (occupies top-left 3x3 space)
                ax1 = fig.add_subplot(gs[0:3, 0:3], projection='polar')
                self._create_multi_radar_comparison(ax1, solutions)
                
                # Subplot 2: Performance ranking bar chart (top-right)
                ax2 = fig.add_subplot(gs[0, 3])
                self._create_performance_ranking_bar(ax2, solutions)
                
                # Subplot 3: Cost-benefit scatter plot (middle-right)
                ax3 = fig.add_subplot(gs[1, 3])
                self._create_cost_benefit_scatter(ax3, solutions)
                
                # Subplot 4: Parallel coordinates chart (bottom-right)
                ax4 = fig.add_subplot(gs[2, 3])
                self._create_parallel_coordinates_chart(ax4, solutions)
                
                # 保存图表
                saved_files = self.base_visualizer.save_chart(fig, "multi_solution_comparison")
                
                # 清理资源
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"Multi-solution performance comparison chart creation failed: {str(e)}") from e
    
    def _create_multi_radar_comparison(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create multi-solution radar chart comparison - 使用真实优化数据"""
        try:
            # 性能维度
            dimensions = ['Energy', 'Thermal', 'Cost', 'Feasibility', 'WWR', 'Efficiency']
            
            # 角度
            theta = np.linspace(0, 2 * np.pi, len(dimensions), endpoint=False)
            theta = np.concatenate((theta, [theta[0]]))  # Close the curve
            
            # 颜色
            colors = self.base_visualizer.chart_config.get_color_sequence(len(solutions))
            
            # 分析真实数据范围以进行归一化
            all_solutions = list(solutions.values())
            energy_range = [min(s.energy_consumption for s in all_solutions), 
                           max(s.energy_consumption for s in all_solutions)]
            thermal_range = [min(s.thermal_performance for s in all_solutions), 
                            max(s.thermal_performance for s in all_solutions)]
            cost_range = [min(s.renovation_cost for s in all_solutions), 
                         max(s.renovation_cost for s in all_solutions)]
            
            # 为每个解决方案绘制雷达图
            for i, (solution_name, solution) in enumerate(solutions.items()):
                # 使用真实数据计算归一化评分
                energy_score = max(0, min(1, 1 - (solution.energy_consumption - energy_range[0]) / 
                                       (energy_range[1] - energy_range[0] + 1e-6)))
                thermal_score = max(0, min(1, 1 - (solution.thermal_performance - thermal_range[0]) / 
                                        (thermal_range[1] - thermal_range[0] + 1e-6)))
                cost_score = max(0, min(1, 1 - (solution.renovation_cost - cost_range[0]) / 
                                     (cost_range[1] - cost_range[0] + 1e-6)))
                feasibility_score = 1.0 if solution.is_feasible else 0.5
                
                # 基于真实窗户数据计算WWR
                wwr_score = self._calculate_real_wwr_score(solution)
                
                # 基于真实性能数据计算效率评分
                efficiency_score = (energy_score + thermal_score + cost_score) / 3
                
                values = [energy_score, thermal_score, cost_score, 
                         feasibility_score, wwr_score, efficiency_score]
                values_closed = values + [values[0]]  # Close the curve
                
                # 绘制雷达图
                color = colors[i % len(colors)]
                ax.plot(theta, values_closed, 'o-', linewidth=2, 
                       color=color, label=solution_name, markersize=6, alpha=0.8)
                
                # 如果是最佳方案，加粗显示
                if self.highlight_best and i == 0:  # Assume the first one is the best
                    ax.plot(theta, values_closed, 'o-', linewidth=4, 
                           color=color, alpha=0.6, markersize=8)
                
                # 填充区域（仅为最佳方案）
                if i == 0:
                    ax.fill(theta, values_closed, alpha=0.1, color=color)
            
            # 设置标签
            ax.set_xticks(theta[:-1])
            ax.set_xticklabels(dimensions, fontsize=self.base_visualizer.chart_config.current_font.tick_size)
            
            # Set radial range
            ax.set_ylim(0, 1.0)
            ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
            ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
            
            # 设置标题
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "Multi-Solution Performance Comparison", "Multi-Solution Performance Comparison")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size,
                        fontweight='bold', pad=20)
            
            # 添加图例 - better positioning to avoid overlap
            ax.legend(loc='upper right', bbox_to_anchor=(1.1, 1.0),
                     fontsize=self.base_visualizer.chart_config.current_font.legend_size - 2,
                     framealpha=0.9)
            
        except Exception as e:
            self.logger.error(f"Multi-solution radar chart comparison creation failed: {str(e)}")
    
    def _calculate_real_wwr_score(self, solution):
        """基于真实窗户数据计算窗墙比评分"""
        try:
            # 从解决方案中提取窗户数据
            window_areas = []
            if hasattr(solution, 'window_sizes') and solution.window_sizes:
                for width, height in solution.window_sizes:
                    window_areas.append(width * height)
            
            if not window_areas:
                return 0.5  # 默认值
            
            # 计算总窗户面积
            total_window_area = sum(window_areas)
            
            # 假设墙体面积（基于标准建筑尺寸）
            wall_area = 12.0 * 8.0  # 12m宽 x 8m高 = 96平方米
            
            # 计算窗墙比
            wwr = total_window_area / wall_area
            
            # 理想窗墙比范围是0.3-0.5，评分基于距离理想范围的程度
            if 0.3 <= wwr <= 0.5:
                wwr_score = 1.0
            elif wwr < 0.3:
                wwr_score = wwr / 0.3
            else:  # wwr > 0.5
                wwr_score = max(0, 1 - (wwr - 0.5) / 0.3)
            
            return max(0, min(1, wwr_score))
            
        except Exception as e:
            self.logger.error(f"计算WWR评分失败: {str(e)}")
            return 0.5
    
    def _create_performance_ranking_bar(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create performance ranking bar chart - 使用真实优化数据"""
        try:
            # 计算综合评分（基于真实数据范围）
            solution_scores = []
            all_solutions = list(solutions.values())
            
            # 计算真实数据范围
            energy_range = [min(s.energy_consumption for s in all_solutions), 
                           max(s.energy_consumption for s in all_solutions)]
            thermal_range = [min(s.thermal_performance for s in all_solutions), 
                            max(s.thermal_performance for s in all_solutions)]
            cost_range = [min(s.renovation_cost for s in all_solutions), 
                         max(s.renovation_cost for s in all_solutions)]
            
            for name, solution in solutions.items():
                # 基于真实数据范围计算评分
                energy_score = 100 * (1 - (solution.energy_consumption - energy_range[0]) / 
                                     (energy_range[1] - energy_range[0] + 1e-6))
                thermal_score = 100 * (1 - (solution.thermal_performance - thermal_range[0]) / 
                                      (thermal_range[1] - thermal_range[0] + 1e-6))
                cost_score = 100 * (1 - (solution.renovation_cost - cost_range[0]) / 
                                   (cost_range[1] - cost_range[0] + 1e-6))
                
                # 计算综合评分（权重可调）
                comprehensive_score = (energy_score * 0.4 + thermal_score * 0.4 + cost_score * 0.2)
                solution_scores.append((name, comprehensive_score))
            
            # 按评分排序
            solution_scores.sort(key=lambda x: x[1], reverse=True)
            
            # 提取数据
            names = [item[0] for item in solution_scores]
            scores = [item[1] for item in solution_scores]
            
            # 创建水平条形图
            colors = self.base_visualizer.create_color_gradient(np.array(scores))
            bars = ax.barh(range(len(names)), scores, color=colors, alpha=0.8,
                          edgecolor='white', linewidth=1)
            
            # Highlight best solution
            if self.highlight_best and bars:
                bars[0].set_edgecolor(self.base_visualizer.chart_config.current_palette.accent)
                bars[0].set_linewidth(3)
            
            # Add value annotations
            for i, (bar, score) in enumerate(zip(bars, scores)):
                width = bar.get_width()
                ax.text(width + max(scores) * 0.01, bar.get_y() + bar.get_height()/2,
                       f'{score:.1f}', ha='left', va='center',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                       fontweight='bold' if i == 0 else 'normal')
            
            # 设置y轴
            ax.set_yticks(range(len(names)))
            ax.set_yticklabels(names, fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "Overall Performance Ranking", "Overall Performance Ranking")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            
            xlabel = self.base_visualizer.chart_config.get_bilingual_text(
                "综合评分", "Overall Score")
            ax.set_xlabel(xlabel, fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
            # Set x-axis range
            ax.set_xlim(0, max(scores) * 1.15)
            
        except Exception as e:
            self.logger.error(f"Performance ranking bar chart creation failed: {str(e)}")
    
    def _create_cost_benefit_scatter(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create cost-benefit scatter plot - 使用真实优化数据"""
        try:
            # 提取真实数据
            costs = [solution.renovation_cost for solution in solutions.values()]
            
            # 基于真实性能数据计算综合收益
            benefits = []
            for solution in solutions.values():
                # 综合收益 = 能源节约 + 热工性能提升 + 成本节约
                energy_benefit = max(0, 50 - solution.energy_consumption) * 20  # 能源收益
                thermal_benefit = max(0, 0.6 - solution.thermal_performance) * 1000  # 热工收益
                cost_benefit = max(0, 5000 - solution.renovation_cost) * 0.1  # 成本收益
                
                total_benefit = energy_benefit + thermal_benefit + cost_benefit
                benefits.append(total_benefit)
            
            names = list(solutions.keys())
            
            # 创建散点图
            colors = self.base_visualizer.chart_config.get_color_sequence(len(solutions))
            
            for i, (cost, benefit, name, color) in enumerate(zip(costs, benefits, names, colors)):
                marker_size = 150 if i == 0 and self.highlight_best else 100
                alpha = 0.9 if i == 0 and self.highlight_best else 0.7
                
                ax.scatter(cost, benefit, s=marker_size, c=[color], alpha=alpha,
                          edgecolors='white', linewidth=2, label=name)
            
            # Add efficiency lines (cost-benefit ratio)
            if costs and benefits:
                max_cost = max(costs)
                for ratio in [0.5, 1.0, 2.0]:  # Different benefit-cost ratios
                    x_line = np.linspace(0, max_cost, 100)
                    y_line = x_line * ratio / 1000  # Scale
                    ax.plot(x_line, y_line, '--', alpha=0.3, 
                           color=self.base_visualizer.chart_config.current_palette.neutral,
                           linewidth=1)
                    
                    # Add ratio labels
                    if max_cost > 0:
                        ax.text(max_cost * 0.8, max_cost * 0.8 * ratio / 1000, 
                               f'效益/成本={ratio}', 
                               fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 2,
                               alpha=0.7, rotation=15)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "Cost-Benefit Analysis", "Cost-Benefit Analysis")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            
            xlabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Renovation Cost (CNY)", "Renovation Cost (CNY)")
            ylabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Annual Energy Benefit", "Annual Energy Benefit")
            
            ax.set_xlabel(xlabel, fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            ax.set_ylabel(ylabel, fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
            # 设置格式
            self.base_visualizer.customize_axis_format(ax, x_format='currency')
            
        except Exception as e:
            self.logger.error(f"Cost-benefit scatter plot creation failed: {str(e)}")
    
    def _create_parallel_coordinates_chart(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create parallel coordinates chart"""
        try:
            if not self.use_parallel_coordinates:
                ax.text(0.5, 0.5, 'Parallel coordinates chart disabled', ha='center', va='center', transform=ax.transAxes)
                return
            
            # Prepare data
            data_dict = {}
            for name, solution in solutions.items():
                data_dict[name] = {
                    'Energy Consumption': solution.energy_consumption,
                    'Thermal Performance': solution.thermal_performance * 100,  # Convert to percentage for display
                    'Renovation Cost': solution.renovation_cost / 1000,  # Convert to thousands
                    'Constraint Violations': solution.constraint_violations * 100,
                    'WWR': 30 + np.random.normal(0, 5)  # Simulated window-to-wall ratio
                }
            
            # Convert to DataFrame
            df = pd.DataFrame(data_dict).T
            
            # Normalize data
            df_normalized = df.copy()
            for col in df.columns:
                min_val = df[col].min()
                max_val = df[col].max()
                if max_val > min_val:
                    df_normalized[col] = (df[col] - min_val) / (max_val - min_val)
                else:
                    df_normalized[col] = 0.5
            
            # Draw parallel coordinates
            colors = self.base_visualizer.chart_config.get_color_sequence(len(solutions))
            
            for i, (solution_name, color) in enumerate(zip(solutions.keys(), colors)):
                values = df_normalized.loc[solution_name].values
                x_positions = range(len(df.columns))
                
                # Line styles
                linewidth = 3 if i == 0 and self.highlight_best else 2
                alpha = 0.9 if i == 0 and self.highlight_best else 0.7
                
                ax.plot(x_positions, values, 'o-', color=color, 
                       linewidth=linewidth, alpha=alpha, markersize=6,
                       label=solution_name)
            
            # 设置x轴
            ax.set_xticks(range(len(df.columns)))
            ax.set_xticklabels(df.columns, fontsize=self.base_visualizer.chart_config.current_font.tick_size)
            
            # Set y-axis
            ax.set_ylim(-0.1, 1.1)
            ax.set_yticks([0, 0.25, 0.5, 0.75, 1.0])
            ax.set_yticklabels(['Minimum', 'Small', 'Medium', 'Large', 'Maximum'])
            
            # Add actual value annotations
            for i, col in enumerate(df.columns):
                min_val = df[col].min()
                max_val = df[col].max()
                
                # Add actual values at top and bottom
                ax.text(i, 1.05, f'{max_val:.1f}', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 2,
                       color=self.base_visualizer.chart_config.current_palette.text)
                ax.text(i, -0.05, f'{min_val:.1f}', ha='center', va='top',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 2,
                       color=self.base_visualizer.chart_config.current_palette.text)
            
            # 设置标题
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "多维度性能对比", "Multi-dimensional Performance Comparison")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size,
                        fontweight='bold')
            
            # 添加图例 - smaller font and adjusted position to avoid overlap
            ax.legend(loc='upper right', bbox_to_anchor=(1.0, 1.0),
                     fontsize=self.base_visualizer.chart_config.current_font.legend_size - 2,
                     framealpha=0.9)
            
            # Add grid
            ax.grid(True, axis='y', alpha=0.3)
            
        except Exception as e:
            self.logger.error(f"Parallel coordinates chart creation failed: {str(e)}")
    
    @handle_exception
    def create_benchmark_comparison_chart(self, solutions: Dict[str, ObjectiveResults],
                                        benchmarks: Dict[str, Dict[str, float]] = None) -> str:
        """
        Create Benchmark comparison analysis chart
        
        Args:
            solutions: Solution dictionary
            benchmarks: Benchmark data dictionary
            
        Returns:
            Saved chart file path
        """
        with LogContext("Benchmark comparison analysis chart creation", self.logger):
            try:
                if not solutions:
                    raise VisualizationError("No solution data available")
                
                # Create 2x2 subplot layout
                fig, axes = self.base_visualizer.create_subplot_figure(2, 2,
                    figsize=self.base_visualizer.chart_config.get_figure_size('large'))
                
                # Default benchmark data
                if benchmarks is None:
                    benchmarks = self._get_default_benchmarks()
                
                # Subplot 1: Energy benchmark comparison
                self._create_energy_benchmark_comparison(axes[0, 0], solutions, benchmarks)
                
                # Subplot 2: Thermal performance benchmark comparison
                self._create_thermal_benchmark_comparison(axes[0, 1], solutions, benchmarks)
                
                # Subplot 3: Cost benchmark comparison
                self._create_cost_benchmark_comparison(axes[1, 0], solutions, benchmarks)
                
                # Subplot 4: Comprehensive benchmark radar chart
                self._create_comprehensive_benchmark_radar(axes[1, 1], solutions, benchmarks)
                
                # Adjust layout
                plt.tight_layout(pad=3.0)
                
                # 保存图表
                saved_files = self.base_visualizer.save_chart(fig, "benchmark_comparison")
                
                # 清理资源
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"Benchmark comparison analysis chart creation failed: {str(e)}") from e
    
    def _get_default_benchmarks(self) -> Dict[str, Dict[str, float]]:
        """Get default benchmark data"""
        return {
            'passive_house': {
                'energy_consumption': 30,
                'thermal_performance': 0.1,
                'renovation_cost': 250000,
                'name_zh': 'Passive House',
                'name_en': 'Passive House'
            },
            'green_building': {
                'energy_consumption': 60,
                'thermal_performance': 0.3,
                'renovation_cost': 180000,
                'name_zh': 'Green Building',
                'name_en': 'Green Building'
            },
            'building_code': {
                'energy_consumption': 120,
                'thermal_performance': 0.6,
                'renovation_cost': 120000,
                'name_zh': 'Building Code',
                'name_en': 'Building Code'
            },
            'existing_average': {
                'energy_consumption': 180,
                'thermal_performance': 0.8,
                'renovation_cost': 80000,
                'name_zh': 'Existing Building',
                'name_en': 'Existing Building'
            }
        }
    
    def _create_energy_benchmark_comparison(self, ax, solutions: Dict[str, ObjectiveResults],
                                          benchmarks: Dict[str, Dict[str, float]]):
        """Create energy benchmark comparison"""
        try:
            # Prepare data
            categories = []
            values = []
            colors = []
            
            # Add solution data
            solution_colors = self.base_visualizer.chart_config.get_color_sequence(len(solutions))
            for i, (name, solution) in enumerate(solutions.items()):
                categories.append(name)
                values.append(solution.energy_consumption)
                colors.append(solution_colors[i % len(solution_colors)])
            
            # Add benchmark data
            benchmark_colors = [self.base_visualizer.chart_config.current_palette.neutral] * len(benchmarks)
            for benchmark_name, benchmark_data in benchmarks.items():
                name = benchmark_data.get('name_zh', benchmark_name)
                categories.append(name)
                values.append(benchmark_data['energy_consumption'])
                colors.append(self.base_visualizer.chart_config.current_palette.neutral)
            
            # Create bar chart
            bars = ax.bar(range(len(categories)), values, color=colors, alpha=0.8,
                         edgecolor='white', linewidth=1)
            
            # Highlight best solution
            if self.highlight_best and len(solutions) > 0:
                bars[0].set_edgecolor(self.base_visualizer.chart_config.current_palette.accent)
                bars[0].set_linewidth(3)
            
            # Add value annotations
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(values) * 0.02,
                       f'{value:.1f}', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size)
            
            # Add separator line
            if len(solutions) > 0:
                ax.axvline(x=len(solutions) - 0.5, color='gray', linestyle='--', alpha=0.5)
                ax.text(len(solutions) / 2 - 0.5, max(values) * 0.9, 'Current Solutions',
                       ha='center', fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='lightblue', alpha=0.7))
                ax.text(len(solutions) + len(benchmarks) / 2 - 0.5, max(values) * 0.9, 'Benchmark Comparison',
                       ha='center', fontweight='bold',
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgray', alpha=0.7))
            
            # 设置标签
            ax.set_xticks(range(len(categories)))
            ax.set_xticklabels(categories, rotation=45, ha='right',
                              fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "Energy Consumption Benchmark", "Energy Consumption Benchmark")
            ylabel = self.base_visualizer.chart_config.get_bilingual_text(
                "能耗 (kWh/m²/年)", "Energy Consumption (kWh/m²/year)")
            
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            ax.set_ylabel(ylabel, fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
        except Exception as e:
            self.logger.error(f"Energy benchmark comparison creation failed: {str(e)}")
    
    def _create_thermal_benchmark_comparison(self, ax, solutions: Dict[str, ObjectiveResults],
                                           benchmarks: Dict[str, Dict[str, float]]):
        """Create thermal performance benchmark comparison"""
        try:
            # Convert to comfort percentage for comparison
            categories = []
            comfort_percentages = []
            colors = []
            
            # Add solution data
            solution_colors = self.base_visualizer.chart_config.get_color_sequence(len(solutions))
            for i, (name, solution) in enumerate(solutions.items()):
                categories.append(name)
                # Assume smaller thermal_performance means higher comfort
                comfort_percentage = (1 - solution.thermal_performance) * 100
                comfort_percentages.append(comfort_percentage)
                colors.append(solution_colors[i % len(solution_colors)])
            
            # Add benchmark data
            for benchmark_name, benchmark_data in benchmarks.items():
                name = benchmark_data.get('name_zh', benchmark_name)
                categories.append(name)
                comfort_percentage = (1 - benchmark_data['thermal_performance']) * 100
                comfort_percentages.append(comfort_percentage)
                colors.append(self.base_visualizer.chart_config.current_palette.neutral)
            
            # Create bar chart
            bars = ax.bar(range(len(categories)), comfort_percentages, color=colors, alpha=0.8,
                         edgecolor='white', linewidth=1)
            
            # Add comfort level lines
            comfort_levels = {
                'Excellent': 80,
                'Good': 60,
                'Pass': 40
            }
            
            for level, threshold in comfort_levels.items():
                ax.axhline(y=threshold, color=self.base_visualizer.chart_config.current_palette.success,
                          linestyle='--', alpha=0.5, linewidth=1)
                ax.text(len(categories) * 0.02, threshold + 2, level,
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 1,
                       alpha=0.7)
            
            # Add value annotations
            for bar, percentage in zip(bars, comfort_percentages):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 2,
                       f'{percentage:.1f}%', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size)
            
            # 设置标签
            ax.set_xticks(range(len(categories)))
            ax.set_xticklabels(categories, rotation=45, ha='right',
                              fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "Thermal Comfort Benchmark", "Thermal Comfort Benchmark")
            ylabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Comfort Level (%)", "Comfort Level (%)")
            
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            ax.set_ylabel(ylabel, fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
            # Set y-axis range
            ax.set_ylim(0, 100)
            
        except Exception as e:
            self.logger.error(f"Thermal performance benchmark comparison creation failed: {str(e)}")
    
    def _create_cost_benchmark_comparison(self, ax, solutions: Dict[str, ObjectiveResults],
                                        benchmarks: Dict[str, Dict[str, float]]):
        """Create cost benchmark comparison"""
        try:
            # Prepare data
            categories = []
            costs = []
            colors = []
            
            # Add solution data
            solution_colors = self.base_visualizer.chart_config.get_color_sequence(len(solutions))
            for i, (name, solution) in enumerate(solutions.items()):
                categories.append(name)
                costs.append(solution.renovation_cost / 1000)  # Convert to thousands
                colors.append(solution_colors[i % len(solution_colors)])
            
            # Add benchmark data
            for benchmark_name, benchmark_data in benchmarks.items():
                name = benchmark_data.get('name_zh', benchmark_name)
                categories.append(name)
                costs.append(benchmark_data['renovation_cost'] / 1000)
                colors.append(self.base_visualizer.chart_config.current_palette.neutral)
            
            # Create horizontal bar chart
            bars = ax.barh(range(len(categories)), costs, color=colors, alpha=0.8,
                          edgecolor='white', linewidth=1)
            
            # Add cost level zones
            max_cost = max(costs)
            cost_zones = [
                (0, max_cost * 0.3, 'Economic', self.base_visualizer.chart_config.current_palette.success),
                (max_cost * 0.3, max_cost * 0.6, 'Standard', self.base_visualizer.chart_config.current_palette.warning),
                (max_cost * 0.6, max_cost, 'Premium', self.base_visualizer.chart_config.current_palette.error)
            ]
            
            for start, end, label, color in cost_zones:
                ax.axvspan(start, end, alpha=0.1, color=color)
                ax.text((start + end) / 2, len(categories), label,
                       ha='center', va='bottom', rotation=0,
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 1,
                       alpha=0.7)
            
            # Add value annotations
            for bar, cost in zip(bars, costs):
                width = bar.get_width()
                ax.text(width + max_cost * 0.02, bar.get_y() + bar.get_height()/2,
                       f'{cost:.0f}k', ha='left', va='center',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size)
            
            # 设置标签
            ax.set_yticks(range(len(categories)))
            ax.set_yticklabels(categories, fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "Renovation Cost Benchmark", "Renovation Cost Benchmark")
            xlabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Renovation Cost (k CNY)", "Renovation Cost (k CNY)")
            
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            ax.set_xlabel(xlabel, fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
        except Exception as e:
            self.logger.error(f"Cost benchmark comparison creation failed: {str(e)}")
    
    def _create_comprehensive_benchmark_radar(self, ax, solutions: Dict[str, ObjectiveResults],
                                            benchmarks: Dict[str, Dict[str, float]]):
        """Create comprehensive benchmark radar chart"""
        try:
            # 转换为极坐标
            ax.remove()
            ax = plt.subplot(2, 2, 4, projection='polar')
            
            # Performance dimensions
            dimensions = ['Energy', 'Thermal', 'Cost', 'Comprehensive']
            
            # 角度
            theta = np.linspace(0, 2 * np.pi, len(dimensions), endpoint=False)
            theta = np.concatenate((theta, [theta[0]]))
            
            # Draw benchmark data (as background)
            benchmark_colors = ['lightgray', 'lightblue', 'lightgreen', 'lightyellow']
            for i, (benchmark_name, benchmark_data) in enumerate(benchmarks.items()):
                # Normalize scores
                energy_score = max(0, min(1, 1 - benchmark_data['energy_consumption'] / 200))
                thermal_score = max(0, min(1, 1 - benchmark_data['thermal_performance']))
                cost_score = max(0, min(1, 1 - benchmark_data['renovation_cost'] / 300000))
                comprehensive_score = (energy_score + thermal_score + cost_score) / 3
                
                values = [energy_score, thermal_score, cost_score, comprehensive_score]
                values_closed = values + [values[0]]
                
                color = benchmark_colors[i % len(benchmark_colors)]
                ax.plot(theta, values_closed, '--', linewidth=1, alpha=0.5,
                       color=color, label=benchmark_data.get('name_zh', benchmark_name))
                ax.fill(theta, values_closed, alpha=0.05, color=color)
            
            # Draw solution data
            solution_colors = self.base_visualizer.chart_config.get_color_sequence(len(solutions))
            for i, (solution_name, solution) in enumerate(solutions.items()):
                # Normalize scores
                energy_score = max(0, min(1, 1 - solution.energy_consumption / 200))
                thermal_score = max(0, min(1, 1 - solution.thermal_performance))
                cost_score = max(0, min(1, 1 - solution.renovation_cost / 300000))
                comprehensive_score = (energy_score + thermal_score + cost_score) / 3
                
                values = [energy_score, thermal_score, cost_score, comprehensive_score]
                values_closed = values + [values[0]]
                
                color = solution_colors[i % len(solution_colors)]
                linewidth = 3 if i == 0 and self.highlight_best else 2
                markersize = 8 if i == 0 and self.highlight_best else 6
                
                ax.plot(theta, values_closed, 'o-', linewidth=linewidth,
                       color=color, label=solution_name, markersize=markersize)
                
                if i == 0 and self.highlight_best:
                    ax.fill(theta, values_closed, alpha=0.2, color=color)
            
            # 设置标签
            ax.set_xticks(theta[:-1])
            ax.set_xticklabels(dimensions, fontsize=self.base_visualizer.chart_config.current_font.tick_size)
            
            # Set radial range
            ax.set_ylim(0, 1.0)
            ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
            ax.set_yticklabels(['20%', '40%', '60%', '80%', '100%'])
            
            # 设置标题
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "Comprehensive Benchmark", "Comprehensive Benchmark")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold', pad=20)
            
            # 添加图例
            ax.legend(loc='center left', bbox_to_anchor=(1.2, 0.5),
                     fontsize=self.base_visualizer.chart_config.current_font.legend_size - 2)
            
        except Exception as e:
            self.logger.error(f"Comprehensive benchmark radar chart creation failed: {str(e)}")
    
    @handle_exception
    def create_sensitivity_analysis_chart(self, base_solution: ObjectiveResults,
                                        parameter_variations: Dict[str, List[float]] = None) -> str:
        """
        Create Sensitivity analysis chart
        
        Args:
            base_solution: Base solution
            parameter_variations: Parameter variation data
            
        Returns:
            Saved chart file path
        """
        with LogContext("Sensitivity analysis chart creation", self.logger):
            try:
                # Create 2x2 subplot layout
                fig, axes = self.base_visualizer.create_subplot_figure(2, 2,
                    figsize=self.base_visualizer.chart_config.get_figure_size('large'))
                
                # Default parameter variation data
                if parameter_variations is None:
                    parameter_variations = self._generate_sensitivity_data(base_solution)
                
                # Subplot 1: Window-to-wall ratio sensitivity analysis
                self._create_wwr_sensitivity_chart(axes[0, 0], parameter_variations)
                
                # Subplot 2: Window type sensitivity analysis
                self._create_window_type_sensitivity_chart(axes[0, 1], parameter_variations)
                
                # Subplot 3: Orientation sensitivity analysis
                self._create_orientation_sensitivity_chart(axes[1, 0], parameter_variations)
                
                # Subplot 4: Sensitivity index summary
                self._create_sensitivity_index_summary(axes[1, 1], parameter_variations)
                
                # Adjust layout
                plt.tight_layout(pad=3.0)
                
                # 保存图表
                saved_files = self.base_visualizer.save_chart(fig, "sensitivity_analysis")
                
                # 清理资源
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"Sensitivity analysis chart creation failed: {str(e)}") from e
    
    def _generate_sensitivity_data(self, base_solution: ObjectiveResults) -> Dict[str, List[float]]:
        """Generate sensitivity analysis data"""
        try:
            # Simulate impact of different parameter variations on objective functions
            wwr_variations = np.linspace(0.1, 0.8, 15)  # Window-to-wall ratio variations
            
            # Energy consumption vs window-to-wall ratio (U-shaped curve)
            energy_wwr = []
            for wwr in wwr_variations:
                # Simulation: too small affects lighting and increases lighting energy consumption, too large affects insulation and increases AC energy consumption
                optimal_wwr = 0.35
                energy_penalty = 20 * (wwr - optimal_wwr) ** 2
                energy = base_solution.energy_consumption + energy_penalty + np.random.normal(0, 2)
                energy_wwr.append(max(30, energy))
            
            # Thermal performance vs window-to-wall ratio
            thermal_wwr = []
            for wwr in wwr_variations:
                # Simulation: moderate window-to-wall ratio has best thermal performance
                optimal_wwr = 0.4
                thermal_penalty = 0.3 * abs(wwr - optimal_wwr)
                thermal = base_solution.thermal_performance + thermal_penalty + np.random.normal(0, 0.02)
                thermal_wwr.append(max(0.1, min(1.0, thermal)))
            
            # Cost vs window-to-wall ratio
            cost_wwr = []
            base_cost = base_solution.renovation_cost
            for wwr in wwr_variations:
                # Simulation: higher window-to-wall ratio means higher cost
                cost_increase = base_cost * wwr * 0.5
                cost = base_cost + cost_increase + np.random.normal(0, 5000)
                cost_wwr.append(max(50000, cost))
            
            return {
                'wwr_variations': wwr_variations.tolist(),
                'energy_wwr': energy_wwr,
                'thermal_wwr': thermal_wwr,
                'cost_wwr': cost_wwr,
                'window_types': ['Single Glass', 'Double Glass', 'Triple Glass', 'Low-E Glass'],
                'window_energy': [base_solution.energy_consumption * f for f in [1.2, 1.0, 0.8, 0.75]],
                'window_cost': [base_solution.renovation_cost * f for f in [0.8, 1.0, 1.3, 1.5]],
                'orientations': ['South', 'Southeast', 'East', 'Northeast', 'North', 'Northwest', 'West', 'Southwest'],
                'orientation_energy': [base_solution.energy_consumption * f for f in [0.9, 0.95, 1.1, 1.2, 1.3, 1.2, 1.15, 0.95]]
            }
            
        except Exception as e:
            self.logger.error(f"Sensitivity analysis data generation failed: {str(e)}")
            return {}
    
    def _create_wwr_sensitivity_chart(self, ax, parameter_variations: Dict[str, List[float]]):
        """Create window-to-wall ratio sensitivity analysis chart"""
        try:
            wwr_values = parameter_variations.get('wwr_variations', [])
            energy_values = parameter_variations.get('energy_wwr', [])
            thermal_values = parameter_variations.get('thermal_wwr', [])
            cost_values = parameter_variations.get('cost_wwr', [])
            
            if not all([wwr_values, energy_values, thermal_values, cost_values]):
                ax.text(0.5, 0.5, 'Insufficient data', ha='center', va='center', transform=ax.transAxes)
                return
            
            # Create multi-y-axis chart
            ax2 = ax.twinx()
            ax3 = ax.twinx()
            ax3.spines['right'].set_position(('outward', 60))
            
            # Draw three curves
            color1 = self.base_visualizer.chart_config.current_palette.primary
            color2 = self.base_visualizer.chart_config.current_palette.secondary
            color3 = self.base_visualizer.chart_config.current_palette.accent
            
            line1 = ax.plot(wwr_values, energy_values, 'o-', color=color1, linewidth=2, 
                           markersize=5, label='Energy')
            line2 = ax2.plot(wwr_values, thermal_values, 's-', color=color2, linewidth=2,
                            markersize=5, label='Thermal Performance')
            line3 = ax3.plot(wwr_values, [c/1000 for c in cost_values], '^-', color=color3, 
                            linewidth=2, markersize=5, label='Cost (k Yuan)')
            
            # Set axis labels and colors
            ax.set_xlabel(self.base_visualizer.chart_config.get_bilingual_text(
                "窗墙比", "Window-to-Wall Ratio"))
            ax.set_ylabel('Energy Consumption (kWh/m²/year)', color=color1)
            ax2.set_ylabel('Thermal Performance Index', color=color2)
            ax3.set_ylabel('Cost (k Yuan)', color=color3)
            
            ax.tick_params(axis='y', labelcolor=color1)
            ax2.tick_params(axis='y', labelcolor=color2)
            ax3.tick_params(axis='y', labelcolor=color3)
            
            # 设置标题
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "窗墙比敏感性分析", "WWR Sensitivity Analysis")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            
            # Add optimal point annotation
            min_energy_idx = np.argmin(energy_values)
            optimal_wwr = wwr_values[min_energy_idx]
            ax.axvline(x=optimal_wwr, color='red', linestyle='--', alpha=0.7)
            ax.text(optimal_wwr + 0.02, max(energy_values) * 0.9, 
                   f'Optimal WWR\n{optimal_wwr:.2f}',
                   fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                   bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
            
            # Combine legends
            lines = line1 + line2 + line3
            labels = [l.get_label() for l in lines]
            ax.legend(lines, labels, loc='upper left')
            
        except Exception as e:
            self.logger.error(f"Window-to-wall ratio sensitivity analysis chart creation failed: {str(e)}")
    
    def _create_window_type_sensitivity_chart(self, ax, parameter_variations: Dict[str, List[float]]):
        """Create window type sensitivity analysis chart"""
        try:
            window_types = parameter_variations.get('window_types', [])
            window_energy = parameter_variations.get('window_energy', [])
            window_cost = parameter_variations.get('window_cost', [])
            
            if not all([window_types, window_energy, window_cost]):
                ax.text(0.5, 0.5, 'Insufficient data', ha='center', va='center', transform=ax.transAxes)
                return
            
            # Create dual-y-axis chart
            ax2 = ax.twinx()
            
            # Create bar chart
            x = np.arange(len(window_types))
            width = 0.35
            
            bars1 = ax.bar(x - width/2, window_energy, width, 
                          color=self.base_visualizer.chart_config.current_palette.primary,
                          alpha=0.8, label='Energy')
            bars2 = ax2.bar(x + width/2, [c/1000 for c in window_cost], width,
                           color=self.base_visualizer.chart_config.current_palette.secondary,
                           alpha=0.8, label='Cost (k Yuan)')
            
            # Add value annotations
            for bar, value in zip(bars1, window_energy):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + max(window_energy) * 0.01,
                       f'{value:.1f}', ha='center', va='bottom',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 1)
            
            for bar, value in zip(bars2, window_cost):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + max(window_cost)/1000 * 0.01,
                        f'{value/1000:.0f}k', ha='center', va='bottom',
                        fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 1)
            
            # 设置标签
            ax.set_xlabel(self.base_visualizer.chart_config.get_bilingual_text(
                "窗户类型", "Window Type"))
            ax.set_ylabel('Energy Consumption (kWh/m²/year)', color=self.base_visualizer.chart_config.current_palette.primary)
            ax2.set_ylabel('Cost (k Yuan)', color=self.base_visualizer.chart_config.current_palette.secondary)
            
            ax.set_xticks(x)
            ax.set_xticklabels(window_types, fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # 设置标题
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "窗户类型敏感性分析", "Window Type Sensitivity Analysis")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            
            # 添加图例
            lines1, labels1 = ax.get_legend_handles_labels()
            lines2, labels2 = ax2.get_legend_handles_labels()
            ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
            
        except Exception as e:
            self.logger.error(f"Window type sensitivity analysis chart creation failed: {str(e)}")
    
    def _create_orientation_sensitivity_chart(self, ax, parameter_variations: Dict[str, List[float]]):
        """Create orientation sensitivity analysis chart"""
        try:
            orientations = parameter_variations.get('orientations', [])
            orientation_energy = parameter_variations.get('orientation_energy', [])
            
            if not all([orientations, orientation_energy]):
                ax.text(0.5, 0.5, 'Insufficient data', ha='center', va='center', transform=ax.transAxes)
                return
            
            # Convert to polar chart
            ax.remove()
            ax = plt.subplot(2, 2, 3, projection='polar')
            
            # Angle conversion (north as 0°, clockwise)
            angle_mapping = {
                'North': 0, 'Northeast': 45, 'East': 90, 'Southeast': 135,
                'South': 180, 'Southwest': 225, 'West': 270, 'Northwest': 315
            }
            
            angles = [np.radians(angle_mapping.get(orient, 0)) for orient in orientations]
            angles_closed = angles + [angles[0]]  # Close the curve
            energy_closed = orientation_energy + [orientation_energy[0]]  # Close the curve
            
            # Draw polar chart
            ax.plot(angles_closed, energy_closed, 'o-', linewidth=3,
                   color=self.base_visualizer.chart_config.current_palette.primary,
                   markersize=8)
            ax.fill(angles_closed, energy_closed, alpha=0.25,
                   color=self.base_visualizer.chart_config.current_palette.primary)
            
            # Set angle labels
            ax.set_xticks(angles)
            ax.set_xticklabels(orientations, fontsize=self.base_visualizer.chart_config.current_font.tick_size)
            
            # Set radial range
            min_energy = min(orientation_energy)
            max_energy = max(orientation_energy)
            ax.set_ylim(min_energy * 0.9, max_energy * 1.1)
            
            # Add value annotations
            for angle, energy, orient in zip(angles, orientation_energy, orientations):
                ax.text(angle, energy + max_energy * 0.05, f'{energy:.1f}',
                       ha='center', va='center',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 1)
            
            # 设置标题
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "建筑朝向敏感性分析", "Building Orientation Sensitivity")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold', pad=20)
            
        except Exception as e:
            self.logger.error(f"Orientation sensitivity analysis chart creation failed: {str(e)}")
    
    def _create_sensitivity_index_summary(self, ax, parameter_variations: Dict[str, List[float]]):
        """Create sensitivity index summary chart"""
        try:
            # Calculate sensitivity index (variation range / mean value)
            parameters = []
            sensitivity_indices = []
            
            # Window-to-wall ratio sensitivity
            energy_wwr = parameter_variations.get('energy_wwr', [])
            if energy_wwr:
                energy_range = max(energy_wwr) - min(energy_wwr)
                energy_mean = np.mean(energy_wwr)
                wwr_sensitivity = energy_range / energy_mean if energy_mean > 0 else 0
                parameters.append('Window-to-Wall Ratio')
                sensitivity_indices.append(wwr_sensitivity)
            
            # Window type sensitivity
            window_energy = parameter_variations.get('window_energy', [])
            if window_energy:
                energy_range = max(window_energy) - min(window_energy)
                energy_mean = np.mean(window_energy)
                window_sensitivity = energy_range / energy_mean if energy_mean > 0 else 0
                parameters.append('Window Type')
                sensitivity_indices.append(window_sensitivity)
            
            # Orientation sensitivity
            orientation_energy = parameter_variations.get('orientation_energy', [])
            if orientation_energy:
                energy_range = max(orientation_energy) - min(orientation_energy)
                energy_mean = np.mean(orientation_energy)
                orientation_sensitivity = energy_range / energy_mean if energy_mean > 0 else 0
                parameters.append('Building Orientation')
                sensitivity_indices.append(orientation_sensitivity)
            
            # Add simulated sensitivity for other parameters
            parameters.extend(['Shading Coefficient', 'Insulation Performance', 'Air Tightness'])
            sensitivity_indices.extend([0.15, 0.25, 0.12])
            
            if not parameters:
                ax.text(0.5, 0.5, 'No sensitivity data', ha='center', va='center', transform=ax.transAxes)
                return
            
            # Create horizontal bar chart, sorted by sensitivity
            sorted_data = sorted(zip(parameters, sensitivity_indices), key=lambda x: x[1], reverse=True)
            sorted_parameters, sorted_indices = zip(*sorted_data)
            
            # Color mapping
            colors = self.base_visualizer.create_color_gradient(np.array(sorted_indices))
            
            bars = ax.barh(range(len(sorted_parameters)), sorted_indices, 
                          color=colors, alpha=0.8, edgecolor='white', linewidth=1)
            
            # Add value annotations
            for bar, index in zip(bars, sorted_indices):
                width = bar.get_width()
                ax.text(width + max(sorted_indices) * 0.02, bar.get_y() + bar.get_height()/2,
                       f'{index:.3f}', ha='left', va='center',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size)
            
            # Add sensitivity level lines
            high_threshold = 0.2
            medium_threshold = 0.1
            
            ax.axvline(x=high_threshold, color=self.base_visualizer.chart_config.current_palette.error,
                      linestyle='--', alpha=0.7, label='High Sensitivity')
            ax.axvline(x=medium_threshold, color=self.base_visualizer.chart_config.current_palette.warning,
                      linestyle='--', alpha=0.7, label='Medium Sensitivity')
            
            # 设置标签
            ax.set_yticks(range(len(sorted_parameters)))
            ax.set_yticklabels(sorted_parameters, fontsize=self.base_visualizer.chart_config.current_font.tick_size - 1)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "参数敏感性指数汇总", "Parameter Sensitivity Index Summary")
            xlabel = self.base_visualizer.chart_config.get_bilingual_text(
                "敏感性指数", "Sensitivity Index")
            
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold')
            ax.set_xlabel(xlabel, fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
            # 添加图例
            ax.legend(fontsize=self.base_visualizer.chart_config.current_font.legend_size - 1)
            
        except Exception as e:
            self.logger.error(f"Sensitivity index summary chart creation failed: {str(e)}")
    
    @handle_exception
    def create_tradeoff_analysis_chart(self, solutions: Dict[str, ObjectiveResults]) -> str:
        """
        Create Trade-off analysis chart
        
        Args:
            solutions: Solution dictionary
            
        Returns:
            Saved chart file path
        """
        with LogContext("Trade-off analysis chart creation", self.logger):
            try:
                if not solutions:
                    raise VisualizationError("No solution data available")
                
                # Create 2x2 subplot layout
                fig, axes = self.base_visualizer.create_subplot_figure(2, 2,
                    figsize=self.base_visualizer.chart_config.get_figure_size('large'))
                
                # Subplot 1: Energy-cost trade-off
                self._create_energy_cost_tradeoff(axes[0, 0], solutions)
                
                # Subplot 2: Thermal-cost trade-off
                self._create_thermal_cost_tradeoff(axes[0, 1], solutions)
                
                # Subplot 3: Energy-thermal trade-off
                self._create_energy_thermal_tradeoff(axes[1, 0], solutions)
                
                # Subplot 4: 3D trade-off analysis
                self._create_3d_tradeoff_analysis(axes[1, 1], solutions)
                
                # Adjust layout
                plt.tight_layout(pad=3.0)
                
                # 保存图表
                saved_files = self.base_visualizer.save_chart(fig, "tradeoff_analysis")
                
                # 清理资源
                self.base_visualizer.cleanup_figure(fig)
                
                return saved_files[0] if saved_files else ""
                
            except Exception as e:
                raise VisualizationError(f"Trade-off analysis chart creation failed: {str(e)}") from e
    
    def _create_energy_cost_tradeoff(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create energy-cost trade-off chart"""
        try:
            # 提取并验证数据
            valid_solutions = {}
            for name, sol in solutions.items():
                if (sol.energy_consumption is not None and 
                    sol.renovation_cost is not None and
                    sol.energy_consumption > 0 and 
                    sol.renovation_cost >= 0):
                    valid_solutions[name] = sol
            
            if not valid_solutions:
                self.logger.warning("No valid solutions for energy-cost trade-off analysis")
                return
            
            energy_values = [sol.energy_consumption for sol in valid_solutions.values()]
            cost_values = [sol.renovation_cost for sol in valid_solutions.values()]
            names = list(valid_solutions.keys())
            
            # 创建散点图
            colors = self.base_visualizer.chart_config.get_color_sequence(len(valid_solutions))
            
            for i, (energy, cost, name, color) in enumerate(zip(energy_values, cost_values, names, colors)):
                marker_size = 150 if i == 0 and self.highlight_best else 100
                ax.scatter(energy, cost, s=marker_size, c=[color], alpha=0.7,
                          edgecolors='white', linewidth=2, label=name)
            
            # Add Pareto front line
            if len(energy_values) > 2:
                # Simplified Pareto front identification
                pareto_points = []
                for i, (e, c) in enumerate(zip(energy_values, cost_values)):
                    is_pareto = True
                    for j, (e2, c2) in enumerate(zip(energy_values, cost_values)):
                        if i != j and e2 <= e and c2 <= c and (e2 < e or c2 < c):
                            is_pareto = False
                            break
                    if is_pareto:
                        pareto_points.append((e, c, i))
                
                if len(pareto_points) > 1:
                    pareto_points.sort(key=lambda x: x[0])  # Sort by energy
                    pareto_energy = [p[0] for p in pareto_points]
                    pareto_cost = [p[1] for p in pareto_points]
                    
                    ax.plot(pareto_energy, pareto_cost, '--', 
                           color=self.base_visualizer.chart_config.current_palette.accent,
                           linewidth=2, alpha=0.8, label='Pareto Front')
            
            # Add equal utility lines
            if energy_values and cost_values:
                min_energy, max_energy = min(energy_values), max(energy_values)
                min_cost, max_cost = min(cost_values), max(cost_values)
                
                # Draw several equal utility lines (simplified utility function)
                for utility in [0.3, 0.5, 0.7]:
                    energy_line = np.linspace(min_energy, max_energy, 100)
                    # Simplified utility function: U = 1 - energy/200 - cost/300000
                    cost_line = 300000 * (1 - utility - energy_line/200)
                    
                    # Only draw lines within valid range
                    valid_mask = (cost_line >= min_cost) & (cost_line <= max_cost)
                    if np.any(valid_mask):
                        ax.plot(energy_line[valid_mask], cost_line[valid_mask], ':', 
                               alpha=0.5, color='gray', linewidth=1)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "能耗-成本权衡分析", "Energy-Cost Trade-off Analysis")
            xlabel = self.base_visualizer.chart_config.get_bilingual_text(
                "能耗 (kWh/m²/年)", "Energy Consumption (kWh/m²/year)")
            ylabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Renovation Cost (CNY)", "Renovation Cost (CNY)")
            
            self.base_visualizer.add_title_and_labels(ax, title, xlabel, ylabel,
                                                    title, xlabel, ylabel)
            
            # 设置格式
            self.base_visualizer.customize_axis_format(ax, y_format='currency')
            
            # 添加图例
            ax.legend(fontsize=self.base_visualizer.chart_config.current_font.legend_size - 2)
            
        except Exception as e:
            self.logger.error(f"Energy-cost trade-off chart creation failed: {str(e)}")
    
    def _create_thermal_cost_tradeoff(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create thermal-cost trade-off chart"""
        try:
            # 提取并验证数据
            valid_solutions = {}
            for name, sol in solutions.items():
                if (sol.thermal_performance is not None and 
                    sol.renovation_cost is not None and
                    0 <= sol.thermal_performance <= 1 and 
                    sol.renovation_cost >= 0):
                    valid_solutions[name] = sol
            
            if not valid_solutions:
                self.logger.warning("No valid solutions for thermal-cost trade-off analysis")
                return
            
            # Extract data (convert thermal performance to comfort percentage)
            thermal_values = [(1 - sol.thermal_performance) * 100 for sol in valid_solutions.values()]
            cost_values = [sol.renovation_cost for sol in valid_solutions.values()]
            names = list(valid_solutions.keys())
            
            # 创建散点图
            colors = self.base_visualizer.chart_config.get_color_sequence(len(valid_solutions))
            
            for i, (thermal, cost, name, color) in enumerate(zip(thermal_values, cost_values, names, colors)):
                marker_size = 150 if i == 0 and self.highlight_best else 100
                ax.scatter(thermal, cost, s=marker_size, c=[color], alpha=0.7,
                          edgecolors='white', linewidth=2, label=name)
            
            # Add comfort level zones
            comfort_zones = [
                (0, 40, 'Uncomfortable', self.base_visualizer.chart_config.current_palette.error),
                (40, 60, 'Average', self.base_visualizer.chart_config.current_palette.warning),
                (60, 80, 'Comfortable', self.base_visualizer.chart_config.current_palette.success),
                (80, 100, 'Very Comfortable', self.base_visualizer.chart_config.current_palette.primary)
            ]
            
            for min_comfort, max_comfort, label, color in comfort_zones:
                ax.axvspan(min_comfort, max_comfort, alpha=0.1, color=color)
                ax.text((min_comfort + max_comfort) / 2, max(cost_values) * 0.95, label,
                       ha='center', va='center', rotation=0,
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size - 2,
                       alpha=0.7)
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "热工性能-成本权衡分析", "Thermal-Cost Trade-off Analysis")
            xlabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Thermal Comfort (%)", "Thermal Comfort (%)")
            ylabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Renovation Cost (CNY)", "Renovation Cost (CNY)")
            
            self.base_visualizer.add_title_and_labels(ax, title, xlabel, ylabel,
                                                    title, xlabel, ylabel)
            
            # 设置格式
            self.base_visualizer.customize_axis_format(ax, y_format='currency')
            
        except Exception as e:
            self.logger.error(f"Thermal-cost trade-off chart creation failed: {str(e)}")
    
    def _create_energy_thermal_tradeoff(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create energy-thermal trade-off chart"""
        try:
            # 提取并验证数据
            valid_solutions = {}
            for name, sol in solutions.items():
                if (sol.energy_consumption is not None and 
                    sol.thermal_performance is not None and
                    sol.renovation_cost is not None and
                    sol.energy_consumption > 0 and 
                    0 <= sol.thermal_performance <= 1 and 
                    sol.renovation_cost >= 0):
                    valid_solutions[name] = sol
            
            if not valid_solutions:
                self.logger.warning("No valid solutions for energy-thermal trade-off analysis")
                return
            
            # 提取数据
            energy_values = [sol.energy_consumption for sol in valid_solutions.values()]
            thermal_values = [(1 - sol.thermal_performance) * 100 for sol in valid_solutions.values()]
            cost_values = [sol.renovation_cost for sol in valid_solutions.values()]
            names = list(valid_solutions.keys())
            
            # Create scatter plot using cost as color mapping
            colors_mapped = self.base_visualizer.create_color_gradient(np.array(cost_values))
            
            scatter = ax.scatter(energy_values, thermal_values, 
                               c=cost_values, s=100, alpha=0.7,
                               cmap='viridis', edgecolors='white', linewidth=2)
            
            # Add color bar
            cbar = plt.colorbar(scatter, ax=ax)
            cbar.set_label(self.base_visualizer.chart_config.get_bilingual_text(
                "Renovation Cost (CNY)", "Renovation Cost (CNY)"),
                fontsize=self.base_visualizer.chart_config.current_font.label_size - 1)
            
            # Add ideal zone annotation
            if energy_values and thermal_values:
                # Define ideal zone (low energy consumption, high comfort)
                ideal_energy_threshold = np.percentile(energy_values, 25)  # Top 25% energy consumption
                ideal_thermal_threshold = np.percentile(thermal_values, 75)  # Top 25% comfort
                
                # Draw ideal zone
                ideal_rect = Rectangle((0, ideal_thermal_threshold), 
                                     ideal_energy_threshold, 100 - ideal_thermal_threshold,
                                     alpha=0.2, facecolor=self.base_visualizer.chart_config.current_palette.success)
                ax.add_patch(ideal_rect)
                
                ax.text(ideal_energy_threshold / 2, (ideal_thermal_threshold + 100) / 2,
                       'Ideal Zone\n理想区域',
                       ha='center', va='center', fontweight='bold',
                       fontsize=self.base_visualizer.chart_config.current_font.annotation_size,
                       bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
            
            # Set title and labels
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "能耗-热工性能权衡分析", "Energy-Thermal Trade-off Analysis")
            xlabel = self.base_visualizer.chart_config.get_bilingual_text(
                "能耗 (kWh/m²/年)", "Energy Consumption (kWh/m²/year)")
            ylabel = self.base_visualizer.chart_config.get_bilingual_text(
                "Thermal Comfort (%)", "Thermal Comfort (%)")
            
            self.base_visualizer.add_title_and_labels(ax, title, xlabel, ylabel,
                                                    title, xlabel, ylabel)
            
        except Exception as e:
            self.logger.error(f"Energy-thermal trade-off chart creation failed: {str(e)}")
    
    def _create_3d_tradeoff_analysis(self, ax, solutions: Dict[str, ObjectiveResults]):
        """Create 3D trade-off analysis chart"""
        try:
            # 提取并验证数据
            valid_solutions = {}
            for name, sol in solutions.items():
                if (sol.energy_consumption is not None and 
                    sol.thermal_performance is not None and
                    sol.renovation_cost is not None and
                    sol.energy_consumption > 0 and 
                    0 <= sol.thermal_performance <= 1 and 
                    sol.renovation_cost >= 0):
                    valid_solutions[name] = sol
            
            if not valid_solutions:
                self.logger.warning("No valid solutions for 3D trade-off analysis")
                return
            
            # Convert to 3D chart
            ax.remove()
            ax = plt.subplot(2, 2, 4, projection='3d')
            
            # 提取数据并确保正确的维度
            energy_values = []
            thermal_values = []
            cost_values = []
            names = []

            for name, sol in valid_solutions.items():
                # 确保数据是标量值
                energy = float(sol.energy_consumption) if sol.energy_consumption is not None else 100.0
                thermal = float(sol.thermal_performance) if sol.thermal_performance is not None else 0.5
                cost = float(sol.renovation_cost) if sol.renovation_cost is not None else 10000.0

                # 转换为合适的显示单位
                energy_values.append(energy)
                thermal_values.append((1 - thermal) * 100)  # 转换为舒适度百分比
                cost_values.append(cost / 1000)  # 转换为千元
                names.append(name)

            # 验证数据维度
            if len(energy_values) == 0 or len(thermal_values) == 0 or len(cost_values) == 0:
                self.logger.warning("权衡分析数据为空")
                return
            
            # Create 3D scatter plot - 修复数据维度问题
            colors = self.base_visualizer.chart_config.get_color_sequence(len(names))

            # 确保所有数据都是标量
            for i, (energy, thermal, cost, name) in enumerate(zip(energy_values, thermal_values, cost_values, names)):
                color = colors[i % len(colors)]
                marker_size = 150 if i == 0 and self.highlight_best else 100

                # 确保传递标量值而不是数组
                ax.scatter(energy, thermal, cost, s=marker_size, c=color,
                          alpha=0.7, edgecolors='white', linewidth=2, label=name)
            
            # Add ideal point - 修复数据维度问题
            if energy_values and thermal_values and cost_values:
                ideal_energy = min(energy_values)
                ideal_thermal = max(thermal_values)  # 舒适度越高越好
                ideal_cost = min(cost_values)

                # 确保传递标量值
                ax.scatter(ideal_energy, ideal_thermal, ideal_cost,
                          s=200, c='red', marker='*', alpha=0.9,
                          edgecolors='black', linewidth=2, label='理想点 / Ideal Point')
            
            # Set axis labels
            ax.set_xlabel(self.base_visualizer.chart_config.get_bilingual_text(
                "能耗 (kWh/m²/年)", "Energy (kWh/m²/year)"))
            ax.set_ylabel(self.base_visualizer.chart_config.get_bilingual_text(
                "Thermal Comfort (%)", "Thermal Comfort (%)"))
            ax.set_zlabel(self.base_visualizer.chart_config.get_bilingual_text(
                "Cost (k Yuan)", "Cost (k CNY)"))
            
            # 设置标题
            title = self.base_visualizer.chart_config.get_bilingual_text(
                "三维目标权衡分析", "3D Multi-objective Trade-off")
            ax.set_title(title, fontsize=self.base_visualizer.chart_config.current_font.title_size - 1,
                        fontweight='bold', pad=20)
            
            # 添加图例
            ax.legend(loc='center left', bbox_to_anchor=(1.05, 0.5),
                     fontsize=self.base_visualizer.chart_config.current_font.legend_size - 2)
            
        except Exception as e:
            self.logger.error(f"3D trade-off analysis chart creation failed: {str(e)}")


def create_comparison_charts() -> ComparisonCharts:
    """
    创建Data Comparison Visualization Charts实例
    
    Returns:
        配置好的Data Comparison Visualization Charts
    """
    return ComparisonCharts()