"""
EPW文件解析器 - 重构版本
基于现有epw_file_parser模块，增强功能和错误处理
"""

import csv
import os
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime, timedelta
import numpy as np

from ..core.config import get_config
from ..core.logging_config import get_logger, LogContext
from ..core.exceptions import EPWParsingError, ClimateDataError, handle_exception
from ..core.data_structures import HourlyClimateData
from ..core.utils import ValidationUtils, FileUtils


class EPWFileParser:
    """
    EPW文件解析器
    
    功能：
    1. 解析标准EPW格式气候数据文件
    2. 提取8760小时的完整气候数据
    3. 数据验证和质量检查
    4. 支持多种编码格式
    5. 提供详细的解析统计信息
    """
    
    def __init__(self):
        """初始化EPW文件解析器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 获取配置参数
        climate_config = self.config.get_section('climate_processing')
        self.encoding = climate_config.get('epw_encoding', 'utf-8')
        self.solar_threshold = climate_config.get('solar_radiation_threshold', 50.0)
        self.wind_threshold = climate_config.get('wind_speed_threshold', 20.0)
        
        # EPW文件格式定义
        self.epw_header_lines = 8  # EPW文件前8行为头部信息
        self.expected_data_lines = 8760  # 一年8760小时
        
        # 数据字段映射（EPW标准格式）
        self.data_field_mapping = {
            'year': 0,
            'month': 1,
            'day': 2,
            'hour': 3,
            'minute': 4,
            'dry_bulb_temperature': 6,
            'dew_point_temperature': 7,
            'relative_humidity': 8,
            'atmospheric_pressure': 9,
            'extraterrestrial_horizontal_radiation': 10,
            'extraterrestrial_direct_normal_radiation': 11,
            'horizontal_infrared_radiation': 12,
            'global_horizontal_radiation': 13,
            'direct_normal_radiation': 14,
            'diffuse_horizontal_radiation': 15,
            'global_horizontal_illuminance': 16,
            'direct_normal_illuminance': 17,
            'diffuse_horizontal_illuminance': 18,
            'zenith_luminance': 19,
            'wind_direction': 20,
            'wind_speed': 21,
            'total_sky_cover': 22,
            'opaque_sky_cover': 23,
            'visibility': 24,
            'ceiling_height': 25,
            'present_weather_observation': 26,
            'present_weather_codes': 27,
            'precipitable_water': 28,
            'aerosol_optical_depth': 29,
            'snow_depth': 30,
            'days_since_last_snowfall': 31,
            'albedo': 32,
            'liquid_precipitation_depth': 33,
            'liquid_precipitation_quantity': 34
        }
        
        self.logger.info("EPW文件解析器初始化完成")
    
    @handle_exception
    def parse_epw_file(self, epw_file_path: str) -> Dict[str, Any]:
        """
        解析EPW文件的主要接口
        
        Args:
            epw_file_path: EPW文件路径
            
        Returns:
            包含气候数据和元信息的完整字典
            
        Raises:
            EPWParsingError: 解析失败时抛出
        """
        with LogContext("EPW文件解析", self.logger):
            # 验证输入
            self._validate_epw_file(epw_file_path)
            
            try:
                # 解析文件头部信息
                header_info = self._parse_header_info(epw_file_path)
                self.logger.info(f"解析EPW头部: 位置 {header_info.get('location', 'Unknown')}")
                
                # 解析气候数据
                hourly_data = self._parse_climate_data(epw_file_path)
                self.logger.info(f"解析完成: {len(hourly_data)} 小时气候数据")
                
                # 数据质量检查
                quality_report = self._perform_quality_check(hourly_data)
                
                # 计算统计信息
                statistics = self._calculate_climate_statistics(hourly_data)
                
                # 整合解析结果
                parsing_result = {
                    'header_info': header_info,
                    'hourly_data': hourly_data,
                    'quality_report': quality_report,
                    'statistics': statistics,
                    'parsing_metadata': {
                        'file_path': epw_file_path,
                        'parsing_timestamp': datetime.now().isoformat(),
                        'data_count': len(hourly_data),
                        'encoding_used': self.encoding
                    }
                }
                
                self.logger.info(f"EPW文件解析成功: {len(hourly_data)} 条数据记录")
                return parsing_result
                
            except Exception as e:
                raise EPWParsingError(f"EPW文件解析失败: {str(e)}") from e
    
    def _validate_epw_file(self, epw_file_path: str) -> None:
        """验证EPW文件"""
        if not ValidationUtils.validate_file_exists(epw_file_path):
            raise EPWParsingError(f"EPW文件不存在: {epw_file_path}")
        
        # 检查文件扩展名
        if not epw_file_path.lower().endswith('.epw'):
            self.logger.warning(f"文件扩展名不是.epw: {epw_file_path}")
        
        # 检查文件大小
        file_size = FileUtils.get_file_size(epw_file_path)
        if file_size < 100000:  # 小于100KB可能不是完整的EPW文件
            self.logger.warning(f"EPW文件大小异常: {file_size} bytes")
    
    def _parse_header_info(self, epw_file_path: str) -> Dict[str, Any]:
        """解析EPW文件头部信息"""
        header_info = {
            'location': {},
            'design_conditions': {},
            'typical_periods': {},
            'ground_temperatures': {},
            'holidays': [],
            'comments': []
        }
        
        try:
            # 尝试多种编码方式读取头部
            encodings_to_try = [self.encoding, 'utf-8', 'gbk', 'latin-1']
            lines = None
            
            for encoding in encodings_to_try:
                try:
                    with open(epw_file_path, 'r', encoding=encoding) as f:
                        lines = [f.readline().strip() for _ in range(self.epw_header_lines)]
                        break
                except UnicodeDecodeError:
                    continue
            
            if lines is None:
                self.logger.warning("無法读取EPW头部信息，使用默认值")
                return header_info
            
            # 解析位置信息（第1行）
            if lines[0].startswith('LOCATION'):
                location_parts = lines[0].split(',')
                if len(location_parts) >= 10:
                    header_info['location'] = {
                        'city': location_parts[1].strip(),
                        'state_province': location_parts[2].strip(),
                        'country': location_parts[3].strip(),
                        'source': location_parts[4].strip(),
                        'wmo_number': location_parts[5].strip(),
                        'latitude': float(location_parts[6]) if location_parts[6].strip() else 0.0,
                        'longitude': float(location_parts[7]) if location_parts[7].strip() else 0.0,
                        'time_zone': float(location_parts[8]) if location_parts[8].strip() else 0.0,
                        'elevation': float(location_parts[9]) if location_parts[9].strip() else 0.0
                    }
            
            # 解析设计条件（第2行）
            if len(lines) > 1 and lines[1].startswith('DESIGN CONDITIONS'):
                header_info['design_conditions']['raw'] = lines[1]
            
            # 解析典型周期（第3行）
            if len(lines) > 2 and lines[2].startswith('TYPICAL/EXTREME PERIODS'):
                header_info['typical_periods']['raw'] = lines[2]
            
            # 解析地面温度（第4行）
            if len(lines) > 3 and lines[3].startswith('GROUND TEMPERATURES'):
                header_info['ground_temperatures']['raw'] = lines[3]
            
            # 解析假期（第5行）
            if len(lines) > 4 and lines[4].startswith('HOLIDAYS/DAYLIGHT SAVINGS'):
                header_info['holidays'] = lines[4].split(',')[1:]
            
            # 解析注释（第6-8行）
            for i in range(5, min(8, len(lines))):
                if lines[i].startswith('COMMENTS'):
                    header_info['comments'].append(lines[i])
            
            return header_info
            
        except Exception as e:
            self.logger.error(f"解析EPW头部信息失败: {str(e)}")
            return header_info
    
    def _parse_climate_data(self, epw_file_path: str) -> List[HourlyClimateData]:
        """解析气候数据"""
        hourly_data = []
        
        try:
            # 尝试多种编码方式
            encodings_to_try = [self.encoding, 'utf-8', 'gbk', 'latin-1']
            file_content = None
            used_encoding = None
            
            for encoding in encodings_to_try:
                try:
                    with open(epw_file_path, 'r', encoding=encoding) as f:
                        file_content = f.readlines()
                        used_encoding = encoding
                        self.logger.info(f"成功使用编码 {encoding} 读取EPW文件")
                        break
                except UnicodeDecodeError:
                    continue
            
            if file_content is None:
                raise EPWParsingError("无法使用任何编码读取EPW文件")
            
            # 跳过头部行
            data_lines = file_content[self.epw_header_lines:]
            
            for hour_index, line in enumerate(data_lines):
                if not line.strip():  # 跳过空行
                    continue
                    
                try:
                    # 使用csv模块解析单行
                    import io
                    csv_reader = csv.reader(io.StringIO(line))
                    row = next(csv_reader)
                    
                    if len(row) < 35:  # EPW标准格式至少35个字段
                        self.logger.warning(f"第{hour_index + 1}行数据字段不足: {len(row)} < 35")
                        continue
                    
                    # 解析单行数据
                    climate_data = self._parse_single_data_row(row, hour_index)
                    hourly_data.append(climate_data)
                    
                except Exception as e:
                    self.logger.warning(f"解析第{hour_index + 1}行数据失败: {str(e)}")
                    continue
            
            self.logger.info(f"EPW数据解析完成: {len(hourly_data)} 小时数据")
            return hourly_data
                
        except Exception as e:
            raise EPWParsingError(f"读取气候数据失败: {str(e)}") from e
    
    def _parse_single_data_row(self, row: List[str], hour_index: int) -> HourlyClimateData:
        """解析单行气候数据"""
        try:
            # 安全的数值转换函数
            def safe_float(value: str, default: float = 0.0) -> float:
                try:
                    return float(value) if value.strip() != '' else default
                except ValueError:
                    return default
            
            def safe_int(value: str, default: int = 0) -> int:
                try:
                    return int(float(value)) if value.strip() != '' else default
                except ValueError:
                    return default
            
            # 提取基础时间信息
            year = safe_int(row[self.data_field_mapping['year']], 2023)
            month = safe_int(row[self.data_field_mapping['month']], 1)
            day = safe_int(row[self.data_field_mapping['day']], 1)
            hour = safe_int(row[self.data_field_mapping['hour']], 1)
            
            # 创建HourlyClimateData对象
            climate_data = HourlyClimateData(
                hour=hour_index,
                month=month,
                day=day,
                time=hour,
                dry_bulb_temperature=safe_float(row[self.data_field_mapping['dry_bulb_temperature']]),
                dew_point_temperature=safe_float(row[self.data_field_mapping['dew_point_temperature']]),
                relative_humidity=safe_float(row[self.data_field_mapping['relative_humidity']]),
                atmospheric_pressure=safe_float(row[self.data_field_mapping['atmospheric_pressure']]),
                wind_speed=safe_float(row[self.data_field_mapping['wind_speed']]),
                wind_direction=safe_float(row[self.data_field_mapping['wind_direction']]),
                direct_normal_irradiance=safe_float(row[self.data_field_mapping['direct_normal_radiation']]),
                diffuse_horizontal_irradiance=safe_float(row[self.data_field_mapping['diffuse_horizontal_radiation']]),
                global_horizontal_irradiance=safe_float(row[self.data_field_mapping['global_horizontal_radiation']]),
                sky_cover=safe_float(row[self.data_field_mapping['total_sky_cover']])
            )
            
            return climate_data
            
        except Exception as e:
            raise EPWParsingError(f"解析数据行失败 (行{hour_index + 1}): {str(e)}") from e
    
    def _perform_quality_check(self, hourly_data: List[HourlyClimateData]) -> Dict[str, Any]:
        """执行数据质量检查"""
        quality_report = {
            'data_completeness': 0.0,
            'missing_data_count': 0,
            'outlier_count': 0,
            'quality_issues': [],
            'recommendations': []
        }
        
        try:
            expected_count = self.expected_data_lines
            actual_count = len(hourly_data)
            
            # 数据完整性检查
            quality_report['data_completeness'] = actual_count / expected_count
            quality_report['missing_data_count'] = max(0, expected_count - actual_count)
            
            if quality_report['data_completeness'] < 1.0:
                quality_report['quality_issues'].append(
                    f"数据不完整: {actual_count}/{expected_count} ({quality_report['data_completeness']:.1%})"
                )
                quality_report['recommendations'].append("建议检查EPW文件是否完整")
            
            # 异常值检测
            outliers = self._detect_outliers(hourly_data)
            quality_report['outlier_count'] = len(outliers)
            
            if outliers:
                quality_report['quality_issues'].extend([
                    f"检测到异常值: {outlier}" for outlier in outliers[:5]  # 只显示前5个
                ])
                if len(outliers) > 5:
                    quality_report['quality_issues'].append(f"... 还有 {len(outliers) - 5} 个异常值")
                
                quality_report['recommendations'].append("建议检查气候数据的合理性")
            
            # 数据连续性检查
            continuity_issues = self._check_data_continuity(hourly_data)
            if continuity_issues:
                quality_report['quality_issues'].extend(continuity_issues)
                quality_report['recommendations'].append("建议检查时间序列的连续性")
            
            self.logger.info(f"数据质量检查完成: {quality_report['data_completeness']:.1%} 完整性, "
                           f"{quality_report['outlier_count']} 个异常值")
            
            return quality_report
            
        except Exception as e:
            self.logger.error(f"数据质量检查失败: {str(e)}")
            return quality_report
    
    def _detect_outliers(self, hourly_data: List[HourlyClimateData]) -> List[str]:
        """检测异常值"""
        outliers = []
        
        for i, data in enumerate(hourly_data):
            # 温度异常检查
            if data.dry_bulb_temperature < -50 or data.dry_bulb_temperature > 60:
                outliers.append(f"第{i+1}小时: 干球温度异常 {data.dry_bulb_temperature}°C")
            
            # 湿度异常检查
            if data.relative_humidity < 0 or data.relative_humidity > 100:
                outliers.append(f"第{i+1}小时: 相对湿度异常 {data.relative_humidity}%")
            
            # 风速异常检查
            if data.wind_speed > self.wind_threshold:
                outliers.append(f"第{i+1}小时: 风速异常 {data.wind_speed} m/s")
            
            # 辐射异常检查
            if data.global_horizontal_irradiance > 1500:  # W/m²
                outliers.append(f"第{i+1}小时: 总辐射异常 {data.global_horizontal_irradiance} W/m²")
            
            # 气压异常检查
            if data.atmospheric_pressure < 80000 or data.atmospheric_pressure > 110000:  # Pa
                outliers.append(f"第{i+1}小时: 气压异常 {data.atmospheric_pressure} Pa")
        
        return outliers
    
    def _check_data_continuity(self, hourly_data: List[HourlyClimateData]) -> List[str]:
        """检查数据连续性"""
        continuity_issues = []
        
        if len(hourly_data) < 2:
            return continuity_issues
        
        # 检查月份连续性
        months = [data.month for data in hourly_data]
        month_changes = []
        
        current_month = months[0]
        for i, month in enumerate(months[1:], 1):
            if month != current_month:
                month_changes.append(i)
                current_month = month
        
        # 应该有11次月份变化（12个月）
        if len(month_changes) < 11:
            continuity_issues.append(f"月份变化次数异常: {len(month_changes)} < 11")
        
        # 检查小时跳跃
        hours = [data.time for data in hourly_data[:100]]  # 检查前100小时
        for i in range(1, len(hours)):
            if hours[i] != hours[i-1] + 1 and not (hours[i-1] == 24 and hours[i] == 1):
                continuity_issues.append(f"第{i}小时: 时间跳跃 {hours[i-1]} -> {hours[i]}")
                break
        
        return continuity_issues
    
    def _calculate_climate_statistics(self, hourly_data: List[HourlyClimateData]) -> Dict[str, Any]:
        """计算气候统计信息"""
        if not hourly_data:
            return {}
        
        try:
            # 提取各种数据数组
            temperatures = [data.dry_bulb_temperature for data in hourly_data]
            humidity = [data.relative_humidity for data in hourly_data]
            wind_speeds = [data.wind_speed for data in hourly_data]
            solar_radiation = [data.global_horizontal_irradiance for data in hourly_data]
            
            statistics = {
                'temperature': {
                    'min': min(temperatures),
                    'max': max(temperatures),
                    'mean': np.mean(temperatures),
                    'std': np.std(temperatures)
                },
                'humidity': {
                    'min': min(humidity),
                    'max': max(humidity),
                    'mean': np.mean(humidity),
                    'std': np.std(humidity)
                },
                'wind_speed': {
                    'min': min(wind_speeds),
                    'max': max(wind_speeds),
                    'mean': np.mean(wind_speeds),
                    'std': np.std(wind_speeds)
                },
                'solar_radiation': {
                    'min': min(solar_radiation),
                    'max': max(solar_radiation),
                    'mean': np.mean(solar_radiation),
                    'std': np.std(solar_radiation),
                    'annual_total': sum(solar_radiation)
                }
            }
            
            # 季节性统计
            statistics['seasonal'] = self._calculate_seasonal_statistics(hourly_data)
            
            # 极值统计
            statistics['extremes'] = self._calculate_extreme_statistics(hourly_data)
            
            return statistics
            
        except Exception as e:
            self.logger.error(f"计算气候统计失败: {str(e)}")
            return {}
    
    def _calculate_seasonal_statistics(self, hourly_data: List[HourlyClimateData]) -> Dict[str, Any]:
        """计算季节性统计"""
        seasons = {
            'spring': [3, 4, 5],    # 春季
            'summer': [6, 7, 8],    # 夏季
            'autumn': [9, 10, 11],  # 秋季
            'winter': [12, 1, 2]    # 冬季
        }
        
        seasonal_stats = {}
        
        for season_name, months in seasons.items():
            season_data = [data for data in hourly_data if data.month in months]
            
            if season_data:
                temperatures = [data.dry_bulb_temperature for data in season_data]
                radiation = [data.global_horizontal_irradiance for data in season_data]
                
                seasonal_stats[season_name] = {
                    'temperature_mean': np.mean(temperatures),
                    'temperature_min': min(temperatures),
                    'temperature_max': max(temperatures),
                    'radiation_mean': np.mean(radiation),
                    'radiation_total': sum(radiation),
                    'hours_count': len(season_data)
                }
        
        return seasonal_stats
    
    def _calculate_extreme_statistics(self, hourly_data: List[HourlyClimateData]) -> Dict[str, Any]:
        """计算极值统计"""
        temperatures = [data.dry_bulb_temperature for data in hourly_data]
        wind_speeds = [data.wind_speed for data in hourly_data]
        radiation = [data.global_horizontal_irradiance for data in hourly_data]
        
        # 找到极值及其发生时间
        min_temp_idx = temperatures.index(min(temperatures))
        max_temp_idx = temperatures.index(max(temperatures))
        max_wind_idx = wind_speeds.index(max(wind_speeds))
        max_radiation_idx = radiation.index(max(radiation))
        
        return {
            'minimum_temperature': {
                'value': temperatures[min_temp_idx],
                'month': hourly_data[min_temp_idx].month,
                'day': hourly_data[min_temp_idx].day,
                'hour': hourly_data[min_temp_idx].time
            },
            'maximum_temperature': {
                'value': temperatures[max_temp_idx],
                'month': hourly_data[max_temp_idx].month,
                'day': hourly_data[max_temp_idx].day,
                'hour': hourly_data[max_temp_idx].time
            },
            'maximum_wind_speed': {
                'value': wind_speeds[max_wind_idx],
                'month': hourly_data[max_wind_idx].month,
                'day': hourly_data[max_wind_idx].day,
                'hour': hourly_data[max_wind_idx].time
            },
            'maximum_solar_radiation': {
                'value': radiation[max_radiation_idx],
                'month': hourly_data[max_radiation_idx].month,
                'day': hourly_data[max_radiation_idx].day,
                'hour': hourly_data[max_radiation_idx].time
            }
        }
    
    def extract_climate_parameters(self, parsing_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        从解析结果中提取关键气候参数
        
        Args:
            parsing_result: EPW解析结果
            
        Returns:
            关键气候参数字典
        """
        hourly_data = parsing_result.get('hourly_data', [])
        if not hourly_data:
            return {}
        
        try:
            # 提取关键参数
            climate_parameters = {
                'location_info': parsing_result.get('header_info', {}).get('location', {}),
                'annual_statistics': parsing_result.get('statistics', {}),
                'heating_degree_days': self._calculate_heating_degree_days(hourly_data),
                'cooling_degree_days': self._calculate_cooling_degree_days(hourly_data),
                'solar_availability': self._calculate_solar_availability(hourly_data),
                'wind_patterns': self._analyze_wind_patterns(hourly_data),
                'design_conditions': self._extract_design_conditions(hourly_data)
            }
            
            return climate_parameters
            
        except Exception as e:
            self.logger.error(f"提取气候参数失败: {str(e)}")
            return {}
    
    def _calculate_heating_degree_days(self, hourly_data: List[HourlyClimateData], 
                                     base_temp: float = 18.0) -> float:
        """计算供暖度日数"""
        daily_temps = {}
        
        # 按日分组计算日平均温度
        for data in hourly_data:
            day_key = (data.month, data.day)
            if day_key not in daily_temps:
                daily_temps[day_key] = []
            daily_temps[day_key].append(data.dry_bulb_temperature)
        
        # 计算供暖度日数
        hdd = 0.0
        for temps in daily_temps.values():
            daily_avg = np.mean(temps)
            if daily_avg < base_temp:
                hdd += base_temp - daily_avg
        
        return hdd
    
    def _calculate_cooling_degree_days(self, hourly_data: List[HourlyClimateData],
                                     base_temp: float = 26.0) -> float:
        """计算制冷度日数"""
        daily_temps = {}
        
        # 按日分组计算日平均温度
        for data in hourly_data:
            day_key = (data.month, data.day)
            if day_key not in daily_temps:
                daily_temps[day_key] = []
            daily_temps[day_key].append(data.dry_bulb_temperature)
        
        # 计算制冷度日数
        cdd = 0.0
        for temps in daily_temps.values():
            daily_avg = np.mean(temps)
            if daily_avg > base_temp:
                cdd += daily_avg - base_temp
        
        return cdd
    
    def _calculate_solar_availability(self, hourly_data: List[HourlyClimateData]) -> Dict[str, float]:
        """计算太阳能可用性"""
        radiation_values = [data.global_horizontal_irradiance for data in hourly_data]
        
        # 有效太阳辐射小时数（>50 W/m²）
        effective_hours = sum(1 for r in radiation_values if r > self.solar_threshold)
        
        # 高辐射小时数（>300 W/m²）
        high_radiation_hours = sum(1 for r in radiation_values if r > 300)
        
        return {
            'annual_total_radiation': sum(radiation_values),
            'effective_solar_hours': effective_hours,
            'high_radiation_hours': high_radiation_hours,
            'solar_availability_ratio': effective_hours / len(radiation_values) if radiation_values else 0
        }
    
    def _analyze_wind_patterns(self, hourly_data: List[HourlyClimateData]) -> Dict[str, Any]:
        """分析风况模式"""
        wind_directions = [data.wind_direction for data in hourly_data]
        wind_speeds = [data.wind_speed for data in hourly_data]
        
        # 主导风向分析
        direction_bins = np.histogram(wind_directions, bins=16, range=(0, 360))[0]
        dominant_direction_index = np.argmax(direction_bins)
        dominant_direction = dominant_direction_index * 22.5  # 每个扇区22.5度
        
        return {
            'dominant_wind_direction': dominant_direction,
            'average_wind_speed': np.mean(wind_speeds),
            'maximum_wind_speed': max(wind_speeds),
            'calm_hours': sum(1 for speed in wind_speeds if speed < 1.0),
            'wind_speed_distribution': {
                'low_wind_hours': sum(1 for speed in wind_speeds if 0 <= speed < 3),
                'moderate_wind_hours': sum(1 for speed in wind_speeds if 3 <= speed < 8),
                'high_wind_hours': sum(1 for speed in wind_speeds if speed >= 8)
            }
        }
    
    def _extract_design_conditions(self, hourly_data: List[HourlyClimateData]) -> Dict[str, float]:
        """提取设计工况参数"""
        temperatures = [data.dry_bulb_temperature for data in hourly_data]
        humidity = [data.relative_humidity for data in hourly_data]
        
        # 排序以找到设计温度
        sorted_temps = sorted(temperatures)
        
        return {
            'winter_design_temperature': sorted_temps[int(len(sorted_temps) * 0.04)],  # 4%设计温度
            'summer_design_temperature': sorted_temps[int(len(sorted_temps) * 0.96)],  # 96%设计温度
            'average_temperature': np.mean(temperatures),
            'average_humidity': np.mean(humidity),
            'temperature_range': max(temperatures) - min(temperatures)
        }


def create_epw_file_parser() -> EPWFileParser:
    """
    创建EPW文件解析器实例
    
    Returns:
        配置好的解析器实例
    """
    return EPWFileParser()