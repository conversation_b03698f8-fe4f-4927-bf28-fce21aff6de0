"""
日志配置系统
提供统一的日志管理和输出
"""

import logging
import logging.handlers
import os
import sys
from typing import Optional, Dict, Any
from contextlib import contextmanager
from pathlib import Path
import threading
import time


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 定义颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        """格式化日志记录"""
        log_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset_color = self.COLORS['RESET']
        
        # 为消息添加颜色
        record.levelname = f"{log_color}{record.levelname}{reset_color}"
        record.name = f"{log_color}{record.name}{reset_color}"
        
        return super().format(record)


class PerformanceLogger:
    """性能监控日志器"""
    
    def __init__(self):
        self.logger = logging.getLogger('performance')
        self._start_times: Dict[str, float] = {}
        self._lock = threading.Lock()
    
    def start_timer(self, operation: str) -> None:
        """开始计时"""
        with self._lock:
            self._start_times[operation] = time.time()
            self.logger.debug(f"开始操作: {operation}")
    
    def end_timer(self, operation: str) -> Optional[float]:
        """结束计时并记录"""
        with self._lock:
            start_time = self._start_times.pop(operation, None)
            if start_time is None:
                self.logger.warning(f"未找到操作的开始时间: {operation}")
                return None
            
            duration = time.time() - start_time
            self.logger.info(f"操作完成: {operation}, 耗时: {duration:.3f}秒")
            return duration
    
    @contextmanager
    def time_operation(self, operation: str):
        """计时上下文管理器"""
        self.start_timer(operation)
        try:
            yield
        finally:
            self.end_timer(operation)


class LoggingManager:
    """日志管理器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化日志管理器
        
        Args:
            config: 日志配置字典
        """
        self.config = config or self._get_default_config()
        self.loggers: Dict[str, logging.Logger] = {}
        self.performance_logger = PerformanceLogger()
        self._setup_logging()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认日志配置"""
        return {
            'level': 'INFO',
            'format': '%(levelname)s - %(message)s',
            'file_output': True,
            'console_output': True,
            'log_directory': 'logs',
            'max_file_size': '10MB',
            'backup_count': 5,
            'use_colors': True
        }
    
    def _setup_logging(self) -> None:
        """设置日志系统"""
        # 创建日志目录
        log_dir = self.config.get('log_directory', 'logs')
        os.makedirs(log_dir, exist_ok=True)
        
        # 设置根日志级别
        level_name = self.config.get('level', 'INFO')
        level = getattr(logging, level_name.upper(), logging.INFO)
        
        # 设置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # 清除现有处理器
        root_logger.handlers.clear()
        
        # 创建格式化器
        formatter = logging.Formatter(
            self.config.get('format', 
                           '%(levelname)s - %(message)s')
        )
        
        colored_formatter = ColoredFormatter(
            self.config.get('format',
                           '%(levelname)s - %(message)s')
        )
        
        # 添加控制台处理器
        if self.config.get('console_output', True):
            console_handler = logging.StreamHandler(sys.stdout)
            if self.config.get('use_colors', True) and sys.stdout.isatty():
                console_handler.setFormatter(colored_formatter)
            else:
                console_handler.setFormatter(formatter)
            console_handler.setLevel(level)
            root_logger.addHandler(console_handler)
        
        # 添加文件处理器
        if self.config.get('file_output', True):
            # 主日志文件
            main_log_file = os.path.join(log_dir, 'main.log')
            file_handler = logging.handlers.RotatingFileHandler(
                main_log_file,
                maxBytes=self._parse_size(self.config.get('max_file_size', '10MB')),
                backupCount=self.config.get('backup_count', 5),
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.setLevel(level)
            root_logger.addHandler(file_handler)
            
            # 错误日志文件
            error_log_file = os.path.join(log_dir, 'error.log')
            error_handler = logging.handlers.RotatingFileHandler(
                error_log_file,
                maxBytes=self._parse_size(self.config.get('max_file_size', '10MB')),
                backupCount=self.config.get('backup_count', 5),
                encoding='utf-8'
            )
            error_handler.setFormatter(formatter)
            error_handler.setLevel(logging.ERROR)
            root_logger.addHandler(error_handler)
            
            # 性能日志文件
            performance_log_file = os.path.join(log_dir, 'performance.log')
            performance_handler = logging.handlers.RotatingFileHandler(
                performance_log_file,
                maxBytes=self._parse_size(self.config.get('max_file_size', '10MB')),
                backupCount=self.config.get('backup_count', 5),
                encoding='utf-8'
            )
            performance_handler.setFormatter(formatter)
            
            # 为性能日志器添加专用处理器
            self.performance_logger.logger.addHandler(performance_handler)
            self.performance_logger.logger.setLevel(logging.DEBUG)
            # 防止性能日志传播到根日志器
            self.performance_logger.logger.propagate = False
            
            # 优化算法专用日志文件
            optimization_log_file = os.path.join(log_dir, 'optimization.log')
            optimization_handler = logging.handlers.RotatingFileHandler(
                optimization_log_file,
                maxBytes=self._parse_size(self.config.get('max_file_size', '10MB')),
                backupCount=self.config.get('backup_count', 5),
                encoding='utf-8'
            )
            optimization_handler.setFormatter(formatter)
            
            # 创建优化专用日志器
            self.optimization_logger = logging.getLogger('optimization')
            self.optimization_logger.addHandler(optimization_handler)
            self.optimization_logger.setLevel(logging.DEBUG)
            # 防止优化日志传播到根日志器
            self.optimization_logger.propagate = False
    
    def _parse_size(self, size_str: str) -> int:
        """解析文件大小字符串"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """
        获取命名日志器
        
        Args:
            name: 日志器名称
            
        Returns:
            日志器实例
        """
        if name not in self.loggers:
            logger = logging.getLogger(name)
            self.loggers[name] = logger
        
        return self.loggers[name]
    
    def get_performance_logger(self) -> PerformanceLogger:
        """获取性能日志器"""
        return self.performance_logger
    
    def set_log_level(self, level: str) -> None:
        """
        设置日志级别
        
        Args:
            level: 日志级别字符串
        """
        level_obj = getattr(logging, level.upper(), logging.INFO)
        logging.getLogger().setLevel(level_obj)
        
        # 更新所有处理器的级别
        for handler in logging.getLogger().handlers:
            if not isinstance(handler, logging.handlers.RotatingFileHandler) or \
               'error.log' not in handler.baseFilename:
                handler.setLevel(level_obj)
    
    def create_module_logger(self, module_name: str, 
                           level: Optional[str] = None) -> logging.Logger:
        """
        为模块创建专用日志器
        
        Args:
            module_name: 模块名称
            level: 日志级别，如果为None则使用全局级别
            
        Returns:
            模块日志器
        """
        logger = self.get_logger(module_name)
        
        if level:
            level_obj = getattr(logging, level.upper(), logging.INFO)
            logger.setLevel(level_obj)
        
        return logger
    
    def log_system_info(self) -> None:
        """记录系统信息"""
        logger = self.get_logger('system')
        
        import sys
        import platform
        
        logger.info("=== 系统信息 ===")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"平台: {platform.platform()}")
        logger.info(f"处理器: {platform.processor()}")
        logger.info(f"架构: {platform.architecture()}")
        
        try:
            import psutil
            logger.info(f"CPU核心数: {psutil.cpu_count()}")
            logger.info(f"内存总量: {psutil.virtual_memory().total / (1024**3):.2f} GB")
        except ImportError:
            logger.debug("psutil未安装，跳过详细系统信息")
        
        logger.info("=== 日志系统初始化完成 ===")


@contextmanager
def LogContext(operation: str, logger: logging.Logger, level: str = 'DEBUG'):
    """
    日志上下文管理器
    
    Args:
        operation: 操作名称
        logger: 日志器
        level: 日志级别
    """
    # 简化日志输出，移除详细的时间戳和模块名
    log_method = getattr(logger, level.lower())
    
    # 为目标函数评估使用更简洁的格式
    if "目标函数评估" in operation:
        short_op = operation.split(" - ")[-1]  # 只保留最后的标识符
        log_method(f"开始: {short_op}")
    else:
        log_method(f"开始: {operation}")
    
    start_time = time.time()
    try:
        yield
        duration = time.time() - start_time
        
        if "目标函数评估" in operation:
            short_op = operation.split(" - ")[-1]
            log_method(f"完成: {short_op} ({duration:.3f}s)")
        else:
            log_method(f"完成: {operation} (耗时: {duration:.3f}秒)")
    except Exception as e:
        duration = time.time() - start_time
        if "目标函数评估" in operation:
            short_op = operation.split(" - ")[-1]
            logger.error(f"失败: {short_op} ({duration:.3f}s) - {str(e)}")
        else:
            logger.error(f"失败: {operation} (耗时: {duration:.3f}秒) - 错误: {str(e)}")
        raise


# 全局日志管理器实例
_global_logging_manager: Optional[LoggingManager] = None


def setup_logging(config: Optional[Dict[str, Any]] = None) -> LoggingManager:
    """
    设置全局日志系统
    
    Args:
        config: 日志配置
        
    Returns:
        日志管理器实例
    """
    global _global_logging_manager
    _global_logging_manager = LoggingManager(config)
    _global_logging_manager.log_system_info()
    return _global_logging_manager


def get_logger(name: str) -> logging.Logger:
    """
    获取日志器
    
    Args:
        name: 日志器名称
        
    Returns:
        日志器实例
    """
    global _global_logging_manager
    if _global_logging_manager is None:
        _global_logging_manager = LoggingManager()
    
    return _global_logging_manager.get_logger(name)


def get_performance_logger() -> PerformanceLogger:
    """获取性能日志器"""
    global _global_logging_manager
    if _global_logging_manager is None:
        _global_logging_manager = LoggingManager()
    
    return _global_logging_manager.get_performance_logger()


def get_optimization_logger() -> logging.Logger:
    """获取优化算法专用日志器"""
    global _global_logging_manager
    if _global_logging_manager is None:
        _global_logging_manager = LoggingManager()
    
    return _global_logging_manager.optimization_logger


def set_log_level(level: str) -> None:
    """设置全局日志级别"""
    global _global_logging_manager
    if _global_logging_manager is None:
        _global_logging_manager = LoggingManager()
    
    _global_logging_manager.set_log_level(level)


# 便利函数
def log_info(message: str, logger_name: str = 'main') -> None:
    """记录信息日志"""
    get_logger(logger_name).info(message)


def log_warning(message: str, logger_name: str = 'main') -> None:
    """记录警告日志"""
    get_logger(logger_name).warning(message)


def log_error(message: str, logger_name: str = 'main') -> None:
    """记录错误日志"""
    get_logger(logger_name).error(message)


def log_debug(message: str, logger_name: str = 'main') -> None:
    """记录调试日志"""
    get_logger(logger_name).debug(message)