"""
建筑立面优化系统 v3.0

一个基于YOLO识别的建筑立面色块图进行优化的智能系统，
通过NSGA-III遗传算法结合EPW气候数据和建筑朝向信息，
实现建筑立面的多目标优化设计。

主要功能：
1. 立面数据提取与处理
2. 气候数据处理与朝向调整
3. NSGA-III多目标遗传算法优化
4. 性能评估（能耗、热工性能、成本）
5. 四维度解决方案选择
6. 11类专业可视化图表生成
7. 交互式主程序界面

作者: AI Assistant
版本: 3.0.0
"""

__version__ = "3.0.0"
__author__ = "AI Assistant"
__email__ = "<EMAIL>"
__description__ = "建筑立面智能优化系统"

# 导入核心模块
from .core.config import ConfigManager, config_manager, setup_config, get_config
from .core.data_structures import (
    # 枚举类型
    ElementType, RenovationMode, Orientation,
    
    # 数据结构
    BuildingElement, WindowElement, WallElement, FacadeElements,
    HourlyClimateData, OrientedSolarData, OrientedWindData, OrientedClimateData,
    FacadeIndividual, ConvergenceHistory, BestSolutions, OptimizationResults,
    EnergyBreakdown, ThermalPerformanceMetrics, CostBreakdown, VisualizationData,
    
    # 工厂函数
    create_empty_facade_elements, create_facade_individual, parse_orientation
)
from .core.exceptions import (
    # 基础异常
    FacadeOptimizationError, ConfigurationError, DataValidationError,
    
    # 模块异常
    FacadeExtractionError, YOLOSegmentationError, ImageProcessingError,
    ClimateDataError, EPWParsingError, OrientationAdjustmentError,
    OptimizationError, GeneticAlgorithmError, ObjectiveFunctionError,
    PerformanceEvaluationError, EnergyCalculationError, ThermalAnalysisError,
    SolutionSelectionError, ParetoFrontError, SolutionRankingError,
    VisualizationError, ChartGenerationError, RenderingError,
    
    # 工具函数
    handle_exception, validate_not_none, validate_positive, validate_range,
    validate_file_exists, validate_directory_exists, ErrorContext
)
from .core.logging_config import (
    setup_logging, get_logger, get_performance_logger, get_optimization_logger, set_log_level,
    LogContext, PerformanceLogger, LoggingManager,
    log_info, log_warning, log_error, log_debug
)
from .core.utils import (
    FileUtils, DataUtils, ValidationUtils, HashUtils, TimeUtils, MathUtils, MemoryUtils,
    create_timestamped_filename, safe_divide, format_number, convert_units
)

# 模块级别的便利函数
def get_system_info():
    """获取系统信息"""
    import platform
    import sys
    
    return {
        'version': __version__,
        'python_version': sys.version,
        'platform': platform.platform(),
        'architecture': platform.architecture(),
        'processor': platform.processor()
    }


def initialize_system(config_path: str = None, log_level: str = 'INFO'):
    """
    初始化系统
    
    Args:
        config_path: 配置文件路径
        log_level: 日志级别
    
    Returns:
        系统初始化状态
    """
    try:
        # 设置日志系统
        setup_logging({'level': log_level})
        logger = get_logger('system')
        
        # 设置配置系统
        config = setup_config(config_path)
        
        # 验证配置
        if not config.validate_config():
            raise ConfigurationError("配置验证失败")
        
        # 记录系统信息
        logger.info(f"系统初始化成功 - 版本: {__version__}")
        
        return {
            'status': 'success',
            'version': __version__,
            'config_loaded': config_path is not None,
            'log_level': log_level
        }
        
    except Exception as e:
        print(f"系统初始化失败: {str(e)}")
        return {
            'status': 'failed',
            'error': str(e)
        }


# 模块导入映射
MODULES = {
    'facade_extraction': 'src.facade_extraction',
    'climate_processing': 'src.climate_processing', 
    'optimization': 'src.optimization',
    'performance': 'src.performance',
    'solution_selection': 'src.solution_selection',
    'visualization': 'src.visualization'
}


def get_module_info(module_name: str):
    """获取模块信息"""
    if module_name not in MODULES:
        raise ValueError(f"未知模块: {module_name}")
    
    try:
        module = __import__(MODULES[module_name], fromlist=[''])
        return {
            'name': module_name,
            'path': MODULES[module_name],
            'description': getattr(module, '__doc__', 'No description'),
            'version': getattr(module, '__version__', 'Unknown')
        }
    except ImportError as e:
        return {
            'name': module_name,
            'path': MODULES[module_name],
            'error': f"模块导入失败: {str(e)}"
        }


def check_dependencies():
    """检查依赖项"""
    required_packages = [
        'numpy', 'scipy', 'pandas', 'matplotlib', 'seaborn',
        'opencv-python', 'scikit-learn', 'pymoo', 'pyyaml'
    ]
    
    missing_packages = []
    available_packages = []
    
    for package in required_packages:
        try:
            if package == 'opencv-python':
                import cv2
                available_packages.append(f"{package} ({cv2.__version__})")
            elif package == 'scikit-learn':
                import sklearn
                version = getattr(sklearn, '__version__', 'Unknown')
                available_packages.append(f"{package} ({version})")
            elif package == 'pyyaml':
                import yaml
                version = getattr(yaml, '__version__', 'Unknown')
                available_packages.append(f"{package} ({version})")
            else:
                module = __import__(package.replace('-', '_'))
                version = getattr(module, '__version__', 'Unknown')
                available_packages.append(f"{package} ({version})")
        except ImportError:
            missing_packages.append(package)
    
    return {
        'available': available_packages,
        'missing': missing_packages,
        'all_satisfied': len(missing_packages) == 0
    }


# 系统启动时的自动检查
def _startup_check():
    """系统启动检查"""
    logger = get_logger('startup')
    
    # 检查依赖项
    dep_status = check_dependencies()
    if not dep_status['all_satisfied']:
        logger.warning(f"缺少依赖项: {dep_status['missing']}")
        logger.info("请运行: pip install -r requirements.txt")
    
    # 检查系统资源
    try:
        memory_info = MemoryUtils.get_memory_usage()
        if 'error' not in memory_info:
            logger.info(f"系统内存使用: {memory_info['percent']:.1f}%")
    except Exception:
        pass
    
    logger.debug("系统启动检查完成")


# 在模块导入时进行启动检查
try:
    _startup_check()
except Exception:
    # 静默处理启动检查错误，不影响模块导入
    pass


# 导出的公共接口
__all__ = [
    # 版本信息
    '__version__', '__author__', '__description__',
    
    # 核心类和枚举
    'ConfigManager', 'config_manager', 'ElementType', 'RenovationMode', 'Orientation',
    
    # 数据结构
    'BuildingElement', 'WindowElement', 'WallElement', 'FacadeElements',
    'HourlyClimateData', 'OrientedClimateData', 'FacadeIndividual', 
    'OptimizationResults', 'VisualizationData',
    
    # 异常类
    'FacadeOptimizationError', 'ConfigurationError', 'DataValidationError',
    
    # 工具类
    'FileUtils', 'DataUtils', 'ValidationUtils', 'TimeUtils', 'MathUtils',
    
    # 系统函数
    'initialize_system', 'get_system_info', 'check_dependencies',
    'setup_config', 'get_config', 'setup_logging', 'get_logger'
]