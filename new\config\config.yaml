# 建筑立面优化系统配置文件

# 图像处理配置
image_processing:
  # 像素到米的转换比例 - 临时默认值，建议根据实际测量设置
  pixel_to_meter_ratio: 0.01
  
  # 颜色映射配置 - 基于立面识别色块图
  color_mapping:
    walls:
      min: [180, 180, 180]  # 浅灰色下限
      max: [255, 255, 255]  # 白色上限
    windows:
      min: [0, 100, 200]    # 蓝色下限
      max: [50, 150, 255]   # 蓝色上限
    doors:
      min: [240, 240, 0]    # 黄色下限
      max: [255, 255, 20]   # 黄色上限
    shading:
      min: [0, 200, 0]      # 绿色下限
      max: [20, 255, 20]    # 绿色上限
    frames:
      min: [0, 0, 0]        # 黑色下限
      max: [30, 30, 30]     # 深灰色上限
  
  # 颜色匹配策略
  color_matching:
    strategy: "range"       # 使用范围匹配
    tolerance: 15           # 颜色容差
    use_hsv: false         # 使用BGR颜色空间
  
  # 预处理参数
  preprocessing:
    noise_filter_kernel: 3    # 噪声过滤核大小
    min_contour_area: 100     # 最小轮廓面积（像素）
    max_contour_area: 50000   # 最大轮廓面积（像素）

# 系统配置
system:
  cache_enabled: true
  debug_mode: false
  log_level: "INFO"

# 输出配置
output:
  save_intermediate_results: true
  output_directory: "output"
  create_debug_visualizations: true

# 优化约束
optimization_constraints:
  window_wall_ratio:
    min: 0.1
    max: 0.8
  
  window_size:
    min_width: 0.5
    max_width: 3.0
    min_height: 0.5
    max_height: 2.5
  
  wall_margin: 0.2

# 可视化配置
visualization:
  default_style: "modern"
  figure_size: [12, 8]
  dpi: 300
  save_format: "png"

# 性能基准配置
performance_benchmarks:
  # 经济性基准（0-1评分）
  economic_benchmark: 0.6
  # 环境性基准（0-1评分）
  environmental_benchmark: 0.7
  # 技术可行性基准（0-1评分）
  technical_benchmark: 0.5
  # 热工性能基准（0-1评分）
  thermal_benchmark: 0.6
  # 能耗性能基准（0-1评分）
  energy_benchmark: 0.8
  # 用户满意度基准（0-1评分）
  user_benchmark: 0.6
  
  # 能耗基准数据
  baseline_energy_consumption: 120  # kWh/m²/year
  energy_price_per_kwh: 0.6        # 元/kWh
  
  # 成本基准数据
  baseline_renovation_cost: 2000    # 元/m²