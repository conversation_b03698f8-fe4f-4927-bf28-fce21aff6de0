#!/usr/bin/env python3
"""
分析图像颜色分布，帮助调整YOLO颜色配置
"""

import cv2
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt

def analyze_image_colors(image_path):
    """分析图像中的主要颜色"""
    print(f"分析图像: {image_path}")
    
    # 读取图像 - 支持中文路径
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()

        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if image is None:
            print(f"无法解码图像: {image_path}")
            return
    except Exception as e:
        print(f"读取图像失败: {e}")
        return
    
    print(f"图像尺寸: {image.shape}")
    
    # 转换为RGB用于显示
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 获取所有像素的颜色
    pixels = image.reshape(-1, 3)
    
    # 统计最常见的颜色
    unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
    
    # 按出现次数排序
    sorted_indices = np.argsort(counts)[::-1]
    top_colors = unique_colors[sorted_indices[:20]]  # 前20种颜色
    top_counts = counts[sorted_indices[:20]]
    
    print("\n前20种最常见的颜色 (BGR格式):")
    for i, (color, count) in enumerate(zip(top_colors, top_counts)):
        percentage = (count / len(pixels)) * 100
        print(f"{i+1:2d}. BGR{color} - {count:6d} 像素 ({percentage:5.2f}%)")
    
    # 分析可能的窗户颜色（紫红色系）
    print("\n分析紫红色系颜色（可能的窗户）:")
    window_colors = []
    for color, count in zip(top_colors, top_counts):
        b, g, r = color
        # 检查是否为紫红色系（蓝色和红色分量高，绿色分量低）
        if b > 200 and r > 200 and g < 50:
            percentage = (count / len(pixels)) * 100
            window_colors.append((color, count, percentage))
            print(f"  紫红色系: BGR{color} - {count} 像素 ({percentage:.2f}%)")

    # 也检查其他可能的窗户颜色
    print("\n其他可能的窗户颜色（非灰色、非白色）:")
    other_colors = []
    for color, count in zip(top_colors, top_counts):
        b, g, r = color
        # 排除灰色和白色
        if not (abs(b-g) < 20 and abs(g-r) < 20 and abs(b-r) < 20):
            percentage = (count / len(pixels)) * 100
            if percentage > 0.1:  # 只显示占比超过0.1%的颜色
                other_colors.append((color, count, percentage))
                print(f"  非灰色: BGR{color} - {count} 像素 ({percentage:.2f}%)")
    
    # 分析可能的墙体颜色（灰白色系）
    print("\n分析灰白色系颜色（可能的墙体）:")
    wall_colors = []
    for color, count in zip(top_colors, top_counts):
        b, g, r = color
        # 检查是否为灰白色系（三个分量都较高且相近）
        if min(b, g, r) > 150 and max(b, g, r) - min(b, g, r) < 50:
            percentage = (count / len(pixels)) * 100
            wall_colors.append((color, count, percentage))
            print(f"  灰白色系: BGR{color} - {count} 像素 ({percentage:.2f}%)")
    
    # 建议的颜色配置
    print("\n建议的颜色配置:")
    
    if window_colors:
        # 基于检测到的窗户颜色计算范围
        window_bgr = [c[0] for c in window_colors]
        min_b = min(c[0] for c in window_bgr)
        max_b = max(c[0] for c in window_bgr)
        min_g = min(c[1] for c in window_bgr)
        max_g = max(c[1] for c in window_bgr)
        min_r = min(c[2] for c in window_bgr)
        max_r = max(c[2] for c in window_bgr)
        
        # 扩展范围以确保覆盖
        min_bgr = [max(0, min_b-20), max(0, min_g-20), max(0, min_r-20)]
        max_bgr = [min(255, max_b+20), min(255, max_g+20), min(255, max_r+20)]
        
        print(f"windows:")
        print(f"  min: {min_bgr}")
        print(f"  max: {max_bgr}")
    else:
        print("未检测到明显的紫红色系颜色")
        # 基于其他颜色分析
        if other_colors:
            print("基于其他非灰色颜色建议配置:")
            all_other_bgr = [c[0] for c in other_colors]
            min_b = min(c[0] for c in all_other_bgr)
            max_b = max(c[0] for c in all_other_bgr)
            min_g = min(c[1] for c in all_other_bgr)
            max_g = max(c[1] for c in all_other_bgr)
            min_r = min(c[2] for c in all_other_bgr)
            max_r = max(c[2] for c in all_other_bgr)

            print(f"windows:")
            print(f"  min: [{min_b}, {min_g}, {min_r}]")
            print(f"  max: [{max_b}, {max_g}, {max_r}]")
    
    if wall_colors:
        # 基于检测到的墙体颜色计算范围
        wall_bgr = [c[0] for c in wall_colors]
        min_b = min(c[0] for c in wall_bgr)
        max_b = max(c[0] for c in wall_bgr)
        min_g = min(c[1] for c in wall_bgr)
        max_g = max(c[1] for c in wall_bgr)
        min_r = min(c[2] for c in wall_bgr)
        max_r = max(c[2] for c in wall_bgr)
        
        print(f"walls:")
        print(f"  min: [{min_b}, {min_g}, {min_r}]")
        print(f"  max: [{max_b}, {max_g}, {max_r}]")

if __name__ == "__main__":
    image_path = r"D:\桌面\立面优化 - 副本\04_simplified_blocks.png"
    analyze_image_colors(image_path)
