#!/usr/bin/env python3
"""
分析图像颜色分布，帮助调整YOLO颜色配置
"""

import cv2
import numpy as np
from collections import Counter
import matplotlib.pyplot as plt

def analyze_image_colors(image_path):
    """分析图像中的主要颜色"""
    print(f"分析图像: {image_path}")
    
    # 读取图像 - 支持中文路径
    try:
        with open(image_path, 'rb') as f:
            image_data = f.read()

        nparr = np.frombuffer(image_data, np.uint8)
        image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

        if image is None:
            print(f"无法解码图像: {image_path}")
            return
    except Exception as e:
        print(f"读取图像失败: {e}")
        return
    
    print(f"图像尺寸: {image.shape}")
    
    # 转换为RGB用于显示
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    
    # 获取所有像素的颜色
    pixels = image.reshape(-1, 3)
    
    # 统计最常见的颜色
    unique_colors, counts = np.unique(pixels, axis=0, return_counts=True)
    
    # 按出现次数排序
    sorted_indices = np.argsort(counts)[::-1]
    top_colors = unique_colors[sorted_indices[:20]]  # 前20种颜色
    top_counts = counts[sorted_indices[:20]]
    
    print("\n前20种最常见的颜色 (BGR格式):")
    for i, (color, count) in enumerate(zip(top_colors, top_counts)):
        percentage = (count / len(pixels)) * 100
        print(f"{i+1:2d}. BGR{color} - {count:6d} 像素 ({percentage:5.2f}%)")
    
    # 分析可能的窗户颜色（蓝色系）
    print("\n分析蓝色系颜色（可能的窗户）:")
    blue_colors = []
    for color, count in zip(top_colors, top_counts):
        b, g, r = color
        # 检查是否为蓝色系（蓝色分量较高）
        if b > g and b > r and b > 100:
            percentage = (count / len(pixels)) * 100
            blue_colors.append((color, count, percentage))
            print(f"  蓝色系: BGR{color} - {count} 像素 ({percentage:.2f}%)")
    
    # 分析可能的墙体颜色（灰白色系）
    print("\n分析灰白色系颜色（可能的墙体）:")
    wall_colors = []
    for color, count in zip(top_colors, top_counts):
        b, g, r = color
        # 检查是否为灰白色系（三个分量都较高且相近）
        if min(b, g, r) > 150 and max(b, g, r) - min(b, g, r) < 50:
            percentage = (count / len(pixels)) * 100
            wall_colors.append((color, count, percentage))
            print(f"  灰白色系: BGR{color} - {count} 像素 ({percentage:.2f}%)")
    
    # 建议的颜色配置
    print("\n建议的颜色配置:")
    
    if blue_colors:
        # 基于检测到的蓝色计算范围
        blue_bgr = [c[0] for c in blue_colors]
        min_b = min(c[0] for c in blue_bgr)
        max_b = max(c[0] for c in blue_bgr)
        min_g = min(c[1] for c in blue_bgr)
        max_g = max(c[1] for c in blue_bgr)
        min_r = min(c[2] for c in blue_bgr)
        max_r = max(c[2] for c in blue_bgr)
        
        # 扩展范围以确保覆盖
        min_bgr = [max(0, min_b-20), max(0, min_g-20), max(0, min_r-20)]
        max_bgr = [min(255, max_b+20), min(255, max_g+20), min(255, max_r+20)]
        
        print(f"windows:")
        print(f"  min: {min_bgr}")
        print(f"  max: {max_bgr}")
    else:
        print("未检测到明显的蓝色系颜色，可能需要手动调整窗户颜色配置")
    
    if wall_colors:
        # 基于检测到的墙体颜色计算范围
        wall_bgr = [c[0] for c in wall_colors]
        min_b = min(c[0] for c in wall_bgr)
        max_b = max(c[0] for c in wall_bgr)
        min_g = min(c[1] for c in wall_bgr)
        max_g = max(c[1] for c in wall_bgr)
        min_r = min(c[2] for c in wall_bgr)
        max_r = max(c[2] for c in wall_bgr)
        
        print(f"walls:")
        print(f"  min: [{min_b}, {min_g}, {min_r}]")
        print(f"  max: [{max_b}, {max_g}, {max_r}]")

if __name__ == "__main__":
    image_path = r"D:\桌面\立面优化 - 副本\04_simplified_blocks.png"
    analyze_image_colors(image_path)
