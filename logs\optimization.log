INFO - 优化算法日志系统初始化完成
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第1代进化完成: 平均能耗=45.8, 可行解=200/200, 缓存命中率=6.8%
DEBUG - 第1代完成: 最佳能耗=33.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 172 个个体
INFO - 第2代进化完成: 平均能耗=45.5, 可行解=172/172, 缓存命中率=9.5%
DEBUG - 第2代完成: 最佳能耗=33.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 198 个个体
INFO - 第3代进化完成: 平均能耗=45.4, 可行解=198/198, 缓存命中率=9.8%
DEBUG - 第3代完成: 最佳能耗=33.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=44.9, 可行解=200/200, 缓存命中率=11.2%
DEBUG - 第4代完成: 最佳能耗=29.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=44.1, 可行解=200/200, 缓存命中率=11.9%
DEBUG - 第5代完成: 最佳能耗=29.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=43.2, 可行解=200/200, 缓存命中率=12.1%
DEBUG - 第6代完成: 最佳能耗=29.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=42.8, 可行解=200/200, 缓存命中率=12.4%
DEBUG - 第7代完成: 最佳能耗=29.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=42.4, 可行解=200/200, 缓存命中率=12.4%
DEBUG - 第8代完成: 最佳能耗=28.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=41.3, 可行解=200/200, 缓存命中率=12.6%
DEBUG - 第9代完成: 最佳能耗=26.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=40.9, 可行解=200/200, 缓存命中率=13.1%
DEBUG - 第10代完成: 最佳能耗=26.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=39.9, 可行解=200/200, 缓存命中率=13.3%
DEBUG - 第11代完成: 最佳能耗=21.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=38.9, 可行解=200/200, 缓存命中率=13.5%
DEBUG - 第12代完成: 最佳能耗=21.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=38.2, 可行解=200/200, 缓存命中率=13.7%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=38.0, 可行解=200/200, 缓存命中率=13.7%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 优化算法日志系统初始化完成
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第1代进化完成: 平均能耗=45.5, 可行解=200/200, 缓存命中率=4.5%
DEBUG - 第1代完成: 最佳能耗=34.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=45.4, 可行解=200/200, 缓存命中率=6.7%
DEBUG - 第2代完成: 最佳能耗=32.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=44.5, 可行解=200/200, 缓存命中率=7.6%
DEBUG - 第3代完成: 最佳能耗=26.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=43.5, 可行解=200/200, 缓存命中率=8.7%
DEBUG - 第4代完成: 最佳能耗=25.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=41.9, 可行解=200/200, 缓存命中率=9.2%
DEBUG - 第5代完成: 最佳能耗=21.8
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=39.9, 可行解=200/200, 缓存命中率=10.5%
DEBUG - 第6代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=39.3, 可行解=200/200, 缓存命中率=11.2%
DEBUG - 第7代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=38.4, 可行解=200/200, 缓存命中率=11.4%
DEBUG - 第8代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=38.0, 可行解=200/200, 缓存命中率=11.4%
DEBUG - 第9代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=37.4, 可行解=200/200, 缓存命中率=12.2%
DEBUG - 第10代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=37.5, 可行解=200/200, 缓存命中率=13.0%
DEBUG - 第11代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=37.0, 可行解=200/200, 缓存命中率=13.4%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=13.7%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=36.9, 可行解=200/200, 缓存命中率=13.4%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第1代进化完成: 平均能耗=44.0, 可行解=142/200, 缓存命中率=42.5%
DEBUG - 第1代完成: 最佳能耗=34.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=43.7, 可行解=144/200, 缓存命中率=33.2%
DEBUG - 第2代完成: 最佳能耗=32.9
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=43.4, 可行解=161/200, 缓存命中率=27.4%
DEBUG - 第3代完成: 最佳能耗=25.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=42.4, 可行解=190/200, 缓存命中率=23.8%
DEBUG - 第4代完成: 最佳能耗=25.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=41.2, 可行解=200/200, 缓存命中率=21.8%
DEBUG - 第5代完成: 最佳能耗=24.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=40.6, 可行解=200/200, 缓存命中率=19.9%
DEBUG - 第6代完成: 最佳能耗=24.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=39.7, 可行解=200/200, 缓存命中率=18.4%
DEBUG - 第7代完成: 最佳能耗=22.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=39.3, 可行解=200/200, 缓存命中率=17.8%
DEBUG - 第8代完成: 最佳能耗=22.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=38.8, 可行解=200/200, 缓存命中率=17.2%
DEBUG - 第9代完成: 最佳能耗=22.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=37.9, 可行解=200/200, 缓存命中率=16.5%
DEBUG - 第10代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=37.1, 可行解=200/200, 缓存命中率=16.0%
DEBUG - 第11代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=15.4%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=36.1, 可行解=200/200, 缓存命中率=15.5%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=36.1, 可行解=200/200, 缓存命中率=15.4%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第15代进化完成: 平均能耗=35.8, 可行解=200/200, 缓存命中率=15.0%
DEBUG - 第15代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第1代进化完成: 平均能耗=45.1, 可行解=199/200, 缓存命中率=39.0%
DEBUG - 第1代完成: 最佳能耗=35.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 159 个个体
INFO - 第2代进化完成: 平均能耗=43.8, 可行解=154/159, 缓存命中率=30.5%
DEBUG - 第2代完成: 最佳能耗=26.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 185 个个体
INFO - 第3代进化完成: 平均能耗=42.7, 可行解=177/185, 缓存命中率=26.2%
DEBUG - 第3代完成: 最佳能耗=26.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=41.9, 可行解=200/200, 缓存命中率=23.2%
DEBUG - 第4代完成: 最佳能耗=26.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=40.8, 可行解=200/200, 缓存命中率=21.1%
DEBUG - 第5代完成: 最佳能耗=26.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=39.6, 可行解=200/200, 缓存命中率=19.6%
DEBUG - 第6代完成: 最佳能耗=26.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=39.2, 可行解=200/200, 缓存命中率=18.5%
DEBUG - 第7代完成: 最佳能耗=25.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=38.3, 可行解=200/200, 缓存命中率=17.2%
DEBUG - 第8代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=37.8, 可行解=200/200, 缓存命中率=16.4%
DEBUG - 第9代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=37.3, 可行解=200/200, 缓存命中率=15.7%
DEBUG - 第10代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=37.0, 可行解=200/200, 缓存命中率=15.2%
DEBUG - 第11代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=14.8%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=14.8%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=14.6%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第15代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=14.2%
DEBUG - 第15代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第1代进化完成: 平均能耗=45.2, 可行解=66/200, 缓存命中率=41.0%
DEBUG - 第1代完成: 最佳能耗=34.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=43.8, 可行解=200/200, 缓存命中率=28.3%
DEBUG - 第2代完成: 最佳能耗=30.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 191 个个体
INFO - 第3代进化完成: 平均能耗=42.6, 可行解=188/191, 缓存命中率=22.2%
DEBUG - 第3代完成: 最佳能耗=30.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 197 个个体
INFO - 第4代进化完成: 平均能耗=41.2, 可行解=192/197, 缓存命中率=18.6%
DEBUG - 第4代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=40.7, 可行解=200/200, 缓存命中率=16.4%
DEBUG - 第5代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=40.1, 可行解=200/200, 缓存命中率=15.0%
DEBUG - 第6代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=39.1, 可行解=200/200, 缓存命中率=13.4%
DEBUG - 第7代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=37.8, 可行解=200/200, 缓存命中率=12.2%
DEBUG - 第8代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=37.1, 可行解=200/200, 缓存命中率=11.4%
DEBUG - 第9代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=10.8%
DEBUG - 第10代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=10.7%
DEBUG - 第11代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=10.3%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=10.4%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=10.0%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第1代进化完成: 平均能耗=40.8, 可行解=197/200, 缓存命中率=39.8%
DEBUG - 第1代完成: 最佳能耗=28.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=40.8, 可行解=200/200, 缓存命中率=29.7%
DEBUG - 第2代完成: 最佳能耗=28.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=40.9, 可行解=200/200, 缓存命中率=24.5%
DEBUG - 第3代完成: 最佳能耗=28.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=40.8, 可行解=200/200, 缓存命中率=21.2%
DEBUG - 第4代完成: 最佳能耗=24.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=40.6, 可行解=200/200, 缓存命中率=18.3%
DEBUG - 第5代完成: 最佳能耗=24.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=40.1, 可行解=200/200, 缓存命中率=16.5%
DEBUG - 第6代完成: 最佳能耗=22.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=39.8, 可行解=200/200, 缓存命中率=15.3%
DEBUG - 第7代完成: 最佳能耗=22.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=39.4, 可行解=200/200, 缓存命中率=14.1%
DEBUG - 第8代完成: 最佳能耗=22.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=39.3, 可行解=200/200, 缓存命中率=13.2%
DEBUG - 第9代完成: 最佳能耗=21.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=38.7, 可行解=200/200, 缓存命中率=12.7%
DEBUG - 第10代完成: 最佳能耗=21.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=38.2, 可行解=200/200, 缓存命中率=12.4%
DEBUG - 第11代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=37.3, 可行解=200/200, 缓存命中率=11.8%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=37.2, 可行解=200/200, 缓存命中率=11.6%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=37.1, 可行解=200/200, 缓存命中率=10.9%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 193 个个体
INFO - 第1代进化完成: 平均能耗=41.2, 可行解=183/193, 缓存命中率=41.0%
DEBUG - 第1代完成: 最佳能耗=34.4
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=41.5, 可行解=200/200, 缓存命中率=29.8%
DEBUG - 第2代完成: 最佳能耗=32.9
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=41.6, 可行解=200/200, 缓存命中率=24.2%
DEBUG - 第3代完成: 最佳能耗=32.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=41.8, 可行解=200/200, 缓存命中率=20.5%
DEBUG - 第4代完成: 最佳能耗=32.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=41.5, 可行解=200/200, 缓存命中率=18.2%
DEBUG - 第5代完成: 最佳能耗=30.9
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=41.4, 可行解=200/200, 缓存命中率=16.1%
DEBUG - 第6代完成: 最佳能耗=30.9
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=41.3, 可行解=200/200, 缓存命中率=15.2%
DEBUG - 第7代完成: 最佳能耗=30.9
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=41.2, 可行解=200/200, 缓存命中率=14.3%
DEBUG - 第8代完成: 最佳能耗=27.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=40.2, 可行解=200/200, 缓存命中率=13.3%
DEBUG - 第9代完成: 最佳能耗=25.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=39.5, 可行解=200/200, 缓存命中率=12.4%
DEBUG - 第10代完成: 最佳能耗=25.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=39.0, 可行解=200/200, 缓存命中率=11.8%
DEBUG - 第11代完成: 最佳能耗=23.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=38.4, 可行解=200/200, 缓存命中率=11.4%
DEBUG - 第12代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=38.2, 可行解=200/200, 缓存命中率=11.1%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=37.8, 可行解=200/200, 缓存命中率=10.8%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 187 个个体
INFO - 第1代进化完成: 平均能耗=41.1, 可行解=183/187, 缓存命中率=39.5%
DEBUG - 第1代完成: 最佳能耗=28.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=41.5, 可行解=200/200, 缓存命中率=28.5%
DEBUG - 第2代完成: 最佳能耗=28.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=41.2, 可行解=200/200, 缓存命中率=23.9%
DEBUG - 第3代完成: 最佳能耗=25.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=41.1, 可行解=200/200, 缓存命中率=20.7%
DEBUG - 第4代完成: 最佳能耗=25.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=40.8, 可行解=200/200, 缓存命中率=19.2%
DEBUG - 第5代完成: 最佳能耗=25.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=40.5, 可行解=200/200, 缓存命中率=17.6%
DEBUG - 第6代完成: 最佳能耗=24.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=39.8, 可行解=200/200, 缓存命中率=16.2%
DEBUG - 第7代完成: 最佳能耗=24.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=39.2, 可行解=200/200, 缓存命中率=14.9%
DEBUG - 第8代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=38.2, 可行解=200/200, 缓存命中率=13.6%
DEBUG - 第9代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=37.2, 可行解=200/200, 缓存命中率=13.0%
DEBUG - 第10代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=12.8%
DEBUG - 第11代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=12.2%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=11.7%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=11.2%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第15代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=10.8%
DEBUG - 第15代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第16代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=10.7%
DEBUG - 第16代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第17代进化完成: 平均能耗=35.8, 可行解=200/200, 缓存命中率=10.3%
DEBUG - 第17代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第18代进化完成: 平均能耗=35.7, 可行解=200/200, 缓存命中率=10.2%
DEBUG - 第18代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第19代进化完成: 平均能耗=35.4, 可行解=200/200, 缓存命中率=10.2%
DEBUG - 第19代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第20代进化完成: 平均能耗=35.2, 可行解=200/200, 缓存命中率=10.1%
DEBUG - 第20代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 177 个个体
INFO - 第1代进化完成: 平均能耗=41.6, 可行解=171/177, 缓存命中率=40.0%
DEBUG - 第1代完成: 最佳能耗=34.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=42.1, 可行解=200/200, 缓存命中率=29.0%
DEBUG - 第2代完成: 最佳能耗=33.6
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=41.9, 可行解=200/200, 缓存命中率=23.0%
DEBUG - 第3代完成: 最佳能耗=30.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=41.8, 可行解=200/200, 缓存命中率=19.3%
DEBUG - 第4代完成: 最佳能耗=30.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=41.5, 可行解=200/200, 缓存命中率=17.1%
DEBUG - 第5代完成: 最佳能耗=30.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=41.2, 可行解=200/200, 缓存命中率=15.3%
DEBUG - 第6代完成: 最佳能耗=22.9
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=40.7, 可行解=200/200, 缓存命中率=14.1%
DEBUG - 第7代完成: 最佳能耗=22.9
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=40.2, 可行解=200/200, 缓存命中率=13.0%
DEBUG - 第8代完成: 最佳能耗=22.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=39.6, 可行解=200/200, 缓存命中率=12.2%
DEBUG - 第9代完成: 最佳能耗=22.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=39.4, 可行解=200/200, 缓存命中率=11.3%
DEBUG - 第10代完成: 最佳能耗=22.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=38.9, 可行解=200/200, 缓存命中率=10.9%
DEBUG - 第11代完成: 最佳能耗=22.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=38.5, 可行解=200/200, 缓存命中率=10.5%
DEBUG - 第12代完成: 最佳能耗=22.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=38.1, 可行解=200/200, 缓存命中率=10.3%
DEBUG - 第13代完成: 最佳能耗=22.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=37.6, 可行解=200/200, 缓存命中率=10.0%
DEBUG - 第14代完成: 最佳能耗=22.7
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第15代进化完成: 平均能耗=37.3, 可行解=200/200, 缓存命中率=9.7%
DEBUG - 第15代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第16代进化完成: 平均能耗=37.1, 可行解=200/200, 缓存命中率=9.5%
DEBUG - 第16代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第17代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=9.2%
DEBUG - 第17代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第18代进化完成: 平均能耗=36.6, 可行解=200/200, 缓存命中率=9.0%
DEBUG - 第18代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第19代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=9.0%
DEBUG - 第19代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第20代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=9.0%
DEBUG - 第20代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
INFO - 智能种群初始化完成: 200 个个体 (随机:120, 启发式:50, 极端:30)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 182 个个体
INFO - 第1代进化完成: 平均能耗=41.5, 可行解=173/182, 缓存命中率=40.5%
DEBUG - 第1代完成: 最佳能耗=35.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=41.8, 可行解=200/200, 缓存命中率=29.5%
DEBUG - 第2代完成: 最佳能耗=34.5
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=41.8, 可行解=200/200, 缓存命中率=23.2%
DEBUG - 第3代完成: 最佳能耗=30.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=41.8, 可行解=200/200, 缓存命中率=20.0%
DEBUG - 第4代完成: 最佳能耗=30.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=41.7, 可行解=200/200, 缓存命中率=17.9%
DEBUG - 第5代完成: 最佳能耗=30.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=41.4, 可行解=200/200, 缓存命中率=16.3%
DEBUG - 第6代完成: 最佳能耗=30.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=41.1, 可行解=200/200, 缓存命中率=14.5%
DEBUG - 第7代完成: 最佳能耗=30.3
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=40.9, 可行解=200/200, 缓存命中率=13.2%
DEBUG - 第8代完成: 最佳能耗=29.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=40.5, 可行解=200/200, 缓存命中率=12.2%
DEBUG - 第9代完成: 最佳能耗=29.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=40.3, 可行解=200/200, 缓存命中率=11.8%
DEBUG - 第10代完成: 最佳能耗=29.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=40.1, 可行解=200/200, 缓存命中率=11.1%
DEBUG - 第11代完成: 最佳能耗=23.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=39.1, 可行解=200/200, 缓存命中率=10.6%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=38.9, 可行解=200/200, 缓存命中率=10.2%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=37.8, 可行解=200/200, 缓存命中率=9.9%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第15代进化完成: 平均能耗=37.4, 可行解=200/200, 缓存命中率=9.7%
DEBUG - 第15代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第16代进化完成: 平均能耗=37.0, 可行解=200/200, 缓存命中率=9.5%
DEBUG - 第16代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第17代进化完成: 平均能耗=36.9, 可行解=200/200, 缓存命中率=9.3%
DEBUG - 第17代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第18代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=9.2%
DEBUG - 第18代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第19代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=8.9%
DEBUG - 第19代完成: 最佳能耗=21.0
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第20代进化完成: 平均能耗=36.0, 可行解=200/200, 缓存命中率=8.8%
DEBUG - 第20代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=80, 参考点数量=110
INFO - 智能种群初始化完成: 80 个个体 (随机:48, 启发式:20, 极端:12)
INFO - 种群评估完成: 80 个个体
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第1代进化完成: 平均能耗=40.8, 可行解=80/80, 缓存命中率=55.0%
DEBUG - 第1代完成: 最佳能耗=34.6
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第2代进化完成: 平均能耗=40.9, 可行解=80/80, 缓存命中率=44.2%
DEBUG - 第2代完成: 最佳能耗=34.6
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第3代进化完成: 平均能耗=41.3, 可行解=80/80, 缓存命中率=39.7%
DEBUG - 第3代完成: 最佳能耗=34.6
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第4代进化完成: 平均能耗=41.3, 可行解=80/80, 缓存命中率=38.0%
DEBUG - 第4代完成: 最佳能耗=34.6
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第5代进化完成: 平均能耗=41.8, 可行解=80/80, 缓存命中率=34.4%
DEBUG - 第5代完成: 最佳能耗=34.6
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第6代进化完成: 平均能耗=41.4, 可行解=80/80, 缓存命中率=32.3%
DEBUG - 第6代完成: 最佳能耗=34.6
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第7代进化完成: 平均能耗=41.9, 可行解=80/80, 缓存命中率=30.5%
DEBUG - 第7代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第8代进化完成: 平均能耗=41.7, 可行解=80/80, 缓存命中率=29.9%
DEBUG - 第8代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第9代进化完成: 平均能耗=41.8, 可行解=80/80, 缓存命中率=29.5%
DEBUG - 第9代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第10代进化完成: 平均能耗=41.9, 可行解=80/80, 缓存命中率=28.4%
DEBUG - 第10代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第11代进化完成: 平均能耗=41.9, 可行解=80/80, 缓存命中率=27.4%
DEBUG - 第11代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第12代进化完成: 平均能耗=42.0, 可行解=80/80, 缓存命中率=27.3%
DEBUG - 第12代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第13代进化完成: 平均能耗=41.9, 可行解=80/80, 缓存命中率=27.8%
DEBUG - 第13代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第14代进化完成: 平均能耗=42.1, 可行解=80/80, 缓存命中率=27.2%
DEBUG - 第14代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第15代进化完成: 平均能耗=41.8, 可行解=80/80, 缓存命中率=26.5%
DEBUG - 第15代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第16代进化完成: 平均能耗=41.7, 可行解=80/80, 缓存命中率=26.0%
DEBUG - 第16代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第17代进化完成: 平均能耗=41.7, 可行解=80/80, 缓存命中率=25.8%
DEBUG - 第17代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第18代进化完成: 平均能耗=42.0, 可行解=80/80, 缓存命中率=25.2%
DEBUG - 第18代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第19代进化完成: 平均能耗=42.0, 可行解=80/80, 缓存命中率=24.8%
DEBUG - 第19代完成: 最佳能耗=35.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第20代进化完成: 平均能耗=42.0, 可行解=80/80, 缓存命中率=24.9%
DEBUG - 第20代完成: 最佳能耗=35.1
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=60, 参考点数量=110
INFO - 智能种群初始化完成: 60 个个体 (随机:36, 启发式:15, 极端:9)
INFO - 种群评估完成: 60 个个体
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第1代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=40.0%
DEBUG - 第1代完成: 最佳能耗=37.9
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第2代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=37.8%
DEBUG - 第2代完成: 最佳能耗=37.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第3代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=35.8%
DEBUG - 第3代完成: 最佳能耗=37.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第4代进化完成: 平均能耗=38.2, 可行解=60/60, 缓存命中率=33.0%
DEBUG - 第4代完成: 最佳能耗=37.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第5代进化完成: 平均能耗=38.0, 可行解=60/60, 缓存命中率=31.4%
DEBUG - 第5代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第6代进化完成: 平均能耗=38.0, 可行解=60/60, 缓存命中率=30.0%
DEBUG - 第6代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第7代进化完成: 平均能耗=38.0, 可行解=60/60, 缓存命中率=29.6%
DEBUG - 第7代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第8代进化完成: 平均能耗=37.9, 可行解=60/60, 缓存命中率=30.7%
DEBUG - 第8代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第9代进化完成: 平均能耗=37.9, 可行解=60/60, 缓存命中率=31.5%
DEBUG - 第9代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第10代进化完成: 平均能耗=37.8, 可行解=60/60, 缓存命中率=31.2%
DEBUG - 第10代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第11代进化完成: 平均能耗=37.9, 可行解=60/60, 缓存命中率=30.0%
DEBUG - 第11代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第12代进化完成: 平均能耗=37.9, 可行解=60/60, 缓存命中率=30.0%
DEBUG - 第12代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第13代进化完成: 平均能耗=38.0, 可行解=60/60, 缓存命中率=30.4%
DEBUG - 第13代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第14代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=31.9%
DEBUG - 第14代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第15代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=30.6%
DEBUG - 第15代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第16代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=29.4%
DEBUG - 第16代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第17代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=28.5%
DEBUG - 第17代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第18代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=28.4%
DEBUG - 第18代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第19代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=28.9%
DEBUG - 第19代完成: 最佳能耗=36.8
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第20代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=28.9%
DEBUG - 第20代完成: 最佳能耗=36.8
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=60, 参考点数量=110
INFO - 智能种群初始化完成: 60 个个体 (随机:36, 启发式:15, 极端:9)
INFO - 种群评估完成: 60 个个体
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第1代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=70.8%
DEBUG - 第1代完成: 最佳能耗=37.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第2代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=70.0%
DEBUG - 第2代完成: 最佳能耗=37.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第3代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=64.2%
DEBUG - 第3代完成: 最佳能耗=37.4
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第4代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=59.3%
DEBUG - 第4代完成: 最佳能耗=37.4
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第5代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=55.6%
DEBUG - 第5代完成: 最佳能耗=37.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第6代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=52.9%
DEBUG - 第6代完成: 最佳能耗=37.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第7代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=50.4%
DEBUG - 第7代完成: 最佳能耗=37.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第8代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=47.6%
DEBUG - 第8代完成: 最佳能耗=37.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第9代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=45.0%
DEBUG - 第9代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第10代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=43.0%
DEBUG - 第10代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第11代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=41.7%
DEBUG - 第11代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第12代进化完成: 平均能耗=38.2, 可行解=60/60, 缓存命中率=40.9%
DEBUG - 第12代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第13代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=39.4%
DEBUG - 第13代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第14代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=38.4%
DEBUG - 第14代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第15代进化完成: 平均能耗=38.2, 可行解=60/60, 缓存命中率=37.0%
DEBUG - 第15代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第16代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=36.0%
DEBUG - 第16代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第17代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=34.9%
DEBUG - 第17代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第18代进化完成: 平均能耗=38.2, 可行解=60/60, 缓存命中率=33.3%
DEBUG - 第18代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第19代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=32.2%
DEBUG - 第19代完成: 最佳能耗=36.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第20代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=31.7%
DEBUG - 第20代完成: 最佳能耗=36.3
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=60, 参考点数量=110
INFO - 智能种群初始化完成: 60 个个体 (随机:36, 启发式:15, 极端:9)
INFO - 种群评估完成: 60 个个体
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第1代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=51.7%
DEBUG - 第1代完成: 最佳能耗=38.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第2代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=44.4%
DEBUG - 第2代完成: 最佳能耗=37.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第3代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=36.7%
DEBUG - 第3代完成: 最佳能耗=37.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第4代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=32.0%
DEBUG - 第4代完成: 最佳能耗=37.1
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第5代进化完成: 平均能耗=38.6, 可行解=60/60, 缓存命中率=28.9%
DEBUG - 第5代完成: 最佳能耗=36.4
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第6代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=28.6%
DEBUG - 第6代完成: 最佳能耗=35.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第7代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=27.7%
DEBUG - 第7代完成: 最佳能耗=35.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第8代进化完成: 平均能耗=38.2, 可行解=60/60, 缓存命中率=26.9%
DEBUG - 第8代完成: 最佳能耗=35.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第9代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=26.8%
DEBUG - 第9代完成: 最佳能耗=35.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第10代进化完成: 平均能耗=38.2, 可行解=60/60, 缓存命中率=27.0%
DEBUG - 第10代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第11代进化完成: 平均能耗=38.0, 可行解=60/60, 缓存命中率=27.1%
DEBUG - 第11代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第12代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=27.2%
DEBUG - 第12代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第13代进化完成: 平均能耗=38.0, 可行解=60/60, 缓存命中率=28.2%
DEBUG - 第13代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第14代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=28.7%
DEBUG - 第14代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第15代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=28.6%
DEBUG - 第15代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第16代进化完成: 平均能耗=38.2, 可行解=60/60, 缓存命中率=28.8%
DEBUG - 第16代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第17代进化完成: 平均能耗=38.1, 可行解=60/60, 缓存命中率=28.9%
DEBUG - 第17代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第18代进化完成: 平均能耗=38.3, 可行解=60/60, 缓存命中率=28.3%
DEBUG - 第18代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第19代进化完成: 平均能耗=38.4, 可行解=60/60, 缓存命中率=28.1%
DEBUG - 第19代完成: 最佳能耗=35.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第20代进化完成: 平均能耗=38.5, 可行解=60/60, 缓存命中率=27.6%
DEBUG - 第20代完成: 最佳能耗=35.3
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=60, 参考点数量=110
DEBUG - 创建真正随机个体: gen0_random0, 2个窗户
DEBUG - 创建真正随机个体: gen0_random1, 1个窗户
DEBUG - 创建真正随机个体: gen0_random2, 7个窗户
DEBUG - 创建真正随机个体: gen0_random3, 5个窗户
DEBUG - 创建真正随机个体: gen0_random4, 2个窗户
DEBUG - 创建真正随机个体: gen0_random5, 6个窗户
DEBUG - 创建真正随机个体: gen0_random6, 6个窗户
DEBUG - 创建真正随机个体: gen0_random7, 2个窗户
DEBUG - 创建真正随机个体: gen0_random8, 8个窗户
DEBUG - 创建真正随机个体: gen0_random9, 4个窗户
DEBUG - 创建真正随机个体: gen0_random10, 5个窗户
DEBUG - 创建真正随机个体: gen0_random11, 4个窗户
DEBUG - 创建真正随机个体: gen0_random12, 3个窗户
DEBUG - 创建真正随机个体: gen0_random13, 2个窗户
DEBUG - 创建真正随机个体: gen0_random14, 1个窗户
DEBUG - 创建真正随机个体: gen0_random15, 1个窗户
DEBUG - 创建真正随机个体: gen0_random16, 4个窗户
DEBUG - 创建真正随机个体: gen0_random17, 4个窗户
DEBUG - 创建真正随机个体: gen0_random18, 5个窗户
DEBUG - 创建真正随机个体: gen0_random19, 8个窗户
DEBUG - 创建真正随机个体: gen0_random20, 3个窗户
DEBUG - 创建真正随机个体: gen0_random21, 8个窗户
DEBUG - 创建真正随机个体: gen0_random22, 8个窗户
DEBUG - 创建真正随机个体: gen0_random23, 7个窗户
DEBUG - 创建真正随机个体: gen0_extreme0, 1个窗户
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
DEBUG - 创建真正随机个体: gen0_extreme1, 2个窗户
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
DEBUG - 创建真正随机个体: gen0_extreme2, 6个窗户
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
DEBUG - 创建真正随机个体: gen0_extreme3, 1个窗户
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
DEBUG - 创建真正随机个体: gen0_extreme4, 2个窗户
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
DEBUG - 创建真正随机个体: gen0_extreme5, 6个窗户
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
DEBUG - 创建真正随机个体: gen0_extreme6, 1个窗户
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
DEBUG - 创建真正随机个体: gen0_extreme7, 2个窗户
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
DEBUG - 创建真正随机个体: gen0_extreme8, 6个窗户
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
DEBUG - 创建真正随机个体: gen0_extreme9, 1个窗户
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
DEBUG - 创建真正随机个体: gen0_extreme10, 2个窗户
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
DEBUG - 创建真正随机个体: gen0_extreme11, 6个窗户
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
DEBUG - 创建真实数据变异个体: gen0_realvar0, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar1, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar2, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar3, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar4, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar5, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar6, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar7, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar8, 变异率60%
INFO - 智能种群初始化完成: 60 个个体 (随机:24, 启发式:15, 极端:12)
INFO - 种群评估完成: 60 个个体
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第1代进化完成: 平均能耗=35.8, 可行解=60/60, 缓存命中率=25.8%
DEBUG - 第1代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第2代进化完成: 平均能耗=35.9, 可行解=60/60, 缓存命中率=28.3%
DEBUG - 第2代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第3代进化完成: 平均能耗=36.5, 可行解=60/60, 缓存命中率=29.6%
DEBUG - 第3代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第4代进化完成: 平均能耗=36.2, 可行解=60/60, 缓存命中率=33.7%
DEBUG - 第4代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第5代进化完成: 平均能耗=36.0, 可行解=60/60, 缓存命中率=36.7%
DEBUG - 第5代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第6代进化完成: 平均能耗=35.7, 可行解=60/60, 缓存命中率=37.9%
DEBUG - 第6代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第7代进化完成: 平均能耗=36.2, 可行解=60/60, 缓存命中率=37.7%
DEBUG - 第7代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第8代进化完成: 平均能耗=37.2, 可行解=60/60, 缓存命中率=39.6%
DEBUG - 第8代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第9代进化完成: 平均能耗=36.5, 可行解=60/60, 缓存命中率=41.2%
DEBUG - 第9代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第10代进化完成: 平均能耗=37.1, 可行解=60/60, 缓存命中率=40.9%
DEBUG - 第10代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第11代进化完成: 平均能耗=36.4, 可行解=60/60, 缓存命中率=42.4%
DEBUG - 第11代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第12代进化完成: 平均能耗=36.5, 可行解=60/60, 缓存命中率=43.2%
DEBUG - 第12代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第13代进化完成: 平均能耗=35.9, 可行解=60/60, 缓存命中率=43.7%
DEBUG - 第13代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第14代进化完成: 平均能耗=36.6, 可行解=60/60, 缓存命中率=44.9%
DEBUG - 第14代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第15代进化完成: 平均能耗=36.2, 可行解=60/60, 缓存命中率=46.2%
DEBUG - 第15代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第16代进化完成: 平均能耗=36.0, 可行解=60/60, 缓存命中率=47.1%
DEBUG - 第16代完成: 最佳能耗=21.0
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第17代进化完成: 平均能耗=36.1, 可行解=60/60, 缓存命中率=47.5%
DEBUG - 第17代完成: 最佳能耗=21.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=60, 参考点数量=110
DEBUG - 创建约束兼容随机个体: gen0_random0, 1个窗户
DEBUG - 创建约束兼容随机个体: gen0_random1, 1个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random2, 2个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random3, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random4, 1个窗户
DEBUG - 受约束限制，停在1个窗户
DEBUG - 创建约束兼容随机个体: gen0_random5, 1个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random6, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random7, 1个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random8, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random9, 2个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random10, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random11, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random12, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random13, 1个窗户
DEBUG - 创建约束兼容随机个体: gen0_random14, 1个窗户
DEBUG - 创建约束兼容随机个体: gen0_random15, 1个窗户
DEBUG - 创建约束兼容随机个体: gen0_random16, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random17, 2个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random18, 2个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random19, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random20, 2个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random21, 2个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random22, 2个窗户
DEBUG - 受约束限制，停在2个窗户
DEBUG - 创建约束兼容随机个体: gen0_random23, 2个窗户
DEBUG - 创建约束兼容随机个体: gen0_extreme0, 1个窗户
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
DEBUG - 创建约束兼容随机个体: gen0_extreme1, 2个窗户
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
DEBUG - 创建约束兼容随机个体: gen0_extreme2, 2个窗户
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
DEBUG - 创建约束兼容随机个体: gen0_extreme3, 1个窗户
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
DEBUG - 创建约束兼容随机个体: gen0_extreme4, 2个窗户
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
DEBUG - 创建约束兼容随机个体: gen0_extreme5, 2个窗户
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
DEBUG - 创建约束兼容随机个体: gen0_extreme6, 1个窗户
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
DEBUG - 创建约束兼容随机个体: gen0_extreme7, 2个窗户
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
DEBUG - 创建约束兼容随机个体: gen0_extreme8, 2个窗户
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
DEBUG - 创建约束兼容随机个体: gen0_extreme9, 1个窗户
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
DEBUG - 创建约束兼容随机个体: gen0_extreme10, 2个窗户
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
DEBUG - 创建约束兼容随机个体: gen0_extreme11, 2个窗户
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
DEBUG - 创建真实数据变异个体: gen0_realvar0, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar1, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar2, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar3, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar4, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar5, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar6, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar7, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar8, 变异率60%
INFO - 智能种群初始化完成: 60 个个体 (随机:24, 启发式:15, 极端:12)
INFO - 种群评估完成: 60 个个体
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第1代进化完成: 平均能耗=44.7, 可行解=60/60, 缓存命中率=46.7%
DEBUG - 第1代完成: 最佳能耗=38.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第2代进化完成: 平均能耗=44.4, 可行解=60/60, 缓存命中率=48.9%
DEBUG - 第2代完成: 最佳能耗=38.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第3代进化完成: 平均能耗=44.9, 可行解=60/60, 缓存命中率=50.8%
DEBUG - 第3代完成: 最佳能耗=38.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第4代进化完成: 平均能耗=45.3, 可行解=60/60, 缓存命中率=53.7%
DEBUG - 第4代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第5代进化完成: 平均能耗=44.9, 可行解=60/60, 缓存命中率=54.7%
DEBUG - 第5代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第6代进化完成: 平均能耗=45.0, 可行解=60/60, 缓存命中率=54.5%
DEBUG - 第6代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第7代进化完成: 平均能耗=44.5, 可行解=60/60, 缓存命中率=54.4%
DEBUG - 第7代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第8代进化完成: 平均能耗=44.7, 可行解=60/60, 缓存命中率=53.5%
DEBUG - 第8代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第9代进化完成: 平均能耗=44.6, 可行解=60/60, 缓存命中率=52.0%
DEBUG - 第9代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第10代进化完成: 平均能耗=44.6, 可行解=60/60, 缓存命中率=51.2%
DEBUG - 第10代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第11代进化完成: 平均能耗=44.4, 可行解=60/60, 缓存命中率=50.7%
DEBUG - 第11代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第12代进化完成: 平均能耗=44.4, 可行解=60/60, 缓存命中率=50.3%
DEBUG - 第12代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第13代进化完成: 平均能耗=44.2, 可行解=60/60, 缓存命中率=50.6%
DEBUG - 第13代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第14代进化完成: 平均能耗=44.7, 可行解=60/60, 缓存命中率=51.0%
DEBUG - 第14代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第15代进化完成: 平均能耗=44.4, 可行解=60/60, 缓存命中率=51.5%
DEBUG - 第15代完成: 最佳能耗=38.3
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第16代进化完成: 平均能耗=44.2, 可行解=60/60, 缓存命中率=50.9%
DEBUG - 第16代完成: 最佳能耗=38.3
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=60, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
DEBUG - 创建真实数据变异个体: gen0_realvar0, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar1, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar2, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar3, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar4, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar5, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar6, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar7, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar8, 变异率60%
INFO - 智能种群初始化完成: 60 个个体 (随机:24, 启发式:15, 极端:12)
INFO - 种群评估完成: 60 个个体
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第1代进化完成: 平均能耗=41.4, 可行解=60/60, 缓存命中率=49.2%
DEBUG - 第1代完成: 最佳能耗=36.2
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第2代进化完成: 平均能耗=42.3, 可行解=60/60, 缓存命中率=39.4%
DEBUG - 第2代完成: 最佳能耗=36.4
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第3代进化完成: 平均能耗=42.3, 可行解=60/60, 缓存命中率=35.4%
DEBUG - 第3代完成: 最佳能耗=36.4
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第4代进化完成: 平均能耗=42.3, 可行解=60/60, 缓存命中率=32.7%
DEBUG - 第4代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第5代进化完成: 平均能耗=42.2, 可行解=60/60, 缓存命中率=30.6%
DEBUG - 第5代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第6代进化完成: 平均能耗=43.0, 可行解=60/60, 缓存命中率=32.1%
DEBUG - 第6代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第7代进化完成: 平均能耗=42.7, 可行解=60/60, 缓存命中率=31.7%
DEBUG - 第7代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第8代进化完成: 平均能耗=42.6, 可行解=60/60, 缓存命中率=29.8%
DEBUG - 第8代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第9代进化完成: 平均能耗=42.3, 可行解=60/60, 缓存命中率=28.7%
DEBUG - 第9代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第10代进化完成: 平均能耗=42.8, 可行解=60/60, 缓存命中率=28.6%
DEBUG - 第10代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第11代进化完成: 平均能耗=42.5, 可行解=60/60, 缓存命中率=28.7%
DEBUG - 第11代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第12代进化完成: 平均能耗=43.2, 可行解=60/60, 缓存命中率=27.8%
DEBUG - 第12代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第13代进化完成: 平均能耗=42.7, 可行解=60/60, 缓存命中率=27.9%
DEBUG - 第13代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第14代进化完成: 平均能耗=43.0, 可行解=60/60, 缓存命中率=27.6%
DEBUG - 第14代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第15代进化完成: 平均能耗=43.4, 可行解=60/60, 缓存命中率=27.2%
DEBUG - 第15代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第16代进化完成: 平均能耗=43.2, 可行解=60/60, 缓存命中率=27.0%
DEBUG - 第16代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第17代进化完成: 平均能耗=42.7, 可行解=60/60, 缓存命中率=26.6%
DEBUG - 第17代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第18代进化完成: 平均能耗=42.9, 可行解=60/60, 缓存命中率=26.4%
DEBUG - 第18代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第19代进化完成: 平均能耗=43.0, 可行解=60/60, 缓存命中率=26.0%
DEBUG - 第19代完成: 最佳能耗=36.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第20代进化完成: 平均能耗=42.8, 可行解=60/60, 缓存命中率=25.7%
DEBUG - 第20代完成: 最佳能耗=36.6
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=80, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme12, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme13, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme14, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme15, 目标=energy
DEBUG - 创建真实数据变异个体: gen0_realvar0, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar1, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar2, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar3, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar4, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar5, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar6, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar7, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar8, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar9, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar10, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar11, 变异率60%
INFO - 智能种群初始化完成: 80 个个体 (随机:32, 启发式:20, 极端:16)
INFO - 种群评估完成: 80 个个体
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第1代进化完成: 平均能耗=40.5, 可行解=80/80, 缓存命中率=55.0%
DEBUG - 第1代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第2代进化完成: 平均能耗=41.0, 可行解=80/80, 缓存命中率=50.4%
DEBUG - 第2代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第3代进化完成: 平均能耗=40.9, 可行解=80/80, 缓存命中率=45.0%
DEBUG - 第3代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第4代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=40.2%
DEBUG - 第4代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第5代进化完成: 平均能耗=41.0, 可行解=80/80, 缓存命中率=36.2%
DEBUG - 第5代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第6代进化完成: 平均能耗=41.1, 可行解=80/80, 缓存命中率=34.8%
DEBUG - 第6代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第7代进化完成: 平均能耗=41.2, 可行解=80/80, 缓存命中率=33.6%
DEBUG - 第7代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第8代进化完成: 平均能耗=40.9, 可行解=80/80, 缓存命中率=32.2%
DEBUG - 第8代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第9代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=32.0%
DEBUG - 第9代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第10代进化完成: 平均能耗=40.6, 可行解=80/80, 缓存命中率=30.7%
DEBUG - 第10代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第11代进化完成: 平均能耗=41.4, 可行解=80/80, 缓存命中率=30.2%
DEBUG - 第11代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第12代进化完成: 平均能耗=41.6, 可行解=80/80, 缓存命中率=30.0%
DEBUG - 第12代完成: 最佳能耗=32.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第13代进化完成: 平均能耗=41.2, 可行解=80/80, 缓存命中率=28.7%
DEBUG - 第13代完成: 最佳能耗=32.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第14代进化完成: 平均能耗=41.5, 可行解=80/80, 缓存命中率=27.4%
DEBUG - 第14代完成: 最佳能耗=32.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第15代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=27.4%
DEBUG - 第15代完成: 最佳能耗=32.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第16代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=26.7%
DEBUG - 第16代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第17代进化完成: 平均能耗=40.6, 可行解=80/80, 缓存命中率=26.1%
DEBUG - 第17代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第18代进化完成: 平均能耗=40.5, 可行解=80/80, 缓存命中率=25.6%
DEBUG - 第18代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第19代进化完成: 平均能耗=40.2, 可行解=80/80, 缓存命中率=25.4%
DEBUG - 第19代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第20代进化完成: 平均能耗=40.4, 可行解=80/80, 缓存命中率=24.8%
DEBUG - 第20代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第21代进化完成: 平均能耗=40.3, 可行解=80/80, 缓存命中率=24.3%
DEBUG - 第21代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第22代进化完成: 平均能耗=40.2, 可行解=80/80, 缓存命中率=23.9%
DEBUG - 第22代完成: 最佳能耗=30.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第23代进化完成: 平均能耗=40.6, 可行解=80/80, 缓存命中率=23.6%
DEBUG - 第23代完成: 最佳能耗=30.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第24代进化完成: 平均能耗=40.6, 可行解=80/80, 缓存命中率=23.4%
DEBUG - 第24代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第25代进化完成: 平均能耗=40.9, 可行解=80/80, 缓存命中率=23.2%
DEBUG - 第25代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第26代进化完成: 平均能耗=41.1, 可行解=80/80, 缓存命中率=22.8%
DEBUG - 第26代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第27代进化完成: 平均能耗=41.1, 可行解=80/80, 缓存命中率=22.5%
DEBUG - 第27代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第28代进化完成: 平均能耗=41.0, 可行解=80/80, 缓存命中率=22.6%
DEBUG - 第28代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第29代进化完成: 平均能耗=40.4, 可行解=80/80, 缓存命中率=22.5%
DEBUG - 第29代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第30代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=22.2%
DEBUG - 第30代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第31代进化完成: 平均能耗=41.0, 可行解=80/80, 缓存命中率=22.0%
DEBUG - 第31代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第32代进化完成: 平均能耗=41.2, 可行解=80/80, 缓存命中率=21.6%
DEBUG - 第32代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第33代进化完成: 平均能耗=40.9, 可行解=80/80, 缓存命中率=21.5%
DEBUG - 第33代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第34代进化完成: 平均能耗=40.6, 可行解=80/80, 缓存命中率=21.2%
DEBUG - 第34代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第35代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=21.0%
DEBUG - 第35代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第36代进化完成: 平均能耗=41.0, 可行解=80/80, 缓存命中率=20.8%
DEBUG - 第36代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第37代进化完成: 平均能耗=41.0, 可行解=80/80, 缓存命中率=20.7%
DEBUG - 第37代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第38代进化完成: 平均能耗=40.8, 可行解=80/80, 缓存命中率=20.5%
DEBUG - 第38代完成: 最佳能耗=31.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第39代进化完成: 平均能耗=40.6, 可行解=80/80, 缓存命中率=20.6%
DEBUG - 第39代完成: 最佳能耗=31.2
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第40代进化完成: 平均能耗=40.5, 可行解=80/80, 缓存命中率=20.4%
DEBUG - 第40代完成: 最佳能耗=31.2
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第41代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=20.4%
DEBUG - 第41代完成: 最佳能耗=31.2
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=80, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme12, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme13, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme14, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme15, 目标=energy
DEBUG - 创建真实数据变异个体: gen0_realvar0, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar1, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar2, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar3, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar4, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar5, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar6, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar7, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar8, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar9, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar10, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar11, 变异率60%
INFO - 智能种群初始化完成: 80 个个体 (随机:32, 启发式:20, 极端:16)
INFO - 种群评估完成: 80 个个体
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第1代进化完成: 平均能耗=40.5, 可行解=80/80, 缓存命中率=55.0%
DEBUG - 第1代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第2代进化完成: 平均能耗=40.8, 可行解=80/80, 缓存命中率=49.6%
DEBUG - 第2代完成: 最佳能耗=32.3
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第3代进化完成: 平均能耗=40.6, 可行解=80/80, 缓存命中率=42.5%
DEBUG - 第3代完成: 最佳能耗=32.0
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第4代进化完成: 平均能耗=41.3, 可行解=80/80, 缓存命中率=37.2%
DEBUG - 第4代完成: 最佳能耗=32.0
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第5代进化完成: 平均能耗=40.5, 可行解=80/80, 缓存命中率=34.2%
DEBUG - 第5代完成: 最佳能耗=32.0
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第6代进化完成: 平均能耗=41.4, 可行解=80/80, 缓存命中率=32.3%
DEBUG - 第6代完成: 最佳能耗=32.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第7代进化完成: 平均能耗=40.5, 可行解=80/80, 缓存命中率=30.9%
DEBUG - 第7代完成: 最佳能耗=32.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第8代进化完成: 平均能耗=40.9, 可行解=80/80, 缓存命中率=29.9%
DEBUG - 第8代完成: 最佳能耗=32.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第9代进化完成: 平均能耗=40.5, 可行解=80/80, 缓存命中率=28.4%
DEBUG - 第9代完成: 最佳能耗=32.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第10代进化完成: 平均能耗=40.9, 可行解=80/80, 缓存命中率=27.6%
DEBUG - 第10代完成: 最佳能耗=32.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第11代进化完成: 平均能耗=41.3, 可行解=80/80, 缓存命中率=27.3%
DEBUG - 第11代完成: 最佳能耗=32.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第12代进化完成: 平均能耗=41.5, 可行解=80/80, 缓存命中率=27.3%
DEBUG - 第12代完成: 最佳能耗=32.1
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第13代进化完成: 平均能耗=40.2, 可行解=80/80, 缓存命中率=26.4%
DEBUG - 第13代完成: 最佳能耗=31.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第14代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=25.6%
DEBUG - 第14代完成: 最佳能耗=31.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第15代进化完成: 平均能耗=40.7, 可行解=80/80, 缓存命中率=24.5%
DEBUG - 第15代完成: 最佳能耗=31.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第16代进化完成: 平均能耗=41.2, 可行解=80/80, 缓存命中率=23.7%
DEBUG - 第16代完成: 最佳能耗=31.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第17代进化完成: 平均能耗=40.8, 可行解=80/80, 缓存命中率=23.0%
DEBUG - 第17代完成: 最佳能耗=31.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第18代进化完成: 平均能耗=41.0, 可行解=80/80, 缓存命中率=22.8%
DEBUG - 第18代完成: 最佳能耗=31.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第19代进化完成: 平均能耗=40.1, 可行解=80/80, 缓存命中率=22.3%
DEBUG - 第19代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第20代进化完成: 平均能耗=40.1, 可行解=80/80, 缓存命中率=22.1%
DEBUG - 第20代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第21代进化完成: 平均能耗=40.3, 可行解=80/80, 缓存命中率=21.5%
DEBUG - 第21代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第22代进化完成: 平均能耗=40.3, 可行解=80/80, 缓存命中率=21.5%
DEBUG - 第22代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第23代进化完成: 平均能耗=40.1, 可行解=80/80, 缓存命中率=21.0%
DEBUG - 第23代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第24代进化完成: 平均能耗=40.3, 可行解=80/80, 缓存命中率=20.8%
DEBUG - 第24代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第25代进化完成: 平均能耗=40.0, 可行解=80/80, 缓存命中率=20.8%
DEBUG - 第25代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第26代进化完成: 平均能耗=40.1, 可行解=80/80, 缓存命中率=20.2%
DEBUG - 第26代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第27代进化完成: 平均能耗=39.8, 可行解=80/80, 缓存命中率=19.8%
DEBUG - 第27代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第28代进化完成: 平均能耗=40.3, 可行解=80/80, 缓存命中率=19.5%
DEBUG - 第28代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第29代进化完成: 平均能耗=40.1, 可行解=80/80, 缓存命中率=19.3%
DEBUG - 第29代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第30代进化完成: 平均能耗=39.9, 可行解=80/80, 缓存命中率=19.1%
DEBUG - 第30代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第31代进化完成: 平均能耗=40.2, 可行解=80/80, 缓存命中率=19.0%
DEBUG - 第31代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第32代进化完成: 平均能耗=39.8, 可行解=80/80, 缓存命中率=18.5%
DEBUG - 第32代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第33代进化完成: 平均能耗=40.0, 可行解=80/80, 缓存命中率=18.9%
DEBUG - 第33代完成: 最佳能耗=28.4
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第34代进化完成: 平均能耗=40.2, 可行解=80/80, 缓存命中率=18.8%
DEBUG - 第34代完成: 最佳能耗=28.4
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=80, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme12, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme13, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme14, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme15, 目标=energy
DEBUG - 创建真实数据变异个体: gen0_realvar0, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar1, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar2, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar3, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar4, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar5, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar6, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar7, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar8, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar9, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar10, 变异率60%
DEBUG - 创建真实数据变异个体: gen0_realvar11, 变异率60%
INFO - 智能种群初始化完成: 80 个个体 (随机:32, 启发式:20, 极端:16)
INFO - 种群评估完成: 80 个个体
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第1代进化完成: 平均能耗=122.9, 可行解=80/80, 缓存命中率=55.0%
DEBUG - 第1代完成: 最佳能耗=106.5
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第2代进化完成: 平均能耗=112.1, 可行解=80/80, 缓存命中率=45.8%
DEBUG - 第2代完成: 最佳能耗=106.5
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第3代进化完成: 平均能耗=107.7, 可行解=80/80, 缓存命中率=46.9%
DEBUG - 第3代完成: 最佳能耗=105.9
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第4代进化完成: 平均能耗=107.5, 可行解=80/80, 缓存命中率=50.2%
DEBUG - 第4代完成: 最佳能耗=105.9
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第5代进化完成: 平均能耗=107.3, 可行解=80/80, 缓存命中率=49.8%
DEBUG - 第5代完成: 最佳能耗=105.9
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第6代进化完成: 平均能耗=107.1, 可行解=80/80, 缓存命中率=48.4%
DEBUG - 第6代完成: 最佳能耗=105.9
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第7代进化完成: 平均能耗=106.9, 可行解=80/80, 缓存命中率=45.9%
DEBUG - 第7代完成: 最佳能耗=105.9
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第8代进化完成: 平均能耗=106.7, 可行解=80/80, 缓存命中率=43.2%
DEBUG - 第8代完成: 最佳能耗=105.9
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第9代进化完成: 平均能耗=106.5, 可行解=80/80, 缓存命中率=41.6%
DEBUG - 第9代完成: 最佳能耗=105.7
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第10代进化完成: 平均能耗=106.3, 可行解=80/80, 缓存命中率=39.5%
DEBUG - 第10代完成: 最佳能耗=105.7
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第11代进化完成: 平均能耗=106.2, 可行解=80/80, 缓存命中率=39.5%
DEBUG - 第11代完成: 最佳能耗=105.0
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第12代进化完成: 平均能耗=106.0, 可行解=80/80, 缓存命中率=39.4%
DEBUG - 第12代完成: 最佳能耗=105.0
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第13代进化完成: 平均能耗=105.8, 可行解=80/80, 缓存命中率=39.0%
DEBUG - 第13代完成: 最佳能耗=105.0
INFO - 种群评估完成: 80 个个体
DEBUG - 改进NSGA-III环境选择完成: 80 个个体
INFO - 第14代进化完成: 平均能耗=105.7, 可行解=80/80, 缓存命中率=38.6%
DEBUG - 第14代完成: 最佳能耗=105.0
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=120, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme12, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme13, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme14, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme15, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme16, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme17, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme18, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme19, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme20, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme21, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme22, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme23, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme24, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme25, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme26, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme27, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme28, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme29, 目标=cost
INFO - 智能种群初始化完成: 120 个个体 (随机:36, 启发式:30, 极端:30)
INFO - 种群评估完成: 120 个个体
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第1代进化完成: 平均能耗=32.7, 可行解=120/120, 缓存命中率=42.9%
DEBUG - 第1代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第2代进化完成: 平均能耗=32.7, 可行解=120/120, 缓存命中率=39.7%
DEBUG - 第2代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第3代进化完成: 平均能耗=32.7, 可行解=120/120, 缓存命中率=36.9%
DEBUG - 第3代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第4代进化完成: 平均能耗=32.6, 可行解=120/120, 缓存命中率=34.7%
DEBUG - 第4代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第5代进化完成: 平均能耗=32.7, 可行解=120/120, 缓存命中率=32.8%
DEBUG - 第5代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第6代进化完成: 平均能耗=32.5, 可行解=120/120, 缓存命中率=30.8%
DEBUG - 第6代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第7代进化完成: 平均能耗=32.5, 可行解=120/120, 缓存命中率=28.3%
DEBUG - 第7代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第8代进化完成: 平均能耗=32.7, 可行解=120/120, 缓存命中率=27.4%
DEBUG - 第8代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第9代进化完成: 平均能耗=32.6, 可行解=120/120, 缓存命中率=26.8%
DEBUG - 第9代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第10代进化完成: 平均能耗=32.4, 可行解=120/120, 缓存命中率=25.3%
DEBUG - 第10代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第11代进化完成: 平均能耗=32.4, 可行解=120/120, 缓存命中率=24.2%
DEBUG - 第11代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第12代进化完成: 平均能耗=32.3, 可行解=120/120, 缓存命中率=23.3%
DEBUG - 第12代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第13代进化完成: 平均能耗=32.4, 可行解=120/120, 缓存命中率=23.0%
DEBUG - 第13代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第14代进化完成: 平均能耗=32.2, 可行解=120/120, 缓存命中率=22.6%
DEBUG - 第14代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第15代进化完成: 平均能耗=32.1, 可行解=120/120, 缓存命中率=22.8%
DEBUG - 第15代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第16代进化完成: 平均能耗=32.2, 可行解=120/120, 缓存命中率=22.6%
DEBUG - 第16代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第17代进化完成: 平均能耗=32.2, 可行解=120/120, 缓存命中率=22.1%
DEBUG - 第17代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第18代进化完成: 平均能耗=32.0, 可行解=120/120, 缓存命中率=21.6%
DEBUG - 第18代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第19代进化完成: 平均能耗=32.1, 可行解=120/120, 缓存命中率=21.6%
DEBUG - 第19代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第20代进化完成: 平均能耗=32.1, 可行解=120/120, 缓存命中率=21.6%
DEBUG - 第20代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第21代进化完成: 平均能耗=31.9, 可行解=120/120, 缓存命中率=21.3%
DEBUG - 第21代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第22代进化完成: 平均能耗=32.0, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第22代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第23代进化完成: 平均能耗=31.9, 可行解=120/120, 缓存命中率=21.0%
DEBUG - 第23代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第24代进化完成: 平均能耗=31.6, 可行解=120/120, 缓存命中率=21.0%
DEBUG - 第24代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第25代进化完成: 平均能耗=31.5, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第25代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第26代进化完成: 平均能耗=31.8, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第26代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第27代进化完成: 平均能耗=31.6, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第27代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第28代进化完成: 平均能耗=31.6, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第28代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第29代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第29代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第30代进化完成: 平均能耗=31.4, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第30代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第31代进化完成: 平均能耗=31.4, 可行解=120/120, 缓存命中率=21.2%
DEBUG - 第31代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第32代进化完成: 平均能耗=31.4, 可行解=120/120, 缓存命中率=21.2%
DEBUG - 第32代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第33代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第33代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第34代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=21.2%
DEBUG - 第34代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第35代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=21.4%
DEBUG - 第35代完成: 最佳能耗=18.1
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=120, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme12, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme13, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme14, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme15, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme16, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme17, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme18, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme19, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme20, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme21, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme22, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme23, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme24, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme25, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme26, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme27, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme28, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme29, 目标=cost
INFO - 智能种群初始化完成: 120 个个体 (随机:36, 启发式:30, 极端:30)
INFO - 种群评估完成: 120 个个体
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第1代进化完成: 平均能耗=32.8, 可行解=120/120, 缓存命中率=42.5%
DEBUG - 第1代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第2代进化完成: 平均能耗=32.9, 可行解=120/120, 缓存命中率=37.2%
DEBUG - 第2代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第3代进化完成: 平均能耗=32.6, 可行解=120/120, 缓存命中率=34.2%
DEBUG - 第3代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第4代进化完成: 平均能耗=32.7, 可行解=120/120, 缓存命中率=30.3%
DEBUG - 第4代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第5代进化完成: 平均能耗=32.8, 可行解=120/120, 缓存命中率=29.2%
DEBUG - 第5代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第6代进化完成: 平均能耗=32.9, 可行解=120/120, 缓存命中率=28.3%
DEBUG - 第6代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第7代进化完成: 平均能耗=32.6, 可行解=120/120, 缓存命中率=26.8%
DEBUG - 第7代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第8代进化完成: 平均能耗=32.6, 可行解=120/120, 缓存命中率=26.4%
DEBUG - 第8代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第9代进化完成: 平均能耗=32.8, 可行解=120/120, 缓存命中率=25.5%
DEBUG - 第9代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第10代进化完成: 平均能耗=32.6, 可行解=120/120, 缓存命中率=24.9%
DEBUG - 第10代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第11代进化完成: 平均能耗=32.8, 可行解=120/120, 缓存命中率=24.1%
DEBUG - 第11代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第12代进化完成: 平均能耗=32.6, 可行解=120/120, 缓存命中率=23.9%
DEBUG - 第12代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第13代进化完成: 平均能耗=32.5, 可行解=120/120, 缓存命中率=23.5%
DEBUG - 第13代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第14代进化完成: 平均能耗=32.5, 可行解=120/120, 缓存命中率=23.6%
DEBUG - 第14代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第15代进化完成: 平均能耗=32.4, 可行解=120/120, 缓存命中率=23.1%
DEBUG - 第15代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第16代进化完成: 平均能耗=32.4, 可行解=120/120, 缓存命中率=22.6%
DEBUG - 第16代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第17代进化完成: 平均能耗=32.4, 可行解=120/120, 缓存命中率=22.3%
DEBUG - 第17代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第18代进化完成: 平均能耗=32.3, 可行解=120/120, 缓存命中率=22.1%
DEBUG - 第18代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第19代进化完成: 平均能耗=32.4, 可行解=120/120, 缓存命中率=22.0%
DEBUG - 第19代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第20代进化完成: 平均能耗=32.2, 可行解=120/120, 缓存命中率=21.9%
DEBUG - 第20代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第21代进化完成: 平均能耗=32.3, 可行解=120/120, 缓存命中率=21.4%
DEBUG - 第21代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第22代进化完成: 平均能耗=32.1, 可行解=120/120, 缓存命中率=21.1%
DEBUG - 第22代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第23代进化完成: 平均能耗=32.2, 可行解=120/120, 缓存命中率=20.7%
DEBUG - 第23代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第24代进化完成: 平均能耗=32.0, 可行解=120/120, 缓存命中率=20.7%
DEBUG - 第24代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第25代进化完成: 平均能耗=32.2, 可行解=120/120, 缓存命中率=20.4%
DEBUG - 第25代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第26代进化完成: 平均能耗=32.1, 可行解=120/120, 缓存命中率=20.0%
DEBUG - 第26代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第27代进化完成: 平均能耗=31.7, 可行解=120/120, 缓存命中率=19.6%
DEBUG - 第27代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第28代进化完成: 平均能耗=31.9, 可行解=120/120, 缓存命中率=19.3%
DEBUG - 第28代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第29代进化完成: 平均能耗=31.8, 可行解=120/120, 缓存命中率=19.2%
DEBUG - 第29代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第30代进化完成: 平均能耗=31.8, 可行解=120/120, 缓存命中率=19.3%
DEBUG - 第30代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第31代进化完成: 平均能耗=31.6, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第31代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第32代进化完成: 平均能耗=31.6, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第32代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第33代进化完成: 平均能耗=31.5, 可行解=120/120, 缓存命中率=18.8%
DEBUG - 第33代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第34代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第34代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第35代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第35代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第36代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第36代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第37代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.2%
DEBUG - 第37代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第38代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.2%
DEBUG - 第38代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第39代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.2%
DEBUG - 第39代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第40代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第40代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第41代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第41代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第42代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=18.9%
DEBUG - 第42代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第43代进化完成: 平均能耗=31.4, 可行解=120/120, 缓存命中率=18.9%
DEBUG - 第43代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第44代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.2%
DEBUG - 第44代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第45代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.2%
DEBUG - 第45代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第46代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第46代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第47代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第47代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第48代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第48代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第49代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第49代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第50代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第50代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第51代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第51代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第52代进化完成: 平均能耗=31.4, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第52代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第53代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.0%
DEBUG - 第53代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第54代进化完成: 平均能耗=31.5, 可行解=120/120, 缓存命中率=19.1%
DEBUG - 第54代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第55代进化完成: 平均能耗=31.4, 可行解=120/120, 缓存命中率=19.2%
DEBUG - 第55代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第56代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.3%
DEBUG - 第56代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第57代进化完成: 平均能耗=31.4, 可行解=120/120, 缓存命中率=19.3%
DEBUG - 第57代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第58代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.4%
DEBUG - 第58代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第59代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.3%
DEBUG - 第59代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第60代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.4%
DEBUG - 第60代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第61代进化完成: 平均能耗=31.3, 可行解=120/120, 缓存命中率=19.4%
DEBUG - 第61代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第62代进化完成: 平均能耗=31.0, 可行解=120/120, 缓存命中率=19.5%
DEBUG - 第62代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第63代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.4%
DEBUG - 第63代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第64代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.5%
DEBUG - 第64代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第65代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.6%
DEBUG - 第65代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第66代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.6%
DEBUG - 第66代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第67代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.7%
DEBUG - 第67代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第68代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.6%
DEBUG - 第68代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第69代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.8%
DEBUG - 第69代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第70代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=19.9%
DEBUG - 第70代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第71代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=20.0%
DEBUG - 第71代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第72代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=20.1%
DEBUG - 第72代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第73代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=19.9%
DEBUG - 第73代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第74代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=20.0%
DEBUG - 第74代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第75代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=20.0%
DEBUG - 第75代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第76代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=20.1%
DEBUG - 第76代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第77代进化完成: 平均能耗=31.0, 可行解=120/120, 缓存命中率=20.1%
DEBUG - 第77代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第78代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=20.1%
DEBUG - 第78代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第79代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=20.1%
DEBUG - 第79代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第80代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=20.1%
DEBUG - 第80代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第81代进化完成: 平均能耗=31.2, 可行解=120/120, 缓存命中率=20.2%
DEBUG - 第81代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第82代进化完成: 平均能耗=31.0, 可行解=120/120, 缓存命中率=20.2%
DEBUG - 第82代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第83代进化完成: 平均能耗=31.0, 可行解=120/120, 缓存命中率=20.3%
DEBUG - 第83代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第84代进化完成: 平均能耗=31.0, 可行解=120/120, 缓存命中率=20.3%
DEBUG - 第84代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第85代进化完成: 平均能耗=31.1, 可行解=120/120, 缓存命中率=20.4%
DEBUG - 第85代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第86代进化完成: 平均能耗=31.0, 可行解=120/120, 缓存命中率=20.4%
DEBUG - 第86代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第87代进化完成: 平均能耗=30.9, 可行解=120/120, 缓存命中率=20.4%
DEBUG - 第87代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第88代进化完成: 平均能耗=31.0, 可行解=120/120, 缓存命中率=20.4%
DEBUG - 第88代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第89代进化完成: 平均能耗=30.9, 可行解=120/120, 缓存命中率=20.3%
DEBUG - 第89代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第90代进化完成: 平均能耗=30.8, 可行解=120/120, 缓存命中率=20.3%
DEBUG - 第90代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第91代进化完成: 平均能耗=30.7, 可行解=120/120, 缓存命中率=20.4%
DEBUG - 第91代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第92代进化完成: 平均能耗=30.7, 可行解=120/120, 缓存命中率=20.5%
DEBUG - 第92代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第93代进化完成: 平均能耗=30.8, 可行解=120/120, 缓存命中率=20.5%
DEBUG - 第93代完成: 最佳能耗=18.1
INFO - 种群评估完成: 120 个个体
DEBUG - 改进NSGA-III环境选择完成: 120 个个体
INFO - 第94代进化完成: 平均能耗=30.9, 可行解=120/120, 缓存命中率=20.6%
DEBUG - 第94代完成: 最佳能耗=18.1
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=200, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme12, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme13, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme14, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme15, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme16, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme17, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme18, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme19, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme20, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme21, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme22, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme23, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme24, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme25, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme26, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme27, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme28, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme29, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme30, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme31, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme32, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme33, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme34, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme35, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme36, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme37, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme38, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme39, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme40, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme41, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme42, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme43, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme44, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme45, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme46, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme47, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme48, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme49, 目标=thermal
INFO - 智能种群初始化完成: 200 个个体 (随机:60, 启发式:50, 极端:50)
INFO - 种群评估完成: 200 个个体
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 175 个个体
INFO - 第1代进化完成: 平均能耗=36.7, 可行解=175/175, 缓存命中率=22.2%
DEBUG - 第1代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第2代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=22.2%
DEBUG - 第2代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第3代进化完成: 平均能耗=36.9, 可行解=200/200, 缓存命中率=22.4%
DEBUG - 第3代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第4代进化完成: 平均能耗=36.9, 可行解=200/200, 缓存命中率=22.8%
DEBUG - 第4代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第5代进化完成: 平均能耗=36.9, 可行解=200/200, 缓存命中率=22.6%
DEBUG - 第5代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第6代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=21.6%
DEBUG - 第6代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第7代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=21.5%
DEBUG - 第7代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第8代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=20.4%
DEBUG - 第8代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第9代进化完成: 平均能耗=36.8, 可行解=200/200, 缓存命中率=19.7%
DEBUG - 第9代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第10代进化完成: 平均能耗=36.9, 可行解=200/200, 缓存命中率=19.3%
DEBUG - 第10代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第11代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=18.5%
DEBUG - 第11代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第12代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=18.1%
DEBUG - 第12代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第13代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=18.1%
DEBUG - 第13代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第14代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=18.1%
DEBUG - 第14代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第15代进化完成: 平均能耗=36.7, 可行解=200/200, 缓存命中率=18.0%
DEBUG - 第15代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第16代进化完成: 平均能耗=36.6, 可行解=200/200, 缓存命中率=18.0%
DEBUG - 第16代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第17代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=18.2%
DEBUG - 第17代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第18代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=18.2%
DEBUG - 第18代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第19代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=18.2%
DEBUG - 第19代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第20代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=18.0%
DEBUG - 第20代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第21代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=17.9%
DEBUG - 第21代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第22代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=17.7%
DEBUG - 第22代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第23代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=17.8%
DEBUG - 第23代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第24代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=17.9%
DEBUG - 第24代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第25代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=18.0%
DEBUG - 第25代完成: 最佳能耗=24.2
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第26代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=18.2%
DEBUG - 第26代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第27代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=18.4%
DEBUG - 第27代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第28代进化完成: 平均能耗=36.6, 可行解=200/200, 缓存命中率=18.4%
DEBUG - 第28代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第29代进化完成: 平均能耗=36.6, 可行解=200/200, 缓存命中率=18.6%
DEBUG - 第29代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第30代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=18.9%
DEBUG - 第30代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第31代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=18.8%
DEBUG - 第31代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第32代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=19.0%
DEBUG - 第32代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第33代进化完成: 平均能耗=36.5, 可行解=200/200, 缓存命中率=19.2%
DEBUG - 第33代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第34代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=19.2%
DEBUG - 第34代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第35代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=19.2%
DEBUG - 第35代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第36代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=19.4%
DEBUG - 第36代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第37代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=19.4%
DEBUG - 第37代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第38代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=19.6%
DEBUG - 第38代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第39代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=19.6%
DEBUG - 第39代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第40代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=19.8%
DEBUG - 第40代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第41代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=19.9%
DEBUG - 第41代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第42代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=20.2%
DEBUG - 第42代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第43代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=20.5%
DEBUG - 第43代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第44代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=20.5%
DEBUG - 第44代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第45代进化完成: 平均能耗=36.4, 可行解=200/200, 缓存命中率=20.6%
DEBUG - 第45代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第46代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=20.7%
DEBUG - 第46代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第47代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=20.8%
DEBUG - 第47代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第48代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=20.8%
DEBUG - 第48代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第49代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=20.9%
DEBUG - 第49代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第50代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=21.0%
DEBUG - 第50代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第51代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=21.0%
DEBUG - 第51代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第52代进化完成: 平均能耗=36.3, 可行解=200/200, 缓存命中率=21.1%
DEBUG - 第52代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第53代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=21.1%
DEBUG - 第53代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第54代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=21.1%
DEBUG - 第54代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第55代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=21.4%
DEBUG - 第55代完成: 最佳能耗=23.1
INFO - 种群评估完成: 200 个个体
DEBUG - 改进NSGA-III环境选择完成: 200 个个体
INFO - 第56代进化完成: 平均能耗=36.2, 可行解=200/200, 缓存命中率=21.5%
DEBUG - 第56代完成: 最佳能耗=23.1
INFO - 生成参考点: 110 个
INFO - 遗传算法操作器初始化完成: 种群大小=300, 参考点数量=110
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme0, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme1, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme2, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme3, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme4, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme5, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme6, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme7, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme8, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme9, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme10, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme11, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme12, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme13, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme14, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme15, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme16, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme17, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme18, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme19, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme20, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme21, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme22, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme23, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme24, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme25, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme26, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme27, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme28, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme29, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme30, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme31, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme32, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme33, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme34, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme35, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme36, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme37, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme38, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme39, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme40, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme41, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme42, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme43, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme44, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme45, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme46, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme47, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme48, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme49, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme50, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme51, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme52, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme53, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme54, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme55, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme56, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme57, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme58, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme59, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme60, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme61, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme62, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme63, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme64, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme65, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme66, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme67, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme68, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme69, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme70, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme71, 目标=cost
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme72, 目标=energy
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme73, 目标=thermal
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
DEBUG - 创建极端个体: gen0_extreme74, 目标=cost
INFO - 智能种群初始化完成: 300 个个体 (随机:90, 启发式:75, 极端:75)
INFO - 种群评估完成: 300 个个体
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 224 个个体
INFO - 第1代进化完成: 平均能耗=34.2, 可行解=224/224, 缓存命中率=13.0%
DEBUG - 第1代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 266 个个体
INFO - 第2代进化完成: 平均能耗=35.0, 可行解=266/266, 缓存命中率=9.3%
DEBUG - 第2代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 289 个个体
INFO - 第3代进化完成: 平均能耗=35.3, 可行解=289/289, 缓存命中率=7.5%
DEBUG - 第3代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第4代进化完成: 平均能耗=35.4, 可行解=300/300, 缓存命中率=6.2%
DEBUG - 第4代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第5代进化完成: 平均能耗=35.3, 可行解=300/300, 缓存命中率=5.8%
DEBUG - 第5代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第6代进化完成: 平均能耗=35.2, 可行解=300/300, 缓存命中率=5.5%
DEBUG - 第6代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第7代进化完成: 平均能耗=35.1, 可行解=300/300, 缓存命中率=5.0%
DEBUG - 第7代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第8代进化完成: 平均能耗=34.9, 可行解=300/300, 缓存命中率=4.9%
DEBUG - 第8代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第9代进化完成: 平均能耗=34.8, 可行解=300/300, 缓存命中率=4.8%
DEBUG - 第9代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第10代进化完成: 平均能耗=34.6, 可行解=300/300, 缓存命中率=4.7%
DEBUG - 第10代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第11代进化完成: 平均能耗=34.4, 可行解=300/300, 缓存命中率=4.7%
DEBUG - 第11代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第12代进化完成: 平均能耗=34.4, 可行解=300/300, 缓存命中率=4.5%
DEBUG - 第12代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第13代进化完成: 平均能耗=34.4, 可行解=300/300, 缓存命中率=4.5%
DEBUG - 第13代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第14代进化完成: 平均能耗=34.2, 可行解=300/300, 缓存命中率=4.4%
DEBUG - 第14代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第15代进化完成: 平均能耗=34.1, 可行解=300/300, 缓存命中率=4.4%
DEBUG - 第15代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第16代进化完成: 平均能耗=34.2, 可行解=300/300, 缓存命中率=4.5%
DEBUG - 第16代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第17代进化完成: 平均能耗=34.2, 可行解=300/300, 缓存命中率=4.5%
DEBUG - 第17代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第18代进化完成: 平均能耗=34.1, 可行解=300/300, 缓存命中率=4.5%
DEBUG - 第18代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第19代进化完成: 平均能耗=34.1, 可行解=300/300, 缓存命中率=4.6%
DEBUG - 第19代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第20代进化完成: 平均能耗=34.1, 可行解=300/300, 缓存命中率=4.8%
DEBUG - 第20代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第21代进化完成: 平均能耗=34.0, 可行解=300/300, 缓存命中率=5.0%
DEBUG - 第21代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第22代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.1%
DEBUG - 第22代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第23代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.3%
DEBUG - 第23代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第24代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.3%
DEBUG - 第24代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第25代进化完成: 平均能耗=33.8, 可行解=300/300, 缓存命中率=5.3%
DEBUG - 第25代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第26代进化完成: 平均能耗=33.7, 可行解=300/300, 缓存命中率=5.3%
DEBUG - 第26代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第27代进化完成: 平均能耗=33.6, 可行解=300/300, 缓存命中率=5.4%
DEBUG - 第27代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第28代进化完成: 平均能耗=33.7, 可行解=300/300, 缓存命中率=5.5%
DEBUG - 第28代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第29代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.5%
DEBUG - 第29代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第30代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.6%
DEBUG - 第30代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第31代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.7%
DEBUG - 第31代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第32代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.8%
DEBUG - 第32代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第33代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=5.7%
DEBUG - 第33代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第34代进化完成: 平均能耗=33.8, 可行解=300/300, 缓存命中率=5.9%
DEBUG - 第34代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第35代进化完成: 平均能耗=33.8, 可行解=300/300, 缓存命中率=5.9%
DEBUG - 第35代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第36代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=6.0%
DEBUG - 第36代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第37代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=6.1%
DEBUG - 第37代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第38代进化完成: 平均能耗=33.8, 可行解=300/300, 缓存命中率=6.2%
DEBUG - 第38代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第39代进化完成: 平均能耗=33.8, 可行解=300/300, 缓存命中率=6.3%
DEBUG - 第39代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第40代进化完成: 平均能耗=33.9, 可行解=300/300, 缓存命中率=6.5%
DEBUG - 第40代完成: 最佳能耗=17.5
INFO - 种群评估完成: 300 个个体
DEBUG - 改进NSGA-III环境选择完成: 300 个个体
INFO - 第41代进化完成: 平均能耗=33.8, 可行解=300/300, 缓存命中率=6.6%
DEBUG - 第41代完成: 最佳能耗=17.5
INFO - 生成参考点: 110 个
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
ERROR - 创建真正随机个体失败: name 'RenovationMode' is not defined
INFO - 第二阶段智能种群初始化完成: 600 个个体 (真实数据:120, 启发式:90, 极端:90, 随机:300)
INFO - 种群评估完成: 600 个个体
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 600 个个体
INFO - 第1代进化完成: 平均能耗=35.2, 可行解=600/600, 缓存命中率=1.0%
DEBUG - 第1代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 345 个个体
INFO - 第2代进化完成: 平均能耗=34.6, 可行解=345/345, 缓存命中率=1.6%
DEBUG - 第2代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 364 个个体
INFO - 第3代进化完成: 平均能耗=34.8, 可行解=364/364, 缓存命中率=1.5%
DEBUG - 第3代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 371 个个体
INFO - 第4代进化完成: 平均能耗=34.8, 可行解=371/371, 缓存命中率=1.8%
DEBUG - 第4代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 372 个个体
INFO - 第5代进化完成: 平均能耗=34.8, 可行解=372/372, 缓存命中率=1.9%
INFO - 自适应参数调整: 变异率 0.850→0.900, 交叉率 0.990→0.990, 停滞4代, 多样性0.252
DEBUG - 第5代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 371 个个体
INFO - 第6代进化完成: 平均能耗=34.8, 可行解=371/371, 缓存命中率=2.0%
INFO - 自适应参数调整: 变异率 0.900→0.950, 交叉率 0.990→0.990, 停滞5代, 多样性0.251
DEBUG - 第6代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 368 个个体
INFO - 第7代进化完成: 平均能耗=34.8, 可行解=368/368, 缓存命中率=1.9%
DEBUG - 第7代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 374 个个体
INFO - 第8代进化完成: 平均能耗=34.8, 可行解=374/374, 缓存命中率=2.0%
DEBUG - 第8代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 376 个个体
INFO - 第9代进化完成: 平均能耗=34.9, 可行解=376/376, 缓存命中率=1.9%
DEBUG - 第9代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 367 个个体
INFO - 第10代进化完成: 平均能耗=35.0, 可行解=367/367, 缓存命中率=1.9%
DEBUG - 第10代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 372 个个体
INFO - 第11代进化完成: 平均能耗=35.0, 可行解=372/372, 缓存命中率=2.0%
DEBUG - 第11代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 387 个个体
INFO - 第12代进化完成: 平均能耗=35.2, 可行解=387/387, 缓存命中率=2.2%
DEBUG - 第12代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 396 个个体
INFO - 第13代进化完成: 平均能耗=35.2, 可行解=396/396, 缓存命中率=2.4%
DEBUG - 第13代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 405 个个体
INFO - 第14代进化完成: 平均能耗=35.3, 可行解=405/405, 缓存命中率=2.6%
DEBUG - 第14代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 413 个个体
INFO - 第15代进化完成: 平均能耗=35.3, 可行解=413/413, 缓存命中率=2.7%
DEBUG - 第15代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 420 个个体
INFO - 第16代进化完成: 平均能耗=35.3, 可行解=420/420, 缓存命中率=2.9%
DEBUG - 第16代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 420 个个体
INFO - 第17代进化完成: 平均能耗=35.3, 可行解=420/420, 缓存命中率=2.9%
DEBUG - 第17代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 390 个个体
INFO - 第18代进化完成: 平均能耗=34.9, 可行解=390/390, 缓存命中率=2.9%
DEBUG - 第18代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 411 个个体
INFO - 第19代进化完成: 平均能耗=35.0, 可行解=411/411, 缓存命中率=2.9%
DEBUG - 第19代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 394 个个体
INFO - 第20代进化完成: 平均能耗=35.2, 可行解=394/394, 缓存命中率=3.1%
DEBUG - 第20代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 404 个个体
INFO - 第21代进化完成: 平均能耗=35.2, 可行解=404/404, 缓存命中率=3.2%
DEBUG - 第21代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 418 个个体
INFO - 第22代进化完成: 平均能耗=35.0, 可行解=418/418, 缓存命中率=3.3%
DEBUG - 第22代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 434 个个体
INFO - 第23代进化完成: 平均能耗=34.8, 可行解=434/434, 缓存命中率=3.4%
DEBUG - 第23代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 443 个个体
INFO - 第24代进化完成: 平均能耗=34.8, 可行解=443/443, 缓存命中率=3.5%
DEBUG - 第24代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 448 个个体
INFO - 第25代进化完成: 平均能耗=34.8, 可行解=448/448, 缓存命中率=3.6%
DEBUG - 第25代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 456 个个体
INFO - 第26代进化完成: 平均能耗=34.7, 可行解=456/456, 缓存命中率=3.7%
DEBUG - 第26代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 461 个个体
INFO - 第27代进化完成: 平均能耗=34.5, 可行解=461/461, 缓存命中率=3.8%
DEBUG - 第27代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 478 个个体
INFO - 第28代进化完成: 平均能耗=34.3, 可行解=478/478, 缓存命中率=3.9%
DEBUG - 第28代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 489 个个体
INFO - 第29代进化完成: 平均能耗=34.1, 可行解=489/489, 缓存命中率=4.0%
DEBUG - 第29代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 502 个个体
INFO - 第30代进化完成: 平均能耗=33.9, 可行解=502/502, 缓存命中率=4.1%
DEBUG - 第30代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 521 个个体
INFO - 第31代进化完成: 平均能耗=33.7, 可行解=521/521, 缓存命中率=4.2%
DEBUG - 第31代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 531 个个体
INFO - 第32代进化完成: 平均能耗=33.5, 可行解=531/531, 缓存命中率=4.5%
DEBUG - 第32代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 539 个个体
INFO - 第33代进化完成: 平均能耗=33.4, 可行解=539/539, 缓存命中率=4.7%
DEBUG - 第33代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 550 个个体
INFO - 第34代进化完成: 平均能耗=33.3, 可行解=550/550, 缓存命中率=4.8%
DEBUG - 第34代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 553 个个体
INFO - 第35代进化完成: 平均能耗=33.2, 可行解=553/553, 缓存命中率=4.9%
DEBUG - 第35代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 554 个个体
INFO - 第36代进化完成: 平均能耗=33.1, 可行解=554/554, 缓存命中率=4.9%
DEBUG - 第36代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 566 个个体
INFO - 第37代进化完成: 平均能耗=33.0, 可行解=566/566, 缓存命中率=5.0%
DEBUG - 第37代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 570 个个体
INFO - 第38代进化完成: 平均能耗=33.0, 可行解=570/570, 缓存命中率=5.0%
DEBUG - 第38代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 583 个个体
INFO - 第39代进化完成: 平均能耗=33.0, 可行解=583/583, 缓存命中率=5.0%
DEBUG - 第39代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 591 个个体
INFO - 第40代进化完成: 平均能耗=32.9, 可行解=591/591, 缓存命中率=5.1%
DEBUG - 第40代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 599 个个体
INFO - 第41代进化完成: 平均能耗=32.9, 可行解=599/599, 缓存命中率=5.1%
DEBUG - 第41代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 582 个个体
INFO - 第42代进化完成: 平均能耗=32.7, 可行解=582/582, 缓存命中率=5.2%
DEBUG - 第42代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 586 个个体
INFO - 第43代进化完成: 平均能耗=32.7, 可行解=586/586, 缓存命中率=5.3%
DEBUG - 第43代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 590 个个体
INFO - 第44代进化完成: 平均能耗=32.7, 可行解=590/590, 缓存命中率=5.4%
DEBUG - 第44代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 584 个个体
INFO - 第45代进化完成: 平均能耗=32.7, 可行解=584/584, 缓存命中率=5.5%
DEBUG - 第45代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 590 个个体
INFO - 第46代进化完成: 平均能耗=32.7, 可行解=590/590, 缓存命中率=5.6%
DEBUG - 第46代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 594 个个体
INFO - 第47代进化完成: 平均能耗=32.8, 可行解=594/594, 缓存命中率=5.6%
DEBUG - 第47代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 596 个个体
INFO - 第48代进化完成: 平均能耗=32.8, 可行解=596/596, 缓存命中率=5.7%
DEBUG - 第48代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 593 个个体
INFO - 第49代进化完成: 平均能耗=32.7, 可行解=593/593, 缓存命中率=5.8%
DEBUG - 第49代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 599 个个体
INFO - 第50代进化完成: 平均能耗=32.7, 可行解=599/599, 缓存命中率=5.8%
DEBUG - 第50代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 600 个个体
INFO - 第51代进化完成: 平均能耗=32.7, 可行解=600/600, 缓存命中率=5.8%
DEBUG - 第51代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 600 个个体
INFO - 第52代进化完成: 平均能耗=32.6, 可行解=600/600, 缓存命中率=5.8%
DEBUG - 第52代完成: 最佳能耗=18.4
INFO - 种群评估完成: 600 个个体
DEBUG - 改进NSGA-III环境选择完成: 600 个个体
INFO - 第53代进化完成: 平均能耗=32.7, 可行解=600/600, 缓存命中率=5.8%
DEBUG - 第53代完成: 最佳能耗=18.4
INFO - 生成参考点: 110 个
INFO - 增强多样性种群初始化完成: 60 个个体 (真实数据:9, 启发式:6, 极端:9, 高度随机:36)
INFO - 种群评估完成: 60 个个体
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第1代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=7.5%
DEBUG - 第1代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第2代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=8.3%
DEBUG - 第2代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第3代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=7.9%
DEBUG - 第3代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第4代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=7.0%
DEBUG - 第4代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第5代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=6.7%
INFO - 自适应参数调整: 变异率 0.400→0.450, 交叉率 0.750→0.750, 停滞4代, 多样性0.264
DEBUG - 第5代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第6代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=6.7%
INFO - 自适应参数调整: 变异率 0.450→0.500, 交叉率 0.750→0.750, 停滞5代, 多样性0.251
DEBUG - 第6代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第7代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=6.2%
INFO - 自适应参数调整: 变异率 0.500→0.550, 交叉率 0.750→0.750, 停滞6代, 多样性0.240
DEBUG - 第7代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第8代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=6.1%
INFO - 自适应参数调整: 变异率 0.550→0.600, 交叉率 0.750→0.750, 停滞7代, 多样性0.251
DEBUG - 第8代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第9代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=6.0%
INFO - 自适应参数调整: 变异率 0.600→0.650, 交叉率 0.750→0.750, 停滞8代, 多样性0.246
DEBUG - 第9代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第10代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=6.4%
INFO - 自适应参数调整: 变异率 0.650→0.700, 交叉率 0.750→0.750, 停滞9代, 多样性0.233
DEBUG - 第10代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第11代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=7.2%
INFO - 自适应参数调整: 变异率 0.700→0.750, 交叉率 0.750→0.750, 停滞10代, 多样性0.225
DEBUG - 第11代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第12代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=8.1%
INFO - 自适应参数调整: 变异率 0.750→0.800, 交叉率 0.750→0.750, 停滞11代, 多样性0.208
DEBUG - 第12代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第13代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=8.8%
INFO - 自适应参数调整: 变异率 0.800→0.850, 交叉率 0.750→0.750, 停滞12代, 多样性0.199
DEBUG - 第13代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第14代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=9.4%
INFO - 自适应参数调整: 变异率 0.850→0.900, 交叉率 0.750→0.750, 停滞13代, 多样性0.186
DEBUG - 第14代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第15代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=9.9%
INFO - 自适应参数调整: 变异率 0.900→0.950, 交叉率 0.750→0.750, 停滞14代, 多样性0.191
DEBUG - 第15代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第16代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.1%
DEBUG - 第16代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第17代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.0%
INFO - 自适应参数调整: 变异率 0.950→0.980, 交叉率 0.750→0.850, 停滞1代, 多样性0.145
DEBUG - 第17代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第18代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.0%
DEBUG - 第18代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第19代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=9.9%
DEBUG - 第19代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第20代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=9.8%
INFO - 自适应参数调整: 变异率 0.980→0.550, 交叉率 0.850→0.850, 停滞4代, 多样性0.144
DEBUG - 第20代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第21代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=9.8%
INFO - 自适应参数调整: 变异率 0.550→0.600, 交叉率 0.850→0.850, 停滞5代, 多样性0.133
DEBUG - 第21代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第22代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.0%
INFO - 自适应参数调整: 变异率 0.600→0.650, 交叉率 0.850→0.850, 停滞6代, 多样性0.141
DEBUG - 第22代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第23代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.3%
INFO - 自适应参数调整: 变异率 0.650→0.600, 交叉率 0.850→0.850, 停滞7代, 多样性0.150
DEBUG - 第23代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第24代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.0%
INFO - 自适应参数调整: 变异率 0.600→0.750, 交叉率 0.850→0.850, 停滞8代, 多样性0.147
DEBUG - 第24代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第25代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.1%
INFO - 自适应参数调整: 变异率 0.750→0.800, 交叉率 0.850→0.850, 停滞9代, 多样性0.137
DEBUG - 第25代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第26代进化完成: 平均能耗=0.7, 可行解=60/60, 缓存命中率=10.2%
INFO - 自适应参数调整: 变异率 0.800→0.850, 交叉率 0.850→0.850, 停滞10代, 多样性0.133
DEBUG - 第26代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第27代进化完成: 平均能耗=0.7, 可行解=60/60, 缓存命中率=10.3%
INFO - 自适应参数调整: 变异率 0.850→0.900, 交叉率 0.850→0.850, 停滞11代, 多样性0.130
DEBUG - 第27代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第28代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.4%
INFO - 自适应参数调整: 变异率 0.900→0.950, 交叉率 0.850→0.850, 停滞12代, 多样性0.139
DEBUG - 第28代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第29代进化完成: 平均能耗=0.7, 可行解=60/60, 缓存命中率=10.6%
INFO - 自适应参数调整: 变异率 0.950→0.900, 交叉率 0.850→0.850, 停滞13代, 多样性0.157
DEBUG - 第29代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第30代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.5%
INFO - 自适应参数调整: 变异率 0.900→0.980, 交叉率 0.850→0.850, 停滞14代, 多样性0.138
DEBUG - 第30代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第31代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=10.6%
DEBUG - 第31代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第32代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.0%
DEBUG - 第32代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第33代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.0%
DEBUG - 第33代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第34代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.2%
DEBUG - 第34代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第35代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.2%
DEBUG - 第35代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第36代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.3%
DEBUG - 第36代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第37代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.1%
DEBUG - 第37代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第38代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.4%
DEBUG - 第38代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第39代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.6%
DEBUG - 第39代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第40代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.5%
DEBUG - 第40代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第41代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.7%
DEBUG - 第41代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第42代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.8%
DEBUG - 第42代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第43代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=11.9%
DEBUG - 第43代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第44代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=12.0%
DEBUG - 第44代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第45代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=12.4%
DEBUG - 第45代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第46代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=12.6%
DEBUG - 第46代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第47代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=12.6%
DEBUG - 第47代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第48代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=12.8%
DEBUG - 第48代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第49代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=12.8%
DEBUG - 第49代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第50代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=13.0%
DEBUG - 第50代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第51代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=13.1%
DEBUG - 第51代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第52代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=13.2%
DEBUG - 第52代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第53代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=13.5%
DEBUG - 第53代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第54代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=13.6%
DEBUG - 第54代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第55代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=13.7%
DEBUG - 第55代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第56代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=13.9%
DEBUG - 第56代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第57代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=14.2%
DEBUG - 第57代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第58代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=14.3%
INFO - 自适应参数调整: 变异率 0.980→0.950, 交叉率 0.850→0.850, 停滞42代, 多样性0.150
DEBUG - 第58代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第59代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.3%
INFO - 自适应参数调整: 变异率 0.950→0.980, 交叉率 0.850→0.850, 停滞43代, 多样性0.139
DEBUG - 第59代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第60代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.5%
DEBUG - 第60代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第61代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.6%
DEBUG - 第61代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第62代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.8%
DEBUG - 第62代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第63代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.8%
DEBUG - 第63代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第64代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=14.9%
DEBUG - 第64代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第65代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=14.9%
DEBUG - 第65代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第66代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第66代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第67代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第67代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第68代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=15.1%
DEBUG - 第68代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第69代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=15.1%
DEBUG - 第69代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第70代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=15.1%
DEBUG - 第70代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第71代进化完成: 平均能耗=0.8, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第71代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第72代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
INFO - 自适应参数调整: 变异率 0.980→0.950, 交叉率 0.850→0.850, 停滞56代, 多样性0.155
DEBUG - 第72代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第73代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
INFO - 自适应参数调整: 变异率 0.950→0.980, 交叉率 0.850→0.850, 停滞57代, 多样性0.137
DEBUG - 第73代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第74代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第74代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第75代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第75代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第76代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第76代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第77代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.9%
DEBUG - 第77代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第78代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.9%
DEBUG - 第78代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第79代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.8%
DEBUG - 第79代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第80代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第80代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第81代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第81代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第82代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=14.9%
DEBUG - 第82代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第83代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
DEBUG - 第83代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第84代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
INFO - 自适应参数调整: 变异率 0.980→0.950, 交叉率 0.850→0.850, 停滞68代, 多样性0.153
DEBUG - 第84代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第85代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.0%
INFO - 自适应参数调整: 变异率 0.950→0.980, 交叉率 0.850→0.850, 停滞69代, 多样性0.137
DEBUG - 第85代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第86代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.1%
DEBUG - 第86代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第87代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.2%
DEBUG - 第87代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第88代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.3%
DEBUG - 第88代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第89代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.4%
DEBUG - 第89代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第90代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.3%
DEBUG - 第90代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第91代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.3%
DEBUG - 第91代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第92代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.3%
DEBUG - 第92代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第93代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.3%
DEBUG - 第93代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第94代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.3%
DEBUG - 第94代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第95代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.4%
DEBUG - 第95代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第96代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.5%
DEBUG - 第96代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第97代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.5%
DEBUG - 第97代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第98代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.6%
DEBUG - 第98代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第99代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.6%
DEBUG - 第99代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第100代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.7%
DEBUG - 第100代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第101代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.8%
DEBUG - 第101代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第102代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.8%
DEBUG - 第102代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第103代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=15.9%
DEBUG - 第103代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第104代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第104代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第105代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第105代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第106代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.1%
DEBUG - 第106代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第107代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.1%
DEBUG - 第107代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第108代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第108代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第109代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第109代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第110代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第110代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第111代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第111代完成: 最佳能耗=0.6
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第112代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第112代完成: 最佳能耗=0.7
INFO - 种群评估完成: 60 个个体
DEBUG - 改进NSGA-III环境选择完成: 60 个个体
INFO - 第113代进化完成: 平均能耗=0.9, 可行解=60/60, 缓存命中率=16.0%
DEBUG - 第113代完成: 最佳能耗=0.7
INFO - 生成参考点: 110 个
INFO - 增强多样性种群初始化完成: 50 个个体 (真实数据:7, 启发式:5, 极端:7, 高度随机:31)
INFO - 种群评估完成: 50 个个体
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第1代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.0%
INFO - 自适应参数调整: 变异率 0.600→0.700, 交叉率 0.600→0.850, 停滞0代, 多样性0.141
DEBUG - 第1代完成: 最佳能耗=0.3
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第2代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=12.0%
DEBUG - 第2代完成: 最佳能耗=0.3
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第3代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.0%
DEBUG - 第3代完成: 最佳能耗=0.3
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第4代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=10.4%
DEBUG - 第4代完成: 最佳能耗=0.3
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第5代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.3%
INFO - 自适应参数调整: 变异率 0.700→0.650, 交叉率 0.850→0.850, 停滞4代, 多样性0.174
DEBUG - 第5代完成: 最佳能耗=0.3
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第6代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.1%
INFO - 自适应参数调整: 变异率 0.650→0.700, 交叉率 0.850→0.850, 停滞5代, 多样性0.166
DEBUG - 第6代完成: 最佳能耗=0.3
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第7代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=8.2%
INFO - 自适应参数调整: 变异率 0.700→0.750, 交叉率 0.850→0.850, 停滞6代, 多样性0.173
DEBUG - 第7代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第8代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=8.2%
INFO - 自适应参数调整: 变异率 0.750→0.800, 交叉率 0.850→0.850, 停滞7代, 多样性0.214
DEBUG - 第8代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第9代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=8.4%
INFO - 自适应参数调整: 变异率 0.800→0.850, 交叉率 0.850→0.850, 停滞8代, 多样性0.212
DEBUG - 第9代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第10代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=8.0%
INFO - 自适应参数调整: 变异率 0.850→0.900, 交叉率 0.850→0.850, 停滞9代, 多样性0.199
DEBUG - 第10代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第11代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=8.7%
INFO - 自适应参数调整: 变异率 0.900→0.950, 交叉率 0.850→0.850, 停滞10代, 多样性0.210
DEBUG - 第11代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第12代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.2%
DEBUG - 第12代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第13代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.3%
DEBUG - 第13代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第14代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.7%
DEBUG - 第14代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第15代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.8%
DEBUG - 第15代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第16代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.5%
DEBUG - 第16代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第17代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.7%
DEBUG - 第17代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第18代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=9.8%
DEBUG - 第18代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第19代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=10.2%
DEBUG - 第19代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第20代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=10.1%
DEBUG - 第20代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第21代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=10.3%
DEBUG - 第21代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第22代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=10.5%
DEBUG - 第22代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第23代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=10.6%
DEBUG - 第23代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第24代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=10.9%
DEBUG - 第24代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第25代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.5%
INFO - 自适应参数调整: 变异率 0.950→0.980, 交叉率 0.850→0.850, 停滞24代, 多样性0.135
DEBUG - 第25代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第26代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.3%
DEBUG - 第26代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第27代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.4%
DEBUG - 第27代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第28代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.7%
DEBUG - 第28代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第29代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.7%
DEBUG - 第29代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第30代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.7%
INFO - 自适应参数调整: 变异率 0.980→0.950, 交叉率 0.850→0.850, 停滞29代, 多样性0.152
DEBUG - 第30代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第31代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.5%
INFO - 自适应参数调整: 变异率 0.950→0.980, 交叉率 0.850→0.850, 停滞30代, 多样性0.146
DEBUG - 第31代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第32代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.8%
DEBUG - 第32代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第33代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.9%
DEBUG - 第33代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第34代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.9%
DEBUG - 第34代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第35代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.7%
DEBUG - 第35代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第36代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=11.9%
DEBUG - 第36代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第37代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=12.1%
DEBUG - 第37代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第38代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=12.4%
DEBUG - 第38代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第39代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=12.6%
DEBUG - 第39代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第40代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=12.7%
DEBUG - 第40代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第41代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=12.9%
DEBUG - 第41代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第42代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=12.9%
DEBUG - 第42代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第43代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=13.2%
DEBUG - 第43代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第44代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=13.3%
DEBUG - 第44代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第45代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=13.2%
DEBUG - 第45代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第46代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=13.6%
DEBUG - 第46代完成: 最佳能耗=0.2
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第47代进化完成: 平均能耗=0.3, 可行解=50/50, 缓存命中率=13.8%
DEBUG - 第47代完成: 最佳能耗=0.2
INFO - 生成参考点: 110 个
INFO - 增强多样性种群初始化完成: 50 个个体 (真实数据:7, 启发式:5, 极端:7, 高度随机:31)
INFO - 种群评估完成: 50 个个体
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第1代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=5.0%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第2代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=8.0%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第3代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=10.5%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第4代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=12.4%
INFO - 自适应参数调整: 变异率 0.600→0.400, 交叉率 0.600→0.600, 停滞3代, 多样性0.067
INFO - 生成参考点: 110 个
INFO - 增强多样性种群初始化完成: 50 个个体 (真实数据:7, 启发式:5, 极端:7, 高度随机:31)
INFO - 种群评估完成: 50 个个体
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第1代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=8.0%
INFO - 自适应参数调整: 变异率 0.600→0.580, 交叉率 0.600→0.650, 停滞0代, 多样性0.103
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第2代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=8.7%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第3代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=10.0%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第4代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=10.8%
INFO - 自适应参数调整: 变异率 0.580→0.400, 交叉率 0.650→0.650, 停滞3代, 多样性0.068
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第5代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=11.7%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第6代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=11.7%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第7代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=11.2%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第8代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=10.9%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第9代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=11.6%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第10代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=12.0%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第11代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=12.3%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第12代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=13.7%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第13代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=15.1%
INFO - 自适应参数调整: 变异率 0.400→0.250, 交叉率 0.650→0.800, 停滞12代, 多样性0.048
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第14代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=16.7%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第15代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=17.2%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第16代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=17.8%
INFO - 自适应参数调整: 变异率 0.250→0.400, 交叉率 0.800→0.800, 停滞15代, 多样性0.051
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第17代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=18.7%
INFO - 自适应参数调整: 变异率 0.400→0.250, 交叉率 0.800→0.800, 停滞16代, 多样性0.047
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第18代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=18.7%
INFO - 自适应参数调整: 变异率 0.250→0.400, 交叉率 0.800→0.800, 停滞17代, 多样性0.052
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第19代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=19.2%
INFO - 自适应参数调整: 变异率 0.400→0.250, 交叉率 0.800→0.800, 停滞18代, 多样性0.045
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第20代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=19.2%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第21代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=19.5%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第22代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=19.7%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第23代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=20.2%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第24代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=20.6%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第25代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=20.8%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第26代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=21.0%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第27代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=21.6%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第28代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=22.1%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第29代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=22.9%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第30代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=23.5%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第31代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=24.2%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第32代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=24.4%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第33代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=24.9%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第34代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=25.3%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第35代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=25.6%
INFO - 种群评估完成: 50 个个体
DEBUG - 改进NSGA-III环境选择完成: 50 个个体
INFO - 第36代进化完成: 平均能耗=50.0, 可行解=50/50, 缓存命中率=26.2%
INFO - 生成参考点: 110 个
INFO - 生成参考点: 110 个
INFO - 生成参考点: 110 个
