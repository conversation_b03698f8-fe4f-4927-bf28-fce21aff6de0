"""
数据验证模块 - 确保不使用模拟数据
严格验证所有输入数据的真实性，禁止模拟数据进入系统
"""

import numpy as np
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import cv2

from .config import get_config
from .logging_config import get_logger, LogContext
from .exceptions import DataValidationError, handle_exception
from .data_structures import FacadeElements, BuildingElement


class DataValidator:
    """
    数据验证器 - 严格禁止模拟数据
    
    功能：
    1. 验证图像数据的真实性
    2. 检测和拒绝模拟数据
    3. 验证尺寸参数的合理性
    4. 确保数据来源的可追溯性
    """
    
    def __init__(self):
        """初始化数据验证器"""
        self.config = get_config()
        self.logger = get_logger(__name__)
        
        # 验证配置
        self.strict_validation = True  # 严格验证模式
        self.allow_simulated_data = False  # 禁止模拟数据
        
        # 合理性检查参数
        self.min_pixel_to_meter_ratio = 0.001  # 最小像素-米比例
        self.max_pixel_to_meter_ratio = 0.1    # 最大像素-米比例
        self.min_facade_area = 10.0            # 最小立面面积（平方米）
        self.max_facade_area = 10000.0         # 最大立面面积（平方米）
        
        self.logger.info("数据验证器初始化完成 - 严格模式，禁止模拟数据")
    
    @handle_exception
    def validate_image_data(self, image_path: str) -> Dict[str, Any]:
        """
        验证图像数据的真实性
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            验证结果字典
            
        Raises:
            DataValidationError: 验证失败时抛出
        """
        with LogContext("图像数据验证", self.logger):
            validation_result = {
                'is_valid': True,
                'validation_errors': [],
                'validation_warnings': [],
                'image_properties': {},
                'data_source_info': {}
            }
            
            try:
                # 1. 文件存在性验证
                if not Path(image_path).exists():
                    validation_result['validation_errors'].append(f"图像文件不存在: {image_path}")
                    validation_result['is_valid'] = False
                    return validation_result
                
                # 2. 加载图像并验证 - 使用支持中文路径的方法
                try:
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                    
                    nparr = np.frombuffer(image_data, np.uint8)
                    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                    
                    if image is None:
                        validation_result['validation_errors'].append(f"无法解码图像文件: {image_path}")
                        validation_result['is_valid'] = False
                        return validation_result
                        
                except Exception as e:
                    validation_result['validation_errors'].append(f"图像文件加载失败: {str(e)}")
                    validation_result['is_valid'] = False
                    return validation_result
                
                # 3. 图像属性验证
                height, width, channels = image.shape
                validation_result['image_properties'] = {
                    'width': width,
                    'height': height,
                    'channels': channels,
                    'total_pixels': width * height,
                    'file_size_bytes': Path(image_path).stat().st_size
                }
                
                # 4. 检测模拟数据特征
                simulation_indicators = self._detect_simulation_indicators(image, image_path)
                if simulation_indicators['has_simulation_indicators']:
                    validation_result['validation_errors'].extend(simulation_indicators['indicators'])
                    validation_result['is_valid'] = False
                
                # 5. 验证图像质量
                quality_check = self._validate_image_quality(image)
                if not quality_check['is_acceptable']:
                    validation_result['validation_warnings'].extend(quality_check['warnings'])
                
                # 6. 数据源信息
                validation_result['data_source_info'] = {
                    'file_path': str(image_path),
                    'file_name': Path(image_path).name,
                    'file_extension': Path(image_path).suffix,
                    'creation_time': Path(image_path).stat().st_ctime,
                    'modification_time': Path(image_path).stat().st_mtime
                }
                
                if validation_result['is_valid']:
                    self.logger.info(f"图像数据验证通过: {image_path}")
                else:
                    self.logger.error(f"图像数据验证失败: {len(validation_result['validation_errors'])} 个错误")
                
                return validation_result
                
            except Exception as e:
                raise DataValidationError(f"图像数据验证过程失败: {str(e)}") from e
    
    def _detect_simulation_indicators(self, image: np.ndarray, image_path: str) -> Dict[str, Any]:
        """检测模拟数据指标"""
        indicators = {
            'has_simulation_indicators': False,
            'indicators': []
        }
        
        try:
            # 1. 检查文件名中的模拟关键词
            filename = Path(image_path).name.lower()
            simulation_keywords = [
                'mock', 'fake', 'dummy', 'test', 'sample', 'demo', 
                'simulate', 'generated', 'synthetic', '模拟', '测试', '示例'
            ]
            
            for keyword in simulation_keywords:
                if keyword in filename:
                    indicators['indicators'].append(f"文件名包含模拟数据关键词: {keyword}")
                    indicators['has_simulation_indicators'] = True
            
            # 2. 检查图像的人工生成特征
            # 检查颜色分布是否过于规整（可能是人工生成的色块图）
            unique_colors = len(np.unique(image.reshape(-1, image.shape[2]), axis=0))
            total_pixels = image.shape[0] * image.shape[1]
            color_diversity_ratio = unique_colors / total_pixels
            
            if color_diversity_ratio < 0.01:  # 颜色种类过少
                indicators['indicators'].append(f"颜色多样性过低 ({color_diversity_ratio:.4f})，可能是人工生成的色块图")
                indicators['has_simulation_indicators'] = True
            
            # 3. 检查是否存在完美的几何形状（可能是人工绘制）
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            perfect_rectangles = 0
            for contour in contours:
                if cv2.contourArea(contour) > 100:  # 忽略小轮廓
                    epsilon = 0.02 * cv2.arcLength(contour, True)
                    approx = cv2.approxPolyDP(contour, epsilon, True)
                    if len(approx) == 4:  # 矩形
                        perfect_rectangles += 1
            
            if perfect_rectangles > 10:  # 过多完美矩形
                indicators['indicators'].append(f"检测到过多完美矩形 ({perfect_rectangles})，可能是人工绘制")
                indicators['has_simulation_indicators'] = True
            
            # 4. 检查纹理特征
            # 真实建筑图像应该有一定的纹理复杂度
            texture_variance = np.var(gray)
            if texture_variance < 100:  # 纹理过于平滑
                indicators['indicators'].append(f"图像纹理过于平滑 (方差: {texture_variance:.2f})，可能是简化的模拟图像")
                indicators['has_simulation_indicators'] = True
            
            return indicators
            
        except Exception as e:
            self.logger.warning(f"模拟数据检测失败: {str(e)}")
            return indicators
    
    def _validate_image_quality(self, image: np.ndarray) -> Dict[str, Any]:
        """验证图像质量"""
        quality_result = {
            'is_acceptable': True,
            'warnings': [],
            'quality_metrics': {}
        }
        
        try:
            height, width = image.shape[:2]
            
            # 1. 分辨率检查
            if width < 200 or height < 200:
                quality_result['warnings'].append(f"图像分辨率过低: {width}x{height}")
                quality_result['is_acceptable'] = False
            
            # 2. 宽高比检查
            aspect_ratio = width / height
            if aspect_ratio > 5 or aspect_ratio < 0.2:
                quality_result['warnings'].append(f"图像宽高比异常: {aspect_ratio:.2f}")
            
            # 3. 亮度检查
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            mean_brightness = np.mean(gray)
            if mean_brightness < 20 or mean_brightness > 235:
                quality_result['warnings'].append(f"图像亮度异常: {mean_brightness:.1f}")
            
            # 4. 对比度检查
            contrast = np.std(gray)
            if contrast < 10:
                quality_result['warnings'].append(f"图像对比度过低: {contrast:.1f}")
            
            quality_result['quality_metrics'] = {
                'resolution': f"{width}x{height}",
                'aspect_ratio': aspect_ratio,
                'mean_brightness': mean_brightness,
                'contrast': contrast
            }
            
            return quality_result
            
        except Exception as e:
            self.logger.warning(f"图像质量验证失败: {str(e)}")
            return quality_result
    
    @handle_exception
    def validate_pixel_to_meter_ratio(self, ratio: float, image_shape: Tuple[int, int]) -> Dict[str, Any]:
        """
        验证像素-米转换比例的合理性
        
        Args:
            ratio: 像素到米的转换比例
            image_shape: 图像尺寸 (height, width)
            
        Returns:
            验证结果
        """
        validation_result = {
            'is_valid': True,
            'validation_errors': [],
            'validation_warnings': [],
            'calculated_dimensions': {}
        }
        
        try:
            # 1. 基本范围检查
            if ratio <= 0:
                validation_result['validation_errors'].append(f"像素-米比例必须为正数: {ratio}")
                validation_result['is_valid'] = False
                return validation_result
            
            if ratio < self.min_pixel_to_meter_ratio or ratio > self.max_pixel_to_meter_ratio:
                validation_result['validation_errors'].append(
                    f"像素-米比例超出合理范围 [{self.min_pixel_to_meter_ratio}, {self.max_pixel_to_meter_ratio}]: {ratio}"
                )
                validation_result['is_valid'] = False
            
            # 2. 计算实际尺寸并验证合理性
            height_pixels, width_pixels = image_shape[:2]
            width_meters = width_pixels * ratio
            height_meters = height_pixels * ratio
            facade_area = width_meters * height_meters
            
            validation_result['calculated_dimensions'] = {
                'width_meters': width_meters,
                'height_meters': height_meters,
                'facade_area_sqm': facade_area,
                'aspect_ratio': width_meters / height_meters if height_meters > 0 else 0
            }
            
            # 3. 立面尺寸合理性检查
            if facade_area < self.min_facade_area or facade_area > self.max_facade_area:
                validation_result['validation_warnings'].append(
                    f"计算得到的立面面积可能不合理: {facade_area:.2f} m² "
                    f"(合理范围: {self.min_facade_area}-{self.max_facade_area} m²)"
                )
            
            # 4. 建筑尺寸常识检查
            if width_meters > 200 or height_meters > 100:
                validation_result['validation_warnings'].append(
                    f"计算得到的立面尺寸异常大: {width_meters:.1f}m × {height_meters:.1f}m"
                )
            
            if width_meters < 3 or height_meters < 2:
                validation_result['validation_warnings'].append(
                    f"计算得到的立面尺寸异常小: {width_meters:.1f}m × {height_meters:.1f}m"
                )
            
            return validation_result
            
        except Exception as e:
            raise DataValidationError(f"像素-米比例验证失败: {str(e)}") from e
    
    @handle_exception
    def validate_facade_elements(self, facade_elements: FacadeElements) -> Dict[str, Any]:
        """
        验证立面元素数据的真实性和合理性
        
        Args:
            facade_elements: 立面元素数据
            
        Returns:
            验证结果
        """
        validation_result = {
            'is_valid': True,
            'validation_errors': [],
            'validation_warnings': [],
            'element_statistics': {}
        }
        
        try:
            all_elements = facade_elements.get_all_elements()
            
            # 1. 基本数量检查
            if len(all_elements) == 0:
                validation_result['validation_errors'].append("未检测到任何建筑元素")
                validation_result['is_valid'] = False
                return validation_result
            
            # 2. 元素尺寸合理性检查
            unrealistic_elements = []
            for element in all_elements:
                # 检查元素尺寸
                if element.width <= 0 or element.height <= 0:
                    unrealistic_elements.append(f"{element.element_id}: 尺寸为零或负数")
                
                # 检查窗户尺寸合理性
                if hasattr(element, 'element_type') and element.element_type.value == 'window':
                    if element.width > 10 or element.height > 5:  # 窗户过大
                        unrealistic_elements.append(f"{element.element_id}: 窗户尺寸异常大 ({element.width:.2f}m × {element.height:.2f}m)")
                    
                    if element.width < 0.3 or element.height < 0.3:  # 窗户过小
                        unrealistic_elements.append(f"{element.element_id}: 窗户尺寸异常小 ({element.width:.2f}m × {element.height:.2f}m)")
            
            if unrealistic_elements:
                validation_result['validation_warnings'].extend(unrealistic_elements)
            
            # 3. 窗墙比合理性检查
            window_area = sum(w.area for w in facade_elements.windows)
            wall_area = sum(w.area for w in facade_elements.walls)
            
            if wall_area > 0:
                wwr = window_area / wall_area
                if wwr > 0.9:  # 窗墙比过高
                    validation_result['validation_warnings'].append(f"窗墙比异常高: {wwr:.3f}")
                elif wwr < 0.05:  # 窗墙比过低
                    validation_result['validation_warnings'].append(f"窗墙比异常低: {wwr:.3f}")
            
            # 4. 元素分布检查
            if len(facade_elements.windows) > 0:
                window_centers = [w.center for w in facade_elements.windows]
                # 检查是否所有窗户都在同一位置（可能是模拟数据）
                unique_positions = len(set(window_centers))
                if unique_positions == 1 and len(facade_elements.windows) > 1:
                    validation_result['validation_errors'].append("所有窗户位置相同，可能是模拟数据")
                    validation_result['is_valid'] = False
            
            # 5. 统计信息
            validation_result['element_statistics'] = {
                'total_elements': len(all_elements),
                'walls': len(facade_elements.walls),
                'windows': len(facade_elements.windows),
                'doors': len(facade_elements.doors),
                'window_wall_ratio': window_area / wall_area if wall_area > 0 else 0,
                'average_window_area': np.mean([w.area for w in facade_elements.windows]) if facade_elements.windows else 0
            }
            
            if validation_result['is_valid']:
                self.logger.info(f"立面元素验证通过: {len(all_elements)} 个元素")
            else:
                self.logger.error(f"立面元素验证失败: {len(validation_result['validation_errors'])} 个错误")
            
            return validation_result
            
        except Exception as e:
            raise DataValidationError(f"立面元素验证失败: {str(e)}") from e
    
    def validate_configuration(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证配置参数，确保不包含模拟数据设置
        
        Args:
            config_dict: 配置字典
            
        Returns:
            验证结果
        """
        validation_result = {
            'is_valid': True,
            'validation_errors': [],
            'validation_warnings': []
        }
        
        try:
            # 1. 检查是否明确禁用了模拟数据
            image_config = config_dict.get('image_processing', {})
            
            # 必须明确设置像素-米比例
            if 'pixel_to_meter_ratio' not in image_config:
                validation_result['validation_errors'].append("配置中缺少必需的 pixel_to_meter_ratio 参数")
                validation_result['is_valid'] = False
            
            # 2. 检查是否有模拟数据相关配置
            simulation_indicators = [
                'use_mock_data', 'enable_simulation', 'generate_test_data',
                'mock_', 'simulate_', 'fake_', 'dummy_'
            ]
            
            def check_dict_for_simulation(d: Dict[str, Any], path: str = ""):
                for key, value in d.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # 检查键名
                    for indicator in simulation_indicators:
                        if indicator in key.lower():
                            validation_result['validation_warnings'].append(
                                f"配置项可能涉及模拟数据: {current_path}"
                            )
                    
                    # 递归检查嵌套字典
                    if isinstance(value, dict):
                        check_dict_for_simulation(value, current_path)
            
            check_dict_for_simulation(config_dict)
            
            return validation_result
            
        except Exception as e:
            validation_result['validation_errors'].append(f"配置验证过程失败: {str(e)}")
            validation_result['is_valid'] = False
            return validation_result
    
    def generate_validation_report(self, validation_results: List[Dict[str, Any]]) -> str:
        """
        生成数据验证报告
        
        Args:
            validation_results: 验证结果列表
            
        Returns:
            Markdown格式的验证报告
        """
        try:
            report_lines = [
                "# 数据验证报告",
                "",
                f"**验证时间**: {self._get_current_time()}",
                f"**验证模式**: 严格模式（禁止模拟数据）",
                "",
                "## 验证摘要",
                ""
            ]
            
            total_validations = len(validation_results)
            passed_validations = sum(1 for r in validation_results if r.get('is_valid', False))
            
            report_lines.extend([
                f"- **总验证项**: {total_validations}",
                f"- **通过验证**: {passed_validations}",
                f"- **失败验证**: {total_validations - passed_validations}",
                f"- **通过率**: {passed_validations/total_validations*100:.1f}%" if total_validations > 0 else "- **通过率**: N/A",
                ""
            ])
            
            # 详细验证结果
            for i, result in enumerate(validation_results, 1):
                report_lines.extend([
                    f"## 验证项 {i}",
                    "",
                    f"**状态**: {'✅ 通过' if result.get('is_valid', False) else '❌ 失败'}",
                    ""
                ])
                
                if result.get('validation_errors'):
                    report_lines.extend([
                        "### 错误信息",
                        ""
                    ])
                    for error in result['validation_errors']:
                        report_lines.append(f"- ❌ {error}")
                    report_lines.append("")
                
                if result.get('validation_warnings'):
                    report_lines.extend([
                        "### 警告信息",
                        ""
                    ])
                    for warning in result['validation_warnings']:
                        report_lines.append(f"- ⚠️ {warning}")
                    report_lines.append("")
            
            return "\n".join(report_lines)
            
        except Exception as e:
            self.logger.error(f"生成验证报告失败: {str(e)}")
            return f"# 验证报告生成失败\n\n错误: {str(e)}"
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def create_data_validator() -> DataValidator:
    """
    创建数据验证器实例
    
    Returns:
        配置好的数据验证器
    """
    return DataValidator()